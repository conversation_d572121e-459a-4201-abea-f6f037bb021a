package admin

import (
	"zfeng/admin/controllers"
	"zfeng/middleware"

	"github.com/goccy/go-json"
	"github.com/gofiber/fiber/v2"
)

// InitAdminApp 初始化管理后台应用
func InitAdminApp() *fiber.App {
	app := fiber.New(fiber.Config{
		JSONEncoder: json.Marshal,
		JSONDecoder: json.Unmarshal,
	})

	// 中间件
	app.Use(middleware.InitFavicon())
	app.Use(middleware.InitCORS())
	app.Use(middleware.InitLimiter())
	app.Use(middleware.InitRecover())

	// 路由
	controllers.SetupRoutes(app)

	return app
}
