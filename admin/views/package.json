{"name": "views", "version": "0.0.1", "description": "zfeng - admin", "productName": "ZFeng 管理系统", "author": "饶兆锋 <<EMAIL>>", "type": "module", "private": true, "scripts": {"lint": "eslint --ext .js,.ts,.vue ./", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build"}, "dependencies": {"@quasar/extras": "^1.16.4", "axios": "^1.2.1", "echarts": "^5.6.0", "pinia": "^2.0.11", "qrcode": "^1.5.4", "quasar": "^2.17.4", "vue": "^3.4.18", "vue-i18n": "^9.2.2", "vue-router": "^4.0.12"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^2.0.0", "@quasar/app-vite": "^2.0.0-beta.12", "@quasar/cli": "^2.4.1", "@types/node": "^20.5.9", "@types/qrcode": "^1.5.5", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "autoprefixer": "^10.4.2", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^3.0.3", "typescript": "~5.5.3", "vite-plugin-checker": "^0.8.0", "vue-tsc": "^2.0.29"}, "engines": {"node": "^24 || ^22 || ^20 || ^18", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}