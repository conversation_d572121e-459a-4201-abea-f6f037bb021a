<template>
  <div class="q-pa-md" v-if="tableLoading">
    <div class="row q-mb-md">
      <FormInputs
        :inputs="searchForm.inputs"
        :params="searchForm.params"
        :common-options="commonOptions"
        :label="searchForm.label"
        :field="searchForm.field"
        :scan-field="searchForm.scanField"
        :options="searchForm.options"
        :show-search-button="true"
        @submit="searchSubmitFunc"
      />
    </div>

    <div class="row q-mb-md" v-if="tableTools && tableTools.length > 0">
      <template v-for="toolsRows in tableTools" :key="toolsRows.id">
        <div class="row q-col-gutter-sm">
          <template v-for="tool in toolsRows" :key="tool.id">
            <ButtonDialogComponents
              v-if="$userStore.hasRoute(tool.url)"
              :id="tool.id"
              :row="defaultQuery"
              :title="tool.title"
              :button="tool.button"
              :form="tool.form"
              :url="tool.url"
              :options="tool.options"
              :common-options="commonOptions"
              :small="tool.small"
              :content="tool.content"
              :size="tool.size"
              :full-width="tool.fullWidth"
              :full-height="tool.fullHeight"
              :selected="selected"
              :select-options="buttonDialogSelected"
              @done="fetchData"
              @close="closeDialogFunc"
            ></ButtonDialogComponents>
          </template>
        </div>
      </template>
    </div>

    <q-table
      :class="columnOptions && columnOptions.length > 0 ? 'my-sticky-last-column-table' : ''"
      :rows="rows"
      :columns="columns"
      row-key="id"
      v-model:pagination="searchForm.params.pagination"
      @request="onRequest"
      selection="multiple"
      v-model:selected="selected"
    >
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td auto-width>
            <q-checkbox v-model="props.selected" />
          </q-td>
          <q-td v-for="col in props.cols" :key="col.name" :props="props">
            <div v-if="col.name === 'options'">
              <template v-if="columnOptions && columnOptions.length > 0">
                <template v-for="optionsRows in columnOptions" :key="optionsRows.id">
                  <div class="row q-col-gutter-sm" :style="{minWidth: (optionsRows.length * 60) + 'px'}">
                    <template v-for="(options, optionsIndex) in optionsRows" :key="optionsIndex">
                      <ButtonDialogComponents
                        class="q-mb-xs"
                        :id="options.id"
                        v-if="displayEvalFunc(props.row, options.display as string) && $userStore.hasRoute(options.url)"
                        :title="options.title"
                        :type="options.type"
                        :button="options.button"
                        :form="options.form"
                        :url="options.url"
                        :options="options.options"
                        :common-options="commonOptions"
                        :small="options.small"
                        :content="options.content"
                        :size="options.size"
                        :full-width="options.fullWidth"
                        :full-height="options.fullHeight"
                        :selected="selected"
                        :select-options="buttonDialogSelected"
                        :row="props.row"
                        @done="fetchData"
                      />
                    </template>
                  </div>
                </template>
              </template>
            </div>
            <div v-else v-html="col.value"></div>
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>

  <!-- 排骨架加载图 -->
  <div class="q-pa-md" v-else>
    <div class="row q-gutter-sm q-mb-md">
      <div v-for="n in 10" :key="n">
        <q-skeleton height="32px" style="width: 160px;" type="QInput" />
      </div>
    </div>

    <div class="row q-gutter-sm q-mb-md">
      <q-skeleton height="32px" type="QBtn" />
      <q-skeleton height="32px" type="QBtn" />
    </div>

    <q-markup-table>
      <thead>
        <tr>
          <th class="text-left" style="width: 150px">
            <q-skeleton animation="blink" type="text" />
          </th>
          <th class="text-right">
            <q-skeleton animation="blink" type="text" />
          </th>
          <th class="text-right">
            <q-skeleton animation="blink" type="text" />
          </th>
          <th class="text-right">
            <q-skeleton animation="blink" type="text" />
          </th>
          <th class="text-right">
            <q-skeleton animation="blink" type="text" />
          </th>
          <th class="text-right">
            <q-skeleton animation="blink" type="text" />
          </th>
        </tr>
      </thead>

      <tbody>
        <tr v-for="n in 5" :key="n">
          <td class="text-left">
            <q-skeleton animation="blink" type="text" width="85px" />
          </td>
          <td class="text-right">
            <q-skeleton animation="blink" type="text" width="50px" />
          </td>
          <td class="text-right">
            <q-skeleton animation="blink" type="text" width="35px" />
          </td>
          <td class="text-right">
            <q-skeleton animation="blink" type="text" width="65px" />
          </td>
          <td class="text-right">
            <q-skeleton animation="blink" type="text" width="25px" />
          </td>
          <td class="text-right">
            <q-skeleton animation="blink" type="text" width="85px" />
          </td>
        </tr>
      </tbody>
    </q-markup-table>
  </div>

  <!-- 图片预览弹窗 -->
  <q-dialog v-model="imagesDialog">
    <q-card style="max-width: 650px">
      <q-card-section class="q-gutter-y-lg">
        <img v-for="image in imagesList" :key="image" :src="image" alt="" class="full-width">
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUpdate, onDeactivated } from 'vue';
import { requestConfigure } from 'src/apis';
import { api } from 'src/boot/axios';
import {useRoute} from 'vue-router';
import { useUserStore } from 'src/stores/users';
import {Form, ButtonDialog} from 'src/components/types'
import FormInputs from 'src/components/form.vue'
import ButtonDialogComponents from 'src/components/dialog.vue'

defineOptions({
  name: 'TablePage'
});

const $userStore = useUserStore();
const currentPath = useRoute();
const props = defineProps({
  url: {
    type: String,
    default: ''
  }
})

let defaultPath = currentPath.path
let defaultQuery = currentPath.query as any

if (props.url) {
  const urlObj = new URL(props.url, window.location.origin)
  defaultPath = urlObj.pathname
  defaultQuery = Object.fromEntries(urlObj.searchParams)
}
defaultQuery = Object.fromEntries(Object.entries(defaultQuery).map(([key, value]) => [key, isNaN(Number(value)) ? value : Number(value)]))

// 是否自动刷新
const refreshInterval = ref(null) as any
const autoRefreshIntervalTime = 10000
const autoRefresh = ref(false)

// 数据表格地址
const requestUrl = ref('')
// 公用 commonOptions
const commonOptions = ref<Record<string, Array<{ label: string; value: any }>>>({})
// 查询表单Form
const searchForm = ref<Form>({
  label: '',
  field: '',
  scanField: '',
  inputs: [],
  params: { pagination: {} },
  options: false
});

//  Table 工具栏 二维数组
const tableTools = ref() as any
// Table 表格列选项
const columns = ref<Array<any>>([]);
// Table 表格Options
const columnOptions = ref<ButtonDialog[][]>([])
// Table 表格数据
const rows = ref([]);
// TableLoading
const tableLoading = ref(false)
// Table 多选框
const selected = ref([]);
// Table 预设的多选框字段
const buttonDialogSelected = ref() as any

// 查询参数请求方法
const searchSubmitFunc = (formField: string, formIndex: number, formChildrenIndex: number, formParams: any) => {
  searchForm.value.params = formParams
  onRequest(searchForm.value.params)
}

const fetchData = () => {
  return api.post(requestUrl.value, { ...searchForm.value.params, ...defaultQuery }, {
    headers: {
      'X-Skip-Loading': autoRefresh.value ? 'false' : 'true'
    }
  }).then((res: any) => {
    rows.value = res.items
    searchForm.value.params.pagination.rowsNumber = res.count
  })
};

const onRequest = (props: any) => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;
  searchForm.value.params.pagination = { ...searchForm.value.params.pagination, page, rowsPerPage, sortBy, descending };
  fetchData();
};

onMounted(() => {
  // 如果不是登录状态那么停止请求配置
  if (!$userStore.isAuthenticated) {
    return;
  }

  // 请求配置文件
  requestConfigure(defaultPath, defaultQuery).then((res: any) => {
    requestUrl.value = res.url
    searchForm.value = res.searchs
    commonOptions.value = res.commonOptions

    // 公用options 添加 全部 option
    Object.keys(commonOptions.value).forEach(key => {
      if (Array.isArray(commonOptions.value[key])) {
        commonOptions.value[key].unshift({ label: '全部', value: null });
      }
    });

    buttonDialogSelected.value = res.selected
    tableTools.value = res.tools

    columns.value = res.columns
    // format 字符串转成 方法
    columns.value = columns.value.map(column => {
      if (typeof column.format === 'string' && column.format.trim() !== '') {
        try {
          column.format = eval(`(${column.format})`)
        } catch (error) {
          column.format = (val: any) => val;
        }
      }
      return column;
    });

    // 是否需要添加 Table options
    columnOptions.value = res.columnOptions
    if (columnOptions.value && columnOptions.value.length > 0) {
      columns.value.push({
        name: 'options',
        label: '操作按钮',
        field: 'options',
        align: 'center',
        sortable: false,
        required: true,
      })
    }

    // 请求数据表格
    fetchData().then(() => {
      tableLoading.value = true
      autoRefresh.value = res.autoRefresh
      startAutoRefresh()

      // 添加图片点击事件
      addClickImage()
    })
  })
});

onBeforeUpdate(() => {
  startAutoRefresh()
})

// 离开页面操作
onDeactivated(() => {
  stopAutoRefresh()
})

// 启动自动刷新功能
const startAutoRefresh = () => {
  if (autoRefresh.value && refreshInterval.value === null) {
    refreshInterval.value = setInterval(() => {
      fetchData()
    }, autoRefreshIntervalTime) // Convert seconds to milliseconds
  }
}

// 停止自动刷新功能
const stopAutoRefresh = () => {
  if (refreshInterval.value !== null) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

// displayEvalFunc 是否显示执行字符串
const displayEvalFunc = (row: any, display: string) => {
  return display === '' || eval(display)
}

// 关闭弹窗
const closeDialogFunc = () => {
  selected.value = []
}

// 打开图片预览弹窗
const imagesDialog = ref(false)
const imagesList = ref([]) as any

// 添加图片点击事件
const addClickImage = ()=>{
  setTimeout(()=>{
    document.querySelectorAll('.images').forEach(container => {
      container.addEventListener('click', function () {
        imagesList.value = []
        container.querySelectorAll('img').forEach((img:any)=>{
          imagesList.value.push(img.src)
          imagesDialog.value = true
        })
      });
    });
  })
}
</script>

<style lang="scss" scoped>
.my-sticky-last-column-table {
  /* specifying max-width so the example can
    highlight the sticky column on any browser window */
  max-width: 100%;

  thead tr:last-child th:last-child {
    /* bg color is important for th; just specify one */
    background-color: #f0f0f0;
  }

  td:last-child {
    background-color: #f0f0f0;
  }

  th:last-child,
  td:last-child {
    position: sticky;
    right: 0;
  }
}

td {
    max-width: 320px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // New styles for table cells
  td > div {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
