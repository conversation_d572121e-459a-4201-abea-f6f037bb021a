<template>
  <div class="q-ma-sm">
    <div class="row">
      <div v-for="item in statis" :key="item.label" class="col-md-3 col-sm-6 col-xs-12 q-pa-xs">
        <q-card :class="item.class">
          <q-card-actions>
            <div class="row full-width justify-between items-center">
              <div class="text-body1">{{ item.label }}</div>
              <div class="text-h6">{{ item.total }}</div>
            </div>
          </q-card-actions>
          <q-card-section style="padding: 8px;">
            <div class="row full-width items-center">
              <div>今日: {{ item.today }}</div>
              <div class="q-ml-md">昨日: {{ item.yesterday }}</div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>


    <div class="q-mt-lg q-mx-sm">
      <div :id="echartsId" style="height: 400px; width: 100%"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { requestIndex } from 'src/apis/index'
import { onMounted, ref } from 'vue'
import * as echarts from 'echarts';

defineOptions({
  name: 'IndexPage'
});

const echartsId = ref<string>('echarts')
const statis = ref<any[]>([])
const echatsXAxis = ref<string[]>([])

onMounted(() => {
  requestIndex().then((res: any) => {
    statis.value = res.statis
    echatsXAxis.value = res.category

    chartSetOptions()
  })
})


const chartSetOptions = () => {
  const echatsElement = document.getElementById(echartsId.value) as HTMLElement;
  const echartsInstance = echarts.init(echatsElement)
  let options: echarts.EChartsOption;

  const legendList: string[] = []
  const series: any[] = []
  for (const item of statis.value) {
    legendList.push(item.label)
    series.push({
      name: item.label,
      data: item.data,
      type: 'line',
    })
  }

  options = {
    tooltip: { trigger: 'axis' },
    legend: { data: legendList },
    grid: { left: '0', right: '0', bottom: '0', containLabel: true },
    toolbox: { feature: { saveAsImage: {} } },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: echatsXAxis.value,
    },
    yAxis: { type: 'value' },
    series: series,
  }

  echartsInstance.setOption(options);
  window.addEventListener('resize', () => {
    setTimeout(echartsInstance.resize, 300);
  });
}

</script>
