<template>
  <q-layout>
    <q-page-container>
      <q-page class="flex flex-center">
        <div class="chat-container">
          <!-- 头部区域 -->
          <div class="chat-header">
            <q-toolbar>
              <q-btn flat round icon="arrow_back" @click="$router.back()" />
              <q-toolbar-title class="text-center">
                <div class="user-info">
                  <span class="username">{{ chatUser }}</span>
                  <q-badge rounded color="green" v-if="isOnline" class="online-status">
                    在线
                  </q-badge>
                  <q-badge rounded color="grey" v-else class="online-status">
                    离线
                  </q-badge>
                </div>
              </q-toolbar-title>
              <q-avatar size="32px">
                <img src="https://cdn.quasar.dev/img/avatar.png" />
              </q-avatar>
            </q-toolbar>
          </div>

          <!-- 聊天记录区域 -->
          <div class="chat-messages" ref="messageContainer">
            <q-chat-message
              v-for="(message, index) in messages"
              :key="index"
              :sent="message.type === 'sent'"
              :text="message.text ? [message.text] : undefined"
              :stamp="message.time"
            >
              <template v-if="message.image">
                <img :src="message.image" class="chat-image" />
              </template>
            </q-chat-message>
          </div>

          <!-- 输入区域 -->
          <div class="chat-input">
            <q-input
              v-model="newMessage"
              dense
              outlined
              placeholder="输入消息..."
              @keyup.enter="sendMessage"
            >
              <template v-slot:before>
                <q-btn round flat icon="image" @click="triggerImageUpload">
                  <input
                    type="file"
                    ref="imageInput"
                    accept="image/*"
                    style="display: none"
                    @change="handleImageUpload"
                  />
                </q-btn>
              </template>
              <template v-slot:after>
                <q-btn round flat icon="send" @click="sendMessage" />
              </template>
            </q-input>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
defineOptions({
  name: 'ChatPageIndex'
})
import { ref, nextTick } from 'vue'


interface Message {
  text?: string
  image?: string
  type: 'sent' | 'received'
  time: string
}

const messages = ref<Message[]>([])
const newMessage = ref('')
const chatUser = ref('Setupdo 001客服') // 当前聊天对象名称
const isOnline = ref(true) // 在线状态
const messageContainer = ref<HTMLElement | null>(null)
const imageInput = ref<HTMLInputElement | null>(null)

const sendMessage = () => {
  if (!newMessage.value.trim()) return

  messages.value.push({
    text: newMessage.value,
    type: 'sent',
    time: new Date().toLocaleTimeString()
  })

  newMessage.value = ''
  nextTick(() => {
    scrollToBottom()
  })
}

const triggerImageUpload = () => {
  imageInput.value?.click()
}

const handleImageUpload = (event: Event) => {
  const input = event.target as HTMLInputElement
  if (input.files && input.files[0]) {
    const file = input.files[0]
    const reader = new FileReader()

    reader.onload = (e) => {
      messages.value.push({
        image: e.target?.result as string,
        type: 'sent',
        time: new Date().toLocaleTimeString()
      })
      nextTick(() => {
        scrollToBottom()
      })
    }

    reader.readAsDataURL(file)
    input.value = '' // Reset input
  }
}

const scrollToBottom = () => {
  if (messageContainer.value) {
    messageContainer.value.scrollTop = messageContainer.value.scrollHeight
  }
}
</script>

<style scoped>
.chat-container {
  width: 100%;
  max-width: 600px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.chat-header {
  background: white;
  border-bottom: 1px solid #ddd;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.username {
  font-weight: 500;
}

.online-status {
  font-size: 12px;
  padding: 2px 6px;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.chat-input {
  padding: 10px;
  background: white;
  border-top: 1px solid #ddd;
}

.chat-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  margin: 4px 0;
}
</style>
