<template>
  <div class="row items-center q-pa-md login-bg">
    <div class="col login-img text-center">
      <q-img :src="loginBackground" alt="Login Background"></q-img>
    </div>
    <div class="col text-center row justify-center">
      <div class="full-width" style="max-width: 320px">
        <q-img :src="logoImg" height="60px" width="160px" no-spinner alt="Logo"></q-img>
        <h1 class="text-h4 text-bold text-grey q-pb-lg">
          ZFeng Admin
        </h1>
        <q-form @submit.prevent="onSubmit" class="q-mt-lg">
          <q-input
            v-model="params.username"
            outlined
            dense
            label="账号"
            :rules="[val => !!val || '请输入账号']"
          >
            <template #prepend>
              <q-icon name="person" />
            </template>
          </q-input>
          <q-input
            v-model="params.password"
            outlined
            dense
            type="password"
            label="密码"
            :rules="[val => !!val || '请输入密码']"
          >
            <template #prepend>
              <q-icon name="lock" />
            </template>
          </q-input>
          <q-input
            v-model="params.captchaVal"
            outlined
            dense
            label="验证码"
            :rules="[val => !!val || '请输入验证码']"
          >
            <template #prepend>
              <q-icon name="security" />
            </template>
            <template #append>
              <q-img
                v-if="params.captchaId"
                :src="captchaUrl"
                width="120px"
                height="32px"
                @click="refreshCaptcha"
                alt="Captcha"
                class="cursor-pointer"
                no-spinner
              ></q-img>
            </template>
          </q-input>
          <div class="row justify-between items-center q-mt-sm">
            <q-checkbox
              v-model="params.remember"
              size="xs"
              class="text-grey-8"
              label="记住密码"
            />
            <q-btn flat dense class="text-primary" @click="forgotPassword">忘记密码？</q-btn>
          </div>

          <q-btn
            type="submit" :loading="loading"
            class="full-width bg-primary text-white"
            label="登录"
          ></q-btn>
        </q-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useUserStore } from 'src/stores/users';
import { useRouter } from 'vue-router';
import { dynamicRoutes } from 'src/router';
import { requestNewCaptcha, requestLogin } from 'src/apis/index';
import loginBackground from 'src/assets/images/loginBg.svg';
import logoImg from 'src/assets/images/logo.png';

defineOptions({
  name: 'LoginPage'
});

const userStore = useUserStore();
const router = useRouter();
const loading = ref(false)
const params = ref({
  username: '',
  password: '',
  captchaId: '',
  captchaVal: '',
  remember: true
});

const captchaUrl = computed(() =>
  params.value.captchaId ? `${process.env.baseURL}/captcha/${params.value.captchaId}` : ''
);

const refreshCaptcha = () => {
  requestNewCaptcha().then((res: any) => {
    params.value.captchaId = res;
  })
};

const onSubmit = () => {
  requestLogin(params.value).then((res: any) => {
    userStore.setUserToken(res.token);
    userStore.setMenus(res.menus);
    userStore.setRouters(res.routes);
    userStore.setUserInfo(res.info)

    loading.value = true
    dynamicRoutes(router, res.menus).then(() => {
      loading.value = false
      router.push('/');
    })
  }).catch(() => {
    loading.value = false
    refreshCaptcha();
  })
};

const forgotPassword = () => {
  // Implement forgot password logic here
  console.log('Forgot password clicked');
};

onMounted(refreshCaptcha);
</script>

<style scoped>
.login-bg {
  height: 100vh;
  padding-bottom: 30%;
}

.login-img {
  display: none;
}

@media screen and (min-width: 1000px) {
  .login-bg {
    background-size: 100% 100%;
    padding-bottom: 10%;
  }

  .login-img {
    display: block;
  }
}
</style>
