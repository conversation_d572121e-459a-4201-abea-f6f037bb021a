import { boot } from 'quasar/wrappers';
import axios, { AxiosInstance } from 'axios';
import { useUserStore } from 'src/stores/users';
import { getTimezoneOffset } from 'src/utils'
import {Loading, QSpinnerBars, Notify} from 'quasar'

declare module 'vue' {
  interface ComponentCustomProperties {
    $axios: AxiosInstance;
    $api: AxiosInstance;
  }
}

// Be careful when using SSR for cross-request state pollution
// due to creating a Singleton instance here;
// If any client changes this (global) instance, it might be a
// good idea to move this instance creation inside of the
// "export default () => {}" function below (which runs individually
// for each client)
const api = axios.create({ baseURL: process.env.baseURL });

// 添加常量 /v1
export const API_PREFIX = '/v1';

export default boot(({ app, router }) => {

  // 添加请求拦截器
  api.interceptors.request.use(
    (config) => {
      if (!config.headers['X-Skip-Loading'] || config.headers['X-Skip-Loading'] == 'true') {
        Loading.show({
          spinner: QSpinnerBars,
          spinnerColor: 'secondary',
          spinnerSize: 50,
          message: '一些重要的过程正在进行中, 请等待...',
        });
      }

      const userStore = useUserStore();
      const { userToken } = userStore;

      // 如果userToken存在且Authorization未设置，则添加Authorization头
      if (userToken && !config.headers.Authorization) {
        config.headers.Authorization = `Bearer ${userToken}`;
        // 如果有设置 token，那么添加 /v1 前缀
        if (!config.url?.startsWith(API_PREFIX)) {
          config.url = API_PREFIX + config.url;
        }
      }

      // 设置默认头部
      config.headers['Accept-Language'] = 'zh-CN';
      config.headers['Time-Zone'] = getTimezoneOffset();

      return config;
    },
    (error) => Promise.reject(error)
  );

  // 添加响应拦截器
  api.interceptors.response.use(
    (response) => {
      Loading.hide();

      // 如果错误代码 为0 那么返回 data数据
      if (response.data.hasOwnProperty('code') && response.data.code == 200) {
        return response.data.data
      }

      // 如果有设置错误信息, 那么返回错误信息
      if (response.data.hasOwnProperty('msg') && response.data.msg != '') {
        Notify.create({
          type: 'negative',
          message: response.data.msg
        });
        return Promise.reject(response.data.msg);
      }

      return response.data;
    },
    (error) => {
      Loading.hide();
      const userStore = useUserStore();

      if (error.response) {
        switch (error.response.status) {
          case 401:
            // 未授权，清除用户信息并重定向到登录页
            userStore.clearAllUserData();
            void router.push('/login');
            break;
          case 403:
            // 权限不足
            Notify.create({
              type: 'negative',
              message: '权限不足，无法访问该资源'
            });
            break;
          case 500:
            // 服务器错误
            Notify.create({
              type: 'negative',
              message: '服务器错误，请稍后再试'
            });
            break;
          default:
            // 其他错误
            Notify.create({
              type: 'negative',
              message: `请求失败: ${error.message}`
            });
        }
      } else {
        // 请求被取消或者网络问题
        Notify.create({
          type: 'negative',
          message: '网络错误，请检查您的网络连接'
        });
      }

      return Promise.reject(error);
    }
  );

  // for use inside Vue files (Options API) through this.$axios and this.$api

  app.config.globalProperties.$axios = axios;
  // ^ ^ ^ this will allow you to use this.$axios (for Vue Options API form)
  //       so you won't necessarily have to import axios in each vue file

  app.config.globalProperties.$api = api;
  // ^ ^ ^ this will allow you to use this.$api (for Vue Options API form)
  //       so you can easily perform requests against your app's API
});

export { api };
