<template>
  <div>
    <!-- 聊天浮动按钮 -->
    <q-page-sticky position="bottom-right" :offset="chatsOffset" @mouseover="chatsOffset = [0, 18]" @mouseleave="chatsOffset = [-80, 18]"
      style="transition: all 0.3s ease-in-out;">
      <q-btn fab color="primary" @click="unReadCount = 0; showChat = true" class="no-padding" style="width: 120px;
        border-top-right-radius: 0;border-bottom-right-radius: 0;border-top-left-radius: 28px;border-bottom-left-radius: 28px;">
        <div>
          <div class="row items-center" v-if="unReadCount === 0">
            <q-spinner-rings color="green" size="2em" />
            <div class="col q-ml-sm">等待接收</div>
          </div>
          <div class="row items-center" v-else>
            <q-badge color="red" :label="unReadCount" />
            <div class="col q-ml-sm">新到消息</div>
          </div>
        </div>
      </q-btn>
    </q-page-sticky>

    <q-dialog v-model="showChat">
      <q-card style="width: 860px; max-width: 80vw; height: 80vh">
        <q-card-section>
          <div class="row items-center">
            <q-avatar>
              <q-img :src="imageSrc($userStore.userInfo.avatar)" no-spinner />
            </q-avatar>
            <div class="text-h6 q-ml-sm">{{ $userStore.userInfo.nickname }}</div>
          </div>
        </q-card-section>
        <q-separator />
        <q-card-section horizontal style="height: calc(100% - 81px)">
          <q-card-section :style="{width: '246px', 'border-right': '1px solid #e0e0e0', height: '100%'}" class="no-padding">
            <div class="full-height">
              <ChatsSessionsComponents class="full-height" ref="chatsSessionsRef" @switchSession="switchSession" />
            </div>
          </q-card-section>
          <q-card-section class="col no-padding" :style="{height: '100%'}">
            <div class="full-height">
              <ChatsMessagesComponents class="full-height" ref="chatsMessagesRef" @changeSession="changeSession" @playAudio="playAudio" />
            </div>
          </q-card-section>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 音频播放器 -->
    <audio ref="audioPlayer" style="display: none;" :src="audioSrc"></audio>
  </div>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'ChatsLayout'
});

import { onMounted, ref } from 'vue';
import { imageSrc } from 'src/utils';
import { useUserStore } from 'src/stores/users';
import { useChatsStore, MESSAGE_OP_MESSAGE } from 'src/stores/chats';
import ChatsSessionsComponents from 'src/layouts/chatsSessions.vue';
import ChatsMessagesComponents from 'src/layouts/chatsMessages.vue';


const $userStore = useUserStore();
const $chatsStore = useChatsStore();

const audioPlayer = ref(null) as any;
const audioSrc = ref('');
const chatsSessionsRef = ref(null) as any;
const chatsMessagesRef = ref(null) as any;
const currentSessionInfo = ref({});
const chatsOffset = ref([-80, 18]);
const showChat = ref(false);  // 是否显示聊天窗口
const unReadCount = ref(0); // 未读消息数量

// 监听消息
const onMessage = (msg: any) => {
  chatsSessionsRef.value?.onMessage(msg);

  if (showChat.value) {
    chatsMessagesRef.value?.onMessage(msg);
  } else {
    switch (msg.op) {
      case MESSAGE_OP_MESSAGE:
      playAudio(imageSrc('/mp3/msg.mp3'))
      unReadCount.value++;
        break;
    }
  }
};

// 切换会话
const switchSession = (sessionInfo: any) => {
  currentSessionInfo.value = sessionInfo;
  chatsMessagesRef.value.switchSession(sessionInfo);
}

// 更新会话信息
const changeSession = (sessionInfo: any) => {
  currentSessionInfo.value = sessionInfo;
  chatsSessionsRef.value.changeSession(sessionInfo);
}

// 播放音频
const playAudio = (src: string) => {
  audioSrc.value = src;
  audioPlayer.value.play();
}

onMounted(() => {
  // 绑定当前用户
  $chatsStore.init($userStore.userToken as string);

  // 监听消息
  $chatsStore.onMessage(onMessage);

});
</script>

<style scoped>

</style>
