<template>
  <div>
    <PaginationComponents url="/chats/sessions" :params="bodyParams" @changeRows="updateSessionList" trigger>
      <template v-slot:item-body="{rows}">
        <q-list v-if="rows.length > 0">
          <template v-for="row in rows" :key="row.id">
            <q-item clickable style="padding: 8px 8px;"
              @click="switchSessionFunc(row)" :class="{'bg-grey-3': row.id === currentSessionInfo.id}">
              <q-item-section avatar>
                <q-avatar class="bg-grey-3">
                  <q-img :src="imageSrc(row.userInfo.avatar)" no-spinner v-if="row.online" />
                  <div v-else class="text-grey-8 text-caption">离线</div>
                </q-avatar>
              </q-item-section>
              <q-item-section>
                <div style="max-width: 132px;">
                  <div class="text-body2 ellipsis">{{ row.userInfo.username }}</div>
                  <div class="text-caption text-grey">
                    <div class="ellipsis" v-if="row.data.type === MESSAGE_TYPE_IMAGE">[图片]</div>
                    <div class="ellipsis" v-else>{{ row.data.message }}</div>
                  </div>
                </div>
              </q-item-section>
              <q-item-section side>
                <q-badge rounded color="red" :label="row.number" v-if="row.number > 0"></q-badge>
              </q-item-section>
            </q-item>
          </template>
        </q-list>
      </template>
    </PaginationComponents>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'ChatsSessionsComponents'
})

import { ref } from 'vue'
import { imageSrc } from 'src/utils'
import {MESSAGE_TYPE_IMAGE, MESSAGE_OP_MESSAGE, MESSAGE_OP_ONLINE, MESSAGE_OP_OFFLINE} from 'src/stores/chats'
import PaginationComponents from 'src/components/pagination.vue'
import { requestChatsSession } from 'src/apis/chats'

const emits = defineEmits(['switchSession'])

const sessionList = ref([]) as any;
const currentSessionInfo = ref({id: 0});
const bodyParams = ref({ pagination: { sortBy: 'updated_at', descending: true, page: 1, rowsPerPage: 10 } })

// 切换会话
const switchSessionFunc = (sessionInfo: any) => {
  currentSessionInfo.value = sessionInfo;
  emits('switchSession', currentSessionInfo.value)
}

// 监听消息
const onMessage = (msg: any) => {
  switch (msg.op) {
    case MESSAGE_OP_MESSAGE: {
      // 如果新的消息, 那么需要移动到第一位
      const sessionInfo = sessionList.value.find((item: any) => item.sessionId === msg.data.sessionId);
      if (sessionInfo) {
        // Remove existing session from array
        sessionList.value = sessionList.value.filter((item: any) => item.sessionId !== msg.data.sessionId);
        // Update session data and add to beginning
        sessionInfo.data = msg.data;
        // 如果不是当前的会话, 那么未读数量+1
        if (sessionInfo.id !== currentSessionInfo.value.id) {
          sessionInfo.number++;
        }
        sessionList.value.unshift(sessionInfo);
      } else {
        // 新的用户
        requestChatsSession({ sessionId: msg.data.sessionId }).then((res: any) => {
          sessionList.value.unshift(res);
        })
      }
      break;
    }

    case MESSAGE_OP_ONLINE: {
      // 在线
      const sessionInfo = sessionList.value.find((item: any) => item.userId === msg.data);
      if (sessionInfo) {
        sessionInfo.online = true;
      }
      break;
    }

    case MESSAGE_OP_OFFLINE: {
      // 离线
      const sessionInfo = sessionList.value.find((item: any) => item.userId === msg.data);
      if (sessionInfo) {
        sessionInfo.online = false;
      }
      break;
    }
  }
}

// 更新会话列表
const updateSessionList = (rows: any) => {
  if (rows.length > 0 && currentSessionInfo.value.id === 0) {
    currentSessionInfo.value = rows[0];
    emits('switchSession', currentSessionInfo.value)
  }
  sessionList.value = rows;
}

// 更新会话信息
const changeSession = (sessionInfo: any) => {
  currentSessionInfo.value = sessionInfo;
}

defineExpose({
  onMessage,
  changeSession
})
</script>
