<template>
  <q-drawer
    v-model="rightDrawerOpen"
    show-if-above
    bordered
    behavior="mobile"
    side="right"
  >
    <div class="column q-ma-md">
      <div class="col-auto">
        <div class="text-h6">偏好设置</div>
      </div>
      <div class="col-auto">
        <q-toggle v-model="$settingStore.isAudio" label="开启提示音" @update:model-value="$settingStore.setIsAudio($event)" />
      </div>
    </div>
  </q-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useSettingStore } from 'src/stores/setting';
defineOptions({
  name: 'RightDrawerLayout'
});

const $settingStore = useSettingStore();
const rightDrawerOpen = ref(false);

defineExpose({
  toggleRightDrawer: () => {
    rightDrawerOpen.value = !rightDrawerOpen.value;
  }
});
</script>

<style scoped>
/* Add any scoped styles here if needed */
</style>
