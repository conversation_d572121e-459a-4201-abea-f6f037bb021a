<template>
  <q-drawer
    v-model="leftDrawerOpen"
    show-if-above
    bordered
    side="left"
    class="bg-grey-1 q-pt-md"
    :width="260"
  >
    <q-list>
      <RecursiveMenu :menu="defaultMenu"></RecursiveMenu>
      <template v-for="menu in menus" :key="menu.id">
        {{ void (defaultMenuId = defaultMenuId == 0 && menu.children.length > 0 ? menu.id : defaultMenuId) }}
        <RecursiveMenu :menu="menu" :default-opened="defaultMenuId == menu.id"></RecursiveMenu>
      </template>
    </q-list>
  </q-drawer>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useUserStore } from 'src/stores/users';
import RecursiveMenu from 'src/components/menu.vue'

defineOptions({
  name: 'LeftDrawerLayout'
});

const userStore = useUserStore();
const leftDrawerOpen = ref(false);
const menus = ref([]) as any;
const defaultMenuId = ref(0);

const defaultMenu = {
  id: 'default',
  name: '控制台',
  route: '/',
  data: {
    icon: 'dashboard'
  }
};

onMounted(() => {
  menus.value = userStore.menus;
});

defineExpose({
  toggleLeftDrawer: () => {
    leftDrawerOpen.value = !leftDrawerOpen.value;
  }
});
</script>

<style scoped>
/* Add any scoped styles here if needed */
</style>
