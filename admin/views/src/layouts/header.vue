<template>
  <q-header bordered class="bg-primary text-white">
    <q-toolbar>
      <q-btn
        flat
        dense
        round
        icon="menu"
        @click="$emit('toggle-drawer')"
      />

      <q-toolbar-title style="min-width: 120px;">
        管理系统
      </q-toolbar-title>

      <div class="col" v-if="$q.screen.gt.xs">
        <q-tabs align="left" :breakpoint="0"
        inline-label narrow-indicator
        :model-value="$route.fullPath">
        <q-tab v-for="(tab, tabIndex) in $tabs.tabs.values()"
          :key="tabIndex" no-caps
          :name="tab.route"
          :label="tab.label"
          @click.stop="router.push(tab.route)"
        >
          <q-badge color="primary" floating v-if="tabIndex > 0">
            <q-icon name="close" @click.stop="$tabs.removeTab(router, tab.route)"></q-icon>
          </q-badge>
        </q-tab>
      </q-tabs>
      </div>



      <q-btn flat round dense
        :icon="$q.fullscreen.isActive ? 'fullscreen_exit' : 'fullscreen'"
        @click="$q.fullscreen.toggle()" />
      <q-btn flat round dense icon="notifications">
        <q-badge color="red" floating v-if="noticesNums > 0">{{ noticesNums }}</q-badge>
        <q-popup-proxy>
          <q-card flat v-if="notices.length > 0">
            <q-list class="q-ma-sm">
              <q-item v-for="notice in notices" :key="notice.id" :class="notice.status === 10 ? '' : 'text-grey'" @click="noticeDetailFunc(notice)" clickable>
                <q-item-section avatar>
                  <q-avatar :color="notice.status === 10 ? 'primary' : 'grey-6'" text-color="white" icon="notifications" />
                </q-item-section>
                <q-item-section class="ellipsis">
                  {{ notice.title }}
                  <div class="text-caption">{{ date.formatDate(notice.createdAt, 'YYYY-MM-DD HH:mm:ss') }}</div>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card>
          <q-card flat v-else>
            <q-card-section>
              <div class="text-center text-caption">暂无消息通知</div>
            </q-card-section>
          </q-card>
        </q-popup-proxy>
      </q-btn>
      <q-btn flat dense :icon="'img:' + imageSrc(userStore.userInfo.avatar)" :label="userStore.userInfo.username" no-caps>
        <q-menu>
          <q-list style="min-width: 148px">
            <q-item clickable v-close-popup @click="updateUserInfoDialog = true">
              <q-item-section avatar style="min-width: 0;padding-right: 8px;">
                <q-icon name="person" />
              </q-item-section>
              <q-item-section>更新管理信息</q-item-section>
            </q-item>
            <q-item clickable v-close-popup @click="updateParams.type = 1; updatePasswordDialog = true">
              <q-item-section avatar style="min-width: 0;padding-right: 8px;">
                <q-icon name="key" />
              </q-item-section>
              <q-item-section>更新登录密码</q-item-section>
            </q-item>
            <q-item clickable v-close-popup @click="updateParams.type = 2; updatePasswordDialog = true">
              <q-item-section avatar style="min-width: 0;padding-right: 8px;">
                <q-icon name="password" />
              </q-item-section>
              <q-item-section>更新支付密码</q-item-section>
            </q-item>
            <q-item clickable v-close-popup @click="logoutFunc">
              <q-item-section avatar style="min-width: 0;padding-right: 8px;">
                <q-icon name="logout" />
              </q-item-section>
              <q-item-section>退出登录</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
      <q-btn flat dense round icon="settings" @click="$emit('toggle-right-drawer')"/>
    </q-toolbar>
  </q-header>


  <!-- 更新密码 -->
  <q-dialog v-model="updatePasswordDialog">
    <q-card style="min-width: 280px;">
      <q-card-section>
        <div class="text-h6">{{ updateParams.type === 1 ? '更新登录密码' : '更新支付密码' }}</div>
      </q-card-section>
      <q-card-section>
        <div class="q-gutter-sm">
          <q-input dense filled v-model="updateParams.oldPassword" label="旧密码" />
          <q-input dense filled v-model="updateParams.newPassword" label="新密码" />
          <q-input dense filled v-model="updateParams.cmfPassword" label="确认密码" />
        </div>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn flat label="取消" v-close-popup />
        <q-btn flat label="确定" @click="updatePasswordFunc" />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <!-- 更新用户信息 -->
  <q-dialog v-model="updateUserInfoDialog">
    <q-card style="min-width: 280px;">
      <q-card-section>
        <div class="text-h6">更新用户信息</div>
      </q-card-section>

      <q-card-section>
        <div class="q-gutter-sm">
          <uploader-components :path="userStore.userInfo.avatar" @uploaded="(path: any) => {userStore.userInfo.avatar = path;}" />
          <q-input dense readonly filled :model-value="date.formatDate(userStore.userInfo.expiredAt, 'YYYY-MM-DD HH:mm:ss')" label="过期时间" />
          <q-input dense filled v-model="userStore.userInfo.nickname" label="昵称" />
          <q-input dense filled v-model="userStore.userInfo.email" label="邮箱" />
          <q-input dense filled v-model="userStore.userInfo.telephone" label="号码" />
          <q-input dense filled v-model="userStore.userInfo.chatUrl" label="客服链接(http|https)" />
          <q-input dense filled type="textarea" v-model="userStore.userInfo.domains" label="域名组" />
        </div>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn flat label="取消" v-close-popup />
        <q-btn flat label="确定" @click="updateUserInfoFunc" />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <!-- 查看消息通知 -->
  <q-dialog v-model="noticeDetailDialog">
    <q-card style="min-width: 280px;" class="bg-red text-white">
      <q-card-section>
        <div class="text-h6">{{ noticeDetail.title }}</div>
      </q-card-section>

      <q-card-section>
        <div v-html="noticeDetail.content"></div>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat label="确定" v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <audio ref="audioRef"></audio>
</template>

<script setup lang="ts">
defineOptions({
  name: 'HeaderLayout'
});

import uploaderComponents from 'src/components/uploader.vue';
import { ref, onMounted, onUnmounted } from 'vue';
import { useQuasar, date } from 'quasar';
import { useRouter } from 'vue-router';
import { useUserStore } from 'src/stores/users';
import { useSettingStore } from 'src/stores/setting';
import { useTabs } from 'src/stores/tabs';
import { imageSrc } from 'src/utils';
import { requestInit, requestUpdatePassword, requestUpdateUserInfo, requestNotifyInfo, requestAudio } from 'src/apis';

const router = useRouter();
const userStore = useUserStore();
const $tabsStore = useTabs();
const $settingStore = useSettingStore();
const $tabs = useTabs();
const $q = useQuasar();
const audioRef = ref<any>(null)
const audioInterval = ref<any>(null)

const noticeDetailDialog = ref<boolean>(false)
const notices = ref<any>([])
const noticesNums = ref<number>(0)
const noticeDetail = ref<any>({id: 0})

const updateUserInfoDialog = ref<boolean>(false)

const updatePasswordDialog = ref<boolean>(false)
const updateParams = ref<any>({
  type: 1,
  oldPassword: '',
  newPassword: '',
  cmfPassword: '',
})

onMounted(() => {
  requestInit().then((res: any) => {
    notices.value = res.notices;
    noticesNums.value = res.notices.filter((notice: any) => {
      if (notice.status === 10 && noticeDetail.value.id == 0) {
        noticeDetail.value = notice;
      }
      return notice.status === 10
    }).length;

    if (noticeDetail.value.id > 0) {
      $q.notify({
        message: noticeDetail.value.title,
        icon: 'info',
        color: 'red',
        position: 'center',
        actions: [
          { label: '忽略', color: 'white', handler: () => { /* ... */ } },
          { label: '查看', color: 'white', handler: () => { noticeDetailFunc(noticeDetail.value) } }
        ]
      })
    }
  })

  // 循环请求提示音
  if (audioInterval.value == null) {
    audioInterval.value = setInterval(() => {
      audioFunc()
    }, 20000)
  }
})

onUnmounted(() => {
  // 清除循环请求提示音
  if (audioInterval.value != null) {
    clearInterval(audioInterval.value)
    audioInterval.value = null
  }
})

// 退出登录
const logoutFunc = () => {
  $tabsStore.initTabs();
  userStore.clearAllUserData();
  router.push('/login');
};

// 更新登录密码｜支付密码
const updatePasswordFunc = () => {
  requestUpdatePassword(updateParams.value).then(() => {
    updatePasswordDialog.value = false;
    if (updateParams.value.type === 1) {
      logoutFunc()
    }

    $q.notify({
      message: '更新成功',
      type: 'positive',
    })
  });
};

// 更新用户信息
const updateUserInfoFunc = () => {
  requestUpdateUserInfo(userStore.userInfo).then(() => {
    updateUserInfoDialog.value = false;
    userStore.setUserInfo(userStore.userInfo);
    $q.notify({
      message: '更新成功',
      type: 'positive',
    })
  });
};

// 查看消息通知
const noticeDetailFunc = (notice: any) => {
  requestNotifyInfo({ id: notice.id }).then(() => {
    noticeDetail.value = notice;
    if (notice.status === 10) {
      noticeDetail.value.status = 20;
      noticesNums.value--;
    }
    noticeDetailDialog.value = true;
  })
}

// 提示音
const audioFunc = () => {
  if (!$settingStore.isAudio) {
    return;
  }

  requestAudio().then((res: any) => {
    if (audioRef.value != null && res.source != '') {
      audioRef.value.src = imageSrc(res.source);
      audioRef.value.play();

      $q.notify({
        message: res.label,
        type: 'warning',
      })
    }
  })
}

defineEmits(['toggle-drawer', 'toggle-right-drawer']);
</script>

<style scoped>
</style>
