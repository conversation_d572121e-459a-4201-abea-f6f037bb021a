<template>
  <div class="full-height">
    <div :style="{height: 'calc(100% - 152px)'}">
      <PaginationComponents :style="{height: '100%'}" url="/chats/messages" :params="bodyParams" reverse
      @changeRows="updateMessageList" v-if="bodyParams.sessionId != ''" ref="paginationRef">
        <template v-slot:item-body="{rows}">
          <div v-for="item in rows" :key="item.id" :class="['q-my-md', item.senderType == MESSAGE_SENDER_ADMIN ? 'q-pl-xl q-pr-md' : 'q-pl-md q-pr-xl']">
            <q-chat-message :sent="item.senderType == MESSAGE_SENDER_ADMIN"
              :avatar="imageSrc(item.senderType == MESSAGE_SENDER_ADMIN ? $userStore.userInfo.avatar : currentSessionInfo.userInfo.avatar)"
              :bg-color="item.senderType == MESSAGE_SENDER_ADMIN ? 'green-2' : 'grey-2'"
              style="margin-bottom: 2px">
              <template #default>
                <!-- 普通文本 -->
                <div v-if="item.type == MESSAGE_TYPE_TEXT" v-html="item.message" style="white-space: pre-line;"></div>
                <!-- 图片 -->
                <div v-else-if="item.type == MESSAGE_TYPE_IMAGE">
                  <q-img @click="imagePreviewPath = item.message; showImagePreview = true;" :src="imageSrc(item.message)" style="width: 200px;" alt=""
                    class="cursor-pointer" @load="isLoadingImage = false" />
                  <q-skeleton v-if="isLoadingImage" type="QAvatar" style="height: 200px;" />
                </div>
              </template>
            </q-chat-message>
            <div class="row text-grey" style="font-size: 10px;" :class="item.senderType == MESSAGE_SENDER_ADMIN?'q-pr-xl justify-end':'q-pl-xl'">
              <div v-if="item.senderType == MESSAGE_SENDER_ADMIN" @click="revokeMessage(item.id)" class="text-grey text-weight-medium cursor-pointer" style="font-size: 10px">
                <q-icon name="undo" color="grey"></q-icon>
                撤回
              </div>
              <div class="q-ml-sm">{{ date.formatDate(item.createdAt, 'MM/DD HH:mm') }}</div>
              <div class="q-ml-xs" v-if="item.senderType == MESSAGE_SENDER_ADMIN">
                <q-icon name="done_all" color="green" size="14px" v-if="item.status == MESSAGE_READ" />
                <q-icon name="done_all" color="grey" size="14px" v-else />
              </div>
            </div>
          </div>
        </template>
      </PaginationComponents>
    </div>
    <div>
      <q-separator />
      <div class="row items-center">
        <div class="q-ma-xs">
          <UploaderComponents @uploaded="sendImageMessage" style="width: 30px;height: 30px;">
            <template #default>
              <q-icon name="o_image" size="30px" class="text-grey-8 cursor-pointer"></q-icon>
            </template>
          </UploaderComponents>
        </div>
      </div>
      <q-input v-model="message" borderless autofocus maxlength="2040" dense :disable="currentSessionInfo.id == 0"
        placeholder="请输入消息" type="textarea" @keydown.enter="sendTextMessage($event)"
        class="no-padding no-margin" input-style="padding: 0 8px; resize: none;"/>
    </div>


    <!-- 图片预览 -->
    <q-dialog v-model="showImagePreview" full-width>
      <div @click="showImagePreview = false" class="row justify-center items-center cursor-pointer">
        <q-img :src="imageSrc(imagePreviewPath)" style="width: 60vw" class="bg-primary-2" />
      </div>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'ChatsMessagesComponents'
})

import { ref } from 'vue'
import { imageSrc } from 'src/utils'
import { date } from 'quasar'
import { MESSAGE_SENDER_ADMIN, MESSAGE_TYPE_TEXT, MESSAGE_TYPE_IMAGE, MESSAGE_READ, MESSAGE_OP_MESSAGE, MESSAGE_OP_READ } from 'src/stores/chats'
import PaginationComponents from 'src/components/pagination.vue'
import UploaderComponents from 'src/components/uploader.vue'
import { useUserStore } from 'src/stores/users'
import {requestChatsSend, requestChatsRead, requestChatsRevoke} from 'src/apis/chats'
import { useQuasar } from 'quasar'

const emits = defineEmits(['changeSession', 'playAudio'])
const $q = useQuasar()
const showImagePreview = ref(false)
const imagePreviewPath = ref('')
const isLoadingImage = ref(false)
const $userStore = useUserStore()
const currentSessionInfo = ref({id: 0, userInfo: { avatar: '' }}) as any
const bodyParams = ref({ sessionId: '', pagination: { sortBy: 'id', descending: true, page: 1, rowsPerPage: 10 } })
const paginationRef = ref(null) as any
const messageList = ref([]) as any
const message = ref('')

// 更新消息列表
const updateMessageList = (rows: any) => {
  messageList.value = rows

  // 获取未读消息的IDs
  const unreadMessageIDs = messageList.value.filter((item: any) => item.status !== MESSAGE_READ && item.senderType != MESSAGE_SENDER_ADMIN).map((item: any) => item.id)
  if (unreadMessageIDs.length > 0) {
    currentSessionInfo.value.number -= unreadMessageIDs.length
    emits('changeSession', currentSessionInfo.value)
    requestChatsRead({ ids: unreadMessageIDs })
  }
}

// 发送文本消息
const sendTextMessage = (e:any) => {
  if (!message.value.trim()) {
    e.preventDefault()
    return false;
  }
  if (e && e.key == 'Enter' && e.shiftKey) {
    return false
  }
  sendMessage(MESSAGE_TYPE_TEXT)
}

// 发送图片消息
const sendImageMessage = (path: string) => {
  if (currentSessionInfo.value.id == 0) {
    $q.notify({
      message: '请先选择会话',
      type: 'warning'
    })
    return
  }

  message.value = path
  sendMessage(MESSAGE_TYPE_IMAGE)
}

// 发送消息
const sendMessage = (messageType: number) => {
  requestChatsSend({
    sessionId: bodyParams.value.sessionId,
    message: message.value,
    type: messageType
  }).then((res: any) => {
    emits('playAudio', imageSrc('/mp3/send.mp3'))
    messageList.value.push(res)
    message.value = ''
    // 更新当前会话的最后消息
    currentSessionInfo.value.data = res
    emits('changeSession', currentSessionInfo.value)
    paginationRef.value?.animateScroll()
  })
}

// 监听消息
const onMessage = (msg: any) => {
  switch (msg.op) {
    case MESSAGE_OP_MESSAGE:
      // 接收消息
      if (currentSessionInfo.value.sessionId == msg.data.sessionId) {
        messageList.value.push(msg.data)
        paginationRef.value?.animateScroll()
        requestChatsRead({ ids: [msg.data.id] })
      }
      emits('playAudio', imageSrc('/mp3/msg2.mp3'))
      break
    case MESSAGE_OP_READ:
      // 阅读消息
      messageList.value.forEach((item: any) => {
        if (item.id == msg.data) {
          item.status = MESSAGE_READ
        }
      })
      break
  }
}

// 切换会话
const switchSession = (sessionInfo: any) => {
  paginationRef.value?.resetInfiniteScroll()
  currentSessionInfo.value = sessionInfo
  bodyParams.value.sessionId = sessionInfo.sessionId
}

// 撤回消息
const revokeMessage = async (id: any) => {
  await requestChatsRevoke({ id }).catch(()=>{ return false })
  deleteRevokeMessage(id)
}

// 获取对应id的消息，删除
const deleteRevokeMessage = (id: any) => {
  if (messageList.value.length <= 0) return false
  const messageIndex = messageList.value.findIndex((message: any) => {
    return message.id == id
  })
  messageList.value.splice(messageIndex, 1)

  currentSessionInfo.value.data = messageList.value[messageList.value.length-1]
  emits('changeSession', currentSessionInfo.value)
}

defineExpose({
  onMessage,
  switchSession
})
</script>

<style scoped lang="scss">
:deep(.q-message)  {
  .q-message-text {
    min-height: auto;
  }
}
</style>
