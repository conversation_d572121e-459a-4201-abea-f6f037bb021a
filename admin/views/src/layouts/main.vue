<template>
  <q-layout view="hHh Lpr lFf">
    <HeaderLayout @toggle-drawer="toggleLeftDrawer" @toggle-right-drawer="toggleRightDrawer" />
    <LeftDrawerLayout ref="leftDrawerRef" />
    <RightDrawerLayout ref="rightDrawerRef" />
    <q-page-container>
      <router-view v-slot="{ Component }">
        <keep-alive :max="30">
          <component :key="$tabsStore.tabs.get($route.fullPath)?.key ?? $route.fullPath" :is="Component"></component>
        </keep-alive>
      </router-view>
    </q-page-container>

    <!-- 添加的弹窗客服系统 -->
    <ChatsLayout />
  </q-layout>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import HeaderLayout from 'src/layouts/header.vue';
import LeftDrawerLayout from 'src/layouts/leftDrawer.vue';
import RightDrawerLayout from 'src/layouts/rightDrawer.vue';
import ChatsLayout from 'src/layouts/chats.vue';
import { useTabs } from 'src/stores/tabs';

const $tabsStore = useTabs();
const leftDrawerRef = ref<InstanceType<typeof LeftDrawerLayout> | null>(null);
const rightDrawerRef = ref<InstanceType<typeof RightDrawerLayout> | null>(null);

const toggleLeftDrawer = () => {
  leftDrawerRef.value?.toggleLeftDrawer();
};

const toggleRightDrawer = () => {
  rightDrawerRef.value?.toggleRightDrawer();
};

defineOptions({
  name: 'MainLayout'
});
</script>

<style scoped>
/* Add any scoped styles here if needed */
</style>
