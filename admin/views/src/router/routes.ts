import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Layouts',
    component: () => import('layouts/main.vue'),
    meta: { requireAuth: true, keepAlive: true },
    children: [
      { path: '/', component: () => import('pages/index.vue') }
    ],
  },
  {
    path: '/chat',
    component: () => import('pages/chat.vue'),
    meta: { requireAuth: false, keepAlive: false },
  },
  {
    path: '/login',
    component: () => import('pages/login.vue'),
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/404.vue'),
  },
];

export default routes;
