import { route } from 'quasar/wrappers';
import {
  createM<PERSON>oryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory,
} from 'vue-router';
import routes from 'src/router/routes';
import { useUserStore } from 'src/stores/users';
import { useTabs } from 'src/stores/tabs';

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

export default route(function (/* { store, ssrContext } */) {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : (process.env.VUE_ROUTER_MODE === 'history' ? createWebHistory : createWebHashHistory);

  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,

    // Leave this as is and make changes in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    history: createHistory(process.env.VUE_ROUTER_BASE),
  });

  Router.beforeEach(async (to, from, next) => {
    const $userStore = useUserStore();
    const $tabs = useTabs();

    if ($userStore.userToken && to.path === '/login') {
      next('/');
      return;
    }

    if (to.meta.requireAuth && !$userStore.userToken) {
      next('/login');
      return;
    }

    if (!$userStore.isRoutesAdded) {
      await dynamicRoutes(Router, $userStore.menus);
      $userStore.setRoutesAdded(true);
      next({ ...to, replace: true });
    } else {
      $tabs.addTab({ key: to.path, label: to.name as string, route: to.path });
      next();
    }
  });

  return Router;
});

const routePageList = import.meta.glob('../pages/**/*.vue');

// dynamicRoutes 动态添加路由
export const dynamicRoutes = (router: any, menus: any): Promise<void> => {
  return new Promise((resolve) => {
    menus.forEach((menu: any) => {
      if (menu.route != '' && menu.data.template != '') {
        const route = {
          path: menu.route,
          name: menu.name + '-' + menu.id,
          component: routePageList['../pages/' + menu.data.vueFile],
          meta: { requireAuth: true, keepAlive: true }
        };

        router.addRoute('Layouts', route);
      }

      if (menu.children && menu.children.length > 0) {
        dynamicRoutes(router, menu.children);
      }
    });

    resolve();
  });
}
