import { api } from 'src/boot/axios';


// 获取聊天会话列表
export const requestChatsSessions = () => {
  return api.post('/chats/sessions');
};

// 获取聊天会话信息
export const requestChatsSession = (data: any) => {
  return api.post('/chats/session', data, {
    headers: {
      'X-Skip-Loading': 'true'
    }
  });
};

// 获取聊天记录
export const requestChatsMessages = (data: any) => {
  return api.post('/chats/messages', data);
};

// 发送消息
export const requestChatsSend = (data: any) => {
  return api.post('/chats/send', data);
};

// 阅读消息
export const requestChatsRead = (data: any) => {
  return api.post('/chats/read', data, {
    headers: {
      'X-Skip-Loading': 'true'
    }
  });
};

// 撤回消息
export const requestChatsRevoke = (data: any) => {
  return api.post('/chats/revoke', data);
};
