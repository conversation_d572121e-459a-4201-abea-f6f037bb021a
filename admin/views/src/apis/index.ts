import { api } from 'src/boot/axios';

// 请求新的验证码
export const requestNewCaptcha = () => {
  return api.get('/captcha/create', {
    headers: {
      'X-Skip-Loading': 'true'
    }
  });
};

// 管理请求登录
export const requestLogin = (params: any) => {
  return api.post('/login', params)
}

// 请求菜单配置
export const requestConfigure = (route: string, query: any) => {
  return api.post('/configure', {route: route, query: query})
}

// 请求语言标签
export const requestLangTags = (params: any) => {
  return api.post('/lang/tabs', params)
}

// 获取选项
export const requestOptions = (params: any) => {
  return api.post('/options', params)
}

// 更新翻译
export const requestUpdateTranslate = (params: any) => {
  return api.post('/update/translate', params)
}

// 初始化数据
export const requestInit = () => {
  return api.post('/init')
}

// 控制台信息
export const requestIndex = () => {
  return api.post('/index')
}

// 更新登录密码｜支付密码
export const requestUpdatePassword = (params: any) => {
  return api.post('/update/password', params)
}

// 更新用户信息
export const requestUpdateUserInfo = (params: any) => {
  return api.post('/update', params)
}

// 通知详情
export const requestNotifyInfo = (params: any) => {
  return api.post('/notify/info', params)
}

// 提示音
export const requestAudio = () => {
  return api.post('/audio', {}, {
    headers: {
      'X-Skip-Loading': 'true'
    }
  })
}
