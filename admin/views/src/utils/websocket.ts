interface WebSocketOptions {
  url: string;
  onMessage?: (data: any) => void;
  onOpen?: () => void;
  onClose?: () => void;
  onError?: (error: Event) => void;
}

export class WebSocketClient {
  private ws: WebSocket | null = null;
  private options: WebSocketOptions;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000; // 3 seconds
  private reconnectTimeoutId: number | null = null;

  constructor(options: WebSocketOptions) {
    this.options = options;
    this.connect();
  }

  private connect() {
    try {
      this.ws = new WebSocket(this.options.url);

      this.ws.onopen = () => {
        this.reconnectAttempts = 0;
        if (this.options.onOpen) {
          this.options.onOpen();
        }
      };

      this.ws.onmessage = (event) => {
        if (this.options.onMessage) {
          let data;
          try {
            data = JSON.parse(event.data);
          } catch {
            data = event.data;
          }
          this.options.onMessage(data);
        }
      };

      this.ws.onclose = () => {
        if (this.options.onClose) {
          this.options.onClose();
        }
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        if (this.options.onError) {
          this.options.onError(error);
        }
        this.ws?.close();
      };

    } catch (error) {
      console.error('WebSocket connection error:', error);
      this.attemptReconnect();
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      if (this.reconnectTimeoutId) {
        window.clearTimeout(this.reconnectTimeoutId);
      }
      this.reconnectTimeoutId = window.setTimeout(() => {
        this.connect();
      }, this.reconnectInterval);
    }
  }

  public send(data: string | object) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      const message = typeof data === 'string' ? data : JSON.stringify(data);
      this.ws.send(message);
    }
  }

  public close() {
    if (this.reconnectTimeoutId) {
      window.clearTimeout(this.reconnectTimeoutId);
    }
    this.ws?.close();
  }

  public getState() {
    return this.ws?.readyState;
  }
}
