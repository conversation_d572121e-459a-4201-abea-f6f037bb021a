import { copyToClipboard } from 'quasar'

// 获取时区偏移量
export const getTimezoneOffset = () => {
  // 获取当前日期时间
  const now = new Date();

  // 构造出带有时区的日期时间字符串
  const offsetMinutes = now.getTimezoneOffset();
  const absOffsetHours = Math.abs(Math.floor(offsetMinutes / 60));
  const absOffsetMinutes = Math.abs(offsetMinutes % 60);
  const offsetSign = offsetMinutes <= 0 ? '+' : '-';
  return `${offsetSign}${String(absOffsetHours).padStart(2, '0')}:${String(absOffsetMinutes).padStart(2, '0')}`;
}

// 获取项目图片地址
export const imageSrc = (path: string): string => {
  if (path == '') {
    path = '/icon.png'
  }

  let baseURL = process.env.baseURL as string;

  if (!baseURL.startsWith('http')) {
    baseURL = new URL(window.location.origin).origin;
  } else {
    baseURL = new URL(baseURL).origin;
  }

  return `${baseURL}${path}`;
}

// 复制到剪贴板
export const copyText = ($q: any, text: string) => {
  copyToClipboard(text).then(() => {
    $q.notify({
      message: '复制成功',
      type: 'positive',
      position: 'top',
      timeout: 1000
    })
  }).catch(() => {
    $q.notify({
      message: '复制失败',
      type: 'negative',
      position: 'top',
      timeout: 1000
    })
  })
}
