import { defineStore } from 'pinia';
import { LocalStorage } from 'quasar';

const SETTING_AUDIO_KEY = '_settingAudio';

export const useSettingStore = defineStore('setting', {
  state: () => {
    return {
      isAudio: LocalStorage.hasItem(SETTING_AUDIO_KEY) ? LocalStorage.getItem(SETTING_AUDIO_KEY) : true,
    }
  },
  actions: {
    // 设置是否开启音频
    setIsAudio(isAudio: boolean) {
      this.isAudio = isAudio;
      LocalStorage.set(SETTING_AUDIO_KEY, isAudio);
    }
  }
});
