import { defineStore } from 'pinia';
import { WebSocketClient } from 'src/utils/websocket';

export const MESSAGE_TYPE_TEXT = 1; // 文本
export const MESSAGE_TYPE_IMAGE = 2; // 图片
export const MESSAGE_UNREAD = 1; // 未读
export const MESSAGE_READ = 2; // 已读
export const MESSAGE_SENDER_USER = 1; // 用户
export const MESSAGE_SENDER_ADMIN = 2; // 客服

export const MESSAGE_OP_BIND_USER = 'bindUser'; // 绑定用户
export const MESSAGE_OP_ONLINE = 'online'; // 在线
export const MESSAGE_OP_OFFLINE = 'offline'; // 离线
export const MESSAGE_OP_MESSAGE = 'message'; // 消息
export const MESSAGE_OP_READ = 'read'; // 阅读消息

export const useChatsStore = defineStore('chats', {
  state: () => {
    return {
      userToken: '',
      clientSocket: null as WebSocketClient | null,
      onMessage: null as any,
    };
  },
  getters: {
    // 获取客户端 ws
    socket: (state) => {
      if (!state.clientSocket) {
        state.clientSocket = new WebSocketClient({
          url: process.env.baseURL + '/chats/ws',
          onOpen: () => {
            // 发送绑定当前用户请求
            state.clientSocket?.send({
              op: MESSAGE_OP_BIND_USER,
              data: state.userToken,
            });
          },
          onMessage: (msg: any) => {
            if (state.onMessage) {
              state.onMessage(msg);
            }
          },
        });
      }
      return state.clientSocket;
    },
  },
  actions: {
    // 初始化ws
    init(token: string) {
      this.userToken = token;
      this.socket;
    },
    // 监听消息
    onMessage(fun: (msg: any) => void) {
      this.onMessage = fun;
    },
  },
});
