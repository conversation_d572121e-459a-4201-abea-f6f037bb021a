import { defineStore } from 'pinia';
import { LocalStorage } from 'quasar';

// 用户令牌的本地存储键
const USER_TOKEN_KEY = '_userToken';
// 用户信息的本地存储键
const USER_INFO_KEY = '_userInfo';
// 用户菜单的本地存储键
const USER_MENUS_KEY = '_userMenus';
// 用户路由的本地存储键
const USER_ROUTERS_KEY = '_userRouters';

export const useUserStore = defineStore('users', {
  state: () => ({
    // 用户令牌
    userToken: LocalStorage.getItem(USER_TOKEN_KEY) || '',
    // 用户信息
    userInfo: JSON.parse(LocalStorage.getItem(USER_INFO_KEY) || '{}'),
    // 用户路由
    routers: JSON.parse(LocalStorage.getItem(USER_ROUTERS_KEY) || '[]'),
    // 用户菜单
    menus: JSON.parse(LocalStorage.getItem(USER_MENUS_KEY) || '[]'),
    //  路由是否添加
    isRoutesAdded: false,
  }),
  getters: {
    // 判断用户是否已认证
    isAuthenticated(): boolean {
      return Boolean(this.userToken);
    },
  },
  actions: {
    // 设置用户令牌
    setUserToken(token: string) {
      this.userToken = token;
      LocalStorage.set(USER_TOKEN_KEY, token);
    },
    // 清除用户令牌
    clearUserToken() {
      this.userToken = '';
      LocalStorage.remove(USER_TOKEN_KEY);
    },
    // 设置用户信息
    setUserInfo(info: object) {
      this.userInfo = info;
      LocalStorage.set(USER_INFO_KEY, JSON.stringify(info));
    },
    // 设置用户路由
    setRouters(routers: any) {
      this.routers = routers;
      LocalStorage.set(USER_ROUTERS_KEY, JSON.stringify(routers));
    },
    // 设置用户菜单
    setMenus(menus: any) {
      this.menus = menus;
      LocalStorage.set(USER_MENUS_KEY, JSON.stringify(menus));
    },
    // 设置路由已加载完成
    setRoutesAdded(isAdded: boolean) {
      this.isRoutesAdded = isAdded
    },
    // 清除所有用户数据
    clearAllUserData() {
      this.userToken = '';
      this.userInfo = {};
      this.routers = [];
      this.menus = [];
      LocalStorage.remove(USER_TOKEN_KEY);
      LocalStorage.remove(USER_INFO_KEY);
      LocalStorage.remove(USER_ROUTERS_KEY);
      LocalStorage.remove(USER_MENUS_KEY);
    },
    // 判断用户是否存在当前路由
    hasRoute(route: string): boolean {
      route = route.split('?')[0];
      return this.routers.includes(route);
    },
  },
});
