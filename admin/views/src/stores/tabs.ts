import { defineStore } from 'pinia';

interface TabsState {
  key: string;
  label: string;
  route: string;
}

export const useTabs = defineStore('tabs', {
  state: () => {
    const mathRandom = Math.random().toString()
    return {
      tabs: new Map([
        ['/', { key: '/' + mathRandom, label: '控制台', route: '/' }],
      ]),
    }
  },
  getters: {},
  actions: {
    // 添加Tab
    addTab(tab: TabsState) {
      if (this.tabs.has(tab.route) || !tab.label) {
        return;
      }
      const mathRandom = Math.random().toString()
      tab.key = tab.route + mathRandom;
      tab.label = tab.label.split('-')[0];
      this.tabs.set(tab.route, tab);
    },

    // 删除Tab
    removeTab(router: any, currentRoute: string) {
      if (router.currentRoute.value.fullPath === currentRoute) {
        const parentRoute = this.getParentTab(currentRoute);
        router.push(parentRoute);
        this.tabs.delete(router.currentRoute.value.fullPath);
      } else  {
        this.tabs.delete(currentRoute);
      }
    },

    // 获取上级Tab
    getParentTab(route: string) {
      const keys = Array.from(this.tabs.keys());
      const index = keys.indexOf(route);
      if (index > 0) {
        return keys[index - 1];
      }
      return '/'
    },

    // 初始化
    initTabs() {
      this.tabs = new Map([
        ['/', { key: '/', label: '控制台', route: '/' }],
      ]);
    },
  },
});
