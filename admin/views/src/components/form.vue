<template>
  <div v-if="showInputs" :style="style">
    <q-form>
      <InputFields
      :children-form="childrenForm"
      :inputs="inputs"
      :params="formParams"
      :common-options="commonOptions"
      :show-search-button="showSearchButton"
      :cols-class="colsClass"
      @change="handleInputChange"
      @search="onSubmit"></InputFields>
    </q-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import InputFields from 'src/components/inputs.vue';
import { Form } from 'src/components/types'

defineOptions({
  name: 'FormInputs'
});

interface FormProps extends Form {
  index?: number,
  itemIndex?: number,
  showInputs?: boolean,
  showSearchButton?: boolean;
  colsClass?: string;
  style?: Record<string, string>;
  commonOptions?: Record<string, Array<{ label: string; value: any }>>;
}

const props = withDefaults(defineProps<FormProps>(), {
  index: -1,
  itemIndex: -1,
  showInputs: true,
  inputs: () => [],
  params: () => ({}),
  options: false,
  colsClass: '',
  style: () => ({}),
  commonOptions: () => ({}),
  childrenForm: () => ({})
});

const formParams = ref({ ...props.params });
watch(() => props.params, (newParams: any) => {
  formParams.value = { ...newParams }
})

const emit = defineEmits(['submit']);
const onSubmit = () => {
  emit('submit', props.field, props.index, props.itemIndex, formParams.value);
};

// 同步inputs参数
const handleInputChange = (newParams: Record<string, any>) => {
  formParams.value = newParams;
  if (props.field !== '') {
    onSubmit();
  }
};

// Expose onSubmit method to parent components
defineExpose({
  onSubmit
});
</script>

<style scoped>
/* Add any scoped styles here if needed */
</style>
