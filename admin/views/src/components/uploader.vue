<template>
  <div>
    <!-- Single Image Upload -->
    <q-uploader
      ref="uploaderRef"
      field-name="file"
      :url="baseURL + API_PREFIX + '/upload'"
      :headers="[{ name: 'Authorization', value: 'Bearer ' + $userStore.userToken }]"
      :accept="'*'"
      :max-files="type == 'multiple' ? 5 : 1"
      :max-file-size="type == 'file' ? 200 * 1024 * 1024 : 5 * 1024 * 1024"
      :multiple="type == 'multiple'" auto-upload
      style="width: 100%;background: transparent;"
      @uploaded="onUploaded"
      @failed="onUploadFailed"
    >
      <template v-slot:header="scope">
        <div class="row no-wrap items-center q-pa-sm q-gutter-xs" v-if="type == 'file'">
          <q-btn v-if="scope.queuedFiles.length > 0" icon="clear_all" @click="scope.removeQueuedFiles" round dense flat >
            <q-tooltip>Clear All</q-tooltip>
          </q-btn>
          <q-btn v-if="scope.uploadedFiles.length > 0" icon="done_all" @click="scope.removeUploadedFiles" round dense flat >
            <q-tooltip>删除已上传的文件</q-tooltip>
          </q-btn>
          <q-spinner v-if="scope.isUploading" class="q-uploader__spinner" />
          <div class="col">
            <div class="q-uploader__title">上传您的文件</div>
            <div class="q-uploader__subtitle">{{ scope.uploadSizeLabel }} / {{ scope.uploadProgressLabel }}</div>
          </div>
          <q-btn v-if="scope.canAddFiles" type="a" icon="add_box" @click="scope.pickFiles" round dense flat>
            <q-uploader-add-trigger />
            <q-tooltip>选择文件</q-tooltip>
          </q-btn>
          <q-btn v-if="scope.canUpload" icon="cloud_upload" @click="scope.upload" round dense flat >
            <q-tooltip>上传文件</q-tooltip>
          </q-btn>

          <q-btn v-if="scope.isUploading" icon="clear" @click="scope.abort" round dense flat >
            <q-tooltip>终止上传</q-tooltip>
          </q-btn>
        </div>
      </template>
      <template v-slot:list="scope">
        <div v-if="type == 'file'" class="q-ma-md">
          {{ currentPath }}
        </div>

        <div v-else-if="type == 'multiple'">
          <div>{{ label }}</div>
          <div class="row justify-between">
            <div></div>
            <div>
              <q-btn flat icon="add_box" @click="scope.pickFiles" color="primary">
                <q-uploader-add-trigger />
                <q-tooltip>Pick Files</q-tooltip>
              </q-btn>
            </div>
          </div>
          <q-list separator>
            <q-item v-for="(imagePath, itemIndex) in currentPath" :key="itemIndex">
              <q-item-section thumbnail>
                <img :src="imageSrc(imagePath)" class="q-mx-auto">
              </q-item-section>
              <q-item-section></q-item-section>
              <q-item-section side>
                <q-btn flat round dense icon="delete" color="red" @click="deleteValueFunc(itemIndex)"></q-btn>
              </q-item-section>
            </q-item>

            <div v-if="currentPath.length == 0" class="flex justify-center items-center cursor-pointer q-py-md">
              <q-img :src="imageSrc('/icon.png')" no-spinner width="80px" height="80px" class="q-mx-auto"></q-img>
            </div>
          </q-list>
        </div>

        <div @click="scope.removeFile;scope.pickFiles" v-else>
          <q-uploader-add-trigger />
          <slot>
            <div v-if="type !== 'icon'">{{ label }}</div>
            <div class="flex justify-center items-center cursor-pointer q-py-md">
              <q-img :src="imageSrc(currentPath as string)" no-spinner width="26px" height="26px" class="q-mx-auto" v-if="type == 'icon'"></q-img>

              <q-img :src="imageSrc(currentPath as string)" no-spinner width="80px" height="80px" class="q-mx-auto" v-else></q-img>
              <q-tooltip>{{ label ?? '点击上传图片' }}</q-tooltip>
            </div>
          </slot>
        </div>
      </template>
    </q-uploader>
  </div>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import { useQuasar } from 'quasar';
import { imageSrc } from 'src/utils';
import {API_PREFIX} from 'src/boot/axios'
import { useUserStore } from 'src/stores/users';

defineOptions({
  name: 'UploaderComponents'
});

const $q = useQuasar();
const uploaderRef = ref<any>(null)
const $userStore = useUserStore()
const baseURL = process.env.baseURL
const currentPath = ref<string | string[]>('')
const props = withDefaults(defineProps<{
  label?: string;
  type?: 'multiple' | 'image' | 'file' | 'icon';
  path?: string | string[];
}>(), {
  type: 'image'
});

currentPath.value = props.type === 'multiple' ? (props.path as string[] || []) : (props.path as string || '');

const emit = defineEmits(['uploaded']);
const onUploaded = (info: any) => {
  $q.notify({
    type: 'positive',
    message: '上传成功'
  });

  const res = JSON.parse(info.xhr.response)

  // 重置方法
  uploaderRef.value.reset()
  if (res.code !== 200) {
    $q.notify({
      type: 'negative',
      message: res.msg || '上传失败'
    });
    return;
  }

  if (Array.isArray(currentPath.value)) {
    currentPath.value.push(res.data);
  } else {
    currentPath.value = res.data;
  }
  emit('uploaded', currentPath.value);
};

const onUploadFailed = () => {
  $q.notify({
    type: 'negative',
    message: '上传失败, 请检查网络状态'
  });
};
const deleteValueFunc = (index: number) => {
  if (Array.isArray(currentPath.value)) {
    currentPath.value.splice(index, 1);
    emit('uploaded', currentPath.value);
  } else {
    console.error('Cannot delete from non-array value');
  }
}
</script>

<style scoped>
.q-uploader {
  box-shadow: none !important;
  max-height: none !important;
  border: none !important;
}

.q-uploader__header {
  display: none !important;
}

.q-uploader__list {
  padding: 0 !important;
  min-height: 0 !important;
}
/* Override the default q-uploader__list styles */
:deep(.q-uploader__list) {
  padding: 0 !important;
  min-height: 0 !important;
}
</style>
