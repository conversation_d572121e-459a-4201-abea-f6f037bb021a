<template>
  <div>
    <q-btn
      :label="button.label"
      :size="button.size"
      :color="button.color"
      :class="button.class"
      :style="button.style"
      @click.stop="openDialog"
    />
    <q-dialog v-model="isOpen" @hide="closeDialog">
      <q-card :style="cardStyle">
        <q-card-section>
          <div class="text-h6">{{ title }}</div>
          <div v-if="small" class="text-caption text-grey-7" style="word-break: break-all" v-html="small"></div>
        </q-card-section>

        <q-card-section v-if="contentData" style="word-break: break-all">
          {{ contentData }}
        </q-card-section>

        <q-card-section class="q-pa-none">
          <FormInputs
            ref="dialogFormRef"
            :show-inputs="showInputs"
            :children-form="childrenForm"
            :inputs="formInputs"
            :params="formParams"
            :common-options="commonOptions"
            :label="form.label"
            :field="form.field"
            :scan-field="form.scanField"
            :options="form.options"
            :cols-class="'col'"
            :style="{ padding: '16px'}"
            @submit="eventSubmitFunc"
          />
        </q-card-section>

        <q-card-actions align="right" class="q-pa-md" style="position: sticky; bottom: 0; background-color: white; z-index: 1;" v-if="options && (options?.submit || options?.cancel)">
          <q-btn v-if="options?.cancel" :label="options.cancel.label" :size="options.cancel.size" :color="options.cancel.color" v-close-popup />
          <q-btn v-if="options?.submit" :label="options.submit.label" :size="options.cancel.size" :color="options.submit.color" @click="submitFunc" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- 路由弹窗 -->
    <q-dialog v-model="routeDialog" full-height full-width>
      <q-card>
        <q-card-section>
          <TablePage :url="routeUrl" />
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- 二维码弹窗 -->
    <q-dialog v-model="qrcodeDialog">
      <q-card>
        <q-card-section>
          <div class="text-h6">{{ title }}</div>
          <div v-if="small" class="text-caption text-grey-7" style="word-break: break-all" v-html="small"></div>
        </q-card-section>

        <q-card-section>
          <img :src="qrcodeUrl" />
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {api} from 'src/boot/axios'
import {useQuasar} from 'quasar'
import {date} from 'quasar'
import {copyText} from 'src/utils'
import {ButtonDialog, InputTypeDateTimePicker, InputTypeDynamic} from 'src/components/types'
import FormInputs from 'src/components/form.vue'
import TablePage from 'src/pages/table.vue'
import QRCode from 'qrcode';

defineOptions({
  name: 'ButtonDialog'
});

const $q = useQuasar()
interface ButtonDialogProps extends ButtonDialog {
  commonOptions?: Record<string, Array<{ label: string; value: any }>>;
  row?: Record<string, any>;
  selected?: any[];
  selectOptions?: Record<string, string>;
}

// 显示框内容
const dialogFormRef = ref()
const contentData = ref('')
const formParams = ref<Record<string, any>>({})
const formInputs = ref<Array<Array<any>>>([])
const childrenForm = ref<Record<string, any>>({})
const showInputs = ref(true)
const props = withDefaults(defineProps<ButtonDialogProps>(), {
  fullWidth: false,
  fullHeight: false,
  commonOptions: () => ({}),
});

formParams.value = { ...props.form.params }
childrenForm.value = { ...props.form.childrenForm }

const routeDialog = ref(false);
const qrcodeUrl = ref('')
const qrcodeDialog = ref(false);
const routeUrl = ref('')

const isOpen = ref(false);
const cardStyle = computed(() => {
  let style = {};
  switch (props.size) {
    case 'small':
      style = { ...style, width: '300px' };
      break;
    case 'medium':
      style = { ...style, width: '700px', maxWidth: '80vw' };
      break;
    case 'fullWidth':
      style = { ...style, width: '100%' };
      break;
    case 'smallFullHeigth':
      style = { ...style, width: '300px', maxHeight: '100vh' };
      break;
    case 'mediumFullHeigth':
      style = { ...style, width: '700px', maxWidth: '80vw', maxHeight: '100vh' };
      break;
    case 'fullHeigth':
      style = { ...style, height: '100%' };
      break;
    default:
      if (props.size) {
        style = { ...style, width: props.size };
      }
  }
  if (props.fullWidth) {
    style = { ...style, width: '100%' };
  }
  if (props.fullHeight) {
    style = { ...style, height: '100%' };
  }
  return style;
});

// Method to open the dialog
const openDialog = () => {
  contentData.value = props.content;
  switch (props.type) {
    case 'route':
      // 处理路由弹窗
      routeUrl.value = props.url.replace(/row\.(\w+)/g, (match, field) => props.row?.[field] || match);
      routeDialog.value = true;
      break;
    case 'copy':
      // 处理复制弹窗
      const content = props.content.replace(/row\.(\w+)/g, (match, field) => props.row?.[field] || match);
      copyText($q, content)
      break;
    case 'qrcode':
      // 处理二维码弹窗
      qrcodeDialog.value = true;
      QRCode.toDataURL(props.content, { width: 256 }, (err: any, url: string) => {
        qrcodeUrl.value = url
      });
      break;
    default:
      // 处理动态表单
      formInputs.value = props.form.inputs.map(inputGroup =>
        inputGroup.map(input => {
          // 处理参数设置的 数据在 options中
          if (input.type === InputTypeDynamic && input.options) {
            const updatedInput = { ...input, ...input.options.input };
            if (props.form.childrenForm && input.options.childrenForm) {
              childrenForm.value = { ...updatedInput.childrenForm, ...input.options.childrenForm };
            }
            return updatedInput;
          } else if (input.type === InputTypeDynamic && input.mask && props.row && props.row.hasOwnProperty(input.mask)) {
            // 处理参数设置的 数据在 options中
            const updatedInput = { ...input, ...props.row[input.mask].input };
            if (props.form.childrenForm && props.row[input.mask].childrenForm) {
              childrenForm.value = { ...updatedInput.childrenForm, ...props.row[input.mask].childrenForm };
            }
            return updatedInput;
          }
          return input;
        })
      );

      // Handle single row fields
      if (props.row) {
        formInputs.value.flat().forEach(input => {
          if (input.field && props.row) {
            let value;
            if (input.scanField) {
              // Handle nested properties
              const fields = input.scanField.split('.');
              value = fields.reduce((obj: any, field: string) => obj && obj[field], props.row);
            } else {
              value = props.row[input.field];
            }

            if (value !== undefined) {
              switch (input.type) {
                case InputTypeDateTimePicker:
                  formParams.value[input.field] = date.formatDate(value, 'YYYY/MM/DD HH:mm:ss');
                  break;
                default:
                  formParams.value[input.field] = value;
                  break;
              }
            }
          }
        });
      }

      // Handle selected items for multi-select
      if (props.selectOptions?.[props.id]) {
        if (!props.selected?.length) {
          $q.notify({
            type: 'warning',
            message: '请先选择数据',
            position: 'top'
          });
          return;
        }

        const selectedField = props.selectOptions[props.id];
        formInputs.value.flat().forEach(input => {
          if (input.field === selectedField && props.selected) {
            // Handle multi-select fields
            formParams.value[input.field] = props.selected.map(item => item[input.scanField]);
            contentData.value = formParams.value[input.field].join(',');
          }
        });
      }

      // Check if inputs should be displayed
      showInputs.value = formInputs.value.flat().some(input => !input.display);
      isOpen.value = true;
      break;
  }
};

// Define emits
const emit = defineEmits(['done', 'close']);

// 激活请求
const submitFunc = () => {
  dialogFormRef.value.onSubmit()
};

// 关闭弹窗
const closeDialog = () => {
  isOpen.value = false
  emit('close')
}

// Form 提交事件, 回调
const eventSubmitFunc = (formField: string, formIndex: number, formItemIndex: number, formParams: any) => {
  api.post(props.url, formParams).then(() => {
    isOpen.value = false
    emit('done');
    $q.notify({
      type: 'positive',
      message: '操作成功',
      position: 'top',
      timeout: 1000
    });
  })
}

</script>
