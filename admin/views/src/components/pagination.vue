<template>
  <q-scroll-area style="height: 100%;width: 100%;" :thumb-style="{width: '4px'}" ref="scrollAreaRef">
    <div ref="scrollContainerRef">
      <q-infinite-scroll ref="scrollTargetRef" @load="onLoad" :offset="0" :reverse="reverse" :disable="!isLoad || totalPages == -1">
        <!-- 内容插槽 -->
        <slot name="item-body" :rows="rows"></slot>

        <NoneComponents v-if="rows.length === 0" style="margin-top: 100px;" />
        <template v-slot:loading>
          <div class="row justify-center q-my-md" v-if="isLoad">
            <q-spinner-dots color="primary" size="40px" />
          </div>
        </template>
      </q-infinite-scroll>
    </div>
  </q-scroll-area>
</template>

<script setup lang="ts">
defineOptions({
  name: 'PaginationComponents'
})

import { ref, onMounted } from 'vue'
import { api } from 'src/boot/axios'
import NoneComponents from 'src/components/none.vue'


const emits = defineEmits(['changeRows'])
const props = defineProps({
  url: {
    type: String,
    default: '',
    required: true
  },
  trigger: {
    type: Boolean,
    default: false
  },
  reverse: {
    type: Boolean,
    default: false
  },
  params: {
    type: Object,
    default: () => ({
      pagination: { sortBy: 'id', descending: true, page: 1, rowsPerPage: 10 },
    })
  }
})

const scrollAreaRef = ref<any>(null);
const scrollContainerRef = ref<any>(null);
const scrollTargetRef = ref<any>(null);
const isLoad = ref(true)
const totalPages = ref(0)
const bodyParams = ref(props.params)
const rows = ref([]) as any

// 滚动事件
const onLoad = (index: number, done: () => void) => {
  bodyParams.value.pagination.page = index
  api.post(props.url, bodyParams.value).then((res: any) => {
    if (props.reverse) {
        rows.value.unshift(...res.items.reverse())
      } else {
        rows.value.push(...res.items)
    }

    isLoad.value = res.items.length >= bodyParams.value.pagination.rowsPerPage;
    totalPages.value = Math.ceil(res.count / bodyParams.value.pagination.rowsPerPage);
    if (totalPages.value === 0) totalPages.value = -1;

    emits('changeRows', rows.value)
    done()
  })
}

onMounted(() => {
  if (props.trigger) {
    scrollTargetRef.value.trigger()
  }
})

// 滚动到底部
const animateScroll = () => {
  scrollAreaRef.value?.setScrollPosition('vertical', scrollContainerRef.value.clientHeight, 500);
};

// 重置滚动条
const resetInfiniteScroll = () => {
  scrollTargetRef.value.reset()
  rows.value = []
  isLoad.value = true
  totalPages.value = 0
}

defineExpose({
  resetInfiniteScroll,
  animateScroll
})
</script>
