<template>
  <div @keyup.enter="handleSearch">
    <template v-for="inputRows in inputs" :key="inputRows.id">
        <div class="row q-col-gutter-sm q-mb-sm">
          <template v-for="input in inputRows" :key="input.field">
            <div :class="colsClass" v-if="!input.display">
              <!-- Text Input -->
              <q-input @update:model-value="handleChange"
                v-if="input.type === InputTypeText" :readonly="input.readonly"
                v-model="currentParams[input.field]"
                :label="input.label"
                filled dense
              />

              <!-- Html Input -->
              <div v-else-if="input.type === InputTypeHtml" v-html="currentParams[input.field]"></div>

              <!-- Line Input -->
              <EchartsLine v-else-if="input.type === InputTypeLine" :params="currentParams[input.field] ?? []" />

              <!-- Textarea Input -->
              <q-input @update:model-value="handleChange"
                v-else-if="input.type === InputTypeTextarea" :readonly="input.readonly"
                v-model="currentParams[input.field]"
                :label="input.label"
                type="textarea"
                filled dense
              />

              <!-- Number Input -->
              <q-input @update:model-value="() => {
                $nextTick(() => {
                  currentParams[input.field] = currentParams[input.field] === '' ? null : currentParams[input.field]
                  handleChange()
                })
              }"
                v-else-if="input.type === InputTypeNumber" :readonly="input.readonly"
                v-model.number="currentParams[input.field]"
                :label="input.label"
                type="number"
                filled dense
              />

              <!-- Editor Input -->
              <EditorComponents v-else-if="input.type === InputTypeEditor" v-model="currentParams[input.field]" @update:model-value="handleChange" />

              <!-- Select Input -->
              <q-select dense options-dense emit-value map-options @update:model-value="handleChange"
                v-else-if="input.type === InputTypeSelect"
                v-model="currentParams[input.field]" :label="input.label"
                :options="input.options ?? commonOptions[input.scanField ?? input.field] ?? []"
                filled
              />

              <!-- Search Select Input -->
              <q-select dense options-dense emit-value map-options @update:model-value="handleChange"
                @filter="(val, update) => selectSearchFilterFn(val, input.mask, input.options, update)" use-input use-chip
                v-else-if="input.type === InputTypeSelectSearch" style="min-width: 146px;"
                v-model="currentParams[input.field]" :label="input.label"
                :options="searchOptions"
                filled
              />

              <!-- Radio Input -->
              <q-option-group @update:model-value="handleChange"
                v-else-if="input.type === InputTypeRadio" :readonly="input.readonly"
                v-model="currentParams[input.field]"
                :options="input.options ?? commonOptions[input.field] ?? []"
                type="radio"
              />

              <!-- Checkbox Input -->
              <div v-else-if="input.type === InputTypeCheckbox">
                <div>{{ input.label }}</div>
                <q-checkbox @update:model-value="handleChange" :readonly="input.readonly"
                  v-for="(option, optionIndex) in currentParams[input.field]" :key="optionIndex"
                  v-model="currentParams[input.field][optionIndex].checked" :label="option.label"
                />
              </div>

              <!-- Date Picker Input -->
              <q-date @update:model-value="handleChange"
                v-else-if="input.type === InputTypeDatePicker" :readonly="input.readonly"
                v-model="currentParams[input.field]"
                :label="input.label"
                filled
              />

               <!-- Time Picker Input -->
              <q-input @update:model-value="handleChange"
                v-else-if="input.type === InputTypeTimePicker" :readonly="input.readonly"
                :model-value="currentParams[input.field]"
                :label="input.label"
                dense filled
              >
                <template v-slot:append>
                  <q-icon name="access_time" class="cursor-pointer">
                    <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                      <q-time
                        v-model="currentParams[input.field]"
                        with-seconds
                        format24h
                        @update:model-value="handleChange"
                      >
                        <div class="row items-center justify-end">
                          <q-btn v-close-popup label="Close" color="primary" flat />
                        </div>
                      </q-time>
                    </q-popup-proxy>
                  </q-icon>
                </template>
              </q-input>

              <!-- DateTime Picker Input -->
              <q-input
                v-else-if="input.type === InputTypeDateTimePicker" :readonly="input.readonly"
                :model-value="currentParams[input.field]"
                :label="input.label"
                dense filled
              >
                <template v-slot:prepend>
                  <q-icon name="event" class="cursor-pointer">
                    <q-popup-proxy
                      cover
                      transition-show="scale"
                      transition-hide="scale"
                    >
                      <q-date
                        v-model="currentParams[input.field]"
                        mask="YYYY/MM/DD HH:mm:ss"
                        @update:model-value="handleChange"
                      >
                      </q-date>
                    </q-popup-proxy>
                  </q-icon>
                </template>

                <template v-slot:append>
                  <q-icon name="access_time" class="cursor-pointer">
                    <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                      <q-time v-model="currentParams[input.field]" mask="YYYY/MM/DD HH:mm:ss" format24h with-seconds
                              @update:model-value="handleChange">
                        <div class="row items-center justify-end">
                          <q-btn v-close-popup label="关闭" color="primary" flat />
                        </div>
                      </q-time>
                    </q-popup-proxy>
                  </q-icon>
                </template>
              </q-input>

              <!-- Range Date Picker Input -->
               <q-input
                 v-else-if="input.type === InputTypeDateRangePicker"
                 :model-value="currentParams[input.field] ? currentParams[input.field].from + ' - ' + currentParams[input.field].to : ''"
                 :label="input.label"
                 dense filled
               >
                 <template v-slot:append>
                   <q-icon name="event" class="cursor-pointer">
                     <q-popup-proxy
                       cover
                       transition-show="scale"
                       transition-hide="scale"
                     >
                       <q-date
                         range
                         v-model="currentParams[input.field]"
                         @update:model-value="() => changeDateRangePicker(input.field)"
                       >
                       </q-date>
                     </q-popup-proxy>
                   </q-icon>
                 </template>
               </q-input>

              <!-- File Upload Input -->
              <UploaderComponents @uploaded="(path: any) => {currentParams[input.field] = path; handleChange();}"
                v-else-if="input.type === InputTypeFile" :path="currentParams[input.field]" type="file" :label="input.label"
               ></UploaderComponents>


              <!-- Image Upload Input -->
              <UploaderComponents @uploaded="(path: any) => {currentParams[input.field] = path; handleChange();}"
                v-else-if="input.type === InputTypeImage" :path="currentParams[input.field]" :label="input.label"
               ></UploaderComponents>

              <!-- Icon Upload Input -->
              <UploaderComponents @uploaded="(path: any) => {currentParams[input.field] = path; handleChange();}"
                v-else-if="input.type === InputTypeIcon" :path="currentParams[input.field]" type="icon" :label="input.label"
               ></UploaderComponents>

              <!-- Multiple Image Upload Input -->
              <UploaderComponents @uploaded="(path: any) => {currentParams[input.field] = path; handleChange();}"
                v-else-if="input.type === InputTypeMultipleImage" :path="currentParams[input.field]" type="multiple" :label="input.label"
               ></UploaderComponents>

              <!-- Toggle Input -->
              <q-toggle @update:model-value="handleChange"
                v-else-if="input.type === InputTypeToggle" :readonly="input.readonly"
                v-model="currentParams[input.field]" :true-value="true" :false-value="false"
                :label="input.label"
              />

              <!-- Color Picker Input -->
              <q-color @update:model-value="handleChange"
                v-else-if="input.type === InputTypeColor" :readonly="input.readonly"
                v-model="currentParams[input.field]"
                :label="input.label"
                filled
              />

              <!-- Translate Input -->
              <q-input @update:model-value="handleChange" dense @click="showLangTabsFunc(input)"
                v-else-if="input.type === InputTypeTranslate" readonly
                v-model="currentParams[input.field]"
                :label="input.label"
                filled
              />

              <!-- Struct Input -->
              <div v-else-if="input.type === InputTypeStruct" :readonly="input.readonly">
                <!-- Implement struct input logic here -->
                <FormInputs
                  v-if="childrenForm?.[input.field]"
                  :show-inputs="true"
                  :inputs="childrenForm[input.field].inputs"
                  :params="currentParams[input.field] ?? {}"
                  :children-form="childrenForm"
                  :common-options="commonOptions"
                  :label="input.label"
                  :field="input.field"
                  :scan-field="input.scanField"
                  :options="childrenForm[input.field].options || false"
                  cols-class="col"
                  @submit="childrenSubmitFunc"
                />
              </div>

              <!-- Slice Input -->
              <div v-else-if="input.type === InputTypeSlice" :readonly="input.readonly">
                <q-list>
                  <q-item class="no-padding" dense>
                    <q-item-section>
                      <div class="text-caption text-bold text-grey">{{ input.label }}</div>
                    </q-item-section>
                    <q-item-section side>
                      <q-btn flat dense size="xs" icon="add" @click="handleAddSliceItem(input.field, -1, -1)" color="primary" />
                    </q-item-section>
                  </q-item>
                  <q-item v-for="(item, itemIndex) in currentParams[input.field]" :key="itemIndex" class="no-padding">
                    <q-item-section side class="no-padding q-mr-xs" style="width: 18px;">
                      <div>#{{ itemIndex + 1 }}</div>
                    </q-item-section>
                    <q-item-section class="no-padding">
                      <div v-if="Array.isArray(item)">
                        <template v-for="(inputItem, inputItemIndex) in item" :key="inputItemIndex">
                          <FormInputs
                            :index="itemIndex"
                            :item-index="inputItemIndex"
                            v-if="childrenForm?.[input.field]"
                            :show-inputs="true"
                            :inputs="childrenForm[input.field].inputs"
                            :children-form="childrenForm"
                            :params="inputItem ?? {}"
                            :common-options="commonOptions"
                            :label="input.label"
                            :field="input.field"
                            :scan-field="input.scanField"
                            :options="childrenForm[input.field].options || false"
                            cols-class="col"
                            @submit="childrenSubmitFunc"
                          />
                        </template>
                      </div>
                      <div v-else>
                        <FormInputs
                          :index="itemIndex"
                          :item-index="-1"
                          v-if="childrenForm?.[input.field]"
                          :show-inputs="true"
                          :inputs="childrenForm[input.field].inputs"
                          :params="item ?? {}"
                          :children-form="childrenForm"
                          :common-options="commonOptions"
                          :label="input.label"
                          :field="input.field"
                          :scan-field="input.scanField"
                          :options="childrenForm[input.field].options || false"
                          cols-class="col"
                          @submit="childrenSubmitFunc"
                        />
                      </div>
                    </q-item-section>
                    <q-item-section side class="no-padding q-ml-sm" style="width: 18px;" v-if="!input.readonly">
                      <div class="column">
                        <q-btn flat dense size="xs" icon="add" @click="handleAddSliceItem(input.field, itemIndex, Array.isArray(item) ? 0 : -1)" color="primary" />
                        <q-btn flat dense size="xs" icon="delete" @click="handleDeleteSliceItem(input.field, itemIndex, Array.isArray(item) ? 0 : -1)" color="grey" />
                      </div>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>

              <!-- Default fallback -->
              <q-input @update:model-value="handleChange"
                v-else :readonly="input.readonly"
                v-model="currentParams[input.field]"
                :label="input.label"
                filled dense
              />
            </div>

            <!-- Search Button -->
             <div v-if="showSearchButton && inputRows === inputs[inputs.length - 1] && input === inputRows[inputRows.length - 1]">
              <q-btn
                color="primary"
                icon="search"
                label="搜索"
                class="full-height"
                @click="handleSearch"
              />
             </div>
          </template>
        </div>
      </template>
  </div>

  <div>
    <q-dialog v-model="showLangTabs">
      <q-card class="full-width q-pb-md">
        <q-card-section style="border-bottom: 1px solid #e0e0e0;">
          <q-tabs v-model="currentTab" inline-label active-color="primary" align="left">
            <q-tab v-for="lang in langTabs" :name="lang.lang" :label="lang.label" :key="lang.lang" no-caps @click="handleTabChange(lang.lang)" />
          </q-tabs>
        </q-card-section>
        <q-card-section>
          <EditorComponents v-model="updateTranslateParams.value" />
        </q-card-section>
        <q-card-actions align="right" class="q-pa-md">
          <q-btn flat label="取消" v-close-popup />
          <q-btn color="primary" v-close-popup label="提交" @click="handleUpdateTranslate" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { requestLangTags, requestUpdateTranslate, requestOptions } from 'src/apis';
import { defineProps, ref, watch } from 'vue';
import {Form, InputField} from 'src/components/types'
import FormInputs from 'src/components/form.vue'
import UploaderComponents from 'src/components/uploader.vue'
import EditorComponents from 'src/components/editor.vue'
import EchartsLine from 'src/components/echartsLine.vue'
import {
  InputTypeText, InputTypeHtml, InputTypeTextarea, InputTypeNumber, InputTypeEditor,
  InputTypeRadio, InputTypeCheckbox, InputTypeDatePicker, InputTypeDateTimePicker,
  InputTypeDateRangePicker, InputTypeTimePicker, InputTypeSelect, InputTypeSelectSearch,
  InputTypeImage, InputTypeMultipleImage, InputTypeFile, InputTypeIcon,
  InputTypeToggle, InputTypeColor, InputTypeTranslate,
  InputTypeStruct, InputTypeSlice, InputTypeLine
} from 'src/components/types';

defineOptions({
  name: 'InputFields'
});

const emit = defineEmits(['search', 'change']);
const $q = useQuasar()
const showLangTabs = ref(false)
const langTabs = ref([]) as any
const currentTab = ref('zh-CN')
const updateTranslateParams = ref<Record<string, any>>({})
const searchOptions = ref([]) as any

const props = defineProps<{
  childrenForm?: Record<string, Form>;
  inputs: InputField[][];
  params: Record<string, any>;
  commonOptions: Record<string, Array<{ label: string; value: any }>>;
  showSearchButton?: boolean;
  colsClass: string;
}>();

const currentParams = ref({ ...props.params }) as any

watch(() => props.params, (newParams: any) => {
  currentParams.value = { ...newParams }
})

props.inputs.flat().forEach((input: InputField) => {
  // 如果参数未设置，则赋予默认值
  if (currentParams.value[input.field] === undefined) {
    currentParams.value[input.field] = input.default !== undefined ? input.default : null;

    // 如果是富文本类型, 那么只能空字符串
    if (input.type === InputTypeEditor && currentParams.value[input.field] === null) {
      currentParams.value[input.field] = ''
    }
  }
});
emit('change', currentParams.value)

// 搜索下拉选择过滤
const selectSearchFilterFn = (val: string, mask: string, inputOptions: any, doneFn: (callbackFn: () => void, afterFn: (val: any) => void) => void) => {
  const [model, options, where] = mask.split('#')
  const [label, value] = options.split('>')
  requestOptions({model: model, label: label, value: value, words: val, where: where, merchant: inputOptions && inputOptions.hasOwnProperty('merchant') ? inputOptions.merchant : false}).then((res: any) => {
    // Add an "All" option to the search options
    searchOptions.value = [
      { label: '全部', value: null },
      ...res
    ]
    doneFn(() => {}, () => {
      // console.log(val)
    })
  })
}

// 点击查询按钮, 通知到Form
const handleSearch = () => {
  if (props.showSearchButton) {
    emit('search');
  }
};

// 数据更新, 那么更新到Form数据
const handleChange = () => {
  emit('change', currentParams.value)
}

// 子集更新
const childrenSubmitFunc = (childrenFormField: string, childrenIndex: number, childrenItemIndex: number, childrenFormParams: any) => {
  if (childrenIndex === -1) {
    currentParams.value[childrenFormField] = childrenFormParams
  } else {
    if (childrenItemIndex === -1) {
      currentParams.value[childrenFormField][childrenIndex] = childrenFormParams
    } else {
      currentParams.value[childrenFormField][childrenIndex][childrenItemIndex] = childrenFormParams
    }
  }
  handleChange()
}

// 显示语言标签
const showLangTabsFunc = (input: InputField) => {
  requestLangTags({field: currentParams.value[input.field]}).then((res: any) => {
    langTabs.value = res

    if (res.length > 0) {
      updateTranslateParams.value = res[0]
      currentTab.value = res[0].lang
    }
    showLangTabs.value = true
  })
}

// 切换语言标签
const handleTabChange = (tab: string) => {
  currentTab.value = tab;
  const selectedTab = langTabs.value.find((item: any) => item.lang === tab);
  if (selectedTab) {
    updateTranslateParams.value = { ...selectedTab };
  }
};


// 更新翻译
const handleUpdateTranslate = () => {
  requestUpdateTranslate(updateTranslateParams.value).then(() => {
    const selectedTab = langTabs.value.find((item: any) => item.lang === updateTranslateParams.value.lang);
    selectedTab.value = updateTranslateParams.value.value
    $q.notify({
      type: 'positive',
      message: '翻译修改成功'
    });
  })
}


// 添加切片
const handleAddSliceItem = (field: string, index: number, itemIndex: number) => {
  // 如果index为-1, 表示添加第一条数据, 并且判断是否二维数组, 如果是二维数组, 那么添加二维的数据
  if (index === -1) {
    if (currentParams.value[field] === null) {
      currentParams.value[field] = []
    }
    currentParams.value[field].push({})
    return
  }


  // 在当前下标添加一条新数据，并使用当前数据作为值
  const newItem = {}
  if (itemIndex === -1) {
    currentParams.value[field].splice(index + 1, 0, newItem)
  } else {
    currentParams.value[field][itemIndex].splice(index + 1, 0, newItem)
  }
}

// 删除切片
const handleDeleteSliceItem = (field: string, index: number, itemIndex: number) => {
  if (itemIndex === -1) {
    currentParams.value[field].splice(index, 1)
  } else {
    currentParams.value[field][itemIndex].splice(index, 1)
  }
  handleChange()
}


// 日期范围选择
const changeDateRangePicker = (field: string) => {
  if (typeof currentParams.value[field] === 'string') {
    currentParams.value[field] = {
      from: currentParams.value[field],
      to: currentParams.value[field]
    }
  }
  handleChange()
}
</script>

<style scoped>
/* Add any scoped styles here if needed */
</style>
