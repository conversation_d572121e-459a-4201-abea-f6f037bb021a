<template>
  <q-item
    v-if="!menu.children || menu.children.length === 0"
    :to="menu.route"
    clickable
    v-ripple
    class="menu-item"
    :class="{ 'active-route': isCurrentRoute }"
  >
    <q-item-section avatar class="custom-avatar-section">
      <q-icon :name="menu.data.icon" color="primary" size="sm" />
    </q-item-section>
    <q-item-section>
      <q-item-label>{{ menu.name }}</q-item-label>
    </q-item-section>
  </q-item>

  <q-expansion-item
    v-else
    :label="menu.name"
    :default-opened="isExpanded || defaultOpened"
    group="menu"
    expand-separator
    class="menu-expansion-item"
  >
    <template v-slot:header>
      <q-item-section avatar class="custom-avatar-section">
        <q-icon :name="menu.data.icon" color="primary" size="sm" />
      </q-item-section>
      <q-item-section>
        <q-item-label>{{ menu.name }}</q-item-label>
      </q-item-section>
    </template>
    <q-list class="q-pl-lg q-pb-sm">
      <recursive-menu
        v-for="subMenu in menu.children"
        :key="subMenu.id"
        :menu="subMenu"
      />
    </q-list>
  </q-expansion-item>
</template>

<script setup lang="ts">
import { defineProps, computed } from 'vue';
import { useRoute } from 'vue-router';

defineOptions({
  name: 'RecursiveMenu'
});

const props = defineProps({
  defaultOpened: {
    type: Boolean,
    default: false
  },
  menu: {
    type: Object,
    required: true
  }
});

const route = useRoute();

const isCurrentRoute = computed(() => {
  return route.path === props.menu.route;
});

const isExpanded = computed(() => {
  if (isCurrentRoute.value) {
    return true;
  }
  if (props.menu.children) {
    return props.menu.children.some((child: any) => isChildRouteActive(child, route.path));
  }
  return false;
});

const isChildRouteActive = (menuItem: any, currentPath: string): boolean => {
  if (menuItem.route === currentPath) {
    return true;
  }
  if (menuItem.children) {
    return menuItem.children.some((child: any) => isChildRouteActive(child, currentPath));
  }
  return false;
};
</script>

<style scoped>
.menu-item {
  border-radius: 8px;
  margin: 4px 0;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.menu-expansion-item {
  margin: 4px 0;
}

.menu-expansion-item :deep(.q-expansion-item__container) {
  border-radius: 8px;
}

.menu-expansion-item :deep(.q-expansion-item__content) {
  padding-left: 8px;
}

.custom-avatar-section {
  min-width: unset;
}

.active-route {
  background-color: #E3F2FD; /* Light blue color */
}
</style>
