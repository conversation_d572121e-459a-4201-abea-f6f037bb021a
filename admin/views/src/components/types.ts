export const InputTypeText: string            = 'text'      // 表示文本输入类型
export const InputTypeHtml: string            = 'html'      // 表示html输入类型
export const InputTypeTextarea: string        = 'textarea'  // 表示多行文本输入类型
export const InputTypeNumber: string          = 'number'    // 表示数字输入类型
export const InputTypeEditor: string          = 'editor'    // 表示富文本输入类型
export const InputTypeSelect: string          = 'select'    // 表示下拉选择输入类型
export const InputTypeSelectMultiple: string    = 'selectMultiple' // 表示多选下拉选择输入类型
export const InputTypeSelectSearch: string     = 'selectSearch'   // 表示搜索下拉选择输入类型
export const InputTypeRadio: string           = 'radio'     // 表示单选按钮输入类型
export const InputTypeCheckbox: string        = 'checkbox'  // 表示复选框输入类型
export const InputTypeDatePicker: string      = 'date'      // 表示日期输入类型
export const InputTypeDateRangePicker: string = 'dateRange' // 表示范围日期输入类型
export const InputTypeTimePicker: string      = 'time'      // 表示时间输入类型
export const InputTypeDateTimePicker: string  = 'dateTime'  // 表示日期时间输入类型
export const InputTypeFile: string           = 'file'      // 表示文件上传
export const InputTypeImage: string           = 'image'     // 表示单图片上传
export const InputTypeIcon  : string           = 'icon'     // 表示图标上传
export const InputTypeMultipleImage: string   = 'images'    // 表示多张图片上传
export const InputTypeToggle: string          = 'toggle'    // 表示开关输入类型
export const InputTypeColor: string           = 'color'     // 表示颜色选择输入类型
export const InputTypeTranslate: string       = 'translate' // 翻译类型
export const InputTypeStruct: string          = 'struct'    // 结构体类型
export const InputTypeSlice: string          = 'slice'      // 切片类型
export const InputTypeDynamic: string         = 'dynamic'   // 动态输入类型
export const InputTypeLine: string           = 'line'      // 线性输入类型

export interface Form {
  label: string
  field: string
  scanField: string
  inputs: InputField[][];
  params: Record<string, any>;
  options: boolean;
  childrenForm?: Record<string, Form>;
}

export interface ButtonDialog {
  id: string;
  type?: string | 'form' | 'route' | 'copy' | 'qrcode';
  display?: string;
  url: string;
  title: string;
  small: string;
  content: string;
  size: string;
  fullWidth: boolean;
  fullHeight: boolean;
  form: Form;
  button: Button;
  options: ButtonDialogOptions;
}

export interface ButtonDialogOptions {
  cancel: Button
  submit: Button
}

export interface InputField {
  label: string;
  field: string;
  scanField: string;
  type: string;
  default: any;
  readonly: boolean;
  display: boolean;
  mask: string;
  options: any;
}

export interface Button {
  label: string;
  color: string;
  size: string
  class: string
  style: string
}

export interface Column {
  name: string;     // 字段名称
  label: string;    // 显示标题
  field: string;    // 字段名称
  required: boolean; // 始终可见
  align: string;    // 字符对齐
  sortable: boolean; // 是否排序
  format: string;   // 格式化数据 val: any, row: any 可以使用的变量
}
