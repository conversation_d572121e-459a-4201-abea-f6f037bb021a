<template>
  <div>
    <q-editor
      min-height="5rem"
      :readonly="readonly"
      ref="editorRef"
      :placeholder="label"
      :toolbar="[
        ['uploader', 'colors'],
        [
          {
            label: $q.lang.editor.align,
            icon: $q.iconSet.editor.align,
            fixedLabel: true,
            options: ['left', 'center', 'right', 'justify'],
          },
        ],
        ['bold', 'italic', 'strike', 'underline', 'subscript', 'superscript'],
        ['hr', 'link', 'custom_btn'],
        [
          {
            label: $q.lang.editor.fontSize,
            icon: $q.iconSet.editor.fontSize,
            fixedLabel: true,
            fixedIcon: true,
            list: 'no-icons',
            options: [
              'size-1',
              'size-2',
              'size-3',
              'size-4',
              'size-5',
              'size-6',
              'size-7'
            ]
          },
          {
            label: $q.lang.editor.defaultFont,
            icon: $q.iconSet.editor.font,
            fixedIcon: true,
            list: 'no-icons',
            options: [
              'default_font',
              'arial',
              'arial_black',
              'comic_sans',
              'courier_new',
              'impact',
              'times_new_roman',
              'verdana'
            ]
          },
        ],
        ['viewsource'],
      ]"
      :fonts="{
        arial: 'Arial',
        arial_black: 'Arial Black',
        comic_sans: 'Comic Sans MS',
        courier_new: 'Courier New',
        impact: 'Impact',
        times_new_roman: 'Times New Roman',
        verdana: 'Verdana',
      }"
      v-model="currentValue"
      @update:model-value="updateValue"
      :dense="$q.screen.lt.md"
    >
      <template v-slot:uploader>
        <div style="width: 50px;">
          <UploaderComponents type="icon" @uploaded="onUploaded" />
        </div>
      </template>
      <template v-slot:colors>
        <div class="flex justify-center items-center">
          <q-btn flat dense label="颜色" size="xs" class="full-width bg-primary text-white" @click="showColorPicker = true" />
        </div>
      </template>
    </q-editor>

    <q-dialog v-model="showColorPicker">
      <q-card>
        <q-card-section>
          <q-color
            :model-value="hex"
            @change="editTextColor"
            style="max-width: 250px"
          />
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, watch} from 'vue';
import { imageSrc } from 'src/utils';
import UploaderComponents from 'src/components/uploader.vue'

defineOptions({
  name: 'EditorComponents'
})

const props = defineProps({
  modelValue: { type: undefined, default: '' },
  label: { type: String, default: '' },
  readonly: { type: Boolean, default: false },
})

const showColorPicker = ref(false)
const hex = ref('') as any
const currentValue = ref(props.modelValue) as any
const editorRef = ref(null) as any

watch(() => props.modelValue, (newVal) => {
  currentValue.value = newVal
})

const emits = defineEmits(['update:modelValue'])
const updateValue = (val: any) => {
  currentValue.value = val == '<br>' || val == '\n' ? '' : val
  emits('update:modelValue', currentValue.value)
}

// 上传图片
const onUploaded = (val: any) => {
  currentValue.value += '<img src="' + imageSrc(val) + '" width="100%" alt="" />';
  emits('update:modelValue', currentValue.value)
}
// 编辑颜色
const editTextColor = (val: any) => {
  const selection = window.getSelection();
  const selectedText = selection?.toString().trim() || '';
  currentValue.value = currentValue.value.replace(selectedText, `<span style="color: ${val}">${selectedText}</span>`);
  showColorPicker.value = false;
  emits('update:modelValue', currentValue.value)
}
</script>
