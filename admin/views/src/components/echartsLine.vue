<template>
  <div>
    <div ref="echartsLine" style="height: 460px;"></div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'EchartsLine'
});

import * as echarts from 'echarts';
import { ref, onMounted } from 'vue';
import { date } from 'quasar'

const echartsLine = ref<HTMLDivElement | null>(null);
let echartsInstance: echarts.EChartsType;
const data = ref([]) as any;


const props = defineProps<{
  params: Array<[number, number]>;
}>();

const symbolSize = 8;

const showTooltip = (dataIndex: number) => {
  echartsInstance.dispatchAction({
    type: 'showTip',
    seriesIndex: 0,
    dataIndex: dataIndex
  });
}
const hideTooltip = () => {
  echartsInstance.dispatchAction({
    type: 'hideTip'
  });
}
const onPointDragging = (dataIndex: number, pos: number[]) => {
  data.value[dataIndex][1] = echartsInstance.convertFromPixel('grid', pos)[1] ?? 0;
  echartsInstance.setOption({
    series: [
      {
        id: 'a',
        data: data.value
      }
    ]
  });
}

onMounted(() => {
  echartsInstance = echarts.init(echartsLine.value);
  data.value = props.params;

  let yVal: number[] = [];
  data.value.forEach(([, y]: [number, number]) => yVal.push(y)); // 只关心 y
  let maxY = Math.max(...yVal); // 获取最大值
  let minY = Math.min(...yVal); // 获取最小值

  echartsInstance.setOption({
    tooltip: {
      triggerOn: 'none',
      formatter: function (params: any) {
        return (
          '日期: ' +
          date.formatDate(params.data[0], 'YYYY-MM-DD HH:mm:ss') +
          '<br>数值: ' +
          params.data[1].toFixed(2)
        );
      }
    },
    grid: {
      top: '5%',
      left: '8%',
      right: '8%',
      bottom: '12%'
    },
    xAxis: {
      type: 'time',
      axisLine: { onZero: false }
    },
    yAxis: {
      min: minY,
      max: maxY,
      type: 'value',
      axisLine: { onZero: false },
      splitLine: {
        show: true, // 显示 Y 轴的网格线
        lineStyle: {
          color: 'rgba(255,168,168,0.38)',
          type: 'dashed',
        }
      },
    },
    dataZoom: [
      {
        type: 'inside',
        xAxisIndex: 0,
        filterMode: 'none'
      },
      {
        type: 'inside',
        filterMode: 'none'
      }
    ],
    series: [
      {
        id: 'a',
        type: 'line',
        smooth: true,
        symbolSize: symbolSize,
        data: data.value
      }
    ]
  });

  window.addEventListener('resize', updatePosition);
  echartsInstance.on('dataZoom', updatePosition);
  function updatePosition() {
    let [dataZoom]: any = echartsInstance.getOption().dataZoom;
    const startValue= dataZoom.startValue
    const endValue= dataZoom.endValue
    // 你可以根据这些数据范围进一步筛选数据
    const filteredData = data.value.filter(([x]: [number, number]) =>x >= startValue && x <= endValue);
    // 提取所有的 value
    let yValues: number[] = [];
    filteredData.forEach(([, y]: [number, number]) => yValues.push(y)); // 只关心 y
    // 获取最大值
    let maxY = Math.max(...yValues);
    // 获取最小值
    let minY = Math.min(...yValues);



    echartsInstance.setOption({
      graphic: data.value.map(function (item: any) {
      return {
        position: echartsInstance.convertToPixel('grid', item)
        };
      }),
      yAxis: {
        min: minY,
        max: maxY,
      }
    });
  }

  setTimeout(() => {
    echartsInstance.setOption({
    graphic: data.value.map(function (item: any, dataIndex: number) {
      return {
        type: 'circle',
        position: echartsInstance.convertToPixel('grid', item),
        shape: {
          cx: 0,
          cy: 0,
          r: 10
        },
        invisible: true,
        draggable: true,
        ondrag: (dx: any) => {
          onPointDragging(dataIndex, [dx.offsetX, dx.offsetY]);
        },
        onmousemove: () => {
          showTooltip(dataIndex);
        },
        onmouseout: () => {
          hideTooltip();
          },
          z: 100
        };
      })
    });
  }, 100);
});



</script>
