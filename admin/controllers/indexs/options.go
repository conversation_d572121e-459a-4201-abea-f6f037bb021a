package indexs

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/service"
)

// OptionsParams 获取选项参数
type OptionsParams struct {
	Model    string `json:"model" validate:"required"`
	Where    string `json:"where"`
	Label    string `json:"label" validate:"required"`
	Value    string `json:"value" validate:"required"`
	Words    string `json:"words"`
	Merchant bool   `json:"merchant"`
}

// Options 获取选项
func Options(c *context.CustomCtx, bodyParams *OptionsParams) error {
	adminService := service.NewAdminUserService()
	currentAdminId := c.Claims.AdminID
	if bodyParams.Merchant {
		currentAdminId = c.Claims.MerchantID
	}
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, currentAdminId)

	data := make([]map[string]interface{}, 0)
	db := model.NewModel()
	query := db.Table(bodyParams.Model).Select(bodyParams.Label, bodyParams.Value).
		Where(bodyParams.Label+" LIKE ?", bodyParams.Words+"%").
		Where("deleted_at IS NULL")
	if bodyParams.Model == "admin_user" {
		query.Where("id IN ?", subAdminIDs)
	} else {
		query.Where("admin_id IN ?", subAdminIDs)
	}

	if bodyParams.Where != "" {
		query.Where(bodyParams.Where)
	}
	query.Limit(50).Find(&data)

	options := make([]*views.SelectOption, 0)
	for _, v := range data {
		option := views.SelectOption{
			Label: v[bodyParams.Label].(string),
			Value: v[bodyParams.Value],
		}
		options = append(options, &option)
	}

	return c.SuccessJson(options)
}
