package indexs

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// Info 管理信息
func Info(c *context.CustomCtx, bodyParams *context.NoRequestBody) error {
	var adminInfo models.AdminUser
	result := model.NewModel().Where("id = ?", c.Claims.AdminID).First(&adminInfo)
	if result.Error != nil {
		return c.<PERSON><PERSON><PERSON><PERSON><PERSON>("管理员不存在")
	}

	return c.<PERSON><PERSON><PERSON>(adminInfo.ToDisplayData())
}
