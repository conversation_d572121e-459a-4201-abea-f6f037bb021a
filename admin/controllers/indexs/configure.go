package indexs

import (
	"zfeng/core/context"
	"zfeng/core/views"
)

// AdminMenuConfigureFunc 管理菜单配置
var AdminMenuConfigureFunc = map[string]func(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table{}

// ConfigureParams 配置参数
type ConfigureParams struct {
	Route string                 `json:"route"` // 配置路由
	Query map[string]interface{} `json:"query"` // 查询参数
}

// Configure 菜单配置
func Configure(c *context.CustomCtx, bodyParams *ConfigureParams) error {
	if _, ok := AdminMenuConfigureFunc[bodyParams.Route]; !ok {
		return c.ErrorJson("当前菜单没有配置～")
	}

	// 输出内容
	vueTable := AdminMenuConfigureFunc[bodyParams.Route](c, bodyParams.Query)

	return c.SuccessJson(vueTable)
}
