package indexs

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// LangTabsParams 语言标签参数
type LangTabsParams struct {
	Field string `json:"field" validate:"required"`
}

// LangTabsRow 语言标签行
type LangTabsRow struct {
	Label string `json:"label"`
	Lang  string `json:"lang"`
	Field string `json:"field"`
	Value string `json:"value"`
}

// LangTabs 语言标签
func LangTabs(c *context.CustomCtx, params *LangTabsParams) error {
	db := model.NewModel()
	langs := make([]*models.LangDisplayData, 0)

	data := make([]*LangTabsRow, 0)
	db.Model(&models.Lang{}).Where("admin_id = ?", c.Claims.MerchantID).Where("status = ?", models.LangStatusEnabled).Find(&langs)

	translateService := service.NewTranslateService()
	for _, v := range langs {
		row := &LangTabsRow{
			Label: v.<PERSON>,
			Lang:  v.Symbol,
			Field: params.Field,
			Value: translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, v.Symbol, params.Field),
		}
		data = append(data, row)
	}

	return c.SuccessJson(data)
}
