package indexs

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// UpdateTranslateParams 更新翻译参数
type UpdateTranslateParams struct {
	Lang  string `json:"lang"`  //	语言别名
	Field string `json:"field"` //	翻译字段
	Value string `json:"value"` //	翻译值
}

// UpdateTranslate 更新翻译
func UpdateTranslate(c *context.CustomCtx, bodyParams *UpdateTranslateParams) error {
	if bodyParams.Value == "<br>" {
		bodyParams.Value = ""
	}
	db := model.NewModel()
	zhTranslate := &models.Translate{}
	db.Model(&models.Translate{}).Where("admin_id = ? AND lang = ? AND field = ?", c.Claims.MerchantID, models.DefaultLang, bodyParams.Field).First(zhTranslate)
	if zhTranslate.ID == 0 {
		zhTranslate.AdminID = c.Claims.MerchantID
		zhTranslate.Lang = models.DefaultLang
		zhTranslate.Name = bodyParams.Field
		zhTranslate.Field = bodyParams.Field
		zhTranslate.Value = bodyParams.Value
		zhTranslate.Type = models.TranslateTypeSystem
	}

	translate := &models.Translate{}
	db.Model(&models.Translate{}).Where("admin_id = ? AND lang = ? AND field = ?", c.Claims.MerchantID, bodyParams.Lang, bodyParams.Field).First(translate)
	if translate.ID == 0 {
		// Create a new translation if it doesn't exist
		newTranslate := &models.Translate{
			AdminID: c.Claims.MerchantID,
			Lang:    bodyParams.Lang,
			Name:    zhTranslate.Name,
			Field:   bodyParams.Field,
			Value:   bodyParams.Value,
			Desc:    zhTranslate.Desc,
			Type:    zhTranslate.Type,
			Status:  zhTranslate.Status,
		}
		if err := db.Create(newTranslate).Error; err != nil {
			return c.ErrorJson("Failed to create new translation")
		}
	} else {
		// Update the existing translation
		translate.Value = bodyParams.Value
		if err := db.Save(translate).Error; err != nil {
			return c.ErrorJson("Failed to update translation")
		}
	}

	// 删除所有翻译缓存
	translateService := service.NewTranslateService()
	translateService.DeleteAllTranslateCache(c.Rds, c.Claims.MerchantID)

	return c.SuccessJson(nil)
}
