package indexs

import (
	"zfeng/core/context"

	"github.com/dchest/captcha"
	"github.com/gofiber/fiber/v2"
)

// NewCaptcha 生成新的验证码
func NewCaptcha(c *fiber.Ctx) error {
	captchaId := captcha.NewLen(4)
	return c.JSON(context.SuccessResponse(captchaId))
}

// ShowCaptcha 显示验证码图片
func ShowCaptcha(c *fiber.Ctx) error {
	captchaId := c.Params("captchaId")
	if captchaId == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Missing captcha ID",
		})
	}

	c.Set("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Set("Pragma", "no-cache")
	c.Set("Expires", "0")

	if !captcha.Reload(captchaId) {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Captcha not found",
		})
	}

	c.Set("Content-Type", "image/png")

	err := captcha.WriteImage(c.Response().BodyWriter(), captchaId, captcha.StdWidth, captcha.StdHeight)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to generate captcha image",
		})
	}

	return nil
}
