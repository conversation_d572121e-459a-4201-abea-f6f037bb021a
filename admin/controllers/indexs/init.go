package indexs

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

type InitRows struct {
	Notices []*models.NotifyDisplayData `json:"notices"`
}

// 初始化数据
func Init(c *context.CustomCtx, _ *context.NoRequestBody) error {
	data := &InitRows{
		Notices: make([]*models.NotifyDisplayData, 0),
	}

	db := model.NewModel()

	// 消息通知数据
	db.Model(&models.Notify{}).Where("admin_id = ?", c.Claims.AdminID).Where("mode = ?", models.NotifyModeBackend).Order("id desc").Limit(10).Find(&data.Notices)

	return c.<PERSON><PERSON><PERSON>(data)
}
