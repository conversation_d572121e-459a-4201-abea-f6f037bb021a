package indexs

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

type UpdateParams struct {
	Avatar    string `json:"avatar"`    //	头像
	Email     string `json:"email"`     //	邮箱
	Telephone string `json:"telephone"` //	号码
	NickName  string `json:"nickname"`  //	昵称
	Domains   string `json:"domains"`   //	域名
	ChatURL   string `json:"chatUrl"`   //	客服链接
}

// Update 更新管理信息
func Update(c *context.CustomCtx, bodyParams *UpdateParams) error {
	var adminInfo models.AdminUser
	result := model.NewModel().Where("id = ?", c.Claims.AdminID).First(&adminInfo)
	if result.Error != nil {
		return c.ErrorJson("管理员不存在")
	}

	// 判断绑定的域名是否被占用
	adminService := service.NewAdminUserService()
	err := adminService.CheckDomainValidity(c.Rds, adminInfo, bodyParams.Domains)
	if err != nil {
		return c.<PERSON><PERSON>r<PERSON>(err.<PERSON>rror())
	}

	// 构建更新字段映射
	updateFields := map[string]interface{}{
		"avatar":   bodyParams.Avatar,
		"nickname": bodyParams.NickName,
		"domains":  bodyParams.Domains,
		"chat_url": bodyParams.ChatURL,
	}

	if bodyParams.Email != "" {
		updateFields["email"] = bodyParams.Email
	}

	// 只有当电话号码不为空时才更新
	if bodyParams.Telephone != "" {
		updateFields["telephone"] = bodyParams.Telephone
	}

	if err := model.NewModel().Model(&adminInfo).Where("id = ?", c.Claims.AdminID).Updates(updateFields).Error; err != nil {
		return c.ErrorJson("更新管理员信息失败")
	}

	// 删除管理客服连接缓存
	_ = adminService.DeleteAdminUserByChatURL(c.Rds, adminInfo.ID)

	return c.SuccessOk()
}
