package indexs

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

type IndexRows struct {
	Category []string  `json:"category"`
	Items    []*Statis `json:"statis"`
}

// Index 管理首页数据
func Index(c *context.CustomCtx, bodyParams *context.NoRequestBody) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	data := &IndexRows{Items: make([]*Statis, 0), Category: make([]string, 0)}
	nowTime := time.Now()
	todayTime := time.Now().AddDate(0, 0, -1)

	nowTimeStartTime := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, time.Local)
	todayTimeStartTime := time.Date(todayTime.Year(), todayTime.Month(), todayTime.Day(), 0, 0, 0, 0, time.Local)

	// 访问量
	accessStatis := &Statis{
		Label: "访问量",
		Icon:  "visibility",
		Class: "bg-blue text-white", // 访问量用蓝色表示流量
		Data:  make([]any, 0),
	}
	data.Items = append(data.Items, accessStatis)
	db.Model(&models.Access{}).Select("COUNT(DISTINCT ip)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.AccessTypeRefresh).Where("created_at >= ?", nowTimeStartTime).Find(&accessStatis.Today)
	db.Model(&models.Access{}).Select("COUNT(DISTINCT ip)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.AccessTypeRefresh).Where("created_at BETWEEN ? AND ?", todayTimeStartTime, nowTimeStartTime).Find(&accessStatis.Yesterday)
	db.Model(&models.Access{}).Select("COUNT(DISTINCT ip)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.AccessTypeRefresh).Find(&accessStatis.Total)

	// 注册量
	userStatis := &Statis{
		Label: "注册量",
		Icon:  "person_add",
		Class: "bg-orange text-white", // 注册量用橙色表示新增
		Data:  make([]any, 0),
	}
	data.Items = append(data.Items, userStatis)
	db.Model(&models.User{}).Select("count(*)").Where("admin_id IN ?", subAdminIDs).Where("created_at >= ?", nowTimeStartTime).Find(&userStatis.Today)
	db.Model(&models.User{}).Select("count(*)").Where("admin_id IN ?", subAdminIDs).Where("created_at BETWEEN ? AND ?", todayTimeStartTime, nowTimeStartTime).Find(&userStatis.Yesterday)
	db.Model(&models.User{}).Select("count(*)").Where("admin_id IN ?", subAdminIDs).Find(&userStatis.Total)

	// 充值量
	depositStatis := &Statis{
		Label: "充值量",
		Icon:  "payments",
		Class: "bg-green text-white", // 充值用绿色表示收入
		Data:  make([]any, 0),
	}
	data.Items = append(data.Items, depositStatis)
	db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeDeposit).Where("status = ?", models.WalletOrderStatusCompleted).Where("created_at >= ?", nowTimeStartTime).Find(&depositStatis.Today)
	db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeDeposit).Where("status = ?", models.WalletOrderStatusCompleted).Where("created_at BETWEEN ? AND ?", todayTimeStartTime, nowTimeStartTime).Find(&depositStatis.Yesterday)
	db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeDeposit).Where("status = ?", models.WalletOrderStatusCompleted).Find(&depositStatis.Total)

	// 提现量
	withdrawStatis := &Statis{
		Label: "提现量",
		Icon:  "money_off",
		Class: "bg-red text-white", // 提现用红色表示支出
		Data:  make([]any, 0),
	}
	data.Items = append(data.Items, withdrawStatis)
	db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeWithdrawal).Where("status = ?", models.WalletOrderStatusCompleted).Where("created_at >= ?", nowTimeStartTime).Find(&withdrawStatis.Today)
	db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeWithdrawal).Where("status = ?", models.WalletOrderStatusCompleted).Where("created_at BETWEEN ? AND ?", todayTimeStartTime, nowTimeStartTime).Find(&withdrawStatis.Yesterday)
	db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeWithdrawal).Where("status = ?", models.WalletOrderStatusCompleted).Find(&withdrawStatis.Total)

	// 订单数
	orderNumsStatis := &Statis{
		Label: "订单量",
		Icon:  "receipt_long",
		Class: "bg-purple text-white", // 订单量用紫色表示交易
		Data:  make([]any, 0),
	}
	data.Items = append(data.Items, orderNumsStatis)
	db.Model(&models.Order{}).Select("count(*)").Where("admin_id IN ?", subAdminIDs).Where("status > ?", models.ProductOrderStatusCancelled).Where("created_at >= ?", nowTimeStartTime).Find(&orderNumsStatis.Today)
	db.Model(&models.Order{}).Select("count(*)").Where("admin_id IN ?", subAdminIDs).Where("status > ?", models.ProductOrderStatusCancelled).Where("created_at BETWEEN ? AND ?", todayTimeStartTime, nowTimeStartTime).Find(&orderNumsStatis.Yesterday)
	db.Model(&models.Order{}).Select("count(*)").Where("admin_id IN ?", subAdminIDs).Where("status > ?", models.ProductOrderStatusCancelled).Find(&orderNumsStatis.Total)

	// 订单金额
	orderMoneyStatis := &Statis{
		Label: "订单金额",
		Icon:  "attach_money",
		Class: "bg-teal text-white", // 订单金额用青色表示金额
		Data:  make([]any, 0),
	}
	data.Items = append(data.Items, orderMoneyStatis)
	db.Model(&models.Order{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("status > ?", models.ProductOrderStatusCancelled).Where("created_at >= ?", nowTimeStartTime).Find(&orderMoneyStatis.Today)
	db.Model(&models.Order{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("status > ?", models.ProductOrderStatusCancelled).Where("created_at BETWEEN ? AND ?", todayTimeStartTime, nowTimeStartTime).Find(&orderMoneyStatis.Yesterday)
	db.Model(&models.Order{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("status > ?", models.ProductOrderStatusCancelled).Find(&orderMoneyStatis.Total)

	// 资产充值
	assetDepositStatis := &Statis{
		Label: "资产充值",
		Icon:  "account_balance",
		Class: "bg-cyan text-white", // 资产充值用浅蓝色表示资产增加
		Data:  make([]any, 0),
	}
	data.Items = append(data.Items, assetDepositStatis)
	db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeAssetDeposit).Where("status = ?", models.WalletOrderStatusCompleted).Where("created_at >= ?", nowTimeStartTime).Find(&assetDepositStatis.Today)
	db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeAssetDeposit).Where("status = ?", models.WalletOrderStatusCompleted).Where("created_at BETWEEN ? AND ?", todayTimeStartTime, nowTimeStartTime).Find(&assetDepositStatis.Yesterday)
	db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeAssetDeposit).Where("status = ?", models.WalletOrderStatusCompleted).Find(&assetDepositStatis.Total)

	// 资产提现
	assetWithdrawStatis := &Statis{
		Label: "资产提现",
		Icon:  "account_balance_wallet",
		Class: "bg-brown text-white", // 资产提现用棕色表示资产减少
		Data:  make([]any, 0),
	}
	data.Items = append(data.Items, assetWithdrawStatis)
	db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeAssetWithdrawal).Where("status = ?", models.WalletOrderStatusCompleted).Where("created_at >= ?", nowTimeStartTime).Find(&assetWithdrawStatis.Today)
	db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeAssetWithdrawal).Where("status = ?", models.WalletOrderStatusCompleted).Where("created_at BETWEEN ? AND ?", todayTimeStartTime, nowTimeStartTime).Find(&assetWithdrawStatis.Yesterday)
	db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeAssetWithdrawal).Where("status = ?", models.WalletOrderStatusCompleted).Find(&assetWithdrawStatis.Total)

	for i := -14; i <= 0; i++ {
		nowTimeTmp := time.Now().AddDate(0, 0, i)
		sourceTime := time.Date(nowTimeTmp.Year(), nowTimeTmp.Month(), nowTimeTmp.Day(), 0, 0, 0, 0, time.Local)
		staTime := sourceTime
		endTime := staTime.Add(24 * time.Hour)
		data.Category = append(data.Category, sourceTime.Format("01/02"))

		// 访问量
		var accessTmp float64
		db.Model(&models.Access{}).Select("COUNT(DISTINCT ip)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.AccessTypeRefresh).Where("created_at BETWEEN ? AND ?", staTime, endTime).Find(&accessTmp)
		accessStatis.Data = append(accessStatis.Data, accessTmp)

		// 注册量
		var userTmp float64
		db.Model(&models.User{}).Select("count(*)").Where("admin_id IN ?", subAdminIDs).Where("created_at BETWEEN ? AND ?", staTime, endTime).Find(&userTmp)
		userStatis.Data = append(userStatis.Data, userTmp)

		// 充值量
		var depositTmp float64
		db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeDeposit).Where("status = ?", models.WalletOrderStatusCompleted).Where("created_at BETWEEN ? AND ?", staTime, endTime).Find(&depositTmp)
		depositStatis.Data = append(depositStatis.Data, depositTmp)

		// 提现量
		var withdrawTmp float64
		db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeWithdrawal).Where("status = ?", models.WalletOrderStatusCompleted).Where("created_at BETWEEN ? AND ?", staTime, endTime).Find(&withdrawTmp)
		withdrawStatis.Data = append(withdrawStatis.Data, withdrawTmp)

		// 订单量
		var orderNumsTmp float64
		db.Model(&models.Order{}).Select("count(*)").Where("admin_id IN ?", subAdminIDs).Where("status > ?", models.ProductOrderStatusCancelled).Where("created_at BETWEEN ? AND ?", staTime, endTime).Find(&orderNumsTmp)
		orderNumsStatis.Data = append(orderNumsStatis.Data, orderNumsTmp)

		// 订单金额
		var orderMoneyTmp float64
		db.Model(&models.Order{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("status > ?", models.ProductOrderStatusCancelled).Where("created_at BETWEEN ? AND ?", staTime, endTime).Find(&orderMoneyTmp)
		orderMoneyStatis.Data = append(orderMoneyStatis.Data, orderMoneyTmp)

		// 资产充值
		var assetDepositTmp float64
		db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeAssetDeposit).Where("status = ?", models.WalletOrderStatusCompleted).Where("created_at BETWEEN ? AND ?", staTime, endTime).Find(&assetDepositTmp)
		assetDepositStatis.Data = append(assetDepositStatis.Data, assetDepositTmp)

		// 资产提现
		var assetWithdrawTmp float64
		db.Model(&models.WalletOrder{}).Select("IFNULL(sum(money), 0)").Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeAssetWithdrawal).Where("status = ?", models.WalletOrderStatusCompleted).Where("created_at BETWEEN ? AND ?", staTime, endTime).Find(&assetWithdrawTmp)
		assetWithdrawStatis.Data = append(assetWithdrawStatis.Data, assetWithdrawTmp)
	}

	return c.SuccessJson(data)
}

// Statis 统计
type Statis struct {
	Label     string  `json:"label"`     //	标题
	Icon      string  `json:"icon"`      //	图标
	Class     string  `json:"class"`     //	样式
	Today     float64 `json:"today"`     //	今日
	Yesterday float64 `json:"yesterday"` //	昨日
	Total     float64 `json:"total"`     //	总数
	Data      []any   `json:"data"`      //	数据
}
