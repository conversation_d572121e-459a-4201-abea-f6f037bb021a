package indexs

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/middleware"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"

	"github.com/dchest/captcha"
	"github.com/pquerna/otp/totp"
)

type LoginParams struct {
	Username   string `json:"username"`   //	管理名称
	Password   string `json:"password"`   //	管理密码
	CaptchaId  string `json:"captchaId"`  //	验证码ID
	CaptchaVal string `json:"captchaVal"` //	验证码值
}

type loginData struct {
	Token  string                         `json:"token"`  //	管理Token
	Meunus []*models.AdminMenuDisplayData `json:"menus"`  //	管理菜单
	Routes []string                       `json:"routes"` //	权限路由
	Info   models.AdminUserDisplayData    `json:"info"`   //	管理信息
}

// Login 管理登录
func Login(c *context.CustomCtx, bodyParams *LoginParams) error {
	// 查找管理用户
	var adminInfo models.AdminUser
	model.NewModel().Where("username = ? AND status = ?", bodyParams.Username, models.AdminUserStatusEnabled).Find(&adminInfo)
	if adminInfo.ID == 0 || adminInfo.Password != utils.EncryptPassword(bodyParams.Password) {
		return c.ErrorJson("账户或密码不正确")
	}

	// 验证码验证
	if adminInfo.Data.Auth.Enable {
		if !totp.Validate(bodyParams.CaptchaVal, adminInfo.Data.Auth.Key) {
			return c.ErrorJson("验证器不正确")
		}
	} else {
		if !captcha.VerifyString(bodyParams.CaptchaId, bodyParams.CaptchaVal) {
			return c.ErrorJson("验证码不正确")
		}
	}

	// 获取管理菜单
	routes, menus, _ := service.NewAdminMenuService().GetAdminMenuTreeWithCache(c.Rds, adminInfo.ID)

	// 生成管理Token
	merchantID, err := service.NewAdminUserService().GetMerchantIDWithCache(c.Rds, adminInfo.ID)
	if merchantID == 0 || err != nil {
		return c.ErrorJson("获取商户ID异常")
	}
	adminToken, err := middleware.GenerateAdminToken(c.Ctx, adminInfo.ID, merchantID, 30*86400*time.Second, 10)
	if err != nil {
		return c.ErrorJson("生成管理Token 失败～")
	}
	return c.SuccessJson(&loginData{
		Token:  adminToken,
		Meunus: menus,
		Routes: routes,
		Info:   adminInfo.ToDisplayData(),
	})
}
