package indexs

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// 通知详情参数
type NotifyInfoParams struct {
	ID uint `json:"id" validate:"required"`
}

// 通知详情
func NotifyInfo(c *context.CustomCtx, bodyParams *NotifyInfoParams) error {
	db := model.NewModel()
	notify := &models.Notify{}
	if err := db.Model(&models.Notify{}).Where("id = ?", bodyParams.ID).Where("admin_id = ?", c.Claims.AdminID).First(notify).Error; err != nil {
		return c.<PERSON><PERSON><PERSON><PERSON><PERSON>(err.Error())
	}

	// 标记已读状态
	if notify.Status == models.NotifyStatusUnread {
		db.Model(&models.Notify{}).Where("id = ?", notify.ID).Update("status", models.NotifyStatusRead)
	}

	return c.<PERSON><PERSON><PERSON>(notify.GetDisplayData())
}
