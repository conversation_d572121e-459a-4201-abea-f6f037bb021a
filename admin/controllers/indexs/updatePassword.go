package indexs

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/middleware"
	"zfeng/models"
	"zfeng/utils"
)

const (
	updatePasswdTypePasswd   = 1 //	更新登录密码
	updatePasswdTypeSecurity = 2 //	更新提现密码
)

type UpdatePasswordParams struct {
	Type        int    `json:"type" validate:"required,oneof=1 2"` //	更新类型 1登录密码 2提现密码
	OldPassword string `json:"oldPassword" validate:"required"`    //	旧密码
	NewPassword string `json:"newPassword" validate:"required"`    //	新密码
	CmfPassword string `json:"cmfPassword" validate:"required"`    //	确认密码
}

// UpdatePassword 更新管理密码
func UpdatePassword(c *context.CustomCtx, bodyParams *UpdatePasswordParams) error {
	var adminInfo models.AdminUser
	result := model.NewModel().Where("id = ?", c.Clai<PERSON>.AdminID).First(&adminInfo)
	if result.Error != nil {
		return c.ErrorJson("管理员不存在")
	}

	// 验证新密码和确认密码是否一致
	if bodyParams.NewPassword != bodyParams.CmfPassword {
		return c.ErrorJson("新密码和确认密码不一致")
	}

	updateFields := map[string]interface{}{}

	// 根据更新类型验证旧密码并更新新密码
	switch bodyParams.Type {
	case updatePasswdTypePasswd:
		// 验证旧登录密码
		if utils.EncryptPassword(bodyParams.OldPassword) != adminInfo.Password {
			return c.ErrorJson("旧登录密码不正确")
		}
		// 更新登录密码
		updateFields["password"] = utils.EncryptPassword(bodyParams.NewPassword)

		// 清除管理Token缓存
		middleware.ClearAdminTokenCache(c.Rds, c.Claims.AdminID)
	case updatePasswdTypeSecurity:
		// 验证旧提现密码
		if utils.EncryptPassword(bodyParams.OldPassword) != adminInfo.SecurityKey {
			return c.ErrorJson("旧提现密码不正确")
		}
		// 更新提现密码
		updateFields["security_Key"] = utils.EncryptPassword(bodyParams.NewPassword)
	default:
		return c.ErrorJson("无效的更新类型")
	}

	// 保存更新后的管理员信息
	if err := model.NewModel().Model(&adminInfo).Where("id = ?", adminInfo.ID).Updates(updateFields).Error; err != nil {
		return c.ErrorJson("更新密码失败")
	}

	return c.SuccessOk()
}
