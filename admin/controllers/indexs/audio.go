package indexs

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// AudioRows 提示音
type AudioRows struct {
	Label  string `json:"label"`  //	标签
	Source string `json:"source"` //	来源
}

// Audio 提示音
func Audio(c *context.CustomCtx, bodyParams *context.NoRequestBody) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	db := model.NewModel()

	data := &AudioRows{Label: "", Source: ""}
	audioInfo := struct {
		ID       uint    `json:"id"`       //	ID
		Username string  `json:"username"` //	用户名
		Amount   float64 `json:"amount"`   //	金额
	}{}
	// 余额充值提示音
	db.Model(&models.WalletOrder{}).Select("wallet_order.id", "user.username", "wallet_order.money as amount").
		Joins("left join user on user.id = wallet_order.user_id").Where("wallet_order.admin_id in ?", subAdminIDs).
		Where("wallet_order.type = ?", models.WalletOrderTypeDeposit).Where("wallet_order.is_hint = ?", model.BoolTrue).
		Where("wallet_order.status = ?", models.WalletOrderStatusPending).Find(&audioInfo)
	if audioInfo.ID > 0 {
		data.Label = fmt.Sprintf("%s 余额充值了 %.2f, 请及时处理～", audioInfo.Username, audioInfo.Amount)
		data.Source = "/mp3/deposit.mp3"
	}

	// 资产充值提示音
	db.Model(&models.WalletOrder{}).Select("wallet_order.id", "user.username", "wallet_order.money as amount").
		Joins("left join user on user.id = wallet_order.user_id").Where("wallet_order.admin_id in ?", subAdminIDs).
		Where("wallet_order.type = ?", models.WalletOrderTypeAssetDeposit).Where("wallet_order.is_hint = ?", model.BoolTrue).
		Where("wallet_order.status = ?", models.WalletOrderStatusPending).Find(&audioInfo)
	if audioInfo.ID > 0 {
		data.Label = fmt.Sprintf("%s 资产充值了 %.2f, 请及时处理～", audioInfo.Username, audioInfo.Amount)
		data.Source = "/mp3/deposit.mp3"
		return c.SuccessJson(data)
	}

	// 余额提现提示音
	db.Model(&models.WalletOrder{}).Select("wallet_order.id", "user.username", "wallet_order.money as amount").
		Joins("left join user on user.id = wallet_order.user_id").Where("wallet_order.admin_id in ?", subAdminIDs).
		Where("wallet_order.type = ?", models.WalletOrderTypeWithdrawal).Where("wallet_order.is_hint = ?", model.BoolTrue).
		Where("wallet_order.status = ?", models.WalletOrderStatusPending).Find(&audioInfo)
	if audioInfo.ID > 0 {
		data.Label = fmt.Sprintf("%s 余额提现了 %.2f, 请及时处理～", audioInfo.Username, audioInfo.Amount)
		data.Source = "/mp3/withdraw.mp3"
		return c.SuccessJson(data)
	}

	// 资产提现提示音
	db.Model(&models.WalletOrder{}).Select("wallet_order.id", "user.username", "wallet_order.money as amount").
		Joins("left join user on user.id = wallet_order.user_id").Where("wallet_order.admin_id in ?", subAdminIDs).
		Where("wallet_order.type = ?", models.WalletOrderTypeAssetWithdrawal).Where("wallet_order.is_hint = ?", model.BoolTrue).
		Where("wallet_order.status = ?", models.WalletOrderStatusPending).Find(&audioInfo)
	if audioInfo.ID > 0 {
		data.Label = fmt.Sprintf("%s 资产提现了 %.2f, 请及时处理～", audioInfo.Username, audioInfo.Amount)
		data.Source = "/mp3/withdraw.mp3"
		return c.SuccessJson(data)
	}

	// 用户认证提示音
	db.Model(&models.UserAuth{}).Select("user_auth.id", "user.username").
		Joins("left join user on user.id = user_auth.user_id").Where("user_auth.admin_id in ?", subAdminIDs).
		Where("user_auth.status = ?", models.UserAuthStatusPending).Find(&audioInfo)
	if audioInfo.ID > 0 {
		data.Label = fmt.Sprintf("%s 用户提交了认证, 请及时处理～", audioInfo.Username)
		data.Source = "/mp3/tip.mp3"
		return c.SuccessJson(data)
	}

	return c.SuccessJson(data)
}
