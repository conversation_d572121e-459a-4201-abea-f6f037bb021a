package products

import (
	"fmt"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"
)

const (
	OrderStakingIndexURL  = "/products/order/staking/index"
	OrderStakingCreateURL = "/products/order/staking/create"
	OrderStakingUpdateURL = "/products/order/staking/update"
	OrderStakingDeleteURL = "/products/order/staking/delete"
)

// OrderStakingIndexParams 默认订单列表参数
type OrderStakingIndexParams struct {
	AdminID    uint                   `json:"adminID" views:"label:管理;type:select"`
	OrderSN    string                 `json:"orderSN" views:"label:订单编号;"`
	UserID     uint                   `json:"userID" views:"label:账户;type:selectSearch;mask:user#username>id"`
	ProductID  uint                   `json:"productID" views:"label:产品;type:selectSearch;mask:product#name>id"`
	Status     int8                   `json:"status" views:"label:状态;type:select"`
	UpdatedAt  *model.RangeDatePicker `json:"updatedAt" views:"label:成交时间;type:dateRange"`
	Pagination *model.Pagination      `json:"pagination" views:"-"`
}

// OrderStakingIndexRows 质押订单列表工具
type OrderStakingIndexRows struct {
	ID              uint                   `json:"id" column:"label:ID"`
	AdminID         uint                   `json:"adminID" column:"label:管理"`
	UserName        string                 `json:"userName" gorm:"-" column:"label:账户;scanField:userInfo.username"`
	ProductName     string                 `json:"productName" gorm:"-" column:"label:产品;scanField:productInfo.name"`
	OrderSN         string                 `json:"orderSN"`
	Money           float64                `json:"money" column:"label:金额"`
	Type            int8                   `json:"type"`
	UserID          uint                   `json:"userID"`
	ProductID       uint                   `json:"productID"`
	StakingType     string                 `json:"stakingType" gorm:"-" column:"label:质押类型"`
	Rate            float64                `json:"rate"  gorm:"-" column:"label:收益率(%)"`
	Amount          float64                `json:"amount"  gorm:"-" column:"label:累计收益"`
	Price           float64                `json:"price"  gorm:"-" column:"label:本期收益"`
	WhichDay        float64                `json:"whichDay"  gorm:"-" column:"label:当前周期"`
	Config          string                 `json:"config"  gorm:"-" column:"label:配置"`
	StakingStrategy models.StakingStrategy `json:"stakingStrategy" gorm:"-"`
	Status          int8                   `json:"status" column:"label:状态;type:select"`
	IncomeAt        time.Time              `json:"incomeAt" column:"label:收益时间;type:date" gorm:"-"`
	ExpiredAt       time.Time              `json:"expiredAt" column:"label:过期时间;type:date"`
	CreatedAt       time.Time              `json:"createdAt" column:"label:下单时间;type:date"`
	UserInfo        models.User            `json:"userInfo" gorm:"foreignKey:UserID"`
	ProductInfo     models.Product         `json:"productInfo" gorm:"foreignKey:ProductID"`
	Data            models.OrderData       `json:"data"`
}

// OrderStakingIndex 质押订单列表
func OrderStakingIndex(c *context.CustomCtx, bodyParams *OrderStakingIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	data := &model.IndexData{Items: make([]*OrderStakingIndexRows, 0), Count: 0}
	orderStakingList := make([]*OrderStakingIndexRows, 0)

	query := db.Equal("user_id", bodyParams.UserID).
		Equal("admin_id", bodyParams.AdminID).
		Equal("user_id", bodyParams.UserID).
		Equal("product_id", bodyParams.ProductID).
		Equal("status", bodyParams.Status).
		BetweenTime("updated_at", bodyParams.UpdatedAt, c.TimeZone).
		Model(&models.Order{})
	query.Where("admin_id IN ?", subAdminIDs).
		Where("type = ?", models.ProductOrderTypeStaking).
		Preload("UserInfo").
		Preload("ProductInfo").
		Count(&data.Count).
		Scopes(bodyParams.Pagination.Scopes()).
		Find(&orderStakingList)

	for _, rows := range orderStakingList {
		if rows.Data.StakingStrategy.Value == 0 {
			rows.StakingType = "活期"
		} else {
			rows.StakingType = "定期"
		}

		stakingStrategyType := rows.Data.StakingStrategy.Type
		for _, v := range views.FormatTimePickerOptions {
			if v.Value == rows.Data.StakingStrategy.Type {
				stakingStrategyType = v.Label
				break
			}
		}
		rows.Config = fmt.Sprintf("%d%s/%.2f期", rows.Data.StakingStrategy.Value, stakingStrategyType, rows.Data.StakingStrategy.RateCycle)
		rows.WhichDay = rows.Data.WhichDay
		rows.Rate = rows.Data.StakingStrategy.Rate
		rows.Amount = utils.FloatAccuracy(rows.Data.Amount, 2)
		rows.Price = utils.FloatAccuracy(rows.Data.Price, 2)
		rows.IncomeAt = rows.ExpiredAt
		rows.ExpiredAt = rows.CreatedAt.Add(time.Second * time.Duration(rows.Data.StakingStrategy.GetSecond()))
	}

	data.Items = orderStakingList
	return c.SuccessJson(data)
}

// OrderStakingIndexConfigure 质押订单列表配置
func OrderStakingIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(OrderStakingIndexURL).SetAutoRefresh()
	adminService := service.NewAdminUserService()
	adminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)

	statusOptions := []*views.SelectOption{
		{Label: "完成", Value: models.ProductOrderStatusCompleted},
		{Label: "取消", Value: models.ProductOrderStatusCancelled},
		{Label: "运行", Value: models.ProductOrderStatusRunning},
		{Label: "等待", Value: models.ProductOrderStatusWaiting},
		{Label: "爆仓", Value: models.ProductOrderStatusLiquidate},
		{Label: "输", Value: models.ProductOrderStatusLose},
		{Label: "赢", Value: models.ProductOrderStatusWin},
	}

	vueTable.SetCommonOptions("status", statusOptions)
	vueTable.SetCommonOptions("adminID", adminOptions)

	// 搜索
	searchForm := views.NewForm().Struct(OrderStakingIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(OrderCreateParams{}).FlattenInputs()
	createForm.SetDefaultParam("side", models.ProductOrderSideBuy)
	createForm.SetDefaultParam("mode", models.ProductOrderModeMarket)
	createForm.SetDefaultParam("type", models.ProductOrderTypeStaking)
	//createForm.SetFieldInputParam("index", "label", "策略")
	createForm.SetFieldInputParam("money", "label", "购买数量")

	createForm.ResetInputs([][]string{{"id"}, {"userID"}, {"money"}})
	createForm.SetFieldInputParam("id", "mask", fmt.Sprintf("product#name>id#admin_id = %d and category_id in (select id from category where type = %d and status = %d)", c.Claims.MerchantID, models.CategoryTypeStaking, models.CategoryStatusEnabled))
	createForm.SetFieldInputParam("userID", "mask", fmt.Sprintf("user#username>id#admin_id = %d and status = %d", c.Claims.MerchantID, models.UserStatusActive))
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("质押订单", OrderCreateURL, createForm))

	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", OrderDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(OrderStakingIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("adminID").SetSelectFormat(adminOptions)

	updateForm := views.NewForm().Struct(ProductStarkingRedemptionParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("赎回", OrderStakingUpdateURL, updateForm).SetDisplay(fmt.Sprintf("row.status == %v ", models.ProductOrderStatusRunning)))

	return vueTable
}
