package products

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// CategoryUpdateParams 更新分类参数
type CategoryUpdateParams struct {
	ID       uint   `json:"id" validate:"required" views:"label:ID;display:true"`
	Icon     string `json:"icon" views:"label:图标;type:image"`
	ParentID uint   `json:"parentId" views:"label:父级;type:select"`
	Type     int8   `json:"type" views:"label:类型;type:select"`
	Name     string `json:"name" validate:"required" views:"label:名称;type:translate"`
	Sort     int16  `json:"sort" views:"label:排序"`
	Desc     string `json:"desc" views:"label:描述;type:editor"`
}

// CategoryUpdate 更新分类
func CategoryUpdate(c *context.CustomCtx, bodyParams *CategoryUpdateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	result := db.Model(&models.Category{}).Where("admin_id IN ?", subAdminIDs).Where("id = ?", bodyParams.ID).Updates(bodyParams)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新分类失败: %s", result.Error.Error()))
	}

	return c.SuccessOk()
}
