package products

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// ProductKlineSettingParams 产品Kline设置
type ProductKlineSettingParams struct {
	ID         uint                     `json:"id" views:"label:产品ID;display:true"`
	TargetTime model.GormDateTimeParams `json:"targetTime" views:"label:目标时间【默认一小时后】;type:dateTime"`
	Increase   float64                  `json:"increase" views:"label:涨幅(%);type:number"`
}

// ProductKlineSetting 产品Kline设置
func ProductKlineSetting(c *context.CustomCtx, params *ProductKlineSettingParams) error {
	startTime := time.Now().Add(time.Minute).Truncate(time.Minute)
	targetTime := startTime.Add(time.Hour)
	if params.TargetTime != "" {
		targetTime = params.TargetTime.ToLocalTime(c.TimeZone).In(startTime.Location()).Truncate(time.Minute)
	}

	index := int(targetTime.Sub(startTime).Minutes())
	if index <= 10 {
		return c.ErrorJson("相差时间不能小于10分钟!")
	}

	db := model.NewModel()
	productInfo := &models.Product{}
	result := db.Model(&models.Product{}).
		Where("id = ?", params.ID).
		Where("type = ?", models.ProductTypeCustomize).
		Where("status = ?", models.ProductStatusEnabled).
		Find(&productInfo)
	if result.RowsAffected <= 0 {
		return c.ErrorJson("找不到产品！！！")
	}

	lastProductKline := &models.ProductKline{}
	result = db.Model(&models.ProductKline{}).
		Where("product_symbol = ?", productInfo.Symbol).
		Where("created_at = ?", startTime).
		Find(&lastProductKline)
	if result.RowsAffected <= 0 {
		return c.ErrorJson("找不到k线图！！！")
	}

	originalStartTime := startTime
	startPrice := lastProductKline.ClosePrice * (params.Increase/100 + 1)
	if startPrice <= 0 {
		return c.ErrorJson("价格不能为负数！！！")
	}
	productKlineService := service.NewProductKlineService()
	productKlines := productKlineService.GenerateKline(startPrice, startTime, targetTime, lastProductKline)

	err := db.Transaction(func(tx *gorm.DB) error {
		err := tx.Unscoped().Where("product_symbol = ?", productInfo.Symbol).Where("created_at >= ?", originalStartTime).Delete(&models.ProductKline{}).Error
		if err != nil {
			return err
		}

		err = tx.Create(&productKlines).Error
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return c.ErrorJson("插入错误！！！")
	}
	return c.SuccessOk()
}
