package products

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	CollectIndexURL  = "/products/collect/index"
	CollectUpdateURL = "/products/collect/update"
	CollectDeleteURL = "/products/collect/delete"
	CollectCreateURL = "/products/collect/create"
)

type CollectIndexParams struct {
	ProductName string                 `json:"productName" views:"label:名称"`
	AdminID     uint                   `json:"adminId" views:"label:管理;type:select"`
	UserID      uint                   `json:"userId" views:"label:账户;type:selectSearch;mask:user#username>id"`
	Data        string                 `json:"data" views:"label:数据"`
	Status      int                    `json:"status" views:"label:状态"`
	UpdatedAt   *model.RangeDatePicker `json:"updatedAt" views:"label:收盘时间;type:dateRange"`
	CreatedAt   *model.RangeDatePicker `json:"createdAt" views:"label:开盘时间;type:dateRange"`
	Pagination  *model.Pagination      `json:"pagination" views:"-"`
}

type CollectIndexRows struct {
	ID          uint      `json:"id" column:"label:ID"`
	AdminID     string    `json:"adminName" column:"label:管理账户"`
	UserName    string    `json:"username" column:"label:账户;scanField:userInfo.username" gorm:"-"`
	ProductName string    `json:"productName" column:"label:产品名"`
	Data        string    `json:"data" column:"label:数据"`
	Status      int       `json:"status" column:"label:10收藏"`
	UpdatedAt   time.Time `json:"updatedAt" column:"label:收盘时间;type:date"`
	CreatedAt   time.Time `json:"createdAt" column:"label:开盘时间;type:date"`
}

func CollectIndex(c *context.CustomCtx, bodyParams *CollectIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIds, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	data := &model.IndexData{Items: make([]*CollectIndexRows, 0), Count: 0}
	db := model.NewModel()
	query := db.Equal("user_id", bodyParams.UserID).
		Equal("admin_id", bodyParams.AdminID).
		Like("name", bodyParams.ProductName).
		BetweenTime("updated_at", bodyParams.UpdatedAt, c.TimeZone).
		BetweenTime("created_at", bodyParams.CreatedAt, c.TimeZone).
		Model(&models.Collect{})

	query.Where("admin_id IN ?", subAdminIds).
		Count(&data.Count).
		Scopes(bodyParams.Pagination.Scopes()).
		Find(&data.Items)

	return c.SuccessJson(data)
}

// CollectIndexConfigure 收藏资产列表配置
func CollectIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	statusOptions := []*views.SelectOption{
		{Label: "完成", Value: models.CollectStatusEnabled},
		{Label: "删除", Value: models.CollectStatusDisable},
	}

	adminService := service.NewAdminUserService()
	adminsOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)

	vueTable := views.NewTable(CollectIndexURL)
	vueTable.SetCommonOptions("adminId", adminsOptions)

	searchForm := views.NewForm().Struct(CollectIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	createForm := views.NewForm().Struct(CollectCreateParams{}).FlattenInputs()
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("创建收藏", CollectCreateURL, createForm))

	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", CollectDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(CollectIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)

	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(CollectUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", CollectUpdateURL, updateForm))

	return vueTable
}
