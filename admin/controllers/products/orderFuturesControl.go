package products

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// OrderFuturesControlParams 产品订单控状态参数
type OrderFuturesControlParams struct {
	Id        uint `json:"id"  views:"label:ID;display:true"`                             //  主键
	LoseOrWin int  `json:"loseOrWin" validate:"required"  views:"label:输赢状态;type:select"` //  状态 1输 2赢
}

// OrderFuturesControl 期权控制
func OrderFuturesControl(ctx *context.CustomCtx, params *OrderFuturesControlParams) error {
	db := model.NewModel()

	orderInfo := &models.Order{}
	if result := db.Where("id = ?", params.Id).
		Where("type = ?", models.ProductOrderTypeFutures).
		Where("status = ?", models.ProductOrderStatusRunning).
		Find(orderInfo); result.RowsAffected == 0 {
		return ctx.ErrorJson("找不到对应订单")
	}
	productInfo := &models.Product{}
	if result := db.Where("id = ?", orderInfo.ProductID).
		Where("status = ?", models.ProductStatusEnabled).
		Find(productInfo); result.RowsAffected == 0 {
		return ctx.ErrorJson("找不到对应产品")
	}

	orderData := orderInfo.Data

	orderData.SellPrice = service.NewProductService().LoseOrWin(orderData.Price, productInfo.Data.SymbolAssetsDecimal, params.LoseOrWin, orderInfo.Side)
	if result := db.Updates(&models.Order{BaseModel: model.BaseModel{ID: params.Id}, Data: orderData}); result.Error != nil {
		return ctx.ErrorJson("更新失败")
	}

	return ctx.SuccessOk()
}
