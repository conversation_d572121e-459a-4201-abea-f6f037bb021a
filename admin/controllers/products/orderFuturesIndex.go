package products

import (
	"fmt"
	"strconv"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"
)

const (
	OrderFuturesIndexURL   = "/products/order/futures/index"
	OrderFuturesControlURL = "/products/order/futures/control"
)

// OrderFuturesIndexParams 合约订单列表参数
type OrderFuturesIndexParams struct {
	AdminID    uint                   `json:"adminID" views:"label:管理;type:select"`
	UserID     uint                   `json:"userID" views:"label:账户;type:selectSearch;mask:user#username>id"`
	ProductID  uint                   `json:"productID" views:"label:产品;type:selectSearch;mask:product#name>id"`
	Side       int8                   `json:"side" views:"label:方向;type:select"`
	WinOrLose  int8                   `json:"winOrLose" views:"label:输赢;type:select"`
	Status     int8                   `json:"status" views:"label:状态;type:select"`
	UpdatedAt  *model.RangeDatePicker `json:"expiredAt" views:"label:成交时间;type:dateRange"`
	Pagination *model.Pagination      `json:"pagination" views:"-"`
}

// OrderFuturesIndexRows 合约订单列表
type OrderFuturesIndexRows struct {
	ID           uint                  `json:"id" column:"label:ID"`
	AdminID      uint                  `json:"adminID" column:"label:管理"`
	UserName     string                `json:"userName" gorm:"-" column:"label:账户;scanField:userInfo.username"`
	Images       model.GormStringSlice `json:"images" gorm:"-" column:"label:图片;type:icon"`
	ProductName  string                `json:"productName" gorm:"-" column:"label:产品;scanField:productInfo.name"`
	Side         int                   `json:"side"  column:"label:方向"`
	OrderSN      string                `json:"orderSN"`
	Money        float64               `json:"money" column:"label:购买金额"`
	UserID       uint                  `json:"userID"`
	ProductID    uint                  `json:"productID"`
	RateOfReturn string                `json:"rateOfReturn"  gorm:"-" column:"label:回报率"`
	Price        float64               `json:"price"  gorm:"-" column:"label:买入单价;scanField:data.price"`
	SellPrice    float64               `json:"sellPrice"  gorm:"-" column:"label:卖出单价;scanField:data.sellPrice"`
	Amount       float64               `json:"amount"  gorm:"-" column:"label:结算金额"`
	WinOrLose    int                   `json:"winOrLose" gorm:"-" column:"label:输赢"`
	Status       int8                  `json:"status" column:"label:状态;type:select"`
	Type         int8                  `json:"type"`
	CreatedAt    time.Time             `json:"createdAt" column:"label:下单时间;type:date"`
	UpdatedAt    time.Time             `json:"updatedAt" column:"label:成交时间;type:date"`
	UserInfo     models.User           `json:"userInfo" gorm:"foreignKey:UserID"`
	ProductInfo  models.Product        `json:"productInfo" gorm:"foreignKey:ProductID"`
	Data         models.OrderData      `json:"data"`
}

// OrderFuturesIndex 期货订单列表
func OrderFuturesIndex(c *context.CustomCtx, bodyParams *OrderFuturesIndexParams) error {
	productOrderList := make([]*OrderFuturesIndexRows, 0)
	data := &model.IndexData{}

	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	query := db.Equal("admin_id", bodyParams.AdminID).
		Equal("user_id", bodyParams.UserID).
		Equal("product_id", bodyParams.ProductID).
		Equal("type", models.ProductOrderTypeFutures).
		Equal("status", bodyParams.Status).
		Equal("side", bodyParams.Side).
		BetweenTime("updated_at", bodyParams.UpdatedAt, c.TimeZone).
		Model(&models.Order{})

	// 添加输赢筛选条件
	if bodyParams.WinOrLose > 0 {
		query = query.Where("status = ? AND CASE "+
			"WHEN money > CAST(JSON_UNQUOTE(JSON_EXTRACT(data, '$.amount')) AS DECIMAL(20,8)) THEN ? "+
			"WHEN money < CAST(JSON_UNQUOTE(JSON_EXTRACT(data, '$.amount')) AS DECIMAL(20,8)) THEN ? "+
			"END = ?",
			models.ProductOrderStatusCompleted,
			models.ProductOrderStatusLose,
			models.ProductOrderStatusWin,
			bodyParams.WinOrLose)
	}

	query.Where("admin_id IN ?", subAdminIDs).
		Preload("UserInfo").
		Preload("ProductInfo").
		Count(&data.Count).
		Scopes(bodyParams.Pagination.Scopes()).
		Find(&productOrderList)

	data.Items = productOrderList
	for _, v := range productOrderList {
		val := v.Data
		v.RateOfReturn = fmt.Sprintf("%vs / %v%v", val.Second, utils.FloatAccuracy(val.Rate*100, 3), "%")
		if v.Status == models.ProductOrderStatusCompleted && v.Money > val.Amount {
			v.WinOrLose = models.ProductOrderStatusLose
		}
		if v.Status == models.ProductOrderStatusCompleted && v.Money < val.Amount {
			v.WinOrLose = models.ProductOrderStatusWin
		}
		v.Images = v.ProductInfo.Images

		amountStr := fmt.Sprintf("%."+strconv.Itoa(v.ProductInfo.Data.AssetsDecimal)+"f", v.Data.Amount)
		amount, err := strconv.ParseFloat(amountStr, 64)
		if err == nil {
			v.Amount = amount
		} else {
			v.Amount = v.Data.Amount
		}
	}

	return c.SuccessJson(data)
}

// OrderFuturesIndexConfigure 期货订单列表
func OrderFuturesIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(OrderFuturesIndexURL).SetAutoRefresh()
	adminService := service.NewAdminUserService()
	adminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)
	productCategoryService := service.NewProductCategoryService()
	productCategoryFuturesRateOptions := productCategoryService.ProductCategoryFuturesRateOptions(c.Rds, c.Claims.MerchantID)
	statusOptions := []*views.SelectOption{
		{Label: "完成", Value: models.ProductOrderStatusCompleted},
		{Label: "运行", Value: models.ProductOrderStatusRunning},
	}
	sideOptions := []*views.SelectOption{
		{Label: "看涨", Value: models.ProductOrderSideBuy},
		{Label: "看跌", Value: models.ProductOrderSideSell},
	}
	controlOptions := []*views.SelectOption{
		{Label: "输", Value: models.ProductOrderStatusLose},
		{Label: "赢", Value: models.ProductOrderStatusWin},
	}
	vueTable.SetCommonOptions("status", statusOptions).
		SetCommonOptions("side", sideOptions).
		SetCommonOptions("winOrLose", controlOptions).
		SetCommonOptions("adminID", adminOptions)

	// 搜索
	searchForm := views.NewForm().Struct(OrderFuturesIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(OrderCreateParams{}).FlattenInputs()
	createForm.SetDefaultParam("side", models.ProductOrderSideBuy)
	createForm.SetDefaultParam("mode", models.ProductOrderModeMarket)
	createForm.SetDefaultParam("type", models.ProductOrderTypeFutures)
	createForm.SetOptionsParam("index", productCategoryFuturesRateOptions)
	createForm.SetFieldInputParam("index", "type", "select")
	createForm.SetFieldInputParam("index", "label", "策略")
	if len(productCategoryFuturesRateOptions) > 0 {
		createForm.SetDefaultParam("index", productCategoryFuturesRateOptions[0].Value)
	}
	createForm.ResetInputs([][]string{{"id"}, {"userID"}, {"side"}, {"index"}, {"money"}})
	createForm.SetFieldInputParam("id", "mask", fmt.Sprintf("product#name>id#admin_id = %d and category_id in (select id from category where type = %d and status = %d)", c.Claims.MerchantID, models.CategoryTypeFutures, models.CategoryStatusEnabled))
	createForm.SetFieldInputParam("userID", "mask", fmt.Sprintf("user#username>id#admin_id = %d and status = %d", c.Claims.MerchantID, models.UserStatusActive))
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("期货订单", OrderCreateURL, createForm))

	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", OrderDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(OrderFuturesIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("side").SetSelectFormat(sideOptions)
	vueTable.GetFieldColumn("adminID").SetSelectFormat(adminOptions)
	vueTable.GetFieldColumn("winOrLose").SetSelectFormat(controlOptions)

	// 数据表格 - 操作按钮
	revokeForm := views.NewForm().Struct(OrderRevokeParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("撤单", OrderRevokeURL, revokeForm).SetDisplay(fmt.Sprintf("row.status == %v", models.ProductOrderStatusWaiting)))

	controlForm := views.NewForm().Struct(OrderFuturesControlParams{}).FlattenInputs()
	controlForm.SetOptionsParam("loseOrWin", controlOptions).SetDefaultParam("loseOrWin", models.ProductOrderStatusLose)
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("输赢控制", OrderFuturesControlURL, controlForm).SetButtonColor(views.ColorWarning).SetDisplay(fmt.Sprintf("row.status == %v && row.type == %v", models.ProductOrderStatusRunning, models.ProductOrderTypeFutures)))
	return vueTable
}
