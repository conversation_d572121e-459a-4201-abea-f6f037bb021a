package products

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// ProductUpdateParams 更新产品参数
type ProductUpdateParams struct {
	ID                  uint                  `json:"id" views:"label:ID;display:true"`
	Images              model.GormStringSlice `json:"images" views:"label:图片组;type:images"`
	AssetsID            uint                  `json:"assetsId" views:"label:资产;type:select"`
	SymbolAssetsDecimal int                   `json:"symbolAssetsDecimal" views:"label:标识资产精度;type:number;scanField:data.symbolAssetsDecimal" gorm:"-"`
	SymbolAssetsID      uint                  `json:"symbolAssetsId" views:"label:标识资产;type:select" gorm:"-"`
	AssetsDecimal       int                   `json:"assetsDecimal" views:"label:资产精度;type:number;scanField:data.assetsDecimal" gorm:"-"`
	Type                int8                  `json:"type" views:"label:类型;type:select"`
	Recommended         int8                  `json:"recommended" views:"label:推荐;type:select"`
	Name                string                `json:"name" views:"label:名称"`
	Money               float64               `json:"money" views:"label:金额;type:number"`
	MinMoney            float64               `json:"minMoney"  views:"label:最小金额;type:number;scanField:data.minMoney" gorm:"-"` // 最小金额
	Fee                 float64               `json:"fee" views:"label:手续费;type:number"`
	Sort                int8                  `json:"sort" views:"label:排序;type:number"`
	Desc                string                `json:"desc" views:"label:描述;type:editor"`
	Data                models.ProductData    `json:"data"`
}

// ProductUpdate 更新产品
func ProductUpdate(c *context.CustomCtx, bodyParams *ProductUpdateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	productInfo := models.Product{}
	db := model.NewModel()
	result := db.Model(&models.Product{}).Where("id = ?", bodyParams.ID).Where("admin_id IN ?", subAdminIDs).First(&productInfo)
	if result.Error != nil {
		return c.SuccessJson(fmt.Sprintf("查询失败: %v", result.Error))
	}

	// 如果产品是翻译的, 那么更新翻译信息
	if productInfo.IsTranslate == model.BoolTrue {
		translateService := service.NewTranslateService()
		db.Model(&models.Translate{}).Where("field = ?", productInfo.Name).Where("admin_id = ?", productInfo.AdminID).Update("value", bodyParams.Name)
		bodyParams.Name = productInfo.Name
		_ = translateService.DeleteProductCache(c.Rds, c.Claims.MerchantID, c.Lang, productInfo.Name)
	}

	if bodyParams.SymbolAssetsID != productInfo.Data.SymbolAssetsID {
		productInfo.Data.SymbolAssetsID = bodyParams.SymbolAssetsID
		bodyParams.Data = productInfo.Data
	}

	if bodyParams.AssetsID != 0 {
		productInfo.AssetsID = bodyParams.AssetsID
		bodyParams.Data = productInfo.Data
	}

	if bodyParams.SymbolAssetsDecimal != productInfo.Data.SymbolAssetsDecimal {
		productInfo.Data.SymbolAssetsDecimal = bodyParams.SymbolAssetsDecimal
		bodyParams.Data = productInfo.Data
	}

	if bodyParams.AssetsDecimal != productInfo.Data.AssetsDecimal {
		productInfo.Data.AssetsDecimal = bodyParams.AssetsDecimal
		bodyParams.Data = productInfo.Data
	}

	if bodyParams.MinMoney != productInfo.Data.MinMoney {
		productInfo.Data.MinMoney = bodyParams.MinMoney
		bodyParams.Data = productInfo.Data
	}

	result = db.Model(&models.Product{}).Where("id = ?", productInfo.ID).Updates(bodyParams)
	if result.Error != nil {
		return c.SuccessJson(fmt.Sprintf("更新失败: %v", result.Error))
	}

	return c.SuccessOk()
}
