package products

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/inits/instance/markets"
	"zfeng/models"
	"zfeng/module/exchange/interfaces"
	"zfeng/module/socket"
)

// ProductCreateParams 新增产品参数
type ProductCreateParams struct {
	Images         model.GormStringSlice `json:"images" validate:"required" views:"label:图片组;type:images"`
	CategoryID     uint                  `json:"categoryId" validate:"required" views:"label:分类;type:select"`
	SymbolAssetsID uint                  `json:"symbolAssetsId" validate:"required" views:"label:标识资产;type:select"`
	Type           int8                  `json:"type" validate:"required" views:"label:类型;type:select"`
	Money          float64               `json:"money" validate:"required" views:"label:金额;type:number"`
}

// ProductCreate 新增产品
func ProductCreate(c *context.CustomCtx, params *ProductCreateParams) error {
	db := model.NewModel()
	walletSymbolAssetsInfo := models.WalletAssets{}
	result := db.Model(&models.WalletAssets{}).
		Where("id = ?", params.SymbolAssetsID).
		Find(&walletSymbolAssetsInfo)
	if result.RowsAffected <= 0 {
		return c.ErrorJson("请先添加标识资产！！！")
	}

	categoryInfo := models.Category{}
	result = db.Model(&categoryInfo).
		Where("id = ?", params.CategoryID).
		Where("parent_id != ?", 0).
		Find(&categoryInfo)
	if result.RowsAffected <= 0 {
		return c.ErrorJson("请先选择子分类！！！")
	}

	switch {
	case params.Type == models.ProductTypeCustomize && categoryInfo.Type == models.CategoryTypeStaking:
		return c.ErrorJson("质押分类不能添加自定货币类型产品！！！")
	case params.Type == models.ProductTypeStaking && categoryInfo.Type == models.CategoryTypeSpot:
		return c.ErrorJson("加密货币分类不能添加质押类型产品！！！")
	}

	productInfo := models.Product{
		AdminID:    c.Claims.AdminID,
		CategoryID: params.CategoryID,
		Type:       params.Type,
		Money:      params.Money,
		Images:     params.Images,
		Data: models.ProductData{
			SymbolAssetsID:      walletSymbolAssetsInfo.ID,
			SymbolAssetsDecimal: 6,
			AssetsDecimal:       2,
		},
	}

	var assetsId uint = 0
	symbol := walletSymbolAssetsInfo.Currency
	productName := walletSymbolAssetsInfo.Currency
	switch categoryInfo.Type {

	case models.CategoryTypeSpot:
		walletAssetsInfo := models.WalletAssets{}
		result = db.Model(&models.WalletAssets{}).
			Where("currency = ?", "USDT").
			Where("admin_id = ?", c.Claims.AdminID).
			Find(&walletAssetsInfo)
		if result.Error != nil {
			return c.ErrorJson(result.Error.Error())
		}
		assetsId = walletAssetsInfo.ID
		symbol += "-" + walletAssetsInfo.Currency
		productName += "/" + walletAssetsInfo.Currency
		productInfo.Data.Bars = models.BarsCustomize
	case models.CategoryTypeStaking:
		productInfo.Data.StakingStrategy = models.StakingStrategyData
	}
	productInfo.AssetsID = assetsId
	productInfo.Name = productName
	productInfo.Symbol = symbol

	productTmp := &models.Product{}
	result = db.Model(&models.Product{}).
		Where("symbol = ?", symbol).
		Where("category_id = ?", params.CategoryID).
		Where("admin_id = ?", c.Claims.AdminID).
		Where("status = ?", models.ProductStatusEnabled).
		Find(&productTmp)
	if result.RowsAffected > 0 {
		return c.ErrorJson("产品已存在！！！")
	}

	err := db.Create(&productInfo).Error

	if err != nil {
		return c.ErrorJson(fmt.Sprintf("新增失败 %s", err.Error()))
	}

	if categoryInfo.Type == models.CategoryTypeSpot {
		markets.ProductSocket.AddSubscribeChannel(&socket.RedisSubscribeChannel{
			Channel: interfaces.SubscribeChannelTickers, Args: []string{productInfo.Symbol}, ConsumerFunc: nil})
	}

	return c.SuccessOk()
}
