package products

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// OrderUpdateParams 更新默认订单参数
type OrderUpdateParams struct {
	ID        uint    `json:"id" validate:"required" views:"label:ID;display:true"`
	Price     float64 `json:"price" views:"label:单价;type:number"`
	Type      int8    `json:"type" views:"label:类型;display:true"`
	Money     float64 `json:"money" views:"label:金额;type:number"`
	TakePrice float64 `json:"takePrice"  views:"label:止盈价;type:number"`
	StopPrice float64 `json:"stopPrice"  views:"label:止损价;type:number"`
	Status    int8    `json:"status" views:"label:状态;display:true"`
}

// OrderUpdate 更新默认订单
func OrderUpdate(c *context.CustomCtx, bodyParams *OrderUpdateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	orderInfo := &models.Order{}
	db := model.NewModel()
	result := db.Model(&models.Order{}).
		Where("admin_id IN ?", subAdminIDs).
		Where("id = ?", bodyParams.ID).
		Where("status = ?", bodyParams.Status).
		Where("type = ?", bodyParams.Type).
		Find(&orderInfo)
	if result.Error != nil {
		return c.ErrorJson("找不到订单")
	}

	tmpOrderInfo := &models.Order{BaseModel: model.BaseModel{ID: orderInfo.ID}, Data: orderInfo.Data}
	if bodyParams.Price != orderInfo.Data.Price {
		tmpOrderInfo.Data.Price = bodyParams.Price
	}

	if bodyParams.Money != orderInfo.Money {
		tmpOrderInfo.Money = bodyParams.Money
	}

	if bodyParams.TakePrice != orderInfo.Data.TakePrice {
		tmpOrderInfo.Data.TakePrice = bodyParams.TakePrice
	}

	if bodyParams.StopPrice != orderInfo.Data.StopPrice {
		tmpOrderInfo.Data.StopPrice = bodyParams.StopPrice
	}

	result = db.Model(&models.Order{}).Where("id = ?", bodyParams.ID).Updates(tmpOrderInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新失败: %s", result.Error.Error()))
	}

	return c.SuccessOk()
}
