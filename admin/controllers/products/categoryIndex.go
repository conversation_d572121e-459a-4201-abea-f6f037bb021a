package products

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

const (
	CategoryIndexURL  = "/products/category/index"
	CategoryCreateURL = "/products/category/create"
	CategoryUpdateURL = "/products/category/update"
	CategoryDeleteURL = "/products/category/delete"
)

// CategoryIndexParams 分类列表
type CategoryIndexParams struct {
	Name       string            `json:"name" views:"label:名称"`
	AdminID    uint              `json:"adminId" views:"label:管理;type:select"`
	ParentID   uint              `json:"parentId" views:"label:父级;type:select"`
	Type       int8              `json:"type" views:"label:类型;type:select"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// CategoryIndexRows 分类列表
type CategoryIndexRows struct {
	ID            uint                    `json:"id" column:"label:ID"`
	Icon          string                  `json:"icon" column:"label:图标"`
	AdminID       uint                    `json:"adminId" column:"label:管理"`
	ParentID      uint                    `json:"parentId" column:"label:父级"`
	Name          string                  `json:"name"`
	TranslateName string                  `json:"translateName" column:"label:名称"`
	Symbol        string                  `json:"symbol" column:"label:标识"`
	Type          int8                    `json:"type" column:"label:类型"`
	Sort          int16                   `json:"sort" column:"label:排序"`
	Status        int8                    `json:"status" column:"label:状态"`
	SettingList   []*models.ExtendSetting `json:"settingsList" gorm:"foreignKey:SourceID"`
	SettingValues map[string]interface{}  `json:"settingValues" gorm:"-"`
}

// CategoryIndex 分类列表
func CategoryIndex(c *context.CustomCtx, bodyParams *CategoryIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	data := &model.IndexData{Items: make([]*CategoryIndexRows, 0), Count: 0}

	db := model.NewModel()
	query := db.Equal("name", bodyParams.Name).
		Equal("admin_id", bodyParams.AdminID).
		Equal("parent_id", bodyParams.ParentID).
		Equal("type", bodyParams.Type).
		Equal("status", bodyParams.Status).
		Model(&models.Category{})

	query.Where("admin_id IN ?", subAdminIDs).Count(&data.Count)
	query.Preload("SettingList", func(db *gorm.DB) *gorm.DB {
		return db.Where("group_id = ?", models.ExtendSettingGroupCategory)
	})
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	translateService := service.NewTranslateService()
	for _, item := range data.Items.([]*CategoryIndexRows) {
		item.TranslateName = translateService.GetTranslatesByFieldsWithCache(c.Rds, item.AdminID, "zh-CN", item.Name)

		// 扩展设置
		item.SettingValues = make(map[string]interface{})
		for _, setting := range item.SettingList {
			item.SettingValues[setting.Field] = views.InputValueToInterface(setting.Type, setting.Value)
		}
	}

	return c.SuccessJson(data)
}

// CategoryIndexConfigure 分类列表配置
func CategoryIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(CategoryIndexURL)
	adminService := service.NewAdminUserService()
	adminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	categoryService := service.NewProductCategoryService()
	categoryOptions := categoryService.GetCategoryOptions(c.Rds, subAdminIDs)
	statusOptions := []*views.SelectOption{
		{Label: "启用", Value: models.CategoryStatusEnabled},
		{Label: "禁用", Value: models.CategoryStatusDisabled},
	}
	typeOptions := []*views.SelectOption{
		{Label: "数字货币", Value: models.CategoryTypeDefault},
		{Label: "现货", Value: models.CategoryTypeSpot},
		{Label: "合约", Value: models.CategoryTypeForex},
		{Label: "期货", Value: models.CategoryTypeFutures},
		{Label: "质押", Value: models.CategoryTypeStaking},
	}

	vueTable.SetCommonOptions("status", statusOptions).
		SetCommonOptions("type", typeOptions).
		SetCommonOptions("parentId", categoryOptions).
		SetCommonOptions("adminId", adminOptions)

	// 搜索
	searchForm := views.NewForm().Struct(CategoryIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(CategoryCreateParams{}).FlattenInputs()
	createForm.SetFieldInputParam("type", "default", typeOptions[0].Value)
	createForm.SetFieldInputParam("parentId", "default", categoryOptions[0].Value)
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增分类", CategoryCreateURL, createForm))

	if c.Claims.AdminID == models.SuperAdminID {
		deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
		vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", CategoryDeleteURL, deleteForm), "ids", "id")
	}

	// 数据表格数据
	vueTable.StructColumns(CategoryIndexRows{})
	vueTable.GetFieldColumn("icon").SetAvatarFormat("icon")
	vueTable.GetFieldColumn("parentId").SetSelectFormat(categoryOptions)
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("type").SetSelectFormat(typeOptions)
	vueTable.GetFieldColumn("adminId").SetSelectFormat(adminOptions)

	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(CategoryUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", CategoryUpdateURL, updateForm).SetSize(views.DialogSizeMedium))

	return vueTable
}
