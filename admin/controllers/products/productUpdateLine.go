package products

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

type ProductUpdateLineParams struct {
	ID   uint        `json:"id" views:"label:产品ID;display:true"`
	Data [][]float64 `json:"data" views:"label:数据;scanField:lineData;type:line"`
}

func ProductUpdateLine(c *context.CustomCtx, bodyParams *ProductUpdateLineParams) error {
	length := len(bodyParams.Data)
	if length > 0 {
		startTime := time.UnixMilli(int64(bodyParams.Data[0][0]))
		endTime := time.UnixMilli(int64(bodyParams.Data[length-1][0]))

		db := model.NewModel()
		productInfo := &models.Product{}
		result := db.Model(&models.Product{}).
			Where("id = ?", bodyParams.ID).
			Where("type = ?", models.ProductTypeCustomize).
			Where("status = ?", models.ProductStatusEnabled).
			Find(&productInfo)
		if result.RowsAffected <= 0 {
			return c.ErrorJson("找不到产品！！！")
		}

		productKlineList := make([]*models.ProductKline, 0)
		result = db.Model(&models.ProductKline{}).
			Where("product_symbol = ?", productInfo.Symbol).
			Where("created_at between ? and ?", startTime.Add(-time.Minute), endTime.Add(time.Minute)).
			Find(&productKlineList)
		if result.Error != nil || result.RowsAffected <= 0 {
			return c.ErrorJson("更新失败！！！")
		}

		productCreatedAtMap := make(map[int64]*models.ProductKline)
		for _, v := range productKlineList {
			productCreatedAtMap[v.CreatedAt.UnixMilli()] = v
		}

		updateProductKlineList := make([]*models.ProductKline, 0)
		for _, v := range bodyParams.Data {
			createdAt := int64(v[0])
			newEntityHighPrice := v[1]
			val, ok := productCreatedAtMap[createdAt]
			oldEntityHighPrice := val.ClosePrice
			if oldEntityHighPrice < val.OpenPrice {
				oldEntityHighPrice = val.OpenPrice
			}
			if ok && oldEntityHighPrice != newEntityHighPrice {
				if newEntityHighPrice <= 0 {
					return c.ErrorJson("修改的价格不能为负数！！！")
				}
				priceDifference := newEntityHighPrice - oldEntityHighPrice
				// 上涨
				if val.OpenPrice < val.ClosePrice {
					newKlineInfo := &models.ProductKline{
						BaseModel:  model.BaseModel{ID: val.ID},
						ClosePrice: val.ClosePrice + priceDifference,
						HighPrice:  val.HighPrice + priceDifference,
						OpenPrice:  val.OpenPrice,
						LowsPrice:  val.LowsPrice,
					}
					// 前一个
					afterKline, okValKline := productCreatedAtMap[val.CreatedAt.Add(time.Minute).UnixMilli()]
					if okValKline {
						newAfterKline := &models.ProductKline{}
						newAfterKline.ID = afterKline.ID
						// 收盘价大于前一个收盘价
						newAfterKline.OpenPrice = newKlineInfo.ClosePrice
						// 前一个上涨
						if afterKline.OpenPrice < afterKline.ClosePrice {
							// 当前收盘价大于下一个的收盘价
							if newKlineInfo.ClosePrice > afterKline.ClosePrice {
								newAfterKline.HighPrice = newAfterKline.OpenPrice + (afterKline.HighPrice - afterKline.ClosePrice)
								newAfterKline.LowsPrice = afterKline.ClosePrice - (afterKline.OpenPrice - afterKline.LowsPrice)
							} else {
								newAfterKline.LowsPrice = newAfterKline.OpenPrice - (afterKline.OpenPrice - afterKline.LowsPrice)
								if newKlineInfo.OpenPrice > newKlineInfo.ClosePrice {
									// 当开盘价大于收盘价时保留原来的最高价和最低价差
									newKlineInfo.LowsPrice = newKlineInfo.ClosePrice - (val.OpenPrice - val.LowsPrice)
									newKlineInfo.HighPrice = newKlineInfo.OpenPrice + (val.HighPrice - val.ClosePrice)
								}
							}
						} else {
							if newKlineInfo.ClosePrice < afterKline.ClosePrice {
								newAfterKline.HighPrice = afterKline.ClosePrice + (afterKline.HighPrice - afterKline.OpenPrice)
								newAfterKline.LowsPrice = newAfterKline.OpenPrice - (afterKline.ClosePrice - afterKline.LowsPrice)
								if newKlineInfo.OpenPrice > newKlineInfo.ClosePrice {
									// 当开盘价大于收盘价时保留原来的最高价和最低价差
									newKlineInfo.LowsPrice = newKlineInfo.ClosePrice - (val.OpenPrice - val.LowsPrice)
									newKlineInfo.HighPrice = newKlineInfo.OpenPrice + (val.HighPrice - val.ClosePrice)
								}
							} else {
								newAfterKline.HighPrice = newAfterKline.OpenPrice + (afterKline.HighPrice - afterKline.OpenPrice)
							}
						}
						updateProductKlineList = append(updateProductKlineList, newAfterKline)
					}
					updateProductKlineList = append(updateProductKlineList, newKlineInfo)
				} else {
					newKlineInfo := &models.ProductKline{BaseModel: model.BaseModel{ID: val.ID},
						ClosePrice: val.ClosePrice,
						LowsPrice:  val.LowsPrice,
						OpenPrice:  val.OpenPrice + priceDifference,
						HighPrice:  val.HighPrice + priceDifference,
					}
					beforeKline, okValKline := productCreatedAtMap[val.CreatedAt.Add(-time.Minute).UnixMilli()]
					if okValKline {
						newBeforeKline := &models.ProductKline{}
						newBeforeKline.ID = beforeKline.ID
						newBeforeKline.ClosePrice = newKlineInfo.OpenPrice
						if beforeKline.OpenPrice > beforeKline.ClosePrice {
							if newKlineInfo.OpenPrice > beforeKline.OpenPrice {
								newBeforeKline.HighPrice = newBeforeKline.ClosePrice + (beforeKline.HighPrice - beforeKline.OpenPrice)
								newBeforeKline.LowsPrice = beforeKline.OpenPrice - (beforeKline.ClosePrice - beforeKline.LowsPrice)
							} else {
								newBeforeKline.LowsPrice = newBeforeKline.ClosePrice - (beforeKline.ClosePrice - beforeKline.LowsPrice)
								if newKlineInfo.OpenPrice < newKlineInfo.ClosePrice {
									// 当开盘价大于收盘价时保留原来的最高价和最低价差
									newKlineInfo.LowsPrice = newKlineInfo.OpenPrice - (val.ClosePrice - val.LowsPrice)
									newKlineInfo.HighPrice = newKlineInfo.ClosePrice + (val.HighPrice - val.OpenPrice)
								}
							}
						} else {
							// 如果当前的开盘价大于上一个的开盘价，则最大值和最小值需要反转
							if newKlineInfo.OpenPrice < beforeKline.OpenPrice {
								newBeforeKline.HighPrice = beforeKline.OpenPrice + (beforeKline.HighPrice - beforeKline.ClosePrice)
								newBeforeKline.LowsPrice = newBeforeKline.ClosePrice - (beforeKline.OpenPrice - beforeKline.LowsPrice)
								// 如果本身开盘价小于收盘价则替换最高最低价
								if newKlineInfo.OpenPrice < newKlineInfo.ClosePrice {
									newKlineInfo.HighPrice = newKlineInfo.ClosePrice + (val.HighPrice - val.OpenPrice)
									newKlineInfo.LowsPrice = newKlineInfo.OpenPrice - (val.OpenPrice - val.LowsPrice)
								}
							} else {
								newBeforeKline.HighPrice = newBeforeKline.ClosePrice + (beforeKline.HighPrice - beforeKline.ClosePrice)
							}
						}
						updateProductKlineList = append(updateProductKlineList, newBeforeKline)
					}
					updateProductKlineList = append(updateProductKlineList, newKlineInfo)
				}
			}
		}

		for _, v := range updateProductKlineList {
			db.Model(&models.ProductKline{}).Where("id = ?", v.ID).Updates(&v)
		}
	}
	return c.SuccessJson("ok")
}
