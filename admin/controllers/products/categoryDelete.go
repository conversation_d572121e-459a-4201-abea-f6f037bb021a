package products

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// CategoryDelete 删除分类
func CategoryDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	translateService := service.NewTranslateService()
	err := db.Transaction(func(tx *gorm.DB) error {
		for _, v := range bodyParams.Ids {
			categoryInfo := &models.Category{}
			result := tx.Where("id = ?", v).Where("admin_id IN ?", subAdminIDs).First(categoryInfo)
			if result.Error != nil {
				return fmt.Errorf("获取分类失败: %s", result.Error.Error())
			}

			result = tx.Where("id = ?", categoryInfo.ID).Delete(&models.Category{})
			if result.Error != nil {
				return fmt.Errorf("删除分类失败: %s", result.Error.Error())
			}

			result = tx.Unscoped().Where("field = ?", categoryInfo.Name).Delete(&models.Translate{})
			if result.Error != nil {
				return fmt.Errorf("删除分类翻译失败: %s", result.Error.Error())
			}

			// 删除所有翻译缓存
			translateService.DeleteAllTranslateCache(c.Rds, categoryInfo.AdminID)
		}
		return nil
	})
	if err != nil {
		return c.ErrorJson(fmt.Sprintf("删除分类失败: %v", err.Error()))
	}

	return c.SuccessOk()
}
