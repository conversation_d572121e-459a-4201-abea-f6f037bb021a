package products

import (
	"zfeng/core/context"
	"zfeng/models"
	"zfeng/service"
)

// OrderCreateParams 新增默认订单
type OrderCreateParams struct {
	ID     uint    `json:"id" validate:"required" views:"label:产品;type:selectSearch;mask:product#name>id"` //	产品ID
	UserID uint    `json:"userID" validate:"required,gte=0" views:"label:账户;type:selectSearch;mask:user#username>id"`
	Mode   int8    `json:"mode"`                                                                   //	合约/现货=模式 1限价 2市价
	Side   int8    `json:"side" validate:"required,oneof=1 2 3" views:"label:方向;type:select"`      //	期货/合约/现货=方向 1买 2卖 3双赢
	Type   int8    `json:"type" validate:"required,oneof=1 2 3 4" views:"label:type;display:true"` //	类型 1币币 2合约 3期货 4质押
	Index  int     `json:"index" views:"label:倍数;type:number"`                                     //	合约=倍数 期货=索引 质押=索引
	Price  float64 `json:"price" views:"label:单价（不填市价交易）;type:number"`                             //	合约=限价价格 期权=限价价格
	Money  float64 `json:"money" validate:"required,gte=0"  views:"label:金额;type:number"`          // 合约=购买金额 期货=购买金额 质押=购买金额
}

// OrderCreate 新增默认订单
func OrderCreate(ctx *context.CustomCtx, params *OrderCreateParams) error {
	switch params.Type {
	case models.ProductOrderTypeSpot:
		params.Mode = models.ProductOrderModeMarket
		if params.Price != 0 {
			params.Mode = models.ProductOrderModeLimit
		}
	case models.ProductOrderTypeContract:
		params.Mode = models.ProductOrderModeMarket
		if params.Price != 0 {
			params.Mode = models.ProductOrderModeLimit
		}
		if params.Index == 0 {
			return ctx.ErrorJson("倍数不能为0")
		}
		//case models.ProductOrderTypeStaking:
		//	params.Index = 1
	}

	err := service.NewProductOrderService().ProductOrderCreate(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, &service.OrderCreateParams{
		ID:     params.ID,
		UserID: params.UserID,
		Side:   params.Side,
		Type:   params.Type,
		Mode:   params.Mode,
		Index:  params.Index,
		Price:  params.Price,
		Money:  params.Money,
	})
	if err != nil {
		return ctx.ErrorJson(err.Error())
	}
	return ctx.SuccessOk()
}
