package products

import (
	"fmt"
	"strconv"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

const (
	ProductIndexURL          = "/products/product/index"
	ProductCreateURL         = "/products/product/create"
	ProductUpdateURL         = "/products/product/update"
	ProductDeleteURL         = "/products/product/delete"
	ProductUpdateLineURL     = "/products/product/update/line"
	ProductSettingKLineURL   = "/products/product/setting/kline"
	ProductStakingSettingURL = "/products/product/update/staking"
)

// ProductIndexParams 产品列表参数
type ProductIndexParams struct {
	Name       string                 `json:"name" views:"label:名称"`
	AdminID    uint                   `json:"adminId" views:"label:管理;type:select"`
	CategoryID uint                   `json:"categoryId" views:"label:分类;type:select"`
	AssetsID   uint                   `json:"assetsId" views:"label:资产;type:select"`
	Type       int8                   `json:"type" views:"label:类型;type:select"`
	Status     int8                   `json:"status" views:"label:状态;type:select"`
	CreatedAt  *model.RangeDatePicker `json:"createdAt" views:"label:时间;type:dateRange"`
	Pagination *model.Pagination      `json:"pagination" views:"-"`
}

// ProductIndexRows 产品列表行
type ProductIndexRows struct {
	ID                  uint                      `json:"id" column:"label:ID"`
	AdminID             uint                      `json:"adminId" column:"label:管理"`
	CategoryID          uint                      `json:"categoryId" column:"label:分类"`
	AssetsID            uint                      `json:"assetsId" column:"label:资产"`
	SymbolAssetsID      uint                      `json:"symbolAssetsId" column:"label:标识资产;type:select;"`
	Images              model.GormStringSlice     `json:"images" column:"label:图片组;type:images"`
	Name                string                    `json:"name" column:"label:名称"`
	Symbol              string                    `json:"symbol"  column:"label:标识"`
	Type                int8                      `json:"type" column:"label:类型;type:select"`
	Money               float64                   `json:"money" column:"label:金额"`
	Sort                int16                     `json:"sort" column:"label:排序"`
	SymbolAssetsDecimal int                       `json:"symbolAssetsDecimal" column:"label:标识资产精度;scanField:data.symbolAssetsDecimal"`
	AssetsDecimal       int                       `json:"assetsDecimal" column:"label:资产精度;scanField:data.assetsDecimal"`
	Fee                 float64                   `json:"fee" column:"label:手续费"`
	IsTranslate         int8                      `json:"isTranslate" column:"label:翻译;type:select"`
	Status              int8                      `json:"status" column:"label:状态;type:select"`
	Recommended         int8                      `json:"recommended" column:"label:推荐;type:select"`
	Desc                string                    `json:"desc"`
	StakingStrategy     []*models.StakingStrategy `json:"stakingStrategy" gorm:"-"`
	Data                models.ProductData        `json:"data"`
	KlinesTmp           []*models.ProductKline    `json:"-" gorm:"foreignKey:ProductSymbol;references:Symbol"`
	LineData            [][]float64               `json:"lineData" gorm:"-"`
}

// ProductIndex 产品列表
func ProductIndex(c *context.CustomCtx, bodyParams *ProductIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	data := &model.IndexData{Items: make([]*ProductIndexRows, 0), Count: 0}
	db := model.NewModel()
	categoryIds := make([]int, 0)
	if bodyParams.CategoryID != 0 {
		db.Raw(`
WITH RECURSIVE category_hierarchy AS (
    SELECT c1.id, c1.name, c1.parent_id, c1.status
    FROM category as c1
    WHERE c1.parent_id = ? AND c1.status = ? and c1.admin_id IN ? and c1.deleted_at is null -- 选择激活的父级分类
    UNION ALL
    SELECT c.id, c.name, c.parent_id, c.status
    FROM category c
             INNER JOIN category_hierarchy ch ON c.parent_id = ch.id
    WHERE c.status = ?  and c.admin_id IN ?
)
SELECT id FROM category_hierarchy`,
			bodyParams.CategoryID, models.CategoryStatusEnabled, subAdminIDs, models.CategoryStatusEnabled, subAdminIDs).Scan(&categoryIds)
	}

	query := db.In("category_id", categoryIds).
		Equal("admin_id", bodyParams.AdminID).
		Equal("assets_id", bodyParams.AssetsID).
		Equal("type", bodyParams.Type).
		Equal("status", bodyParams.Status).
		Model(&models.Product{})
	nowTime := time.Now().Add(1 * time.Minute)
	query.Where("admin_id IN ?", subAdminIDs).
		Preload("KlinesTmp", func(db *gorm.DB) *gorm.DB {
			return db.Where("created_at between ? and ?", nowTime, nowTime.Add(time.Hour))
		}).
		Count(&data.Count).
		Scopes(bodyParams.Pagination.Scopes()).
		Find(&data.Items)

	translateService := service.NewTranslateService()
	adminUserService := service.NewAdminUserService()
	for _, item := range data.Items.([]*ProductIndexRows) {
		// 是否翻译, 如果翻译那么显示 name, desc 键名
		if item.IsTranslate == model.BoolTrue {
			merchantID, _ := adminUserService.GetMerchantIDWithCache(c.Rds, item.AdminID)
			item.Name = translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, item.Name)
		}
		item.SymbolAssetsID = item.Data.SymbolAssetsID
		for _, kline := range item.KlinesTmp {
			entityHighPrice := kline.ClosePrice
			if entityHighPrice < kline.OpenPrice {
				entityHighPrice = kline.OpenPrice
			}
			item.LineData = append(item.LineData, []float64{float64(kline.CreatedAt.UnixMilli()), entityHighPrice})
		}
		item.StakingStrategy = item.Data.StakingStrategy
	}
	return c.SuccessJson(data)
}

func ProductIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(ProductIndexURL)
	categoryService := service.NewProductCategoryService()
	adminService := service.NewAdminUserService()
	adminOptions := adminService.GetSubAdminOptions(c.Claims.MerchantID)
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.MerchantID)
	categoryOptions := categoryService.GetCategoryOptions(c.Rds, subAdminIDs)

	assetOptions := service.NewWalletAssetsService().GetAssetsOptions(subAdminIDs)
	assetOptions = append([]*views.SelectOption{{Label: "USD", Value: 0}}, assetOptions...)
	statusOptions := []*views.SelectOption{
		{Label: "启用", Value: models.ProductStatusEnabled},
		{Label: "禁用", Value: models.ProductStatusDisabled},
	}
	typeOptions := []*views.SelectOption{
		{Label: "自定义产品", Value: models.ProductTypeCustomize},
		{Label: "OKE产品", Value: models.ProductTypeOKEX},
		{Label: "易汇产品", Value: models.ProductTypeIeForex},
		{Label: "Trading产品", Value: models.ProductTypeTrading},
		{Label: "质押产品", Value: models.ProductTypeStaking},
	}
	recommendedOptions := []*views.SelectOption{
		{Label: "置顶", Value: model.BoolTrue},
		{Label: "正常", Value: model.BoolFalse},
	}
	translateOptions := []*views.SelectOption{
		{Label: "是", Value: model.BoolTrue},
		{Label: "否", Value: model.BoolFalse},
	}

	vueTable.SetCommonOptions("status", statusOptions).
		SetCommonOptions("type", typeOptions).
		SetCommonOptions("categoryId", categoryOptions).
		SetCommonOptions("assetsId", assetOptions).
		SetCommonOptions("symbolAssetsId", assetOptions).
		SetCommonOptions("adminId", adminOptions).
		SetCommonOptions("recommended", recommendedOptions).
		SetCommonOptions("isTranslate", translateOptions)

	// 搜索
	searchForm := views.NewForm().Struct(ProductIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	assetsEndIndex := len(assetOptions) - 1
	createForm := views.NewForm().Struct(ProductCreateParams{}).FlattenInputs()
	createForm.SetFieldInputParam("categoryId", "default", categoryOptions[1].Value)
	createForm.SetFieldInputParam("type", "default", typeOptions[0].Value)
	createForm.SetFieldInputParam("symbolAssetsId", "default", assetOptions[assetsEndIndex].Value)
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增产品", ProductCreateURL, createForm))

	if c.Claims.AdminID == models.SuperAdminID {
		deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
		vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", ProductDeleteURL, deleteForm), "ids", "id")
	}

	// 数据表格数据
	vueTable.StructColumns(ProductIndexRows{})
	vueTable.GetFieldColumn("categoryId").SetSelectFormat(categoryOptions)
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("recommended").SetSelectFormat(recommendedOptions)
	vueTable.GetFieldColumn("type").SetSelectFormat(typeOptions)
	vueTable.GetFieldColumn("isTranslate").SetSelectFormat(translateOptions)
	vueTable.GetFieldColumn("adminId").SetSelectFormat(adminOptions)
	vueTable.GetFieldColumn("assetsId").SetSelectFormat(assetOptions)
	vueTable.GetFieldColumn("symbolAssetsId").SetSelectFormat(assetOptions)

	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(ProductUpdateParams{}).FlattenInputs()
	updateForm.SetFieldInputParam("assetsId", "default", assetOptions[0].Value)
	updateForm.SetFieldInputParam("symbolAssetsId", "default", assetOptions[0].Value)
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", ProductUpdateURL, updateForm).SetSize(views.DialogSizeMedium))

	// 数据表格 - 翻译
	translateForm := views.NewForm().Struct(ProductTranslateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("翻译", "", translateForm).SetDisplay(fmt.Sprintf("row.isTranslate == %d", model.BoolTrue)))

	// 数据表格 - 线性数据
	lineForm := views.NewForm().Struct(ProductUpdateLineParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("图表", ProductUpdateLineURL, lineForm).SetSize(views.DialogSizeMedium).SetDisplay(fmt.Sprintf("row.type == %d", models.ProductTypeCustomize)))

	// 数据表格 - 线性数据
	settingLineForm := views.NewForm().Struct(ProductKlineSettingParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("涨跌", ProductSettingKLineURL, settingLineForm).SetDisplay(fmt.Sprintf("row.type == %d", models.ProductTypeCustomize)))

	// 数据表格 - 质押设置
	stakingSettingsForm := views.NewForm().Struct(ProductUpdateStarkingParams{}).FlattenInputs()
	stakingSettingsForm.AddChildForm("stakingStrategy", &views.Form{
		Inputs: [][]*views.Input{
			{
				{Label: "类型", Field: "type", Type: views.InputTypeSelect, Options: views.FormatTimePickerOptions},
				{Label: "收益时长【0为长期】", Field: "value", Type: views.InputTypeNumber},
				{Label: "每期收益率(%)【必须值】", Field: "rate", Type: views.InputTypeNumber},
				{Label: "收益周期", Field: "rateCycle", Type: views.InputTypeNumber},
			},
		},
	})
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("设置", ProductStakingSettingURL, stakingSettingsForm).SetSize(views.DialogSizeMedium).SetDisplay("row.type == "+strconv.Itoa(int(models.ProductTypeStaking))))

	return vueTable
}
