package products

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/databases"
	"zfeng/models"
	"zfeng/service"
)

// CollectUpdateParams 更新参数
type CollectUpdateParams struct {
	ID   uint   `json:"id"`   //  主键
	Data string `json:"data"` //  数据
}

func CollectUpdate(c *context.CustomCtx, params *CollectUpdateParams) error {
	subAdminIDs, _ := service.NewAdminUserService().GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	result := databases.Db.Model(&models.Collect{}).
		Where("id = ?", params.ID).
		Where("admin_id IN ?", subAdminIDs).
		Updates(params)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新收藏数据失败: %s", result.Error.Error()))
	}
	return c.SuccessOk()
}
