package products

import (
	"errors"
	"gorm.io/gorm"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// ProductStarkingRedemptionParams 质押赎回
type ProductStarkingRedemptionParams struct {
	ID uint `json:"id" validate:"required" views:"label:ID;display:true"`
}

func ProductStarkingRedemption(c *context.CustomCtx, bodyParams *ProductStarkingRedemptionParams) error {
	db := model.NewModel()
	orderInfo := &models.OrderInfo{}
	result := model.NewModel().
		Where("id = ?", bodyParams.ID).
		Where("status = ?", models.ProductOrderStatusRunning).
		Preload("ProductInfo").
		Preload("UserInfo").
		Find(&orderInfo)
	if result.Error != nil || result.RowsAffected <= 0 {
		return c.ErrorJson("没有找到产品")
	}

	walletService := service.NewWalletService()
	userInfo := orderInfo.UserInfo
	merchantID, _ := service.NewAdminUserService().GetMerchantIDWithCache(c.Rds, userInfo.AdminID)
	err := db.Transaction(func(tx *gorm.DB) error {
		result = tx.Model(&models.Order{}).Where("id=?", bodyParams.ID).Update("status", models.ProductOrderStatusCompleted)
		if result.Error != nil {
			return result.Error
		}

		depositAssetsId := orderInfo.ProductInfo.Data.SymbolAssetsID
		if depositAssetsId != 0 {
			assetsInfo := &models.WalletAssets{}
			result = db.Model(assetsInfo).Where("id = ?", depositAssetsId).Where("admin_id = ?", merchantID).Find(assetsInfo)
			if result.RowsAffected == 0 {
				return errors.New("找不到资产")
			}

			// 资产结算
			if err := walletService.IncreaseAssets(tx, c.Rds, c.Lang, models.BillTypeProductEarnings, orderInfo.ID, userInfo, assetsInfo, orderInfo.Money); err != nil {
				return err
			}
		} else {
			// 余额结算
			if err := walletService.IncreaseBalance(tx, c.Rds, c.Lang, models.BillTypeProductEarnings, orderInfo.ID, userInfo, orderInfo.Money); err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return c.ErrorJson(err.Error())
	}

	return c.SuccessOk()
}
