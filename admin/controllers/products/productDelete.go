package products

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// ProductDelete 删除产品
func ProductDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	for _, v := range bodyParams.Ids {
		result := db.Where("id = ?", v).Where("admin_id IN ?", subAdminIDs).Delete(&models.Product{})
		if result.Error != nil {
			return c.<PERSON><PERSON><PERSON>(fmt.Sprintf("删除失败: %v", result.Error))
		}
	}

	return c.SuccessOk()
}
