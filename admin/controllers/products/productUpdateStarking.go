package products

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// ProductUpdateStarkingParams 更新质押设置
type ProductUpdateStarkingParams struct {
	ID    uint                      `json:"id" validate:"required" views:"label:ID;display:true"`
	Value []*models.StakingStrategy `json:"stakingStrategy" views:"label:质押设置;type:slice"`
}

func ProductUpdateStarking(c *context.CustomCtx, bodyParams *ProductUpdateStarkingParams) error {
	for _, value := range bodyParams.Value {
		if value.Rate == 0 {
			return c.ErrorJson("每期收益率(%)不能为0")
		}
	}

	db := model.NewModel()
	productInfo := &models.Product{}
	result := db.Model(&models.Product{}).
		Where("id=?", bodyParams.ID).
		Where("status = ?", models.ProductStatusEnabled).
		Find(&productInfo)
	if result.Error != nil || result.RowsAffected <= 0 {
		return c.ErrorJson("没有找到产品")
	}

	productInfo.Data.StakingStrategy = bodyParams.Value
	result = db.Model(&models.Product{}).Where("id=?", bodyParams.ID).Updates(&models.Product{Data: productInfo.Data})
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新失败%v", result.Error))
	}
	return c.SuccessOk()
}
