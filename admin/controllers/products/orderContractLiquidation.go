package products

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// OrderContractLiquidationParams 合约爆仓参数
type OrderContractLiquidationParams struct {
	ID uint `json:"id" validate:"required" views:"label:ID;display:true"`
}

// OrderContractLiquidation 合约爆仓设置
func OrderContractLiquidation(c *context.CustomCtx, params *OrderContractLiquidationParams) error {
	db := model.NewModel()
	orderInfo := &models.Order{}
	if result := db.Where("id = ?", params.ID).
		Where("status = ?", models.ProductOrderStatusRunning).
		Where("type = ?", models.ProductOrderTypeContract).
		Find(orderInfo); result.RowsAffected == 0 {
		return c.Error<PERSON>son("abnormalOperation")
	}

	productInfo := &models.Product{}
	if result := db.Where("id = ?", orderInfo.ProductID).
		Where("status = ?", models.ProductStatusEnabled).
		Find(productInfo); result.RowsAffected == 0 {
		return c.ErrorJson("abnormalOperation")
	}

	orderData := orderInfo.Data
	// 根据爆仓的倍数来计算爆仓的价格
	currentMultipleRate := 1 / float64(orderData.Index)
	if orderInfo.Side == models.ProductOrderSideBuy {
		currentMultipleRate = -currentMultipleRate
	}
	orderData.SellPrice = currentMultipleRate*orderInfo.Data.Price + orderInfo.Data.Price

	productService := service.NewProductService()
	if orderInfo.Side == models.ProductOrderSideBuy {
		orderData.SellPrice = productService.OrderRandomPrice(orderData.SellPrice, 2, productInfo.Data.AssetsDecimal)
	} else {
		orderData.SellPrice = productService.OrderRandomPrice(orderData.SellPrice, 1, productInfo.Data.AssetsDecimal)
	}

	//	更新合约卖出价格
	if result := db.Updates(&models.Order{BaseModel: model.BaseModel{ID: params.ID}, Status: models.ProductOrderStatusLiquidate, Data: orderData}); result.Error != nil {
		return result.Error
	}
	return c.SuccessOk()
}
