package products

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// OrderContractCloseParams 合约手动平仓参数
type OrderContractCloseParams struct {
	ID uint `json:"id" validate:"required" views:"label:ID;display:true"`
}

// OrderContractClose 合约手动平仓
func OrderContractClose(c *context.CustomCtx, params *OrderContractCloseParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	db := model.NewModel()

	orderInfo := models.OrderInfo{}
	result := db.Preload("ProductInfo").
		Where("id = ?", params.ID).
		Where("admin_id IN ?", subAdminIDs).
		Where("type = ?", models.ProductOrderTypeContract).
		Where("status = ?", models.ProductOrderStatusRunning).
		Find(&orderInfo)
	if result.RowsAffected <= 0 {
		return c.ErrorJson(fmt.Sprintf("没有找到该订单: %s", result.Error.Error()))
	}

	err := service.NewProductOrderService().ProductOrderClose(c.Rds, c.Claims.MerchantID, c.Lang, &orderInfo)
	if err != nil {
		return c.ErrorJson("撤单失败")
	}
	return c.SuccessOk()
}
