package products

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/utils"

	"gorm.io/gorm"
)

// CategoryCreateParams 创建分类参数
type CategoryCreateParams struct {
	Icon     string `json:"icon" validate:"required" views:"label:图标;type:image"`
	ParentID uint   `json:"parentId" views:"label:父级;type:select"`
	Type     int8   `json:"type" validate:"required" views:"label:类型;type:select"`
	Name     string `json:"name" validate:"required" views:"label:名称"`
}

// CategoryCreate 创建分类
func CategoryCreate(c *context.CustomCtx, bodyParams *CategoryCreateParams) error {
	db := model.NewModel()

	err := db.Transaction(func(tx *gorm.DB) error {
		categoryInfo := &models.Category{
			AdminID:  c.Claims.AdminID,
			Icon:     bodyParams.Icon,
			ParentID: bodyParams.ParentID,
			Type:     bodyParams.Type,
			Symbol:   utils.GenerateRandomString(10),
			Name:     bodyParams.Name,
		}
		result := tx.Create(categoryInfo)
		if result.Error != nil {
			return result.Error
		}
		// 添加翻译
		result = tx.Create(&models.Translate{
			AdminID: c.Claims.AdminID,
			Lang:    models.DefaultLang,
			Name:    fmt.Sprintf("产品分类[%s]", categoryInfo.Name),
			Type:    models.TranslateTypeSystem,
			Field:   fmt.Sprintf(models.CategoryTranslatePrefix, categoryInfo.Symbol),
			Value:   bodyParams.Name,
		})
		if result.Error != nil {
			return result.Error
		}

		result = tx.Model(&models.Category{}).Where("id = ?", categoryInfo.ID).Update("name", fmt.Sprintf(models.CategoryTranslatePrefix, categoryInfo.Symbol))
		if result.Error != nil {
			return result.Error
		}
		return result.Error
	})
	if err != nil {
		return c.ErrorJson(fmt.Sprintf("添加分类失败: %s", err.Error()))
	}

	return c.SuccessOk()
}
