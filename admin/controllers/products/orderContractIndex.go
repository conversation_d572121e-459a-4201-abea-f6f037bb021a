package products

import (
	"fmt"
	"strconv"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	OrderContractIndexURL       = "/products/order/contract/index"
	OrderContractCloseURL       = "/products/order/contract/close"
	OrderContractLiquidationURL = "/products/order/contract/liquidation"
)

// OrderContractIndexParams 合约订单列表参数
type OrderContractIndexParams struct {
	AdminID    uint                   `json:"adminID" views:"label:管理;type:select"`
	UserID     uint                   `json:"userID" views:"label:账户;type:selectSearch;mask:user#username>id"`
	ProductID  uint                   `json:"productID" views:"label:产品;type:selectSearch;mask:product#name>id"`
	Status     int8                   `json:"status" views:"label:状态;type:select"`
	Mode       int8                   `json:"mode" views:"label:方式;type:select"`
	Side       int8                   `json:"side" views:"label:方向;type:select"`
	UpdatedAt  *model.RangeDatePicker `json:"updatedAt" views:"label:成交时间;type:dateRange"`
	Pagination *model.Pagination      `json:"pagination" views:"-"`
}

// OrderContractIndexRows 合约订单列表
type OrderContractIndexRows struct {
	ID          uint                  `json:"id" column:"label:ID"`
	AdminID     uint                  `json:"adminID" column:"label:管理"`
	UserName    string                `json:"userName" gorm:"-" column:"label:账户;scanField:userInfo.username"`
	Images      model.GormStringSlice `json:"images" gorm:"-" column:"label:图片;type:icon"`
	ProductName string                `json:"productName" gorm:"-" column:"label:产品;scanField:productInfo.name"`
	Side        int                   `json:"side" column:"label:方向;type:select"`
	Mode        int                   `json:"mode" column:"label:方式;type:select"`
	OrderSN     string                `json:"orderSN"`
	Money       float64               `json:"money" column:"label:订单金额"`
	UserID      uint                  `json:"userID"`
	ProductID   uint                  `json:"productID"`
	Price       float64               `json:"price"  gorm:"-" column:"label:买入单价;scanField:data.price"`
	SellPrice   float64               `json:"sellPrice"  gorm:"-" column:"label:卖出单价;scanField:data.sellPrice"`
	Amount      float64               `json:"amount"  gorm:"-" column:"label:结算金额"`
	Fee         float64               `json:"fee" column:"label:手续费"`
	Multiple    int                   `json:"multiple"  gorm:"-" column:"label:倍数;scanField:data.index"`
	TakePrice   int                   `json:"takePrice"  gorm:"-" column:"label:止盈价;scanField:data.takePrice"`
	StopPrice   int                   `json:"stopPrice"  gorm:"-" column:"label:止损价;scanField:data.stopPrice"`
	Status      int8                  `json:"status" column:"label:状态;type:select"`
	Type        int8                  `json:"type"`
	CreatedAt   time.Time             `json:"createdAt" column:"label:下单时间;type:date"`
	UpdatedAt   time.Time             `json:"updatedAt" column:"label:成交时间;type:date"`
	UserInfo    models.User           `json:"userInfo" gorm:"foreignKey:UserID"`
	ProductInfo models.Product        `json:"productInfo" gorm:"foreignKey:ProductID"`
	Data        models.OrderData      `json:"data"`
}

// OrderContractIndex 合约订单列表
func OrderContractIndex(c *context.CustomCtx, bodyParams *OrderContractIndexParams) error {
	productOrderList := make([]*OrderContractIndexRows, 0)
	data := &model.IndexData{}

	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	query := db.
		Equal("admin_id", bodyParams.AdminID).
		Equal("user_id", bodyParams.UserID).
		Equal("product_id", bodyParams.ProductID).
		Equal("type", models.ProductOrderTypeContract).
		Equal("status", bodyParams.Status).
		Equal("side", bodyParams.Side).
		Equal("mode", bodyParams.Mode).
		BetweenTime("updated_at", bodyParams.UpdatedAt, c.TimeZone).
		Model(&models.Order{})
	query.Where("admin_id IN ?", subAdminIDs).
		Preload("UserInfo").
		Preload("ProductInfo").
		Count(&data.Count).
		Scopes(bodyParams.Pagination.Scopes()).
		Find(&productOrderList)

	data.Items = productOrderList
	for _, v := range productOrderList {
		v.Images = v.ProductInfo.Images

		amountStr := fmt.Sprintf("%."+strconv.Itoa(v.ProductInfo.Data.AssetsDecimal)+"f", v.Data.Amount)
		amount, err := strconv.ParseFloat(amountStr, 64)
		if err == nil {
			v.Amount = amount
		} else {
			v.Amount = v.Data.Amount
		}
	}
	return c.SuccessJson(data)
}

// OrderContractIndexConfigure 合约订单列表配置
func OrderContractIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(OrderContractIndexURL).SetAutoRefresh()
	adminService := service.NewAdminUserService()
	adminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)
	statusOptions := []*views.SelectOption{
		{Label: "完成", Value: models.ProductOrderStatusCompleted},
		{Label: "取消", Value: models.ProductOrderStatusCancelled},
		{Label: "运行", Value: models.ProductOrderStatusRunning},
		{Label: "等待", Value: models.ProductOrderStatusWaiting},
		{Label: "爆仓", Value: models.ProductOrderStatusLiquidate},
	}
	sideOptions := []*views.SelectOption{
		{Label: "做多", Value: models.ProductOrderSideBuy},
		{Label: "做空", Value: models.ProductOrderSideSell},
	}

	modeOptions := []*views.SelectOption{
		{Label: "限价", Value: models.ProductOrderModeLimit},
		{Label: "市价", Value: models.ProductOrderModeMarket},
	}

	vueTable.SetCommonOptions("status", statusOptions).
		SetCommonOptions("side", sideOptions).
		SetCommonOptions("mode", modeOptions).
		SetCommonOptions("adminID", adminOptions)

	// 搜索
	searchForm := views.NewForm().Struct(OrderContractIndexParams{}).SetPagination(nil)
	searchForm.SetFieldInputParam("userID", "mask", fmt.Sprintf("user#username>id#admin_id = %d and  status = %d", c.Claims.MerchantID, models.UserStatusActive))
	searchForm.SetFieldInputParam("productID", "mask", fmt.Sprintf("product#name>id#admin_id = %d and category_id in (select id from category where admin_id = %d and type = %d and status = %d)", c.Claims.MerchantID, c.Claims.MerchantID, models.CategoryTypeForex, models.CategoryStatusEnabled))
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(OrderCreateParams{}).FlattenInputs()
	createForm.SetOptionsParam("side", sideOptions)
	createForm.SetOptionsParam("mode", modeOptions)
	createForm.SetDefaultParam("side", models.ProductOrderSideBuy)
	//createForm.SetDefaultParam("mode", models.ProductOrderModeMarket)
	createForm.SetDefaultParam("type", models.ProductOrderTypeContract)
	createForm.SetDefaultParam("index", 1)

	createForm.SetFieldInputParam("id", "mask", fmt.Sprintf("product#name>id#admin_id = %d and category_id in (select id from category where admin_id = %d and type = %d and status = %d)", c.Claims.MerchantID, c.Claims.MerchantID, models.CategoryTypeForex, models.CategoryStatusEnabled))
	createForm.SetFieldInputParam("userID", "mask", fmt.Sprintf("user#username>id#admin_id = %d and status = %d", c.Claims.MerchantID, models.UserStatusActive))
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("合约订单", OrderCreateURL, createForm))

	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", OrderDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(OrderContractIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("side").SetSelectFormat(sideOptions)
	vueTable.GetFieldColumn("mode").SetSelectFormat(modeOptions)
	vueTable.GetFieldColumn("adminID").SetSelectFormat(adminOptions)

	// 数据表格 - 操作按钮
	revokeForm := views.NewForm().Struct(OrderRevokeParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("撤单", OrderRevokeURL, revokeForm).SetDisplay(fmt.Sprintf("row.status == %v", models.ProductOrderStatusWaiting)))

	closeForm := views.NewForm().Struct(OrderContractCloseParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("平仓", OrderContractCloseURL, closeForm).SetDisplay(fmt.Sprintf("row.status == %v", models.ProductOrderStatusRunning)))

	liquidationForm := views.NewForm().Struct(OrderContractLiquidationParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("爆仓", OrderContractLiquidationURL, liquidationForm).SetButtonColor(views.ColorWarning).SetDisplay(fmt.Sprintf("row.status == %v", models.ProductOrderStatusRunning)))

	updateForm := views.NewForm().Struct(OrderUpdateParams{}).FlattenInputs()
	updateForm.SetFieldInputParam("price", "scanField", "data.price")
	updateForm.SetFieldInputParam("takePrice", "scanField", "data.takePrice")
	updateForm.SetFieldInputParam("stopPrice", "scanField", "data.stopPrice")
	updateForm.ResetInputs([][]string{{"id"}, {"price"}, {"money"}, {"takePrice"}, {"stopPrice"}, {"type"}, {"status"}})
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", OrderUpdateURL, updateForm).SetDisplay(fmt.Sprintf("row.status == %v", models.ProductOrderStatusWaiting)))

	return vueTable
}
