package products

import (
	"fmt"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	OrderCreateURL    = "/products/order/create"
	OrderUpdateURL    = "/products/order/update"
	OrderSpotIndexURL = "/products/order/spot/index"
	OrderDeleteURL    = "/products/order/spot/delete"
)

// OrderSpotIndexParams 默认订单列表参数
type OrderSpotIndexParams struct {
	AdminID    uint                   `json:"adminID" views:"label:管理;type:select"`
	UserID     uint                   `json:"userID" views:"label:账户;type:selectSearch;mask:user#username>id"`
	ProductID  uint                   `json:"productID" views:"label:产品;type:selectSearch;mask:product#name>id"`
	Mode       int8                   `json:"mode" views:"label:方式;type:select"`
	Side       int8                   `json:"side" views:"label:方向;type:select"`
	Status     int8                   `json:"status" views:"label:状态;type:select"`
	UpdatedAt  *model.RangeDatePicker `json:"updatedAt" views:"label:成交时间;type:dateRange"`
	Pagination *model.Pagination      `json:"pagination" views:"-"`
}

// OrderSpotIndexRows 默认订单列表
type OrderSpotIndexRows struct {
	ID          uint                  `json:"id" column:"label:ID"`
	AdminID     uint                  `json:"adminID" column:"label:管理"`
	UserName    string                `json:"userName" gorm:"-" column:"label:账户;scanField:userInfo.username"`
	Images      model.GormStringSlice `json:"images" gorm:"-" column:"label:图片;type:icon"`
	ProductName string                `json:"productName" gorm:"-" column:"label:产品;scanField:productInfo.name"`
	Side        int8                  `json:"side" column:"label:方向;type:select"`
	Mode        int8                  `json:"mode" column:"label:方式;type:select"`
	OrderSN     string                `json:"orderSN"`
	Money       float64               `json:"money" column:"label:消耗"`
	Price       float64               `json:"price"  gorm:"-" column:"label:单价;scanField:data.price"`
	Nums        float64               `json:"Nums" column:"label:获得"`
	Fee         float64               `json:"fee" column:"label:手续费"`
	Status      int8                  `json:"status" column:"label:状态;type:select"`
	Type        int8                  `json:"type"`
	UserID      uint                  `json:"userID"`
	ProductID   uint                  `json:"productID"`
	UserInfo    models.User           `json:"userInfo" gorm:"foreignKey:UserID"`
	ProductInfo models.Product        `json:"productInfo" gorm:"foreignKey:ProductID"`
	CreatedAt   time.Time             `json:"createdAt" column:"label:下单时间;type:date"`
	UpdatedAt   time.Time             `json:"updatedAt" column:"label:成交时间;type:date"`
	Data        models.OrderData      `json:"data"`
}

// OrderSpotIndex 现货订单列表
func OrderSpotIndex(c *context.CustomCtx, bodyParams *OrderSpotIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	data := &model.IndexData{Items: make([]*OrderSpotIndexRows, 0), Count: 0}

	query := db.
		Equal("admin_id", bodyParams.AdminID).
		Equal("user_id", bodyParams.UserID).
		Equal("product_id", bodyParams.ProductID).
		Equal("type", models.ProductOrderTypeSpot).
		Equal("status", bodyParams.Status).
		Equal("mode", bodyParams.Mode).
		Equal("side", bodyParams.Side).
		BetweenTime("updated_at", bodyParams.UpdatedAt, c.TimeZone).
		Model(&models.Order{})
	query.Where("admin_id IN ?", subAdminIDs).
		Preload("UserInfo").
		Preload("ProductInfo").
		Count(&data.Count).
		Scopes(bodyParams.Pagination.Scopes()).
		Find(&data.Items)
	for _, v := range data.Items.([]*OrderSpotIndexRows) {
		v.Images = v.ProductInfo.Images
	}
	return c.SuccessJson(data)
}

// OrderSpotIndexConfigure 现货订单列表配置
func OrderSpotIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(OrderSpotIndexURL).SetAutoRefresh()
	adminService := service.NewAdminUserService()
	adminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)
	statusOptions := []*views.SelectOption{
		{Label: "完成", Value: models.ProductOrderStatusCompleted},
		{Label: "取消", Value: models.ProductOrderStatusCancelled},
		{Label: "等待", Value: models.ProductOrderStatusWaiting},
	}
	sideOptions := []*views.SelectOption{
		{Label: "买入", Value: models.ProductOrderSideBuy},
		{Label: "卖出", Value: models.ProductOrderSideSell},
	}

	modeOptions := []*views.SelectOption{
		{Label: "限价", Value: models.ProductOrderModeLimit},
		{Label: "市价", Value: models.ProductOrderModeMarket},
	}

	vueTable.SetCommonOptions("status", statusOptions).
		SetCommonOptions("side", sideOptions).
		SetCommonOptions("mode", modeOptions).
		SetCommonOptions("adminID", adminOptions)

	// 搜索
	searchForm := views.NewForm().Struct(OrderSpotIndexParams{}).SetPagination(nil)
	searchForm.SetFieldInputParam("userID", "mask", fmt.Sprintf("user#username>id#admin_id = %d and  status = %d", c.Claims.MerchantID, models.UserStatusActive))
	searchForm.SetFieldInputParam("productID", "mask", fmt.Sprintf("product#name>id#admin_id = %d and category_id in (select id from category where admin_id = %d and type = %d and status = %d)", c.Claims.MerchantID, c.Claims.MerchantID, models.CategoryTypeSpot, models.CategoryStatusEnabled))
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(OrderCreateParams{}).FlattenInputs()
	createForm.SetOptionsParam("side", sideOptions)
	createForm.SetDefaultParam("side", models.ProductOrderSideBuy)
	createForm.SetDefaultParam("type", models.ProductOrderTypeSpot)
	createForm.SetDefaultParam("mode", models.ProductOrderModeMarket)
	createForm.ResetInputs([][]string{
		{"id"},
		{"userID"},
		{"side"},
		{"type"},
		{"price"},
		{"money"},
	})
	createForm.SetFieldInputParam("id", "mask", fmt.Sprintf("product#name>id#admin_id = %d and category_id in (select id from category where admin_id = %d and type = %d and status = %d)", c.Claims.MerchantID, c.Claims.MerchantID, models.CategoryTypeSpot, models.CategoryStatusEnabled))
	createForm.SetFieldInputParam("userID", "mask", fmt.Sprintf("user#username>id#admin_id = %d and status = %d", c.Claims.MerchantID, models.UserStatusActive))
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("币币订单", OrderCreateURL, createForm))

	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", OrderDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(OrderSpotIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("side").SetSelectFormat(sideOptions)
	vueTable.GetFieldColumn("mode").SetSelectFormat(modeOptions)
	vueTable.GetFieldColumn("adminID").SetSelectFormat(adminOptions)

	revokeForm := views.NewForm().Struct(OrderRevokeParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("取消", OrderRevokeURL, revokeForm).SetDisplay(fmt.Sprintf("row.status == %v", models.ProductOrderStatusWaiting)))

	updateForm := views.NewForm().Struct(OrderUpdateParams{}).FlattenInputs()
	updateForm.SetFieldInputParam("price", "scanField", "data.price")
	updateForm.ResetInputs([][]string{{"id"}, {"price"}, {"money"}, {"type"}, {"status"}})
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", OrderUpdateURL, updateForm).SetDisplay(fmt.Sprintf("row.status == %v", models.ProductOrderStatusWaiting)))

	return vueTable
}
