package products

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
)

const (
	ExtendSettingURL = "/products/extend/setting"
)

// 扩展设置参数
type ExtendSettingsParams struct {
	ID      uint        `json:"id" validate:"required" views:"label:ID;display:true"`
	GroupID uint        `json:"groupId" validate:"required" views:"label:分组ID;display:true"`
	Type    string      `json:"settingType" validate:"required" views:"label:设置类型;display:true"`
	Field   string      `json:"settingField" validate:"required" views:"label:设置字段;display:true"`
	Value   interface{} `json:"settingValue" views:"label:配置;type:dynamic"`
}

// 扩展设置
func ExtendSettings(c *context.CustomCtx, bodyParams *ExtendSettingsParams) error {
	db := model.NewModel()

	// 设置信息
	settingInfo := models.ExtendSetting{}
	result := db.Model(&settingInfo).Where("source_id = ?", bodyParams.ID).Where("type = ?", bodyParams.Type).Where("field = ?", bodyParams.Field).Find(&settingInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("查询设置信息失败 %s", result.Error.Error()))
	}

	if settingInfo.ID == 0 {
		settingInfo.AdminID = c.Claims.AdminID
		settingInfo.SourceID = bodyParams.ID
		settingInfo.GroupID = bodyParams.GroupID
		settingInfo.Name = fmt.Sprintf("%s(%v)", bodyParams.Field, c.Claims.AdminID)
		settingInfo.Type = bodyParams.Type
		settingInfo.Field = bodyParams.Field
		settingInfo.Value = views.InputValueToString(bodyParams.Value)
		result = db.Create(&settingInfo)
	} else {
		settingInfo.Value = views.InputValueToString(bodyParams.Value)
		result = db.Save(&settingInfo)
	}

	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("保存设置信息失败 %s", result.Error.Error()))
	}

	return c.SuccessOk()
}
