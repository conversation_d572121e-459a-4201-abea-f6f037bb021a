package products

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

const (
	OrderRevokeURL = "/products/order/revoke" // 订单撤单路由
)

// OrderRevokeParams 币币订单撤单参数
type OrderRevokeParams struct {
	ID uint `json:"id" validate:"required" views:"label:ID;display:true"`
}

// OrderSpotRevoke 币币订单撤单
func OrderSpotRevoke(c *context.CustomCtx, bodyParams *OrderRevokeParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	orderInfo := &models.Order{}
	result := db.Model(&models.Order{}).
		Where("admin_id IN ?", subAdminIDs).
		Where("id = ?", bodyParams.ID).
		Where("status NOT IN ?", []int8{models.ProductOrderStatusCancelled, models.ProductOrderStatusCompleted}).
		Find(&orderInfo)
	if result.RowsAffected <= 0 {
		return c.ErrorJson(fmt.Sprintf("没有找到该订单: %s", result.Error.Error()))
	}
	err := service.NewProductOrderService().ProductOrderRevoke(c.Rds, c.Lang, orderInfo)
	if err != nil {
		return c.ErrorJson("撤单失败")
	}
	return c.SuccessOk()
}
