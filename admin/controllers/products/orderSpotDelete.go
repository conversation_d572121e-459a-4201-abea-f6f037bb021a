package products

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// OrderSpotDelete 删除默认订单
func OrderSpotDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	for _, v := range bodyParams.Ids {
		result := db.Where("admin_id IN ?", subAdminIDs).Where("id = ?", v).Where("type = ?", models.ProductOrderTypeSpot).Delete(&models.Order{})
		if result.Error != nil {
			return c.SuccessJson(fmt.Sprintf("删除失败: %v", result.Error))
		}
	}

	return c.SuccessOk()
}
