package products

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/databases"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// CollectCreateParams 新增参数
type CollectCreateParams struct {
	AdminId     uint                     `json:"adminId" views:"label:管理员;type:select"`
	ProductName string                   `json:"productName" views:"label:产品名"`
	UserName    string                   `json:"userName" views:"label:用户名"`
	Data        string                   `json:"data" views:"label:数据;type:text"`
	Status      int                      `json:"status" views:"label:状态;type:select"`
	UpdatedAt   model.GormDateTimeParams `json:"updatedAt" views:"label:收盘时间;type:date"`
	CreatedAt   model.GormDateTimeParams `json:"createdAt" views:"label:开盘时间;type:date"`
}

// CollectCreate 新增接口
func CollectCreate(c *context.CustomCtx, params *CollectCreateParams) error {
	adminInfo := &models.AdminUser{}
	subAdminIDs, _ := service.NewAdminUserService().GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	result := databases.Db.Where("id = ?", params.AdminId).Where("id IN ?", subAdminIDs).Take(adminInfo)
	if result.Error != nil {
		return c.ErrorJson("查询不到管理员")
	}

	userInfo := &models.User{}
	result = databases.Db.Where("username = ?", params.UserName).Where("admin_id IN ?", subAdminIDs).Take(userInfo)
	if result.Error != nil {
		return c.ErrorJson("查询不到用户")
	}

	productInfo := &models.Product{}
	result = databases.Db.Where("name = ?", params.ProductName).Where("admin_id = ?", params.AdminId).Take(productInfo)
	if result.Error != nil {
		return c.ErrorJson("查询不到产品")
	}

	createInfo := &models.Collect{
		AdminId:   params.AdminId,
		ProductId: productInfo.ID,
		UserId:    userInfo.ID,
		Data:      params.Data,
		Status:    models.CollectStatusEnabled,
	}
	collectInfo := &models.Collect{}
	result = databases.Db.Unscoped().Where("admin_id = ?", params.AdminId).
		Where("product_id = ?", productInfo.ID).
		Where("user_id = ?", userInfo.ID).
		Find(collectInfo)

	if result.RowsAffected != 0 {
		nowTime := time.Now()
		collectInfo.Status = models.CollectStatusEnabled
		collectInfo.Data = params.Data
		collectInfo.DeletedAt = gorm.DeletedAt{}
		collectInfo.CreatedAt = nowTime
		result = databases.Db.Save(collectInfo)
		if result.Error != nil {
			return c.ErrorJson("添加失败, 原因 => %v" + result.Error.Error())
		}
		return c.SuccessOk()
	}

	result = databases.Db.Create(createInfo)
	if result.Error != nil {
		return c.ErrorJson("添加失败, 原因 => %v" + result.Error.Error())
	}

	return c.SuccessOk()
}
