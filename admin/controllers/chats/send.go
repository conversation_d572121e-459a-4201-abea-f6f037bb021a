package chats

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/inits/instance"
	"zfeng/models"
	"zfeng/module/socket"
)

// SendParams 发送消息参数
type SendParams struct {
	SessionID string `json:"sessionId" validate:"required"`
	Type      int8   `json:"type" validate:"required,oneof=1 2"`
	Message   string `json:"message" validate:"required"`
}

// Send 发送消息
func Send(c *context.CustomCtx, bodyParams *SendParams) error {
	db := model.NewModel()

	// 获取当前会话
	sessionInfo := models.ChatsSessions{}
	result := db.Model(&models.ChatsSessions{}).Where("session_id = ?", bodyParams.SessionID).Where("admin_id = ?", c.Claims.AdminID).First(&sessionInfo)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	// 创建消息
	messageInfo := models.ChatsMessages{
		AdminID:    sessionInfo.AdminID,
		SessionID:  bodyParams.SessionID,
		SenderID:   sessionInfo.AdminID,
		ReceiverID: sessionInfo.UserID,
		SenderType: models.ChatsMessagesSenderTypeAdmin,
		Message:    bodyParams.Message,
		Type:       bodyParams.Type,
		BaseModel: model.BaseModel{
			CreatedAt: time.Now(),
		},
	}
	result = db.Create(&messageInfo)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	// 更新最后一条消息
	sessionInfo.Data = messageInfo.ToSessionData()
	db.Model(&models.ChatsSessions{}).Where("id = ?", sessionInfo.ID).Updates(sessionInfo)

	// 发送订阅消息
	instance.ChatsSocket.RedisUserPublish(c.Rds, socket.ConnTypeUser, socket.MessageOperateMessage, sessionInfo.UserID, &models.ChatsSessionsData{
		ID:         messageInfo.ID,
		SessionID:  messageInfo.SessionID,
		SenderID:   messageInfo.SenderID,
		ReceiverID: messageInfo.ReceiverID,
		SenderType: messageInfo.SenderType,
		Type:       messageInfo.Type,
		Message:    messageInfo.Message,
		Status:     messageInfo.Status,
		CreatedAt:  messageInfo.CreatedAt,
	})

	return c.SuccessJson(messageInfo)
}
