package chats

import (
	"errors"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/inits/instance"
	"zfeng/models"
	"zfeng/module/socket"

	"gorm.io/gorm"
)

// RevokeParams 撤回消息参数
type RevokeParams struct {
	ID uint `form:"id" json:"id" binding:"required"` // 消息ID
}

// Revoke 撤回消息
func Revoke(c *context.CustomCtx, bodyParams *RevokeParams) error {
	db := model.NewModel()

	// 查询消息
	messageInfo := models.ChatsMessages{}
	result := db.Model(&models.ChatsMessages{}).
		Where("id = ?", bodyParams.ID).
		Where("sender_id = ?", c.Claims.AdminID).
		Where("sender_type = ?", models.ChatsMessagesSenderTypeAdmin).
		First(&messageInfo)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	sessionInfo := models.ChatsSessions{}
	result = db.Model(&models.ChatsSessions{}).Where("session_id = ?", messageInfo.SessionID).Where("admin_id = ?", c.Claims.AdminID).First(&sessionInfo)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		// 删除消息
		result = tx.Unscoped().Delete(&messageInfo)
		if result.Error != nil {
			return result.Error
		}

		// 查询删除后会话的最新一条消息
		var latestMessage models.ChatsMessages
		result = tx.Model(&models.ChatsMessages{}).
			Where("session_id = ?", messageInfo.SessionID).
			Order("created_at desc").
			First(&latestMessage)

		// 更新会话的最后消息信息
		if result.Error == nil {
			// 如果找到了新的最新消息
			sessionInfo.Data = models.ChatsSessionsData{
				ID:         latestMessage.ID,
				SessionID:  latestMessage.SessionID,
				SenderID:   latestMessage.SenderID,
				SenderType: latestMessage.SenderType,
				ReceiverID: latestMessage.ReceiverID,
				Type:       latestMessage.Type,
				Message:    latestMessage.Message,
				Status:     latestMessage.Status,
				CreatedAt:  latestMessage.CreatedAt,
			}
		} else if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 如果会话中没有其他消息了，清空最后消息信息
			sessionInfo.Data = models.ChatsSessionsData{}
		} else {
			// 其他查询错误
			return result.Error
		}

		// 保存更新后的会话信息
		result = tx.Save(&sessionInfo)

		return result.Error
	})

	if err != nil {
		return c.ErrorJson(err.Error())
	}

	// 通知用户端
	instance.ChatsSocket.RedisUserPublish(c.Rds, socket.ConnTypeUser, socket.MessageOperateRevoke, sessionInfo.UserID, messageInfo.ID)

	return c.SuccessOk()
}
