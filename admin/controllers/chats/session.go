package chats

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"

	"gorm.io/gorm"
)

// 会话参数
type SessionParams struct {
	SessionID string `json:"sessionId" validate:"required"`
}

// 会话信息
func Session(c *context.CustomCtx, bodyParams *SessionParams) error {
	db := model.NewModel()

	data := &SessionsRows{}
	db.Model(&models.ChatsSessions{}).Preload("UserInfo", func(db *gorm.DB) *gorm.DB {
		return db.Model(&models.User{}).Select("id", "username", "avatar")
	}).Where("session_id = ?", bodyParams.SessionID).Where("admin_id = ?", c.Claims.AdminID).First(&data)

	return c.<PERSON><PERSON><PERSON>(data)
}
