package chats

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/inits/instance"
	"zfeng/models"
	"zfeng/module/socket"

	"gorm.io/gorm"
)

// SessionsParams 聊天会话参数
type SessionsParams struct {
	Pagination *model.Pagination `json:"pagination" views:"-"` // 分页参数
}

// SessionsRows 聊天会话列表
type SessionsRows struct {
	models.ChatsSessions
	Online   bool     `json:"online"`
	UserInfo UserInfo `json:"userInfo" gorm:"foreignKey:UserID"`
}

// Sessions 聊天会话列表
func Sessions(c *context.CustomCtx, bodyParams *SessionsParams) error {
	data := &model.IndexData{Items: make([]*SessionsRows, 0), Count: 0}

	db := model.NewModel()
	db.Model(&models.ChatsSessions{}).Preload("UserInfo", func(db *gorm.DB) *gorm.DB {
		return db.Model(&models.User{}).Select("id", "username", "avatar")
	}).Where("admin_id = ?", c.Claims.AdminID).Where("type = ?", models.ChatsSessionsTypeUser).
		Count(&data.Count).Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	for _, item := range data.Items.([]*SessionsRows) {
		item.Online = instance.ChatsSocket.IsOnline(c.Rds, socket.ConnTypeUser, item.UserID)
	}

	return c.SuccessJson(data)
}

type UserInfo struct {
	ID       int64  `json:"id"`
	Username string `json:"username"`
	Avatar   string `json:"avatar"`
}
