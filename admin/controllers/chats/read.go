package chats

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/inits/instance"
	"zfeng/models"
	"zfeng/module/socket"

	"gorm.io/gorm"
)

// ReadParams 阅读消息参数
type ReadParams struct {
	IDs []uint `json:"ids" validate:"required"`
}

// Read 阅读消息
func Read(c *context.CustomCtx, bodyParams *ReadParams) error {
	db := model.NewModel()

	// 用户消息
	messageList := make([]*models.ChatsMessages, 0)
	db.Model(&models.ChatsMessages{}).Where("id IN ?", bodyParams.IDs).Where("receiver_id = ?", c.Claims.AdminID).Find(&messageList)

	// 更新会话最后消息接收者ID
	if len(messageList) > 0 {
		db.Model(&models.ChatsMessages{}).Where("id IN ?", bodyParams.IDs).Where("receiver_id = ?", c.Claims.AdminID).Update("status", models.ChatsMessagesStatusRead)

		// 更新会话未读消息数量
		db.Model(&models.ChatsSessions{}).Where("session_id = ?", messageList[0].SessionID).UpdateColumn("number", gorm.Expr("number - ?", len(messageList)))
	}

	// 发送消息给管理员
	for _, messageInfo := range messageList {
		instance.ChatsSocket.RedisUserPublish(c.Rds, socket.ConnTypeUser, socket.MessageOperateRead, messageInfo.SenderID, messageInfo.ID)
	}
	return c.SuccessOk()
}
