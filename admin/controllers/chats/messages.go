package chats

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// MessagesParams 聊天会话消息参数
type MessagesParams struct {
	SessionID  string            `json:"sessionId" validate:"required"` // 会话ID
	Pagination *model.Pagination `json:"pagination" views:"-"`          // 分页参数
}

// Messages 聊天会话消息
func Messages(c *context.CustomCtx, bodyParams *MessagesParams) error {
	db := model.NewModel()
	sessionInfo := &models.ChatsSessions{}
	result := db.Model(&models.ChatsSessions{}).Where("session_id = ?", bodyParams.SessionID).Where("admin_id = ?", c.Claims.AdminID).First(sessionInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("会话不存在: %v", result.Error))
	}

	data := &model.IndexData{Items: make([]*models.ChatsMessages, 0), Count: 0}
	db.Model(&models.ChatsMessages{}).Where("session_id = ?", sessionInfo.SessionID).
		Count(&data.Count).
		Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}
