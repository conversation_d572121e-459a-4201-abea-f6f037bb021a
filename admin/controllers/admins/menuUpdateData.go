package admins

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// MenuUpdateDataParams 更新菜单数据参数
type MenuUpdateDataParams struct {
	ID   uint                 `json:"id" validate:"required" views:"label:菜单ID;type:number;display:true"`
	Data models.AdminMenuData `json:"data" views:"label:数据;type:struct"`
}

// MenuUpdateData 更新菜单数据
func MenuUpdateData(c *context.CustomCtx, bodyParams *MenuUpdateDataParams) error {
	menuInfo := models.AdminMenu{}
	db := model.NewModel()

	result := db.Model(&menuInfo).Where("id = ?", bodyParams.ID).First(&menuInfo)
	if result.Error != nil {
		return c.<PERSON>rror<PERSON><PERSON>("菜单不存在")
	}

	menuInfo.Data = bodyParams.Data
	result = db.Model(&menuInfo).Save(&menuInfo)
	if result.Error != nil {
		return c.<PERSON><PERSON><PERSON><PERSON><PERSON>(result.Error.Error())
	}

	return c.<PERSON>k()
}
