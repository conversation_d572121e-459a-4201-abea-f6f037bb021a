package admins

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// ManageDelete 删除管理
func ManageDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	if len(bodyParams.Ids) == 0 {
		return c.<PERSON>("No IDs provided for deletion")
	}

	db := model.NewModel()
	subAdminIds, _ := service.NewAdminUserService().GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db.Transaction(func(tx *gorm.DB) error {
		for _, id := range bodyParams.Ids {
			adminInfo := models.AdminUser{}
			result := tx.Where("id = ?", id).Where("id IN ?", subAdminIds).First(&adminInfo)
			if result.Error != nil {
				return fmt.Errorf("找不到可操作的管理员 [%v]", id)
			}

			// 删除当前管理
			result = tx.Where("id = ?", adminInfo.ID).Unscoped().Delete(&models.AdminUser{})
			if result.Error != nil {
				return result.Error
			}

			// 如果是商户管理, 那么删除对应的配置文件
			if adminInfo.ParentID == models.SuperAdminID {
				//	TODO...
			}
		}
		return nil
	})

	return c.SuccessOk()
}
