package admins

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// ManageUpdateMerchantSettingParams 更新商户设置参数
type ManageUpdateMerchantSettingParams struct {
	ID       uint                   `json:"id" views:"label:ID;display:true"`
	Settings map[string]interface{} `json:"settings" views:"type:struct;scanField:merchant"`
}

// ManageUpdateMerchantSetting 更新商户设置
func ManageUpdateMerchantSetting(c *context.CustomCtx, bodyParams *ManageUpdateMerchantSettingParams) error {
	subAdminIds, _ := service.NewAdminUserService().GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	adminInfo := models.AdminUser{}
	result := model.NewModel().Where("id = ?", bodyParams.ID).Where("parent_id = ?", models.SuperAdminID).Where("id IN ?", subAdminIds).Find(&adminInfo)
	if result.Error != nil || adminInfo.ID == 0 {
		return c.ErrorJson("没有可更新的管理参数")
	}

	model := model.NewModel()
	settingService := service.NewAdminSettingService()
	for k, v := range bodyParams.Settings {
		// 查询是否存在
		var setting models.AdminSetting
		result := model.Where("admin_id = ? AND field = ?", adminInfo.ID, k).First(&setting)
		if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
			return c.ErrorJson(result.Error.Error())
		}

		if result.Error == gorm.ErrRecordNotFound {
			// 如果不存在，创建新记录
			setting = models.AdminSetting{
				AdminID: adminInfo.ID,
				Field:   k,
				Value:   views.InputValueToString(v),
			}
			result = model.Create(&setting)
		} else {
			// 如果存在，更新记录
			result = model.Model(&setting).Update("value", views.InputValueToString(v))
		}

		if result.Error != nil {
			return c.ErrorJson(result.Error.Error())
		}

		// 删除缓存
		settingService.DeleteAdminSettingByFieldCache(c.Rds, adminInfo.ID, k)
	}

	return c.SuccessOk()
}
