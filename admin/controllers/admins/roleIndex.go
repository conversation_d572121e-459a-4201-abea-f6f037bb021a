package admins

import (
	"fmt"
	"strings"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	RoleIndexURL  = "/admins/role/index"
	RoleUpdateURL = "/admins/role/update"
	RoleCreateURL = "/admins/role/create"
	RoleDeleteURL = "/admins/role/delete"
)

// RoleIndexParams 角色列表参数
type RoleIndexParams struct {
	Name       string            `json:"name" views:"label:角色名称"`
	Pagination *model.Pagination `json:"pagination" views:"-"` // 分页参数
}

type RoleIndexRows struct {
	ID          uint                    `json:"id" column:"label:ID"`
	Name        string                  `json:"name" column:"label:角色名称"`
	Permissions []*views.CheckboxOption `json:"permissions" gorm:"-"`
	CreatedAt   string                  `json:"created_at" column:"label:创建时间;type:date"`
	UpdatedAt   string                  `json:"updated_at" column:"label:更新时间;type:date"`
}

// RoleIndex 管理角色列表
func RoleIndex(c *context.CustomCtx, bodyParams *RoleIndexParams) error {
	data := &model.IndexData{Items: make([]*RoleIndexRows, 0), Count: 0}

	query := model.NewModel().Like("name", bodyParams.Name).Model(&models.AuthItem{})
	//	如果当前管理员不是超级管理员，则只查询当前管理员创建的角色
	query.Where("name LIKE ?", "%"+fmt.Sprintf("(%v)", c.Claims.AdminID))
	query.Where("type = ?", models.AuthItemTypeRole).Count(&data.Count)
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	rbacService := service.NewAdminRbacService()
	roles, _ := rbacService.GetUserRoles(c.Claims.AdminID)
	for _, v := range data.Items.([]*RoleIndexRows) {
		v.Permissions, _ = rbacService.GetRolePermissionOptions(roles, []string{v.Name})
		v.Name = strings.Replace(v.Name, fmt.Sprintf("(%v)", c.Claims.AdminID), "", 1)
	}

	return c.SuccessJson(data)
}

// RoleIndexConfigure 管理角色配置
func RoleIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(RoleIndexURL)
	rbacService := service.NewAdminRbacService()
	roles, _ := rbacService.GetUserRoles(c.Claims.AdminID)
	permissions, _ := rbacService.GetRolePermissionOptions(roles, []string{})

	// 查询结构体, 自动转化成 inputs 设置表格数据
	searchForm := views.NewForm().Struct(RoleIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏添加
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增角色", RoleCreateURL, views.NewForm().Struct(RoleCreateParams{}).FlattenInputs().SetFieldInputParam("permissions", "default", permissions)))
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", RoleDeleteURL, views.NewForm().Struct(model.DeleteParams{})), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(RoleIndexRows{})

	// 更新
	updateForm := views.NewForm().Struct(RoleUpdateParams{}).FlattenInputs()
	updateForm.SetFieldInputParam("permissions", "default", permissions)
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", RoleUpdateURL, updateForm).SetSize(views.DialogSizeMedium))

	return vueTable
}
