package admins

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
)

const (
	MenuIndexURL      = "/admins/menu/index"
	MenuUpdateURL     = "/admins/menu/update"
	MenuUpdateDataURL = "/admins/menu/updateData"
)

// MenuIndexParams 菜单列表
type MenuIndexParams struct {
	Name       string            `json:"name" views:"label:名称"`
	Route      string            `json:"route" views:"label:路由"`
	Status     int               `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// MenuIndexRows 菜单列表
type MenuIndexRows struct {
	ID     uint                 `json:"id" column:"label:ID"`
	Name   string               `json:"name" column:"label:名称"`
	Route  string               `json:"route" column:"label:路由"`
	Sort   int                  `json:"sort" column:"label:排序"`
	Status int                  `json:"status" column:"label:状态;type:select"`
	Data   models.AdminMenuData `json:"data"`
}

// MenuIndex 菜单列表
func MenuIndex(c *context.CustomCtx, bodyParams *MenuIndexParams) error {
	data := &model.IndexData{Items: make([]*MenuIndexRows, 0), Count: 0}

	query := model.NewModel().Equal("name", bodyParams.Name).Equal("route", bodyParams.Route).Equal("status", bodyParams.Status).Model(&models.AdminMenu{})

	query.Count(&data.Count)
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}

// MenuIndexConfigure 菜单配置
func MenuIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(MenuIndexURL)
	statusOptions := []*views.SelectOption{
		{Label: "冻结", Value: models.AdminMenuStatusDisabled},
		{Label: "激活", Value: models.AdminMenuStatusEnabled},
	}
	vueTable.SetCommonOptions("status", statusOptions)

	searchForm := views.NewForm().Struct(MenuIndexParams{}).SetPagination(&model.Pagination{RowsPerPage: 100})
	vueTable.SetSearchs(searchForm)

	vueTable.StructColumns(MenuIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(vueTable.CommonOptions["status"])

	updateForm := views.NewForm().Struct(MenuUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", MenuUpdateURL, updateForm))

	updateDataForm := views.NewForm().Struct(MenuUpdateDataParams{}).FlattenInputs().SetChildFormFlattenInputs("data")
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("配置", MenuUpdateDataURL, updateDataForm).SetButtonColor(views.ColorInfo))
	return vueTable
}
