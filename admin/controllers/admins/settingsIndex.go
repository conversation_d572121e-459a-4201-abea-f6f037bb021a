package admins

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
)

const (
	SettingsIndexURL  = "/admins/settings/index"
	SettingsUpdateURL = "/admins/settings/update"
)

type SettingsIndexParams struct {
	GroupID    uint              `json:"groupId" views:"label:分组;type:select"`
	Name       string            `json:"name" views:"label:名称"`
	Field      string            `json:"field" views:"label:键名"`
	Pagination *model.Pagination `json:"pagination" views:"-"` // 分页参数
}

type SettingsIndexRows struct {
	ID             uint                    `json:"id" column:"label:ID"`
	Name           string                  `json:"name" column:"label:名称"`
	GroupID        uint                    `json:"groupId" column:"label:分组"`
	Field          string                  `json:"field" column:"label:键名"`
	Value          string                  `json:"value" column:"label:键值"`
	ValueInterface interface{}             `json:"valueInterface" gorm:"-"`
	Data           models.AdminSettingData `json:"data"`
}

// SettingsIndex 管理设置列表
func SettingsIndex(c *context.CustomCtx, bodyParams *SettingsIndexParams) error {
	data := &model.IndexData{Items: make([]*SettingsIndexRows, 0), Count: 0}

	query := model.NewModel().Equal("group_id", bodyParams.GroupID).Equal("name", bodyParams.Name).Equal("field", bodyParams.Field).
		Where("admin_id = ?", c.Claims.AdminID).Model(&models.AdminSetting{})
	if c.Claims.AdminID != models.SuperAdminID {
		query.Where("group_id > ?", models.AdminSettingGroupDefault)
	}

	query.Count(&data.Count)
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	for _, v := range data.Items.([]*SettingsIndexRows) {
		// 转换对应的数据
		v.ValueInterface = views.InputValueToInterface(v.Data.Input.Type, v.Value)
	}

	return c.SuccessJson(data)
}

// SettingsIndexConfigure 管理设置配置
func SettingsIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(SettingsIndexURL)
	vueTable.SetCommonOptions("groupId", models.AdminSettingGroupOptions)

	// 查询表单
	searchForm := views.NewForm().Struct(SettingsIndexParams{}).SetPagination(&model.Pagination{RowsPerPage: 100})
	vueTable.SetSearchs(searchForm)

	// 数据表格数据
	vueTable.StructColumns(SettingsIndexRows{})
	vueTable.GetFieldColumn("groupId").SetSelectFormat(vueTable.CommonOptions["groupId"])

	// 更新
	updateForm := views.NewForm().Struct(SettingsUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", SettingsUpdateURL, updateForm).SetSize(views.DialogSizeMedium))

	return vueTable
}
