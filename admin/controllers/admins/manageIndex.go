package admins

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	ManageIndexURL                 = "/admins/manage/index"
	ManageUpdateURL                = "/admins/manage/update"
	ManageDeleteURL                = "/admins/manage/delete"
	ManageCreateURL                = "/admins/manage/create"
	ManageUpdateDataURL            = "/admins/manage/update/data"
	ManageResetURL                 = "/admins/manage/reset"
	ManageUpdateMerchantSettingURL = "/admins/manage/update/merchant/setting"
)

// ManageIndexParams 管理列表参数
type ManageIndexParams struct {
	AdminID    uint                   `json:"adminId" views:"label:管理;type:select"`        // 管理ID
	Username   string                 `json:"username" views:"label:账户"`                   // 用户名
	Nickname   string                 `json:"nickname" views:"label:昵称"`                   // 昵称
	Email      string                 `json:"email" views:"label:邮箱"`                      // 邮箱
	Telephone  string                 `json:"telephone" views:"label:号码"`                  // 号码
	Domains    string                 `json:"domains" views:"label:域名"`                    // 绑定的域名
	Status     int                    `json:"status" views:"label:状态;type:select"`         // 状态 -1冻结 10激活
	UpdatedAt  *model.RangeDatePicker `json:"updatedAt" views:"label:活跃时间;type:dateRange"` // 活跃时间
	Pagination *model.Pagination      `json:"pagination" views:"-"`                        // 分页参数
}

// ManageIndexRows 管理列表数据
type ManageIndexRows struct {
	models.AdminUserDisplayData
	Role     string                 `json:"role" column:"label:角色;after:nickname"`
	Merchant map[string]interface{} `json:"merchant" gorm:"-"`
}

// ManageIndex 管理列表
func ManageIndex(c *context.CustomCtx, bodyParams *ManageIndexParams) error {
	data := &model.IndexData{Items: make([]*ManageIndexRows, 0), Count: 0}

	query := model.NewModel().Equal("admin_user.id", bodyParams.AdminID).Equal("admin_user.username", bodyParams.Username).
		Like("admin_user.nickname", bodyParams.Nickname).
		Like("admin_user.email", bodyParams.Email).Like("admin_user.telephone", bodyParams.Telephone).
		Like("admin_user.domains", bodyParams.Domains).
		Equal("admin_user.status", bodyParams.Status).
		BetweenTime("admin_user.updated_at", bodyParams.UpdatedAt, c.TimeZone).Model(&models.AdminUser{})

	query.Where("admin_user.parent_id = ?", c.Claims.AdminID).Count(&data.Count)

	// 获取角色
	query.Select("admin_user.*, auth_assignment.name as role").
		Joins("LEFT JOIN auth_assignment ON admin_user.id = auth_assignment.admin_id").
		Scopes(bodyParams.Pagination.Scopes()).
		Find(&data.Items)

	for _, v := range data.Items.([]*ManageIndexRows) {
		// 获取商户配置信息
		if v.ParentID == models.SuperAdminID {
			v.Merchant = service.NewAdminSettingService().GetMerchantGroupSettingData(c.Rds, v.ID, models.AdminSettingGroupDefault)
		}
	}

	return c.SuccessJson(data)
}

// ManageIndexConfigure 管理配置
func ManageIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	//	生成配置文件
	vueTable := views.NewTable(ManageIndexURL)
	statusOptions := []*views.SelectOption{
		{Label: "冻结", Value: models.AdminUserStatusDisabled},
		{Label: "激活", Value: models.AdminUserStatusEnabled},
	}
	adminsOptions := service.NewAdminUserService().GetSubAdminOptions(c.Claims.AdminID)
	rolesOptions := service.NewAdminRbacService().GetSubRoleOptions(c.Claims.AdminID)

	// 设置选择框Options
	vueTable.SetCommonOptions("status", statusOptions).SetCommonOptions("adminId", adminsOptions).SetCommonOptions("role", rolesOptions)

	// 查询结构体, 自动转化成 inputs 设置表格数据
	searchForm := views.NewForm().Struct(ManageIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏添加
	createForm := views.NewForm().Struct(ManageCreateParams{}).FlattenInputs()
	if len(rolesOptions) > 0 {
		createForm.SetFieldInputParam("role", "default", rolesOptions[0].Value)
	}
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增管理", ManageCreateURL, createForm))

	deleteForm := views.NewForm().Struct(model.DeleteParams{})
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", ManageDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(ManageIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(vueTable.CommonOptions["status"])

	// 管理更新
	updateOptionsForm := views.NewForm().Struct(ManageUpdateParams{}).FlattenInputs().ResetInputs([][]string{
		{"id"},
		{"avatar"},
		{"nickname", "email"},
		{"password", "securityKey"},
		{"chatUrl", "expiredAt"},
		{"status", "role"},
		{"domains"},
	})
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", ManageUpdateURL, updateOptionsForm).SetSize(views.DialogSizeMedium))

	// 管理配置
	updateDataOptionsForm := views.NewForm().Struct(ManageUpdateDataParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("配置", ManageUpdateDataURL, updateDataOptionsForm).SetButtonColor(views.ColorInfo))

	// 数据表格操作 - 商户配置
	if c.Claims.AdminID == models.SuperAdminID {
		updateMerchantForm := views.NewForm().Struct(ManageUpdateMerchantSettingParams{})
		merchantChildrenForm := views.NewForm().AddInputs(0, service.NewAdminSettingService().
			GetMerchantGroupSettingForm(c.Rds, models.SuperAdminID, models.AdminSettingGroupDefault)...).FlattenInputs()
		updateMerchantForm.AddChildForm("settings", merchantChildrenForm)
		vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("商户", ManageUpdateMerchantSettingURL, updateMerchantForm).SetButtonColor(views.ColorAccent))

		resetMerchantForm := views.NewForm().Struct(ManageResetParams{})
		vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("重置", ManageResetURL, resetMerchantForm).
			SetSmall("删除商户所有数据, 恢复到初始状态, 切记!!! 删除之后数据不可恢复~").
			SetButtonColor(views.ColorAccent))
	}

	return vueTable
}
