package admins

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	LogsIndexURL = "/admins/logs/index"
)

// LogsIndexParams 操作日志列表参数
type LogsIndexParams struct {
	AdminID    uint                   `json:"adminId" views:"label:管理;type:select"`
	IP         string                 `json:"ip" views:"label:IP4"`
	Action     string                 `json:"action" views:"label:操作"`
	Route      string                 `json:"route" views:"label:路由"`
	Method     string                 `json:"method" views:"label:方法"`
	Params     string                 `json:"params" views:"label:参数"`
	CreatedAt  *model.RangeDatePicker `json:"createdAt" views:"label:时间;type:dateRange"`
	Pagination *model.Pagination      `json:"pagination" views:"-"` // 分页参数
}

// LogsIndexRows 操作日志列表数据
type LogsIndexRows struct {
	ID        uint   `json:"id" column:"label:ID"`
	AdminID   uint   `json:"adminId" column:"label:管理;type:select"`
	IP        string `json:"ip" column:"label:IP4"`
	Action    string `json:"action" column:"label:操作"`
	Route     string `json:"route" column:"label:路由"`
	Method    string `json:"method" column:"label:方法"`
	Params    string `json:"params" column:"label:参数"`
	CreatedAt string `json:"createdAt" column:"label:时间"`
}

// LogsIndex 操作日志列表
func LogsIndex(c *context.CustomCtx, bodyParams *LogsIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	data := &model.IndexData{Items: make([]*LogsIndexRows, 0), Count: 0}

	query := model.NewModel().
		Equal("admin_id", bodyParams.AdminID).
		Equal("ip", bodyParams.IP).
		Equal("action", bodyParams.Action).
		Equal("route", bodyParams.Route).
		Equal("method", bodyParams.Method).
		Like("params", bodyParams.Params).
		BetweenTime("created_at", bodyParams.CreatedAt, c.TimeZone).
		Model(&models.AdminLogs{})

	query.Where("admin_id IN ?", subAdminIDs).Count(&data.Count)
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}

// LogsIndexConfigure 操作日志列表配置
func LogsIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(LogsIndexURL)

	adminService := service.NewAdminUserService()
	adminsOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)

	vueTable.SetCommonOptions("adminId", adminsOptions)

	// 查询结构体, 自动转化成 inputs 设置表格数据
	searchForm := views.NewForm().Struct(LogsIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 数据表格数据
	vueTable.StructColumns(LogsIndexRows{})
	vueTable.GetFieldColumn("adminId").SetSelectFormat(vueTable.CommonOptions["adminId"])

	return vueTable
}
