package admins

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// RoleCreateParams 新增角色参数
type RoleCreateParams struct {
	Name        string                  `json:"name" validate:"required" views:"label:角色名称"`
	Permissions []*views.CheckboxOption `json:"permissions" validate:"required" views:"label:权限列表;type:checkbox"`
}

// RoleCreate 新增角色
func RoleCreate(c *context.CustomCtx, bodyParams *RoleCreateParams) error {
	rbacService := service.NewAdminRbacService()

	// 检测当前选择的权限是否在当前管理员权限范围内
	err := rbacService.CheckUserPermissions(c.Claims.AdminID, bodyParams.Permissions)
	if err != nil {
		return c.ErrorJson(err.Error())
	}

	roleInfo := models.AuthItem{}
	model.NewModel().Where("type = ? AND name = ?", models.AuthItemTypeRole, fmt.Sprintf(models.AdminRoleNamePrefix, bodyParams.Name, c.Claims.AdminID)).Find(&roleInfo)
	if roleInfo.ID != 0 {
		return c.ErrorJson("角色名称已存在")
	}

	err = model.NewModel().Transaction(func(tx *gorm.DB) error {
		// 创建当前角色
		result := tx.Create(&models.AuthItem{
			Name: fmt.Sprintf(models.AdminRoleNamePrefix, bodyParams.Name, c.Claims.AdminID),
			Type: models.AuthItemTypeRole,
		})
		if result.Error != nil {
			return result.Error
		}

		// 创建当前管理关联角色
		result = tx.Create(&models.AuthChild{
			Parent: fmt.Sprintf(models.AdminRolesPrefix, c.Claims.AdminID),
			Child:  fmt.Sprintf(models.AdminRoleNamePrefix, bodyParams.Name, c.Claims.AdminID),
			Type:   models.AuthChildTypeRoleRole,
		})
		if result.Error != nil {
			return result.Error
		}

		// 添加对应权限
		for _, permission := range bodyParams.Permissions {
			if permission.Checked {
				result = tx.Create(&models.AuthChild{
					Parent: fmt.Sprintf(models.AdminRoleNamePrefix, bodyParams.Name, c.Claims.AdminID),
					Child:  permission.Value.(string),
					Type:   models.AuthChildTypeRolePermission,
				})
				if result.Error != nil {
					return result.Error
				}
			}
		}
		return nil
	})
	if err != nil {
		return c.ErrorJson(err.Error())
	}
	return c.SuccessOk()
}
