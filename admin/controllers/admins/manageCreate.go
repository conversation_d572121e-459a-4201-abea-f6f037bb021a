package admins

import (
	"fmt"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"

	"github.com/pquerna/otp/totp"
	"gorm.io/gorm"
)

// ManageCreateParams 新增管理参数
type ManageCreateParams struct {
	Username string `json:"username" validate:"required" views:"label:账户"`
	Password string `json:"password" validate:"required" views:"label:密码"`
	Role     string `json:"role" validate:"required" views:"label:角色;type:select"`
}

// 新增管理
func ManageCreate(c *context.CustomCtx, bodyParams *ManageCreateParams) error {
	rbacService := service.NewAdminRbacService()
	if !rbacService.CheckRoleMatchesSubRoles(c.Claims.AdminID, bodyParams.Role) {
		return c.ErrorJson("角色不存在")
	}

	// 判断当前商户是否还能添加代理
	currentAdminInfo := &models.AdminUser{}
	model.NewModel().Where("id = ?", c.Claims.AdminID).Find(currentAdminInfo)
	if currentAdminInfo.ParentID != 0 && currentAdminInfo.ParentID != models.SuperAdminID {
		return c.ErrorJson("没有权限添加代理")
	}

	// 如果是商户管理员, 检查代理数量
	if currentAdminInfo.ParentID == models.SuperAdminID {
		var currentAgentNums int64
		model.NewModel().Where("parent_id = ?", c.Claims.AdminID).Count(&currentAgentNums)
		maxAgentNums, err := service.NewAdminSettingService().GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, "merchantAgentNums").ToInt()
		if err != nil {
			return c.ErrorJson(err.Error())
		}

		if currentAgentNums >= int64(maxAgentNums) {
			return c.ErrorJson("超过可设置的代理数量, 请升级账户权限")
		}
	}

	err := model.NewModel().Transaction(func(tx *gorm.DB) error {
		adminKey, _ := totp.Generate(totp.GenerateOpts{
			Issuer:      "八戒科技",
			AccountName: bodyParams.Username,
		})
		adminInfo := &models.AdminUser{
			ParentID:    c.Claims.AdminID,
			Username:    bodyParams.Username,
			Nickname:    bodyParams.Username,
			Avatar:      "/avatar.png",
			Password:    utils.EncryptPassword(bodyParams.Password),
			SecurityKey: utils.EncryptPassword(bodyParams.Password),
			ExpiredAt:   time.Now().Add(30 * 24 * time.Hour),
			Data: models.AdminUserData{
				Auth: models.AdminUserDataAuth{
					Key:    adminKey.Secret(),
					Enable: false,
				},
			},
		}
		result := tx.Create(&adminInfo)
		if result.Error != nil {
			return fmt.Errorf("创建管理员失败: %v", result.Error)
		}

		if c.Claims.AdminID == models.SuperAdminID {
			merchantService := service.NewMerchantService()
			merchantService.InitMerchantData(tx, adminInfo.ID)
		}
		// 新增管理角色
		result = tx.Create(&models.AuthAssignment{Name: bodyParams.Role, AdminId: adminInfo.ID})
		if result.Error != nil {
			return fmt.Errorf("创建管理角色失败: %v", result.Error)
		}
		return nil
	})
	if err != nil {
		return c.ErrorJson(err.Error())
	}

	// 删除管理子级缓存
	service.NewAdminUserService().DeleteAdminSubadminIDsCache(c.Rds, c.Claims.AdminID)
	return c.SuccessOk()
}
