package admins

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

type RoleUpdateParams struct {
	ID          int64                   `json:"id" validate:"required" views:"label:ID;display:true"`
	Name        string                  `json:"name" views:"label:角色名称"`
	Permissions []*views.CheckboxOption `json:"permissions" views:"label:权限列表;type:checkbox"`
}

// RoleUpdate 更新角色
func RoleUpdate(c *context.CustomCtx, bodyParams *RoleUpdateParams) error {
	db := model.NewModel()
	roleInfo := models.AuthItem{}

	result := db.Where("id = ?", bodyParams.ID).Where("type = ?", models.AuthItemTypeRole).Where("name LIKE ?", "%"+fmt.Sprintf("(%v)", c.Claims.AdminID)).First(&roleInfo)
	if result.Error != nil {
		return c.ErrorJson("找不到可操作的角色")
	}
	oldName := roleInfo.Name

	err := db.Transaction(func(tx *gorm.DB) error {
		// 更新角色名称
		if bodyParams.Name != "" {
			roleInfo.Name = fmt.Sprintf(models.AdminRoleNamePrefix, bodyParams.Name, c.Claims.AdminID)
			result = tx.Model(&roleInfo).Update("name", roleInfo.Name)
			if result.Error != nil {
				return result.Error
			}

			// 更新授权给管理员的角色名称
			result = tx.Model(&models.AuthAssignment{}).Where("name = ?", oldName).Update("name", roleInfo.Name)
			if result.Error != nil {
				return result.Error
			}
		}

		if bodyParams.Permissions != nil || len(bodyParams.Permissions) > 0 {
			// 获取当前角色对应的权限
			rbacService := service.NewAdminRbacService()
			err := rbacService.CheckUserPermissions(c.Claims.AdminID, bodyParams.Permissions)
			if err != nil {
				return c.ErrorJson(err.Error())
			}

			//	删除关联关系
			result = tx.Where("parent = ?", oldName).Unscoped().Delete(&models.AuthChild{})
			if result.Error != nil {
				return c.ErrorJson(result.Error.Error())
			}

			// 添加对应权限
			for _, permission := range bodyParams.Permissions {
				if permission.Checked {
					result = tx.Create(&models.AuthChild{
						Parent: roleInfo.Name,
						Child:  permission.Value.(string),
						Type:   models.AuthChildTypeRolePermission,
					})
					if result.Error != nil {
						return result.Error
					}
				}
			}
		}

		return nil
	})
	if err != nil {
		return c.ErrorJson(err.Error())
	}

	// 删除角色对应的权限缓存
	_ = service.NewAdminRbacService().DeleteRolePermissionsCache(c.Rds, oldName)
	return c.SuccessOk()
}
