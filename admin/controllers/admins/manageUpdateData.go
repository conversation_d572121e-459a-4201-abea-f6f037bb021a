package admins

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// 管理更新数据 管理更新数据参数
type ManageUpdateDataParams struct {
	ID   uint                      `json:"id" validate:"required" views:"label:ID;display:true"`
	Auth *models.AdminUserDataAuth `json:"auth" views:"label:Google验证器配置;type:struct;scanField:data.auth"`
}

// ManageUpdateData 管理更新数据
func ManageUpdateData(c *context.CustomCtx, bodyParams *ManageUpdateDataParams) error {
	subAdminIds, _ := service.NewAdminUserService().GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	adminInfo := models.AdminUser{}
	model.NewModel().Where("id = ?", bodyParams.ID).Where("id IN ?", subAdminIds).Find(&adminInfo)
	if adminInfo.ID == 0 {
		return c.ErrorJson("没有可更新的管理参数")
	}

	if bodyParams.Auth != nil {
		adminInfo.Data.Auth = *bodyParams.Auth
	}

	result := model.NewModel().Updates(&adminInfo)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	return c.SuccessOk()
}
