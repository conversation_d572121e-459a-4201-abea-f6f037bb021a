package admins

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

type SettingsUpdateParams struct {
	ID             uint        `json:"id" validate:"required" views:"label:ID;display:true"`
	ValueInterface interface{} `json:"valueInterface" views:"label:配置;type:dynamic;mask:data"`
}

// 管理配置更新
func SettingsUpdate(c *context.CustomCtx, bodyParams *SettingsUpdateParams) error {
	settingInfo := models.AdminSetting{}
	db := model.NewModel()
	result := db.Model(&settingInfo).Where("id = ?", bodyParams.ID).First(&settingInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("找不到可修改设置 %s", result.Error.Error()))
	}

	// 保存数据
	settingInfo.Value = views.InputValueToString(bodyParams.ValueInterface)
	db.Save(&settingInfo)

	// 清楚缓存
	adminSettingService := service.NewAdminSettingService()
	adminSettingService.DeleteAdminSettingByFieldCache(c.Rds, c.Claims.AdminID, settingInfo.Field)

	return c.SuccessOk()
}
