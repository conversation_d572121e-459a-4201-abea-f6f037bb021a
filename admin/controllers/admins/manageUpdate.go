package admins

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// ManageUpdateParams 更新管理参数
type ManageUpdateParams struct {
	ID          uint                     `json:"id" validate:"required" gorm:"-" views:"label:ID;display:true"`
	Avatar      string                   `json:"avatar" views:"label:头像;type:image"`
	Nickname    string                   `json:"nickname" views:"label:昵称"`
	Email       string                   `json:"email" views:"label:邮箱"`
	Password    model.GormPasswordParams `json:"password" views:"label:密码"`
	SecurityKey model.GormPasswordParams `json:"securityKey" views:"label:提现密码"`
	Domains     string                   `json:"domains" views:"label:绑定域名;type:textarea"`
	ChatURL     *string                  `json:"chatUrl" views:"label:客服链接"`
	Status      int8                     `json:"status" gorm:"-" views:"label:状态;type:select"`
	Role        string                   `json:"role" views:"label:角色;type:select"`
	ExpiredAt   model.GormDateTimeParams `json:"expiredAt" views:"label:过期时间;type:dateTime"`
}

// ManageUpdate 更新管理
func ManageUpdate(c *context.CustomCtx, bodyParams *ManageUpdateParams) error {
	// 如果 过期时间不为空, 那么重组
	bodyParams.ExpiredAt = model.FormatDateTimeToRFC3339(bodyParams.ExpiredAt, c.TimeZone)

	adminInfo := models.AdminUser{}
	adminService := service.NewAdminUserService()
	subAdminIds, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	model.NewModel().Where("id = ?", bodyParams.ID).Where("id IN ?", subAdminIds).Find(&adminInfo)
	if adminInfo.ID == 0 {
		return c.ErrorJson("没有可更新的管理参数")
	}

	// 域名不能重复的
	err := adminService.CheckDomainValidity(c.Rds, adminInfo, bodyParams.Domains)
	if err != nil {
		return c.ErrorJson(err.Error())
	}

	if bodyParams.Role != "" {
		rbacService := service.NewAdminRbacService()
		if !rbacService.CheckRoleMatchesSubRoles(c.Claims.MerchantID, bodyParams.Role) {
			return c.ErrorJson("角色不存在")
		}

		// 更新当前管理角色, 并且删除缓存
		result := model.NewModel().Where("admin_id = ?", bodyParams.ID).Updates(&models.AuthAssignment{Name: bodyParams.Role})
		if result.Error != nil {
			return c.ErrorJson(result.Error.Error())
		}
		// 删除管理的权限缓存
		rbacService.DeleteAdminPermissionsCache(c.Rds, bodyParams.ID)
	}

	// 只能修改自己底部的管理
	result := model.NewModel().Model(&models.AdminUser{}).Where("id = ?", adminInfo.ID).Updates(bodyParams)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	// 删除管理客服连接缓存
	if bodyParams.ChatURL != nil {
		_ = adminService.DeleteAdminUserByChatURL(c.Rds, adminInfo.ID)
	}

	return c.SuccessOk()
}
