package admins

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// MenuUpdateParams 更新菜单参数
type MenuUpdateParams struct {
	ID     uint   `json:"id" validate:"required" views:"label:菜单ID;type:number;display:true"`
	Name   string `json:"name" views:"label:名称"`
	Route  string `json:"route" views:"label:路由"`
	Sort   uint8  `json:"sort" views:"label:排序;type:number"`
	Status int8   `json:"status" views:"label:状态;type:select"`
}

// MenuUpdate 更新菜单
func MenuUpdate(c *context.CustomCtx, bodyParams *MenuUpdateParams) error {
	// 查询当前菜单是否存在
	menuInfo := models.AdminMenu{}
	db := model.NewModel()

	result := db.Model(&menuInfo).Where("id = ?", bodyParams.ID).First(&menuInfo)
	if result.Error != nil {
		return c.ErrorJson("菜单不存在")
	}

	menuInfo.Name = bodyParams.Name
	menuInfo.Route = bodyParams.Route
	menuInfo.Sort = bodyParams.Sort
	menuInfo.Status = bodyParams.Status

	result = db.Model(&menuInfo).Save(&menuInfo)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	if menuInfo.ParentID == 0 {
		result = db.Model(&menuInfo).UpdateColumn("route", nil)
		if result.Error != nil {
			return c.ErrorJson(result.Error.Error())
		}
	}

	// 删除所有管理菜单缓存
	service.NewAdminMenuService().DeleteAdminMenuTreeAllCache(c.Rds)
	return c.SuccessOk()
}
