package admins

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// RoleDelete 删除角色
func RoleDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	db := model.NewModel()
	err := db.Transaction(func(tx *gorm.DB) error {
		for _, id := range bodyParams.Ids {
			roleInfo := models.AuthItem{}
			result := tx.Where("id = ?", id).Where("type = ?", models.AuthItemTypeRole).Where("name LIKE ?", "%"+fmt.Sprintf("(%v)", c.Claims.AdminID)).First(&roleInfo)
			if result.Error != nil {
				return fmt.Errorf("找不到可操作的角色 [%v]", id)
			}

			// 不能删除默认角色
			if roleInfo.Name == models.AuthItemRoleSuperAdmin || roleInfo.Name == models.AuthItemRoleMerchantAdmin || roleInfo.Name == models.AuthItemRoleAgentAdmin || roleInfo.Name == models.AuthItemRoleFinanceAdmin || roleInfo.Name == models.AuthItemRoleBackupAdmin {
				return fmt.Errorf("不能删除默认角色 [%v]", roleInfo.Name)
			}

			//	只能删除跟自己相关角色
			result = tx.Where("id = ?", roleInfo.ID).Where("type = ?", models.AuthItemTypeRole).Unscoped().Delete(&models.AuthItem{})
			if result.Error != nil {
				return result.Error
			}

			// 删除对应的关联关系
			result = tx.Where("parent LIKE ?", "%"+fmt.Sprintf("(%v)", c.Claims.AdminID)+"%").Where("child = ?", roleInfo.Name).Where("type = ?", models.AuthChildTypeRoleRole).Unscoped().Delete(&models.AuthChild{})
			if result.Error != nil {
				return result.Error
			}

			//	删除关联关系
			result = tx.Where("parent = ?", roleInfo.Name).Where("type = ?", models.AuthChildTypeRolePermission).Unscoped().Delete(&models.AuthChild{})
			if result.Error != nil {
				return result.Error
			}

			// 删除管理员与角色的关联关系
			result = tx.Where("name = ?", roleInfo.Name).Unscoped().Delete(&models.AuthAssignment{})
			if result.Error != nil {
				return result.Error
			}

			// 删除角色对应的权限缓存
			_ = service.NewAdminRbacService().DeleteRolePermissionsCache(c.Rds, roleInfo.Name)
		}
		return nil
	})
	if err != nil {
		return c.ErrorJson(err.Error())
	}

	return c.SuccessOk()
}
