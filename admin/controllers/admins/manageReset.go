package admins

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// ManageResetParams 重置商户数据参数
type ManageResetParams struct {
	ID uint `json:"id" validate:"required" views:"label:ID;display:true"`
}

// ManageReset 重置商户数据
func ManageReset(c *context.CustomCtx, bodyParams *ManageResetParams) error {
	adminService := service.NewAdminUserService()
	db := model.NewModel()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	adminInfo := models.AdminUser{}
	result := db.Model(&models.AdminUser{}).Where("id = ?", bodyParams.ID).Where("id IN (?)", subAdminIDs).First(&adminInfo)
	if result.Error != nil {
		return c.<PERSON>rror<PERSON>son(result.Error.Error())
	}

	if adminInfo.ParentID != models.SuperAdminID {
		return c.ErrorJson("非商户管理员无法重置")
	}

	merchantService := service.NewMerchantService()

	err := merchantService.ResetMerchantData(c.Rds, adminInfo.ID)
	if err != nil {
		return c.ErrorJson(err.Error())
	}

	return c.SuccessOk()
}
