package systems

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"

	"gorm.io/gorm"
)

// ArticleCreateParams 新增文章参数
type ArticleCreateParams struct {
	Type    int8   `json:"type" validate:"required" views:"label:类型;type:select"`
	Title   string `json:"title" validate:"required" views:"label:标题"`
	Content string `json:"content" validate:"required" views:"label:内容;type:editor"`
}

// ArticleCreate 新增文章
func ArticleCreate(c *context.CustomCtx, bodyParams *ArticleCreateParams) error {
	db := model.NewModel()

	articleInfo := &models.Article{
		AdminID: c.Claims.AdminID,
		Type:    bodyParams.Type,
		Title:   bodyParams.Title,
		Content: bodyParams.Content,
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		articleInfo.Symbol = utils.GenerateRandomString(10)
		result := tx.Create(articleInfo)
		if result.Error != nil {
			return fmt.Errorf("创建文章失败: %s", result.Error.Error())
		}

		// 更新文章
		newArticle := &models.Article{
			Title:   fmt.Sprintf(models.ArticleTitleTranslatePrefix, articleInfo.Symbol),
			Content: fmt.Sprintf(models.ArticleContentTranslatePrefix, articleInfo.Symbol),
		}
		result = tx.Where("id = ?", articleInfo.ID).Updates(newArticle)
		if result.Error != nil {
			return fmt.Errorf("更新文章翻译失败: %s", result.Error.Error())
		}

		// 添加文章翻译
		translates := make([]models.Translate, 0)
		translates = append(translates, models.Translate{
			AdminID: c.Claims.AdminID,
			Lang:    models.DefaultLang,
			Type:    models.TranslateTypeSystem,
			Name:    fmt.Sprintf("文章[%s]标题", articleInfo.Title),
			Field:   fmt.Sprintf(models.ArticleTitleTranslatePrefix, articleInfo.Symbol),
			Value:   articleInfo.Title,
		})
		translates = append(translates, models.Translate{
			AdminID: c.Claims.AdminID,
			Lang:    models.DefaultLang,
			Type:    models.TranslateTypeSystem,
			Name:    fmt.Sprintf("文章[%s]内容", articleInfo.Title),
			Field:   fmt.Sprintf("articleContent_%s", articleInfo.Symbol),
			Value:   articleInfo.Content,
		})
		result = tx.Create(&translates)
		if result.Error != nil {
			return fmt.Errorf("创建文章翻译失败: %s", result.Error.Error())
		}
		return nil
	})
	if err != nil {
		return c.ErrorJson(fmt.Sprintf("创建文章失败: %s", err.Error()))
	}

	if articleInfo.Type == models.ArticleTypeHelper {
		articleService := service.NewSystemArticleService()
		err := articleService.UpdateHelpers(c.Rds, c.Claims.AdminID)
		if err != nil {
			return c.ErrorJson(fmt.Sprintf("更新帮助文章失败: %s", err.Error()))
		}
	}

	return c.SuccessOk()
}
