package systems

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// ArticleDelete 删除文章
func ArticleDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	db := model.NewModel()

	for _, v := range bodyParams.Ids {
		article := models.Article{}
		result := db.Where("id = ?", v).Where("admin_id = ?", c.Claims.AdminID).First(&article)
		if result.Error != nil {
			return fmt.Errorf("获取文章失败: %s", result.Error.Error())
		}

		result = db.Unscoped().Where("id = ?", article.ID).Delete(&article)
		if result.Error != nil {
			return fmt.Errorf("删除文章失败: %s", result.Error.Error())
		}

		// 如果是帮助文棒, 那么修改 管理设置
		if article.Type == models.ArticleTypeHelper {
			articleService := service.NewSystemArticleService()
			err := articleService.UpdateHelpers(c.Rds, c.Claims.AdminID)
			if err != nil {
				return c.<PERSON>(fmt.Sprintf("更新帮助文章失败: %s", err.Error()))
			}
		}

		// 删除文章翻译
		db.Unscoped().Where("type = ?", models.TranslateTypeSystem).Where("field = ?", fmt.Sprintf(models.ArticleTitleTranslatePrefix, article.Symbol)).Delete(&models.Translate{})
		db.Unscoped().Where("type = ?", models.TranslateTypeSystem).Where("field = ?", fmt.Sprintf(models.ArticleContentTranslatePrefix, article.Symbol)).Delete(&models.Translate{})
	}

	return c.SuccessOk()
}
