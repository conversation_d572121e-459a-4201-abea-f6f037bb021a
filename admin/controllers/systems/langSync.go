package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// LangSyncParams 语言同步参数
type LangSyncParams struct {
	ID uint `json:"id" validate:"required" views:"label:ID;display:true"`
}

// LangSync 同步语言
func LangSync(c *context.CustomCtx, bodyParams *LangSyncParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	db := model.NewModel()
	langInfo := models.Lang{}
	result := db.Where("id = ?", bodyParams.ID).Where("admin_id IN ?", subAdminIDs).First(&langInfo)
	if result.Error != nil {
		return c.ErrorJson("没有找到语言信息")
	}

	// 获取超级管理的语言翻译
	translateList := make([]*models.Translate, 0)
	result = db.Model(&models.Translate{}).Where("admin_id = ?", models.SuperAdminID).Where("lang = ?", langInfo.Symbol).Find(&translateList)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	for _, translate := range translateList {
		currentTranslate := models.Translate{}
		db.Model(&models.Translate{}).Where("admin_id = ?", langInfo.AdminID).Where("field = ?", translate.Field).Where("lang = ?", langInfo.Symbol).Find(&currentTranslate)

		// 插入没有插入的数据
		if currentTranslate.ID == 0 {
			translate.ID = 0
			translate.AdminID = langInfo.AdminID
			result = model.NewModel().Create(translate)
			if result.Error != nil {
				return c.ErrorJson(result.Error.Error())
			}
		}
	}

	// 删除语言翻译缓存
	service.NewTranslateService().DeleteAllTranslateCache(c.Rds, langInfo.AdminID)

	// 删除前台菜单缓存
	systemMenuService := service.NewSystemMenuService()
	systemMenuService.DeleteFrontendMenusAllCacheByLang(c.Rds, langInfo.AdminID, langInfo.Symbol)

	return c.SuccessOk()
}
