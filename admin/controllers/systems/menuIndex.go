package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	MenuIndexURL      = "/systems/menu/index"
	MenuUpdateURL     = "/systems/menu/update"
	MenuUpdateDataURL = "/systems/menu/updateData"
	MenuDeleteURL     = "/systems/menu/delete"
	MenuCreateURL     = "/systems/menu/create"
)

type MenuIndexParams struct {
	Name       string            `json:"name" views:"label:名称"`
	Route      string            `json:"route" views:"label:路由"`
	ParentID   uint              `json:"parentId" views:"label:父级;type:select"`
	Type       int8              `json:"type" views:"label:类型;type:select"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// MenuIndexRows 菜单列表行
type MenuIndexRows struct {
	ID       uint            `json:"id" column:"label:ID"`
	ParentID uint            `json:"parentId" column:"label:父级"`
	Name     string          `json:"name" column:"label:名称"`
	Route    string          `json:"route" column:"label:路由"`
	Sort     uint8           `json:"sort" column:"label:排序"`
	Type     int8            `json:"type" column:"label:类型;type:select"`
	Status   int8            `json:"status" column:"label:状态;type:select"`
	Data     models.MenuData `json:"data"`
}

// MenuIndex 菜单列表
func MenuIndex(c *context.CustomCtx, bodyParams *MenuIndexParams) error {
	data := &model.IndexData{Items: make([]*MenuIndexRows, 0), Count: 0}

	query := model.NewModel().
		Equal("parent_id", bodyParams.ParentID).
		Like("name", bodyParams.Name).
		Equal("route", bodyParams.Route).
		Equal("type", bodyParams.Type).
		Equal("status", bodyParams.Status).
		Model(&models.Menu{})

	query.Where("admin_id = ?", c.Claims.AdminID).Count(&data.Count)
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}

// MenuIndexConfigure 菜单列表配置
func MenuIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(MenuIndexURL)

	systemMenuService := service.NewSystemMenuService()
	parentMenuOptions, _ := systemMenuService.GetParentMenuOptions(c.Claims.AdminID)

	statusOptions := []*views.SelectOption{
		{Label: "启用", Value: models.MenuStatusEnabled},
		{Label: "禁用", Value: models.MenuStatusDisabled},
	}
	typeOptions := []*views.SelectOption{
		{Label: "移动菜单导航", Value: models.MenuTypeMobileNavigationBar},
		{Label: "电脑菜单导航", Value: models.MenuTypeDesktopNavigationBar},
		{Label: "用户更多菜单", Value: models.MenuTypeCommonUserMoreMenu},
		{Label: "公用钱包菜单", Value: models.MenuTypeCommonWalletMenu},
		{Label: "用户更多菜单", Value: models.MenuTypeCommonUserMenu},
		{Label: "公用更多菜单", Value: models.MenuTypeCommonMoreMenu},
	}
	targetOptions := []*views.SelectOption{
		{Label: "当前窗口", Value: models.MenuTargetSelf},
		{Label: "新窗口", Value: models.MenuTargetBlank},
	}

	vueTable.SetCommonOptions("type", typeOptions).SetCommonOptions("status", statusOptions).SetCommonOptions("parentId", parentMenuOptions).SetCommonOptions("target", targetOptions)

	searchForm := views.NewForm().Struct(MenuIndexParams{}).SetPagination(&model.Pagination{RowsPerPage: 100, SortBy: "status"})
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(MenuCreateParams{}).FlattenInputs()
	createForm.SetFieldInputParam("parentId", "default", parentMenuOptions[0].Value)
	createForm.SetFieldInputParam("type", "default", typeOptions[0].Value)
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增菜单", MenuCreateURL, createForm))

	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", MenuDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(MenuIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(vueTable.CommonOptions["status"])
	vueTable.GetFieldColumn("type").SetSelectFormat(vueTable.CommonOptions["type"])
	vueTable.GetFieldColumn("parentId").SetSelectFormat(vueTable.CommonOptions["parentId"])

	// 更新
	updateForm := views.NewForm().Struct(MenuUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", MenuUpdateURL, updateForm))

	updateDataForm := views.NewForm().Struct(MenuUpdateDataParams{}).FlattenInputs().SetChildFormFlattenInputs("data").ResetChildrenFormInputs("data", [][]string{
		{"target"}, {"label"}, {"class"},
		{"darkIcon", "lightIcon", "activeIcon"},
	})
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("配置", MenuUpdateDataURL, updateDataForm))

	return vueTable
}
