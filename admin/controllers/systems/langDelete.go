package systems

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// LangDelete 删除语言
func LangDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	langService := service.NewSystemLangService()

	db := model.NewModel()

	for _, v := range bodyParams.Ids {
		lang := models.Lang{}
		db.Where("id = ?", v).Where("admin_id = ?", c.Claims.AdminID).First(&lang)
		if lang.ID == 0 {
			return c.<PERSON><PERSON>r<PERSON><PERSON>(fmt.Sprintf("语言不存在: %d", v))
		}

		result := db.Unscoped().Where("id = ?", lang.ID).Delete(&models.Lang{})
		if result.Error != nil {
			return c.<PERSON><PERSON><PERSON><PERSON><PERSON>(fmt.Sprintf("删除失败: %s", result.Error.Error()))
		}

		// 删除语言缓存
		langService.DeleteAdminLanguagesCache(c.Rds, lang.AdminID)
	}

	return nil
}
