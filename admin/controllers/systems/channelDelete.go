package systems

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// ChannelDelete 删除渠道
func ChannelDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	db := model.NewModel()
	for _, v := range bodyParams.Ids {
		result := db.Unscoped().Where("id = ?", v).Where("admin_id = ?", c.Claims.AdminID).Delete(&models.Channel{})
		if result.Error != nil {
			return c.<PERSON><PERSON><PERSON><PERSON><PERSON>(fmt.Sprintf("删除渠道失败: %s", result.Error.Error()))
		}
	}

	return c.SuccessOk()
}
