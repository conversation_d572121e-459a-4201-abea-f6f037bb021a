package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

type CountryUpdateParams struct {
	ID     int    `json:"id" validate:"required" views:"label:ID;display:true"`
	Icon   string `json:"icon" views:"label:图标;type:image"`
	Name   string `json:"name" views:"label:名称"`
	Alias  string `json:"alias" views:"label:别名"`
	Code   string `json:"code" views:"label:区号"`
	ISO2   string `json:"iso2" views:"label:ISO2"`
	Sort   int8   `json:"sort" views:"label:排序;type:number"`
	Status int8   `json:"status" views:"label:状态;type:select"`
}

// CountryUpdate 更新国家
func CountryUpdate(c *context.CustomCtx, bodyParams *CountryUpdateParams) error {
	db := model.NewModel()

	country := &models.Country{}
	db.Where("id = ?", bodyParams.ID).Where("admin_id = ?", c.Claims.AdminID).First(country)
	if country.ID == 0 {
		return c.ErrorJson("国家不存在")
	}

	result := db.Model(&models.Country{}).Where("id = ?", country.ID).Updates(bodyParams)
	if result.Error != nil {
		return result.Error
	}

	// 删除缓存
	countryService := service.NewSystemCountryService()
	_ = countryService.DeleteAdminCountriesCache(c.Rds, country.AdminID)
	return c.SuccessOk()
}
