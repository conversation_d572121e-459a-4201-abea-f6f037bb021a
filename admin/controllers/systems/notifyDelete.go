package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// NotifyDelete 删除通知
func NotifyDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	for _, v := range bodyParams.Ids {
		if err := db.Unscoped().Model(&models.Notify{}).Where("admin_id IN ?", subAdminIDs).Where("id = ?", v).Delete(&models.Notify{}).Error; err != nil {
			return c.<PERSON><PERSON>r<PERSON><PERSON>(err.<PERSON>rror())
		}
	}

	return c.SuccessOk()
}
