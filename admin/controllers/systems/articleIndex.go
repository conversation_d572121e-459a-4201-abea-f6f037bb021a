package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	ArticleIndexURL  = "/systems/article/index"
	ArticleCreateURL = "/systems/article/create"
	ArticleUpdateURL = "/systems/article/update"
	ArticleDeleteURL = "/systems/article/delete"
)

// ArticleIndexParams 文章列表参数
type ArticleIndexParams struct {
	Symbol     string            `json:"symbol" views:"label:标识"`
	Type       int8              `json:"type" views:"label:类型;type:select"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// ArticleIndexRows 文章列表行
type ArticleIndexRows struct {
	Id        uint               `json:"id" column:"label:ID;"`
	Cover     string             `json:"cover" column:"label:封面;type:image"`
	Title     string             `json:"title"`
	TitleZh   string             `json:"titleZh" column:"label:标题" gorm:"-"`
	Type      int8               `json:"type" column:"label:类型;type:select"`
	Symbol    string             `json:"symbol" column:"label:标识"`
	Sort      int8               `json:"sort" column:"label:排序;type:number"`
	Nums      uint               `json:"nums" column:"label:浏览;type:number"`
	Status    int8               `json:"status" column:"label:状态;type:select"`
	CreatedAt string             `json:"createdAt" column:"label:时间;type:date"`
	Content   string             `json:"content"`
	Data      models.ArticleData `json:"data"`
}

// ArticleIndex 文章列表
func ArticleIndex(c *context.CustomCtx, bodyParams *ArticleIndexParams) error {
	data := &model.IndexData{Items: make([]*ArticleIndexRows, 0), Count: 0}

	query := model.NewModel().
		Equal("symbol", bodyParams.Symbol).
		Equal("status", bodyParams.Status).
		Equal("type", bodyParams.Type).
		Model(&models.Article{})

	query.Where("admin_id = ?", c.Claims.AdminID).Count(&data.Count)
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	translateService := service.NewTranslateService()
	for _, v := range data.Items.([]*ArticleIndexRows) {
		v.TitleZh = translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, models.DefaultLang, v.Title)
	}

	return c.SuccessJson(data)
}

// ArticleIndexConfigure 文章列表配置
func ArticleIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(ArticleIndexURL)
	statusOptions := []*views.SelectOption{
		{Label: "启用", Value: models.ArticleStatusEnabled},
		{Label: "禁用", Value: models.ArticleStatusDisabled},
	}
	typeOptions := []*views.SelectOption{
		{Label: "新闻咨询", Value: models.ArticleTypeNews},
		{Label: "滚动公告", Value: models.ArticleTypeScroll},
		{Label: "默认文章", Value: models.ArticleTypeDefault},
		{Label: "帮助文章", Value: models.ArticleTypeHelper},
	}
	vueTable.SetCommonOptions("status", statusOptions).SetCommonOptions("type", typeOptions)

	// 搜索表单
	searchForm := views.NewForm().Struct(ArticleIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(ArticleCreateParams{}).FlattenInputs().SetFieldInputParam("type", "default", models.ArticleTypeDefault)
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增文章", ArticleCreateURL, createForm).SetSize(views.DialogSizeMedium))
	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", ArticleDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(ArticleIndexRows{})
	vueTable.GetFieldColumn("cover").SetAvatarFormat("cover")
	vueTable.GetFieldColumn("status").SetSelectFormat(vueTable.CommonOptions["status"])
	vueTable.GetFieldColumn("type").SetSelectFormat(vueTable.CommonOptions["type"])

	// 更新
	updateForm := views.NewForm().Struct(ArticleUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", ArticleUpdateURL, updateForm))

	return vueTable
}
