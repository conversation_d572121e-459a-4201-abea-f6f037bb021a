package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// MenuUpdateDataParams 更新菜单数据参数
type MenuUpdateDataParams struct {
	ID   uint            `json:"id" validate:"required" views:"label:ID;display:true"`
	Data models.MenuData `json:"data" views:"label:数据;type:struct"`
}

// MenuUpdateData 更新菜单数据
func MenuUpdateData(c *context.CustomCtx, bodyParams *MenuUpdateDataParams) error {
	menuInfo := &models.Menu{}

	db := model.NewModel()
	err := db.Where("id = ?", bodyParams.ID).Where("admin_id = ?", c.Claims.AdminID).First(menuInfo).Error
	if err != nil {
		return c.<PERSON><PERSON>r<PERSON><PERSON>("找不到菜单信息")
	}

	menuInfo.Data = bodyParams.Data
	err = db.Save(menuInfo).Error
	if err != nil {
		return c.<PERSON><PERSON><PERSON><PERSON><PERSON>("保存菜单数据失败")
	}

	return c.<PERSON>Ok()
}
