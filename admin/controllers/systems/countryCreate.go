package systems

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// CountryCreateParams 创建国家参数
type CountryCreateParams struct {
	Icon string `json:"icon" validate:"required" views:"label:图标;type:image"`
	Name string `json:"name" validate:"required" views:"label:名称"`
	Code string `json:"code" validate:"required" views:"label:区号"`
	ISO2 string `json:"iso2" validate:"required" views:"label:ISO2"`
}

// CountryCreate 创建国家
func CountryCreate(c *context.CustomCtx, bodyParams *CountryCreateParams) error {
	db := model.NewModel()

	country := &models.Country{
		AdminID: c.Claims.AdminID,
		Icon:    bodyParams.Icon,
		Name:    bodyParams.Name,
		Alias:   bodyParams.Name,
		Code:    bodyParams.Code,
		ISO2:    bodyParams.ISO2,
	}
	result := db.Create(country)
	if result.Error != nil {
		return c.Error<PERSON>son(fmt.Sprintf("创建国家失败: %s", result.Error.Error()))
	}

	// 删除国家缓存
	countryService := service.NewSystemCountryService()
	countryService.DeleteAdminCountriesCache(c.Rds, c.Claims.AdminID)

	return c.SuccessOk()
}
