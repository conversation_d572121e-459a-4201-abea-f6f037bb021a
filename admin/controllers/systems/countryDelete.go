package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// CountryDelete 删除国家
func CountryDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	db := model.NewModel()

	for _, v := range bodyParams.Ids {
		country := &models.Country{}
		db.Where("id = ?", v).Where("admin_id = ?", c.Claims.AdminID).First(country)
		if country.ID == 0 {
			return c.<PERSON><PERSON><PERSON><PERSON><PERSON>("国家不存在")
		}

		// 删除国家
		result := db.Unscoped().Where("id = ?", v).Delete(country)
		if result.Error != nil {
			return result.Error
		}

		// 删除国家缓存
		countryService := service.NewSystemCountryService()
		countryService.DeleteAdminCountriesCache(c.Rds, country.AdminID)
	}

	return c.SuccessOk()
}
