package systems

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// LevelUpdateParams 更新等级参数
type LevelUpdateParams struct {
	Id     int64   `json:"id" validate:"required" views:"label:ID;display:true"`
	Icon   string  `json:"icon" views:"label:图标;type:image"`
	Name   string  `json:"name" views:"label:名称;type:translate"`
	Desc   string  `json:"desc" views:"label:描述;type:translate"`
	Money  float64 `json:"money" views:"label:金额;type:number"`
	Days   int     `json:"days" views:"label:天数;type:number"`
	Status int8    `json:"status" views:"label:状态;type:select"`
}

// LevelUpdate 更新等级
func LevelUpdate(c *context.CustomCtx, bodyParams *LevelUpdateParams) error {
	db := model.NewModel()

	level := &models.Level{}
	db.Model(&models.Level{}).Where("admin_id = ?", c.Claims.AdminID).Where("id = ?", bodyParams.Id).Find(level)
	if level.ID == 0 {
		return c.ErrorJson("等级不存在")
	}

	level.Icon = bodyParams.Icon
	level.Money = bodyParams.Money
	level.Days = bodyParams.Days
	level.Status = bodyParams.Status

	result := db.Save(level)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新等级失败: %s", result.Error.Error()))
	}

	return c.SuccessOk()
}
