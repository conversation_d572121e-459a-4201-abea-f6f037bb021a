package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	LevelIndexURL  = "/systems/level/index"
	LevelCreateURL = "/systems/level/create"
	LevelUpdateURL = "/systems/level/update"
	LevelDeleteURL = "/systems/level/delete"
)

// LevelIndexParams 等级列表参数
type LevelIndexParams struct {
	Type       int               `json:"type" views:"label:类型;type:select"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// LevelIndexRows 等级列表行
type LevelIndexRows struct {
	ID            uint    `json:"id" column:"label:ID"`
	Icon          string  `json:"icon" column:"label:图标"`
	Name          string  `json:"name"`
	TranslateName string  `json:"translateName" column:"label:名称"`
	Type          int8    `json:"type" column:"label:类型"`
	Symbol        int8    `json:"symbol" column:"label:标识"`
	Money         float64 `json:"money" column:"label:金额"`
	Days          int     `json:"days" column:"label:天数"`
	Status        int8    `json:"status" column:"label:状态"`
	Desc          string  `json:"desc"`
}

// LevelIndex 等级列表
func LevelIndex(c *context.CustomCtx, bodyParams *LevelIndexParams) error {
	data := &model.IndexData{Items: make([]*LevelIndexRows, 0), Count: 0}

	query := model.NewModel().
		Equal("type", bodyParams.Type).
		Equal("status", bodyParams.Status).
		Model(&models.Level{})

	query.Where("admin_id = ?", c.Claims.AdminID).Count(&data.Count)
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	translateService := service.NewTranslateService()
	for _, item := range data.Items.([]*LevelIndexRows) {
		item.TranslateName = translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.AdminID, "zh-CN", item.Name)
	}

	return c.SuccessJson(data)
}

// LevelIndexConfigure 等级列表配置
func LevelIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(LevelIndexURL)

	statusOptions := []*views.SelectOption{
		{Label: "启用", Value: models.LevelStatusEnabled},
		{Label: "禁用", Value: models.LevelStatusDisabled},
	}
	typeOptions := []*views.SelectOption{
		{Label: "会员等级", Value: models.LevelTypeMember},
	}

	vueTable.SetCommonOptions("status", statusOptions).SetCommonOptions("type", typeOptions)

	// 搜索表单
	searchForm := views.NewForm().Struct(LevelIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(LevelCreateParams{}).FlattenInputs().SetFieldInputParam("type", "default", models.LevelTypeMember)
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增等级", LevelCreateURL, createForm))

	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", LevelDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(LevelIndexRows{})
	vueTable.GetFieldColumn("icon").SetAvatarFormat("icon")
	vueTable.GetFieldColumn("status").SetSelectFormat(vueTable.CommonOptions["status"])
	vueTable.GetFieldColumn("type").SetSelectFormat(vueTable.CommonOptions["type"])

	// 更新
	updateForm := views.NewForm().Struct(LevelUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", LevelUpdateURL, updateForm))
	return vueTable
}
