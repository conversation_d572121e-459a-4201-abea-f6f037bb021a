package systems

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// LangCreateParams 创建语言参数
type LangCreateParams struct {
	Icon   string `json:"icon" validate:"required" views:"label:图标;type:image"`
	Name   string `json:"name" validate:"required" views:"label:名称"`
	Symbol string `json:"symbol" validate:"required" views:"label:标识"`
}

// LangCreate 创建语言
func LangCreate(c *context.CustomCtx, bodyParams *LangCreateParams) error {
	db := model.NewModel()
	result := db.Create(&models.Lang{
		AdminID: c.Claims.AdminID,
		Name:    bodyParams.Name,
		Alias:   bodyParams.Name,
		Symbol:  bodyParams.Symbol,
		Icon:    bodyParams.Icon,
		Data:    models.LangData{},
	})

	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("创建失败: %s", result.Error.Error()))
	}

	// 删除语言缓存
	langService := service.NewSystemLangService()
	langService.DeleteAdminLanguagesCache(c.Rds, c.Claims.AdminID)

	return c.SuccessOk()
}
