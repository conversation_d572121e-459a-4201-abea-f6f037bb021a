package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"
)

const (
	TranslateIndexURL  = "/systems/translate/index"
	TranslateUpdateURL = "/systems/translate/update"
)

// TranslateIndexParams 翻译列表参数
type TranslateIndexParams struct {
	Name       string            `json:"name" views:"label:名称"`
	Field      string            `json:"field" views:"label:字段"`
	Value      string            `json:"value" views:"label:内容"`
	Type       int8              `json:"type" views:"label:类型;type:select"`
	Lang       string            `json:"lang" views:"label:语言;type:select"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// TranslateIndexRows 翻译列表行
type TranslateIndexRows struct {
	ID        uint   `json:"id" column:"label:ID"`
	Name      string `json:"name" column:"label:名称"`
	Field     string `json:"field" column:"label:字段"`
	Desc      string `json:"desc"`
	Lang      string `json:"lang" column:"label:语言;type:select"`
	Type      int8   `json:"type" column:"label:类型;type:select"`
	Value     string `json:"value"`
	ShowValue string `json:"showValue" column:"label:内容" gorm:"-"`
	Status    int8   `json:"status" column:"label:状态;type:select"`
}

// TranslateIndex 翻译列表
func TranslateIndex(c *context.CustomCtx, bodyParams *TranslateIndexParams) error {
	data := &model.IndexData{Items: make([]*TranslateIndexRows, 0), Count: 0}

	currentAdminIDs := []uint{c.Claims.AdminID}
	if c.Claims.AdminID == models.SuperAdminID {
		currentAdminIDs = append(currentAdminIDs, 0)
	}

	query := model.NewModel().
		Equal("lang", bodyParams.Lang).
		Like("name", bodyParams.Name).
		Equal("type", bodyParams.Type).
		Equal("field", bodyParams.Field).
		Equal("status", bodyParams.Status).
		Like("value", bodyParams.Value).
		Model(&models.Translate{})

	query.Where("admin_id IN ?", currentAdminIDs).Count(&data.Count)
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	for _, v := range data.Items.([]*TranslateIndexRows) {
		v.ShowValue = utils.FilterHTML(v.Value)
	}

	return c.SuccessJson(data)
}

// TranslateIndexConfigure 翻译列表配置
func TranslateIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(TranslateIndexURL)
	langService := service.NewSystemLangService()
	langOptions, _ := langService.GetAdminLangOptions(c.Rds, c.Claims.MerchantID)

	typeOptions := []*views.SelectOption{
		{Label: "系统", Value: models.TranslateTypeSystem},
		{Label: "前台", Value: models.TranslateTypeFrontend},
		{Label: "后台", Value: models.TranslateTypeBackend},
	}
	statusOptions := []*views.SelectOption{
		{Label: "启用", Value: models.TranslateStatusEnabled},
		{Label: "禁用", Value: models.TranslateStatusDisabled},
	}

	vueTable.SetCommonOptions("lang", langOptions).SetCommonOptions("type", typeOptions).SetCommonOptions("status", statusOptions)

	// 查询结构体, 自动转化成 inputs 设置表格数据
	searchForm := views.NewForm().Struct(TranslateIndexParams{}).SetPagination(&model.Pagination{RowsPerPage: 100, SortBy: "status"})
	vueTable.SetSearchs(searchForm)

	// 数据表格数据
	vueTable.StructColumns(TranslateIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(vueTable.CommonOptions["status"])
	vueTable.GetFieldColumn("type").SetSelectFormat(vueTable.CommonOptions["type"])
	vueTable.GetFieldColumn("lang").SetSelectFormat(vueTable.CommonOptions["lang"])

	updateForm := views.NewForm().Struct(TranslateUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", TranslateUpdateURL, updateForm).SetSize(views.DialogSizeMedium))
	return vueTable
}
