package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// TranslateUpdateParams 更新翻译参数
type TranslateUpdateParams struct {
	ID     uint   `json:"id" validate:"required" views:"label:ID;type:number;display:true"`
	Desc   string `json:"desc" views:"label:描述;readonly:true"`
	Value  string `json:"value" views:"label:翻译;type:editor"`
	Status int8   `json:"status" views:"label:状态;type:select"`
}

// TranslateUpdate 更新翻译
func TranslateUpdate(c *context.CustomCtx, bodyParams *TranslateUpdateParams) error {
	translateInfo := &models.Translate{}
	db := model.NewModel()

	currentAdminIDs := []uint{c.Claims.AdminID}
	if c.Claims.AdminID == models.SuperAdminID {
		currentAdminIDs = append(currentAdminIDs, 0)
	}

	if err := db.Model(&models.Translate{}).Where("admin_id IN ?", currentAdminIDs).Where("id = ?", bodyParams.ID).First(&translateInfo).Error; err != nil {
		return c.ErrorJson("找不到可更新的翻译")
	}

	translateInfo.Value = bodyParams.Value
	translateInfo.Status = bodyParams.Status
	if err := db.Save(translateInfo).Error; err != nil {
		return c.ErrorJson("更新失败")
	}

	// 删除翻译缓存
	translateService := service.NewTranslateService()
	translateService.DeleteTranslateCache(c.Rds, translateInfo.AdminID, translateInfo.Lang)

	// 删除前台菜单缓存
	systemMenuService := service.NewSystemMenuService()
	systemMenuService.DeleteFrontendMenusAllCacheByLang(c.Rds, translateInfo.AdminID, translateInfo.Lang)
	return c.SuccessOk()
}
