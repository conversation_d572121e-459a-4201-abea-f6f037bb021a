package systems

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/utils"
)

// ChannelCreateParams 创建渠道参数
type ChannelCreateParams struct {
	Icon   string `json:"icon" validate:"required" views:"label:图标"`
	Type   int8   `json:"type" validate:"required" views:"label:类型"`
	Name   string `json:"name" validate:"required" views:"label:名称"`
	Symbol string `json:"symbol" validate:"required" views:"label:标识"`
}

// ChannelCreate 创建渠道
func ChannelCreate(c *context.CustomCtx, bodyParams *ChannelCreateParams) error {
	db := model.NewModel()

	channel := &models.Channel{
		AdminID:   c.Claims.AdminID,
		AppID:     utils.GenerateNumericString(16),
		SecretKey: utils.GenerateRandomString(32),
		Name:      bodyParams.Name,
		Icon:      bodyParams.Icon,
		Symbol:    bodyParams.Symbol,
		Type:      bodyParams.Type,
		Data:      models.ChannelData{},
	}

	result := db.Create(channel)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("创建频道失败: %s", result.Error.Error()))
	}

	return c.SuccessOk()
}
