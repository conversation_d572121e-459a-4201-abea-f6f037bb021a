package systems

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// LangUpdateParams 更新语言参数
type LangUpdateParams struct {
	ID     uint   `json:"id" validate:"required" views:"label:ID;display:true"`
	Icon   string `json:"icon" views:"label:图标;type:image"`
	Name   string `json:"name" views:"label:名称"`
	Alias  string `json:"alias" views:"label:别名"`
	Symbol string `json:"symbol" views:"label:标识"`
	Sort   int8   `json:"sort" views:"label:排序;type:number"`
	Status int8   `json:"status" views:"label:状态;type:select"`
}

// LangUpdate 更新语言
func LangUpdate(c *context.CustomCtx, bodyParams *LangUpdateParams) error {
	db := model.NewModel()
	lang := models.Lang{}
	result := db.Where("id = ?", bodyParams.ID).Where("admin_id = ?", c.Claims.AdminID).First(&lang)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("语言不存在: %s", result.Error.Error()))
	}

	lang.Icon = bodyParams.Icon
	lang.Name = bodyParams.Name
	lang.Alias = bodyParams.Alias
	lang.Symbol = bodyParams.Symbol
	lang.Sort = bodyParams.Sort
	lang.Status = bodyParams.Status

	result = db.Save(lang)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新语言失败: %s", result.Error.Error()))
	}

	// 删除语言缓存
	service.NewSystemLangService().DeleteAdminLanguagesCache(c.Rds, lang.AdminID)

	return c.SuccessOk()
}
