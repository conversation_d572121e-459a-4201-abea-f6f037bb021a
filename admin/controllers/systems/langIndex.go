package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
)

const (
	LangIndexURL     = "/systems/lang/index"
	LangCreateURL    = "/systems/lang/create"
	LangUpdateURL    = "/systems/lang/update"
	LangDeleteURL    = "/systems/lang/delete"
	LangSyncURL      = "/systems/lang/sync"
	LangTranslateURL = "/systems/lang/translate"
)

// LangIndexParams 语言列表参数
type LangIndexParams struct {
	Name       string            `json:"name" views:"label:名称"`
	Alias      string            `json:"alias" views:"label:别名"`
	Symbol     string            `json:"symbol" views:"label:标识"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// LangIndexRows 语言列表行
type LangIndexRows struct {
	ID     uint   `json:"id" column:"label:ID"`
	Icon   string `json:"icon" column:"label:图标;type:image"`
	Name   string `json:"name" column:"label:名称"`
	Alias  string `json:"alias" column:"label:别名"`
	Symbol string `json:"symbol" column:"label:标识"`
	Sort   int8   `json:"sort" column:"label:排序"`
	Status int8   `json:"status" column:"label:状态;type:select"`
}

// LangIndex 语言列表
func LangIndex(c *context.CustomCtx, bodyParams *LangIndexParams) error {
	data := &model.IndexData{Items: make([]*LangIndexRows, 0), Count: 0}

	query := model.NewModel().
		Like("name", bodyParams.Name).
		Like("alias", bodyParams.Alias).
		Like("symbol", bodyParams.Symbol).
		Equal("status", bodyParams.Status).
		Model(&models.Lang{})

	query.Where("admin_id = ?", c.Claims.AdminID).Count(&data.Count)
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}

// LangIndexConfigure 语言列表配置
func LangIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(LangIndexURL)

	statusOptions := []*views.SelectOption{
		{Label: "启用", Value: models.LangStatusEnabled},
		{Label: "禁用", Value: models.LangStatusDisabled},
	}

	vueTable.SetCommonOptions("status", statusOptions)

	// 搜索表单
	searchForm := views.NewForm().Struct(LangIndexParams{}).SetPagination(&model.Pagination{RowsPerPage: 100, SortBy: "status"})
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(LangCreateParams{}).FlattenInputs()
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增语言", LangCreateURL, createForm))
	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", LangDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(LangIndexRows{})
	vueTable.GetFieldColumn("icon").SetAvatarFormat("icon")
	vueTable.GetFieldColumn("status").SetSelectFormat(vueTable.CommonOptions["status"])

	// options 更新
	updateForm := views.NewForm().Struct(LangUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", LangUpdateURL, updateForm))

	// 同步超级管理员 只有商户才有
	if c.Claims.AdminID != models.SuperAdminID {
		syncForm := views.NewForm().Struct(LangSyncParams{}).FlattenInputs()
		vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("同步", LangSyncURL, syncForm).SetSmall("同步超级管理员翻译数据"))
	}

	// 翻译 只允许超级管理员添加
	if c.Claims.AdminID == models.SuperAdminID {
		translateForm := views.NewForm().Struct(LangTranslateParams{}).FlattenInputs()
		vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("翻译", LangTranslateURL, translateForm).SetSmall("使用Google 翻译, 后台会进行翻译, 可能需要等待一段时间, 等待翻译完成。"))
	}

	return vueTable
}
