package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// MenuUpdateParams 更新菜单信息参数
type MenuUpdateParams struct {
	ID     uint   `json:"id" validate:"required" views:"label:ID;display:true"`
	Name   string `json:"name" views:"label:名称"`
	Route  string `json:"route" views:"label:路由"`
	Sort   uint8  `json:"sort" views:"label:排序"`
	Type   int8   `json:"type" views:"label:类型;type:select"`
	Status int8   `json:"status" views:"label:状态;type:select"`
}

// MenuUpdate 更新菜单信息
func MenuUpdate(c *context.CustomCtx, bodyParams *MenuUpdateParams) error {
	menuInfo := &models.Menu{}

	db := model.NewModel()
	err := db.Where("id = ?", bodyParams.ID).Where("admin_id = ?", c.<PERSON>lai<PERSON>.AdminID).First(menuInfo).Error
	if err != nil {
		return c.ErrorJson("找不到菜单信息")
	}

	menuInfo.Name = bodyParams.Name
	menuInfo.Route = bodyParams.Route
	menuInfo.Sort = bodyParams.Sort
	menuInfo.Type = bodyParams.Type
	menuInfo.Status = bodyParams.Status

	err = db.Save(menuInfo).Error
	if err != nil {
		return c.ErrorJson("保存菜单信息失败")
	}

	// 删除菜单缓存
	menuService := service.NewSystemMenuService()
	menuService.DeleteFrontendMenusCache(c.Rds, menuInfo.AdminID, menuInfo.Type)

	return c.SuccessOk()
}
