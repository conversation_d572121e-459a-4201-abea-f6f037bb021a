package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// NotifyCreateBackendParams 创建通知参数
type NotifyCreateBackendParams struct {
	AdminID uint   `json:"adminId" validate:"required" views:"label:管理;type:select"`
	Title   string `json:"title" validate:"required" views:"label:标题"`
	Content string `json:"content" validate:"required" views:"label:内容;type:editor"`
}

// NotifyCreateBackend 创建通知
func NotifyCreateBackend(c *context.CustomCtx, bodyParams *NotifyCreateBackendParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	adminInfo := &models.AdminUser{}
	if err := db.Model(&models.AdminUser{}).Where("id = ?", bodyParams.AdminID).Where("id IN ?", subAdminIDs).First(adminInfo).Error; err != nil {
		return c.ErrorJson("找不到管理员")
	}

	result := db.Create(&models.Notify{
		AdminID: adminInfo.ID,
		UserID:  0,
		Mode:    models.NotifyModeBackend,
		Type:    models.NotifyTypeSystem,
		Title:   bodyParams.Title,
		Content: bodyParams.Content,
		Data:    models.NotifyData{Label: bodyParams.Title, URL: ""},
	})

	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	return c.SuccessOk()
}
