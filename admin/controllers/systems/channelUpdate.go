package systems

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// ChannelUpdateParams 更新渠道参数
type ChannelUpdateParams struct {
	Id        uint   `json:"id" validate:"required" views:"label:ID;display:true"`
	Icon      string `json:"icon" views:"label:图标;type:image"`
	Name      string `json:"name" views:"label:名称"`
	Symbol    string `json:"symbol" views:"label:标识"`
	AppId     string `json:"appId" views:"label:应用ID"`
	SecretKey string `json:"secretKey" views:"label:密钥"`
	Status    int8   `json:"status" views:"label:状态;type:select"`
}

// ChannelUpdate 更新渠道
func ChannelUpdate(c *context.CustomCtx, bodyParams *ChannelUpdateParams) error {
	db := model.NewModel()
	channelInfo := &models.Channel{}
	result := db.Where("id = ?", bodyParams.Id).Where("admin_id = ?", c.Claims.AdminID).First(channelInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新渠道失败: %s", result.Error.Error()))
	}

	result = db.Model(&models.Channel{}).Where("id =?", channelInfo.ID).Updates(bodyParams)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新渠道失败: %s", result.Error.Error()))
	}

	return c.SuccessOk()
}
