package systems

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// LevelDelete 删除等级
func LevelDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	db := model.NewModel()

	err := db.Transaction(func(tx *gorm.DB) error {
		for _, v := range bodyParams.Ids {
			level := &models.Level{}
			result := db.Where("id = ?", v).Where("admin_id = ?", c.Claims.AdminID).First(level)
			if result.Error != nil {
				return c.<PERSON>rror<PERSON><PERSON>(fmt.Sprintf("找不到等级: %s", result.Error.Error()))
			}

			result = tx.Unscoped().Where("id = ?", level.ID).Delete(&models.Level{})
			if result.Error != nil {
				return c.<PERSON>rror<PERSON><PERSON>(fmt.Sprintf("删除等级失败: %s", result.Error.Error()))
			}

			// 删除翻译
			result = tx.Unscoped().Where("field = ?", fmt.Sprintf("levelName%d", level.Symbol)).Delete(&models.Translate{})
			if result.Error != nil {
				return c.ErrorJson(fmt.Sprintf("删除翻译失败: %s", result.Error.Error()))
			}

			result = tx.Unscoped().Where("field = ?", fmt.Sprintf("levelDesc%d", level.Symbol)).Delete(&models.Translate{})
			if result.Error != nil {
				return c.ErrorJson(fmt.Sprintf("删除翻译失败: %s", result.Error.Error()))
			}
		}

		return nil
	})

	if err != nil {
		return c.ErrorJson(fmt.Sprintf("删除等级失败: %s", err.Error()))
	}

	// 删除所有翻译缓存
	translateService := service.NewTranslateService()
	translateService.DeleteAllTranslateCache(c.Rds, c.Claims.AdminID)

	return c.SuccessOk()
}
