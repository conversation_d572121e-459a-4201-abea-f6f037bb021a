package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	NotifyIndexURL          = "/systems/notify/index"
	NotifyCreateFrontendURL = "/systems/notify/create/frontend"
	NotifyCreateBackendURL  = "/systems/notify/create/backend"
	NotifyUpdateURL         = "/systems/notify/update"
	NotifyDeleteURL         = "/systems/notify/delete"
)

// NotifyIndexParams 通知列表参数
type NotifyIndexParams struct {
	Title      string            `json:"title" views:"label:标题"`
	UserName   string            `json:"username" views:"label:账户"`
	AdminID    uint              `json:"adminId" views:"label:管理;type:select"`
	Type       int8              `json:"type" views:"label:类型;type:select"`
	Mode       int8              `json:"mode" views:"label:模式;type:select"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// NotifyIndexRows 通知列表行
type NotifyIndexRows struct {
	ID        uint        `json:"id" column:"label:ID"`
	AdminID   uint        `json:"adminId" column:"label:管理"`
	Username  string      `json:"username" column:"label:账户;scanField:userInfo.username" gorm:"-"`
	Type      int8        `json:"type" column:"label:类型"`
	Mode      int8        `json:"mode" column:"label:模式"`
	Title     string      `json:"title" column:"label:标题"`
	Content   string      `json:"content"`
	Status    int8        `json:"status" column:"label:状态"`
	CreatedAt string      `json:"createdAt" column:"label:发送时间;type:date"`
	UpdatedAt string      `json:"updatedAt" column:"label:已读时间;type:date"`
	UserID    uint        `json:"userId"`
	UserInfo  models.User `json:"userInfo" gorm:"foreignKey:UserID"`
}

// NotifyIndex 通知列表
func NotifyIndex(c *context.CustomCtx, bodyParams *NotifyIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	data := &model.IndexData{Items: make([]*NotifyIndexRows, 0), Count: 0}
	db := model.NewModel()

	query := db.
		Equal("admin_id", bodyParams.AdminID).
		SubQueryIn("user_id", bodyParams.UserName, db.Model(&models.User{}).Where("username = ?", bodyParams.UserName)).
		Like("title", bodyParams.Title).
		Equal("type", bodyParams.Type).
		Equal("mode", bodyParams.Mode).
		Equal("status", bodyParams.Status).
		Model(&models.Notify{})

	query.Where("admin_id IN ?", subAdminIDs).Count(&data.Count)
	query.Preload("UserInfo")
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}

// NotifyIndexConfigure 通知列表配置
func NotifyIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(NotifyIndexURL)

	adminService := service.NewAdminUserService()
	adminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)

	statusOptions := []*views.SelectOption{
		{Label: "未读", Value: models.NotifyStatusUnread},
		{Label: "已读", Value: models.NotifyStatusRead},
	}

	typeOptions := []*views.SelectOption{
		{Label: "系统通知", Value: models.NotifyTypeSystem},
	}
	modeOptions := []*views.SelectOption{
		{Label: "后台", Value: models.NotifyModeBackend},
		{Label: "前台", Value: models.NotifyModeFrontend},
	}

	vueTable.SetCommonOptions("adminId", adminOptions).SetCommonOptions("status", statusOptions).SetCommonOptions("type", typeOptions).SetCommonOptions("mode", modeOptions)

	// 搜索
	searchForm := views.NewForm().Struct(NotifyIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createFrontendForm := views.NewForm().Struct(NotifyCreateFrontendParams{}).FlattenInputs()
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("用户通知", NotifyCreateFrontendURL, createFrontendForm).SetSize(views.DialogSizeMedium))

	createBackendForm := views.NewForm().Struct(NotifyCreateBackendParams{}).FlattenInputs()
	createBackendForm.SetFieldInputParam("adminId", "default", adminOptions[0].Value)
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("管理通知", NotifyCreateBackendURL, createBackendForm).SetSize(views.DialogSizeMedium))

	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", NotifyDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(NotifyIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(vueTable.CommonOptions["status"])
	vueTable.GetFieldColumn("type").SetSelectFormat(vueTable.CommonOptions["type"])
	vueTable.GetFieldColumn("mode").SetSelectFormat(vueTable.CommonOptions["mode"])
	vueTable.GetFieldColumn("adminId").SetSelectFormat(vueTable.CommonOptions["adminId"])

	// 更新
	updateForm := views.NewForm().Struct(NotifyUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", NotifyUpdateURL, updateForm).SetSize(views.DialogSizeMedium))

	return vueTable
}
