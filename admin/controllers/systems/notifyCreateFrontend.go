package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// NotifyCreateFrontendParams 创建通知参数
type NotifyCreateFrontendParams struct {
	UserID  uint   `json:"userId" validate:"required" views:"label:用户;type:selectSearch;mask:user#username>id"`
	Title   string `json:"title" validate:"required" views:"label:标题"`
	Content string `json:"content" validate:"required" views:"label:内容;type:editor"`
}

// NotifyCreateFrontend 创建通知
func NotifyCreateFrontend(c *context.CustomCtx, bodyParams *NotifyCreateFrontendParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	userInfo := &models.User{}
	if err := db.Model(&models.User{}).Where("id = ?", bodyParams.UserID).Where("admin_id IN ?", subAdminIDs).First(userInfo).Error; err != nil {
		return c.ErrorJson("找不到用户")
	}

	result := db.Create(&models.Notify{
		AdminID: userInfo.AdminID,
		UserID:  userInfo.ID,
		Mode:    models.NotifyModeFrontend,
		Type:    models.NotifyTypeSystem,
		Title:   bodyParams.Title,
		Content: bodyParams.Content,
		Data:    models.NotifyData{Label: bodyParams.Title, URL: ""},
	})

	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	return c.SuccessOk()
}
