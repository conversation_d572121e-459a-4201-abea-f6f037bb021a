package systems

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// LevelCreateParams 创建等级参数
type LevelCreateParams struct {
	Icon string `json:"icon" validate:"required" views:"label:图标;type:image"`
	Type int8   `json:"type" validate:"required" views:"label:类型;type:select"`
	Name string `json:"name" validate:"required" views:"label:中文翻译名称"`
	Desc string `json:"desc" validate:"required" views:"label:中文翻译描述"`
}

// LevelCreate 创建等级
func LevelCreate(c *context.CustomCtx, bodyParams *LevelCreateParams) error {
	db := model.NewModel()

	// 获取等级最后一个 symbol
	var level models.Level
	result := db.Model(&models.Level{}).Where("admin_id = ?", c.Claims.AdminID).Order("symbol desc").Find(&level)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("获取等级失败: %s", result.Error.Error()))
	}
	symbol := level.Symbol + 1

	err := db.Transaction(func(tx *gorm.DB) error {
		result = tx.Create(&models.Level{
			AdminID: c.Claims.AdminID,
			Name:    fmt.Sprintf("levelName%d", symbol),
			Desc:    fmt.Sprintf("levelDesc%d", symbol),
			Icon:    bodyParams.Icon,
			Type:    bodyParams.Type,
			Symbol:  symbol,
			Money:   2 * level.Money,
			Days:    365,
			Data:    models.LevelData{},
		})
		if result.Error != nil {
			return c.ErrorJson(fmt.Sprintf("创建等级失败: %s", result.Error.Error()))
		}

		// 创建翻译
		translate := []models.Translate{
			{
				AdminID: c.Claims.AdminID,
				Lang:    models.DefaultLang,
				Name:    fmt.Sprintf("等级[%s]", bodyParams.Name),
				Type:    models.TranslateTypeSystem,
				Field:   fmt.Sprintf("levelName%d", symbol),
				Value:   bodyParams.Name,
			},
			{
				AdminID: c.Claims.AdminID,
				Lang:    models.DefaultLang,
				Name:    fmt.Sprintf("等级[%s详情]", bodyParams.Name),
				Type:    models.TranslateTypeSystem,
				Field:   fmt.Sprintf("levelDesc%d", symbol),
				Value:   bodyParams.Desc,
			},
		}
		result = tx.Create(&translate)
		if result.Error != nil {
			return c.ErrorJson(fmt.Sprintf("创建翻译失败: %s", result.Error.Error()))
		}

		return nil
	})

	if err != nil {
		return c.ErrorJson(fmt.Sprintf("创建等级失败: %s", err.Error()))
	}

	// 删除所有翻译缓存
	translateService := service.NewTranslateService()
	translateService.DeleteAllTranslateCache(c.Rds, c.Claims.AdminID)

	return c.SuccessOk()
}
