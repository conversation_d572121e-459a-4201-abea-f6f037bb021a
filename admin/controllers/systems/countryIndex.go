package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
)

const (
	CountryIndexURL  = "/systems/country/index"
	CountryCreateURL = "/systems/country/create"
	CountryUpdateURL = "/systems/country/update"
	CountryDeleteURL = "/systems/country/delete"
)

// CountryIndexParams 国家列表参数
type CountryIndexParams struct {
	Name       string            `json:"name" views:"label:名称"`
	Alias      string            `json:"alias" views:"label:别名"`
	Code       string            `json:"code" views:"label:区号"`
	ISO2       string            `json:"iso2" views:"label:ISO2"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// CountryIndexRows 国家列表行
type CountryIndexRows struct {
	ID     int    `json:"id" column:"label:ID"`
	Icon   string `json:"icon" column:"label:图标;type:image"`
	Name   string `json:"name" column:"label:名称"`
	Alias  string `json:"alias" column:"label:别名"`
	Code   string `json:"code" column:"label:区号"`
	ISO2   string `json:"iso2" column:"label:ISO2"`
	Sort   int8   `json:"sort" column:"label:排序"`
	Status int8   `json:"status" column:"label:状态;type:select"`
}

// CountryIndex 国家列表
func CountryIndex(c *context.CustomCtx, bodyParams *CountryIndexParams) error {
	data := &model.IndexData{Items: make([]*CountryIndexRows, 0), Count: 0}

	query := model.NewModel().
		Like("name", bodyParams.Name).
		Like("alias", bodyParams.Alias).
		Equal("code", bodyParams.Code).
		Equal("iso2", bodyParams.ISO2).
		Equal("status", bodyParams.Status).
		Model(&models.Country{})

	query.Where("admin_id = ?", c.Claims.AdminID).Count(&data.Count)
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}

// CountryIndexConfigure 国家列表配置
func CountryIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(CountryIndexURL)
	statusOptions := []*views.SelectOption{
		{Label: "启用", Value: models.CountryStatusEnabled},
		{Label: "禁用", Value: models.CountryStatusDisabled},
	}

	vueTable.SetCommonOptions("status", statusOptions)

	// 搜索表单
	searchForm := views.NewForm().Struct(CountryIndexParams{}).SetPagination(&model.Pagination{RowsPerPage: 100, SortBy: "status"})
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(CountryCreateParams{}).FlattenInputs()
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增国家", CountryCreateURL, createForm))
	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", CountryDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(CountryIndexRows{})
	vueTable.GetFieldColumn("icon").SetAvatarFormat("icon")
	vueTable.GetFieldColumn("status").SetSelectFormat(vueTable.CommonOptions["status"])

	// 更新
	updateForm := views.NewForm().Struct(CountryUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", CountryUpdateURL, updateForm))

	return vueTable
}
