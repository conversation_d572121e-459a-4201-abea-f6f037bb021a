package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// NotifyUpdateParams 更新通知参数
type NotifyUpdateParams struct {
	ID      uint   `json:"id" validate:"required" views:"label:ID;display:true"`
	Title   string `json:"title" validate:"required" views:"label:标题"`
	Content string `json:"content" validate:"required" views:"label:内容;type:editor"`
}

// NotifyUpdate 更新通知
func NotifyUpdate(c *context.CustomCtx, bodyParams *NotifyUpdateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	notifyInfo := &models.Notify{}
	if err := db.Model(&models.Notify{}).Where("id = ?", bodyParams.ID).Where("admin_id IN ?", subAdminIDs).First(notifyInfo).Error; err != nil {
		return c.ErrorJson("找不到通知")
	}

	notifyInfo.Title = bodyParams.Title
	notifyInfo.Content = bodyParams.Content
	if err := db.Save(notifyInfo).Error; err != nil {
		return c.ErrorJson("更新失败")
	}

	return c.SuccessOk()
}
