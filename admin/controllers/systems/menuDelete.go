package systems

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// 删除菜单
func MenuDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	db := model.NewModel()

	db.Transaction(func(tx *gorm.DB) error {
		for _, v := range bodyParams.Ids {
			menuInfo := &models.Menu{}
			result := db.Where("id = ?", v).Where("admin_id = ?", c.Claims.AdminID).First(menuInfo)
			if result.Error != nil {
				return c.Error<PERSON>son(fmt.Sprintf("找不到前台菜单信息: %s", result.Error.Error()))
			}

			// 删除菜单
			result = tx.Unscoped().Where("id = ?", v).Delete(menuInfo)
			if result.Error != nil {
				return c.<PERSON><PERSON><PERSON><PERSON><PERSON>(fmt.Sprintf("删除菜单失败: %s", result.Error.Error()))
			}

			// 删除菜单翻译
			result = tx.Unscoped().
				Where("type = ? AND field = ?", models.TranslateTypeFrontend, fmt.Sprintf("menu_%s", menuInfo.Data.Label)).
				Delete(&models.Translate{})
			if result.Error != nil {
				return c.ErrorJson(fmt.Sprintf("删除菜单翻译失败: %s", result.Error.Error()))
			}

			// 删除菜单缓存
			menuService := service.NewSystemMenuService()
			menuService.DeleteFrontendMenusCache(c.Rds, c.Claims.AdminID, menuInfo.Type)
		}

		return nil
	})

	return c.SuccessOk()
}
