package systems

import (
	"zfeng/core/cache"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils/google/translate"

	"github.com/gomodule/redigo/redis"
)

// LangTranslateParams 语言翻译参数
type LangTranslateParams struct {
	ID uint `json:"id" validate:"required" views:"label:ID;display:true"`
}

// LangTranslate 语言翻译
func LangTranslate(c *context.CustomCtx, bodyParams *LangTranslateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	db := model.NewModel()

	langInfo := models.Lang{}
	result := db.Where("id = ?", bodyParams.ID).Where("admin_id IN ?", subAdminIDs).First(&langInfo)
	if result.Error != nil {
		return c.ErrorJson("没有找到语言信息")
	}

	// 判断当前缓存是否存在
	exists, err := redis.Int64(c.Rds.Do("GET", models.RedisLangTranslateKey))
	if err != redis.ErrNil && exists > 0 {
		return c.ErrorJson("正在翻译中, 请等待翻译完成")
	}

	// 中文不需要翻译
	if langInfo.Alias == models.DefaultLang {
		return c.SuccessOk()
	}

	// 获取超级管理员的中文源语言列表
	srcTranslateList := make([]*models.Translate, 0)
	db.Model(&models.Translate{}).Where("admin_id = ? OR admin_id = ?", 0, models.SuperAdminID).Where("lang = ?", models.DefaultLang).Find(&srcTranslateList)

	// 后台进行翻译
	go googleTranslate(srcTranslateList, &langInfo)

	// 返回成功
	return c.SuccessOk()
}

func googleTranslate(srcLangList []*models.Translate, targetLang *models.Lang) error {
	rdsConn := cache.Rds.Get()
	defer rdsConn.Close()

	defer func() {
		// 删除新增语言的前端翻译缓存
		translateService := service.NewTranslateService()
		translateService.DeleteFrontendTranslatesCache(rdsConn, targetLang.AdminID, targetLang.Symbol)

		// 删除前台菜单缓存
		systemMenuService := service.NewSystemMenuService()
		systemMenuService.DeleteFrontendMenusAllCacheByLang(rdsConn, targetLang.AdminID, targetLang.Symbol)

		// 删除缓存标记
		_, _ = rdsConn.Do("DEL", models.RedisLangTranslateKey)
	}()

	// 设置缓存标记正在翻译
	_, _ = rdsConn.Do("SETEX", models.RedisLangTranslateKey, 300, targetLang.ID)

	db := model.NewModel()
	googleTranslate := translate.New(translate.Config{})
	for _, v := range srcLangList {
		// 如果当前语言有设置翻译
		currentTranslate := models.Translate{}
		var curreltAdminID uint = 0
		if v.AdminID > 0 {
			curreltAdminID = targetLang.AdminID
		}
		db.Where("admin_id = ?", curreltAdminID).Where("lang = ?", targetLang.Symbol).Where("field = ?", v.Field).Find(&currentTranslate)
		if currentTranslate.ID > 0 {
			continue
		}

		// 翻译
		translated, err := googleTranslate.Translate(v.Value, models.DefaultLang, targetLang.Symbol)
		if err == nil {
			db.Create(&models.Translate{
				AdminID: curreltAdminID,
				Lang:    targetLang.Symbol,
				Name:    v.Name,
				Field:   v.Field,
				Type:    v.Type,
				Value:   translated.Text,
				Desc:    v.Desc,
				Status:  v.Status,
			})
		}
	}
	return nil
}
