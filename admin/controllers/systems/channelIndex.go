package systems

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
)

const (
	ChannelIndexURL  = "/systems/channel/index"
	ChannelCreateURL = "/systems/channel/create"
	ChannelUpdateURL = "/systems/channel/update"
	ChannelDeleteURL = "/systems/channel/delete"
)

// ChannelIndexParams 渠道列表参数
type ChannelIndexParams struct {
	Name       string            `json:"name" views:"label:名称"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Type       int8              `json:"type" views:"label:类型;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// ChannelIndexRows 渠道列表行
type ChannelIndexRows struct {
	Id        uint               `json:"id" column:"label:ID;"`
	Icon      string             `json:"icon" column:"label:图标;type:image"`
	Name      string             `json:"name" column:"label:名称"`
	Symbol    string             `json:"symbol" column:"label:标识"`
	Type      int8               `json:"type" column:"label:类型"`
	AppID     string             `json:"appId" column:"label:应用ID"`
	SecretKey string             `json:"secretKey" column:"label:密钥"`
	Status    int8               `json:"status" column:"label:状态"`
	Data      models.ChannelData `json:"data"`
}

// ChannelIndex 渠道列表
func ChannelIndex(c *context.CustomCtx, bodyParams *ChannelIndexParams) error {
	data := &model.IndexData{Items: make([]*ChannelIndexRows, 0), Count: 0}

	query := model.NewModel().
		Like("name", bodyParams.Name).
		Equal("status", bodyParams.Status).
		Equal("type", bodyParams.Type).Model(&models.Channel{})

	query.Where("admin_id = ?", c.Claims.AdminID).Count(&data.Count)
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}

// ChannelIndexConfigure 渠道列表配置
func ChannelIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(ChannelIndexURL)
	statusOptions := []*views.SelectOption{
		{Label: "启用", Value: models.ChannelStatusEnabled},
		{Label: "禁用", Value: models.ChannelStatusDisabled},
	}
	typeOptions := []*views.SelectOption{
		{Label: "登录渠道", Value: models.ChannelTypeDefault},
		{Label: "签名渠道", Value: models.ChannelTypeSign},
	}

	vueTable.SetCommonOptions("status", statusOptions).SetCommonOptions("type", typeOptions)

	// 搜索表单
	searchForm := views.NewForm().Struct(ChannelIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(ChannelCreateParams{}).FlattenInputs()
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增渠道", ChannelCreateURL, createForm))
	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", ChannelDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(ChannelIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(vueTable.CommonOptions["status"])
	vueTable.GetFieldColumn("type").SetSelectFormat(vueTable.CommonOptions["type"])

	// 更新
	updateForm := views.NewForm().Struct(ChannelUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", ChannelUpdateURL, updateForm))

	return vueTable
}
