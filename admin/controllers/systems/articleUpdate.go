package systems

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

type ArticleUpdateParams struct {
	Id      uint   `json:"id" validate:"required" views:"label:ID;display:true"`
	Cover   string `json:"cover" views:"label:封面;type:image"`
	Symbol  string `json:"symbol" views:"label:标识"`
	Sort    uint8  `json:"sort" views:"label:排序;type:number"`
	Nums    uint   `json:"nums" views:"label:浏览;type:number"`
	Type    int8   `json:"type" views:"label:类型;type:select"`
	Status  int8   `json:"status" views:"label:状态;type:select"`
	Title   string `json:"title" views:"label:标题;type:translate"`
	Content string `json:"content" views:"label:内容;type:translate"`
}

// ArticleUpdate 更新文章
func ArticleUpdate(c *context.CustomCtx, bodyParams *ArticleUpdateParams) error {
	db := model.NewModel().Model(&models.Article{})

	articleInfo := models.Article{}
	result := db.Where("id = ?", bodyParams.Id).Where("admin_id = ?", c.Claims.AdminID).First(&articleInfo)
	if result.Error != nil {
		return fmt.Errorf("获取文章失败: %s", result.Error.Error())
	}

	result = db.Where("id = ?", bodyParams.Id).Where("admin_id = ?", c.Claims.AdminID).Updates(bodyParams)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新文章失败: %s", result.Error.Error()))
	}

	// 如果是帮助文棒, 那么修改 管理设置
	if articleInfo.Type == models.ArticleTypeHelper {
		articleService := service.NewSystemArticleService()
		err := articleService.UpdateHelpers(c.Rds, c.Claims.AdminID)
		if err != nil {
			return c.ErrorJson(fmt.Sprintf("更新帮助文章失败: %s", err.Error()))
		}
	}

	return c.SuccessOk()
}
