package systems

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

const (
	DefaultLang = "zh-CN"
)

// MenuCreateParams 菜单创建参数
type MenuCreateParams struct {
	Icon     string `json:"icon" validate:"required" views:"label:图标;type:image"`
	ParentID uint   `json:"parentId" views:"label:父级;type:select"`
	Type     int8   `json:"type" validate:"required" views:"label:类型;type:select"`
	Name     string `json:"name" validate:"required" views:"label:菜单名称"`
	Label    string `json:"label" validate:"required" views:"label:翻译标识"`
	Route    string `json:"route" validate:"required" views:"label:路由"`
}

// MenuCreate 菜单创建
func MenuCreate(c *context.CustomCtx, bodyParams *MenuCreateParams) error {
	adminInfo := models.AdminUser{}
	db := model.NewModel()
	result := db.Where("id = ?", c.Claims.AdminID).First(&adminInfo)
	if result.Error != nil || (adminInfo.ID != models.SuperAdminID && adminInfo.ParentID != models.SuperAdminID) {
		return c.ErrorJson("当前管理员不可操作~")
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		result = tx.Create(&models.Menu{
			AdminID:  adminInfo.ID,
			ParentID: bodyParams.ParentID,
			Type:     bodyParams.Type,
			Name:     bodyParams.Name,
			Route:    bodyParams.Route,
			Data: models.MenuData{
				Label:      bodyParams.Label,
				DarkIcon:   bodyParams.Icon,
				LightIcon:  bodyParams.Icon,
				ActiveIcon: bodyParams.Icon,
				Target:     models.MenuTargetSelf,
			},
		})

		if result.Error != nil {
			return result.Error
		}

		// 添加对应的翻译
		translation := models.Translate{
			AdminID: adminInfo.ID,
			Lang:    DefaultLang,
			Name:    fmt.Sprintf("菜单[%s]", bodyParams.Name),
			Type:    models.TranslateTypeFrontend,
			Field:   fmt.Sprintf("menu_%s", bodyParams.Label),
			Value:   bodyParams.Name,
		}
		result = tx.Create(&translation)
		if result.Error != nil {
			return result.Error
		}

		return nil
	})

	if err != nil {
		return c.ErrorJson(fmt.Sprintf("创建菜单失败: %s", err.Error()))
	}

	// 删除菜单缓存
	menuService := service.NewSystemMenuService()
	menuService.DeleteFrontendMenusByTypeCache(c.Rds, adminInfo.ID, bodyParams.Type, DefaultLang)

	return c.SuccessOk()
}
