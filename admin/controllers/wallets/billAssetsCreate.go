package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// BillAssetsCreateParams 创建账单资产参数
type BillAssetsCreateParams struct {
	AssetsID uint    `json:"assetsId" validate:"required" views:"label:资产;type:select"`
	Type     int8    `json:"type" validate:"required" views:"label:类型;type:select"`
	UserID   uint    `json:"userId" validate:"required" views:"label:账户;type:selectSearch;mask:user#username>id"`
	Amount   float64 `json:"amount" validate:"required" views:"label:金额;type:number"`
}

// BillAssetsCreate 创建账单资产
func BillAssetsCreate(c *context.CustomCtx, bodyParams *BillAssetsCreateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	// 用户信息
	userInfo := models.User{}
	result := db.Where("admin_id IN ?", subAdminIDs).Where("id = ?", bodyParams.UserID).First(&userInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("账户不存在: %v", result.Error))
	}

	assetsInfo := &models.WalletAssets{}
	if bodyParams.AssetsID != 0 {
		result = db.Where("id = ?", bodyParams.AssetsID).Where("admin_id = ?", c.Claims.MerchantID).First(assetsInfo)
		if result.Error != nil {
			return c.ErrorJson(fmt.Sprintf("资产不存在: %v", result.Error))
		}
	}

	// 写入账单
	err := db.Transaction(func(tx *gorm.DB) error {
		billService := service.NewWalletBillService()
		return billService.CreateBill(tx, &userInfo, assetsInfo, bodyParams.Type, 0, bodyParams.Amount)
	})
	if err != nil {
		return c.ErrorJson(fmt.Sprintf("创建账单失败: %v", err))
	}
	return c.SuccessOk()
}
