package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	PaymentAssetsIndexURL      = "/wallets/assets/payment/index"
	PaymentAssetsCreateURL     = "/wallets/assets/payment/create"
	PaymentAssetsUpdateURL     = "/wallets/assets/payment/update"
	PaymentAssetsDeleteURL     = "/wallets/assets/payment/delete"
	PaymentAssetsUpdateDataURL = "/wallets/assets/payment/update/data"
	PaymentAssetsUpdateConfURL = "/wallets/assets/payment/update/conf"
)

// PaymentAssetsIndexParams 支付资产列表参数
type PaymentAssetsIndexParams struct {
	Name       string            `json:"name" views:"label:名称"`
	AssetsID   uint              `json:"assetsId" views:"label:资产;type:select"`
	Mode       int8              `json:"mode" views:"label:模式;type:select"`
	Type       int8              `json:"type" views:"label:类型;type:select"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// PaymentAssetsIndexRows 支付资产列表行
type PaymentAssetsIndexRows struct {
	ID        uint                     `json:"id" column:"label:ID"`
	Icon      string                   `json:"icon" column:"label:图标"`
	Name      string                   `json:"name" column:"label:名称"`
	AssetsID  uint                     `json:"assetsId" column:"label:资产"`
	Currency  string                   `json:"currency" column:"label:符号"`
	Mode      int8                     `json:"mode" column:"label:模式"`
	Type      int8                     `json:"type" column:"label:类型"`
	MaxAmount float64                  `json:"maxAmount" column:"label:最大金额"`
	MinAmount float64                  `json:"minAmount" column:"label:最小金额"`
	Fee       float64                  `json:"fee" column:"label:手续费"`
	StartTime string                   `json:"startTime" column:"label:开始时间"`
	EndTime   string                   `json:"endTime" column:"label:结束时间"`
	IsChats   int8                     `json:"isChats" column:"label:跳转客服"`
	IsProof   int8                     `json:"isProof" column:"label:开启凭证"`
	Status    int8                     `json:"status" column:"label:状态"`
	Data      models.WalletPaymentData `json:"data"`
	Conf      models.WalletPaymentConf `json:"conf"`
	Desc      string                   `json:"desc"`
}

// PaymentAssetsIndex 支付资产列表
func PaymentAssetsIndex(c *context.CustomCtx, bodyParams *PaymentAssetsIndexParams) error {
	db := model.NewModel()
	data := &model.IndexData{Items: make([]*PaymentAssetsIndexRows, 0), Count: 0}

	query := db.Equal("name", bodyParams.Name).
		Equal("assets_id", bodyParams.AssetsID).
		Equal("mode", bodyParams.Mode).
		Equal("type", bodyParams.Type).
		Equal("status", bodyParams.Status).Model(&models.WalletPayment{})

	query.Where("admin_id", c.Claims.AdminID).Where("assets_id > 0").Count(&data.Count)
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	for _, v := range data.Items.([]*PaymentAssetsIndexRows) {
		v.Desc = fmt.Sprintf("walletPaymentDesc%v", v.ID)
	}

	return c.SuccessJson(data)
}

// PaymentAssetsIndexConfigure 支付资产列表配置
func PaymentAssetsIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(PaymentAssetsIndexURL)
	assetsService := service.NewWalletAssetsService()
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	assetsOptions := assetsService.GetAssetsOptions(subAdminIDs)
	modeOptions := []*views.SelectOption{
		{Label: "充值", Value: models.PaymentModeAssetDeposit},
		{Label: "提现", Value: models.PaymentModeAssetWithdrawal},
	}
	statusOptions := []*views.SelectOption{
		{Label: "启用", Value: models.PaymentStatusEnabled},
		{Label: "禁用", Value: models.PaymentStatusDisabled},
	}
	typeOptions := []*views.SelectOption{
		{Label: "数字货币", Value: models.PaymentTypeCrypto},
	}
	isChatsOptions := []*views.SelectOption{
		{Label: "是", Value: model.BoolTrue},
		{Label: "否", Value: model.BoolFalse},
	}
	isProofOptions := []*views.SelectOption{
		{Label: "是", Value: model.BoolTrue},
		{Label: "否", Value: model.BoolFalse},
	}

	vueTable.SetCommonOptions("mode", modeOptions).SetCommonOptions("status", statusOptions).
		SetCommonOptions("type", typeOptions).SetCommonOptions("assetsId", assetsOptions).
		SetCommonOptions("isChats", isChatsOptions).SetCommonOptions("isProof", isProofOptions)

	// 搜索
	searchForm := views.NewForm().Struct(PaymentAssetsIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(PaymentAssetsCreateParams{}).FlattenInputs()
	if len(assetsOptions) > 0 {
		createForm.SetFieldInputParam("assetsId", "default", assetsOptions[0].Value)
	}
	createForm.SetFieldInputParam("mode", "default", models.PaymentModeAssetDeposit)
	createForm.SetFieldInputParam("type", "default", models.PaymentTypeCrypto)

	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增支付", PaymentAssetsCreateURL, createForm))
	// deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	// vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", PaymentAssetsDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(PaymentAssetsIndexRows{})
	vueTable.GetFieldColumn("icon").SetAvatarFormat("icon")
	vueTable.GetFieldColumn("assetsId").SetSelectFormat(assetsOptions)
	vueTable.GetFieldColumn("mode").SetSelectFormat(modeOptions)
	vueTable.GetFieldColumn("type").SetSelectFormat(typeOptions)
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("isChats").SetSelectFormat(isChatsOptions)
	vueTable.GetFieldColumn("isProof").SetSelectFormat(isProofOptions)
	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(PaymentAssetsUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", PaymentAssetsUpdateURL, updateForm))

	// 数据表格 - 更新数据
	updateDataForm := views.NewForm().Struct(PaymentAssetsUpdateDataParams{}).FlattenInputs().SetChildFormFlattenInputs("data")
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("配置", PaymentAssetsUpdateDataURL, updateDataForm))

	// 数据表格 - 设置
	updateConfForm := views.NewForm().Struct(PaymentAssetsUpdateConfParams{}).FlattenInputs().SetChildFormFlattenInputs("conf")
	updateConfForm.ChildrenForm["conf"].SetFieldInputParam("paymentType", "options", typeOptions)
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("设置", PaymentAssetsUpdateConfURL, updateConfForm).SetDisplay(fmt.Sprintf("row.type == %d", models.PaymentTypeThirdParty)))

	return vueTable
}
