package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// PaymentAssetsCreateParams 新增支付资产参数
type PaymentAssetsCreateParams struct {
	Icon     string `json:"icon" validate:"required" views:"label:图标;type:image"`
	AssetsID uint   `json:"assetsId" validate:"required" views:"label:资产;type:select"`
	Mode     int8   `json:"mode" validate:"required" views:"label:模式;type:select"`
	Type     int8   `json:"type" validate:"required" views:"label:类型;type:select"`
	Name     string `json:"name" validate:"required" views:"label:名称"`
	Currency string `json:"currency" validate:"required" views:"label:货币符号"`
}

// PaymentAssetsCreate 新增支付资产
func PaymentAssetsCreate(c *context.CustomCtx, bodyParams *PaymentAssetsCreateParams) error {
	db := model.NewModel()

	assetsInfo := &models.WalletAssets{}
	if err := db.Where("id = ?", bodyParams.AssetsID).Where("admin_id = ?", c.Claims.MerchantID).First(assetsInfo).Error; err != nil {
		return c.ErrorJson(fmt.Sprintf("资产不存在: %v", err))
	}

	result := db.Create(&models.WalletPayment{
		AdminID:   c.Claims.AdminID,
		AssetsID:  bodyParams.AssetsID,
		Name:      bodyParams.Name,
		Icon:      bodyParams.Icon,
		Currency:  bodyParams.Currency,
		Mode:      bodyParams.Mode,
		Type:      bodyParams.Type,
		Fee:       0.01,
		MinAmount: 100,
		MaxAmount: 50000,
		IsChats:   model.BoolFalse,
		Data:      models.WalletPaymentData{},
	})
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("新增失败: %v", result.Error))
	}

	return c.SuccessOk()
}
