package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// TransferAssetsCreateParams 转账资产
type TransferAssetsCreateParams struct {
	AssetsID   uint    `json:"assetsId" validate:"required" views:"label:资产;type:select"`
	SenderID   uint    `json:"senderId" validate:"required" views:"label:发送者;type:selectSearch;mask:user#username>id"`
	ReceiverID uint    `json:"receiverId" validate:"required" views:"label:接收者;type:selectSearch;mask:user#username>id"`
	Amount     float64 `json:"amount" validate:"required" views:"label:金额;type:number"`
}

// TransferAssetsCreate 转账资产
func TransferAssetsCreate(c *context.CustomCtx, bodyParams *TransferAssetsCreateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	if bodyParams.SenderID == bodyParams.ReceiverID {
		return c.ErrorJson("发送者和接收者不能相同")
	}

	db := model.NewModel()

	// 发送者信息
	senderInfo := &models.User{}
	result := db.Model(&models.User{}).Where("id = ?", bodyParams.SenderID).Where("admin_id IN ?", subAdminIDs).First(senderInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("发送者信息获取失败: %v", result.Error))
	}

	// 接收者信息
	receiverInfo := &models.User{}
	result = db.Model(&models.User{}).Where("id = ?", bodyParams.ReceiverID).Where("admin_id IN ?", subAdminIDs).First(receiverInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("接收者信息获取失败: %v", result.Error))
	}

	// 资产信息
	assetsInfo := &models.WalletAssets{}
	result = db.Model(&models.WalletAssets{}).Where("id = ?", bodyParams.AssetsID).Where("admin_id IN ?", subAdminIDs).First(assetsInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("资产信息获取失败: %v", result.Error))
	}

	walletService := service.NewWalletService()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 转账记录
		result := tx.Create(&models.Transfer{
			AdminID:    c.Claims.AdminID,
			AssetsID:   assetsInfo.ID,
			SenderID:   senderInfo.ID,
			ReceiverID: receiverInfo.ID,
			Amount:     bodyParams.Amount,
			Data:       models.TransferData{},
		})
		if result.Error != nil {
			return fmt.Errorf("转账记录创建失败: %v", result.Error)
		}

		// 发送者钱包
		err := walletService.SpendAssets(tx, c.Rds, c.Lang, models.BillTypeTransferSend, 0, senderInfo, assetsInfo, bodyParams.Amount)
		if err != nil {
			return fmt.Errorf("发送者钱包扣款失败: %v", err)
		}

		// 接收者钱包
		err = walletService.IncreaseAssets(tx, c.Rds, c.Lang, models.BillTypeTransferReceive, 0, receiverInfo, assetsInfo, bodyParams.Amount)
		if err != nil {
			return fmt.Errorf("接收者钱包加款失败: %v", err)
		}
		return nil
	})
	if err != nil {
		return c.ErrorJson(fmt.Sprintf("转账失败: %v", err))
	}

	return c.SuccessOk()
}
