package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// AssetsCreateParams 创建资产参数
type AssetsCreateParams struct {
	Icon     string `json:"icon" validate:"required" views:"label:图标;type:image"`
	Type     int8   `json:"type" validate:"required" views:"label:类型;type:select"`
	Name     string `json:"name" validate:"required" views:"label:名称"`
	Currency string `json:"currency" validate:"required" views:"label:货币符号"`
}

// AssetsCreate 创建资产
func AssetsCreate(c *context.CustomCtx, bodyParams *AssetsCreateParams) error {
	db := model.NewModel()

	result := db.Create(&models.WalletAssets{
		AdminID:  c.Claims.AdminID,
		Name:     bodyParams.Name,
		Currency: bodyParams.Currency,
		Type:     bodyParams.Type,
		Icon:     bodyParams.Icon,
		Data:     models.WalletAssetsData{},
	})
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("创建失败: %v", result.Error))
	}

	return c.SuccessOk()
}
