package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// BillUpdateParams 更新账单参数
type BillUpdateParams struct {
	ID    uint    `json:"id" validate:"required" views:"label:ID;display:true"`
	Name  string  `json:"name" views:"label:名称"`
	Money float64 `json:"money" views:"label:金额;type:number"`
}

// BillUpdate 更新账单
func BillUpdate(c *context.CustomCtx, bodyParams *BillUpdateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	result := db.Model(&models.WalletBill{}).Where("id = ?", bodyParams.ID).Where("admin_id IN ?", subAdminIDs).Updates(bodyParams)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新账单失败: %v", result.Error))
	}

	return c.SuccessOk()
}
