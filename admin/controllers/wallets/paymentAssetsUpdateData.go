package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// PaymentAssetsUpdateDataParams 支付资产更新数据
type PaymentAssetsUpdateDataParams struct {
	ID   uint                     `json:"id" validate:"required" views:"label:ID;display:true"`
	Data models.WalletPaymentData `json:"data" views:"label:数据;type:struct"`
}

// PaymentAssetsUpdateData 支付资产更新数据
func PaymentAssetsUpdateData(c *context.CustomCtx, bodyParams *PaymentAssetsUpdateDataParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	// 强制更新 Data 字段下的所有字段，包括零值
	result := db.Model(&models.WalletPayment{}).
		Where("id", bodyParams.ID).
		Where("admin_id IN ?", subAdminIDs).
		Select("data").
		Updates(map[string]interface{}{
			"data": bodyParams.Data,
		})

	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新失败: %v", result.Error))
	}

	return c.SuccessOk()
}
