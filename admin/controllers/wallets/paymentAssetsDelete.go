package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// PaymentAssetsDelete 删除支付资产
func PaymentAssetsDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	db := model.NewModel()

	for _, v := range bodyParams.Ids {
		if err := db.Where("id = ?", v).Where("assets_id > 0").Where("admin_id IN ?", subAdminIDs).Delete(&models.WalletPayment{}).Error; err != nil {
			return c.<PERSON><PERSON>r<PERSON><PERSON>(fmt.Sprintf("删除失败: %v", err))
		}
	}

	return c.SuccessOk()
}
