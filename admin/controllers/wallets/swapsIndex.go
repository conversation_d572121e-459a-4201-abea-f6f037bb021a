package wallets

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	SwapsIndexURL  = "/wallets/swaps/index"
	SwapsCreateURL = "/wallets/swaps/create"
	SwapsUpdateURL = "/wallets/swaps/update"
	SwapsDeleteURL = "/wallets/swaps/delete"
)

// SwapsIndexParams 资产交换
type SwapsIndexParams struct {
	AdminID         uint                   `json:"adminId" views:"label:管理;type:select"`
	UserID          uint                   `json:"userId" views:"label:账户;type:selectSearch;mask:user#username>id"`
	SendAssetsID    uint                   `json:"sendAssetsId" views:"label:发送资产;type:select"`
	ReceiveAssetsID uint                   `json:"receiveAssetsId" views:"label:接收资产;type:select"`
	Type            int8                   `json:"type" views:"label:类型;type:select"`
	Status          int                    `json:"status" views:"label:状态;type:select"`
	CreatedAt       *model.RangeDatePicker `json:"createdAt" views:"label:时间;type:dateRange"`
	Pagination      *model.Pagination      `json:"pagination" views:"-"`
}

// SwapsIndexRows 资产交换
type SwapsIndexRows struct {
	ID              uint        `json:"id" column:"label:ID;display:true"`
	AdminID         uint        `json:"adminId" column:"label:管理"`
	UserName        string      `json:"username" column:"label:账户;scanField:userInfo.username" gorm:"-"`
	SendAssetsID    uint        `json:"sendAssetsId" column:"label:发送资产"`
	SendAmount      float64     `json:"sendAmount" column:"label:发送金额;type:number"`
	ReceiveAssetsID uint        `json:"receiveAssetsId" column:"label:接收资产"`
	ReceiveAmount   float64     `json:"receiveAmount" column:"label:接收金额;type:number"`
	Rate            float64     `json:"rate" column:"label:汇率;type:number"`
	Fee             float64     `json:"fee" column:"label:手续费;type:number"`
	Type            int8        `json:"type" column:"label:类型;type:select"`
	Status          int8        `json:"status" column:"label:状态;type:select"`
	CreatedAt       string      `json:"createdAt" column:"label:时间;type:date"`
	UserID          uint        `json:"userId"`
	UserInfo        models.User `json:"userInfo" gorm:"foreignKey:UserID"`
}

// SwapsIndex 资产交换
func SwapsIndex(c *context.CustomCtx, bodyParams *SwapsIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	data := &model.IndexData{Items: make([]*SwapsIndexRows, 0), Count: 0}
	query := db.Equal("admin_id", bodyParams.AdminID).
		Equal("user_id", bodyParams.UserID).
		Equal("send_assets_id", bodyParams.SendAssetsID).
		Equal("receive_assets_id", bodyParams.ReceiveAssetsID).
		Equal("type", bodyParams.Type).
		Equal("status", bodyParams.Status).
		BetweenTime("created_at", bodyParams.CreatedAt, c.TimeZone).
		Model(&models.Swaps{})

	query.Where("admin_id IN ?", subAdminIDs).Count(&data.Count)
	query.Preload("UserInfo")
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}

// ExchangeIndexConfigure 兑换订单
func SwapsIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(SwapsIndexURL)
	adminService := service.NewAdminUserService()
	assetsService := service.NewWalletAssetsService()
	adminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	assetsOptions := assetsService.GetAssetsOptions(subAdminIDs)
	assetsOptions = append([]*views.SelectOption{{Label: "余额", Value: 0}}, assetsOptions...)
	typeOptions := []*views.SelectOption{
		{Label: "资产交换", Value: models.SwapsTypeAssets},
	}
	statusOptions := []*views.SelectOption{
		{Label: "完成", Value: models.SwapsStatusCompleted},
	}
	vueTable.SetCommonOptions("type", typeOptions).SetCommonOptions("status", statusOptions).
		SetCommonOptions("adminId", adminOptions).
		SetCommonOptions("sendAssetsId", assetsOptions).
		SetCommonOptions("receiveAssetsId", assetsOptions)

	// 搜索
	searchForm := views.NewForm().Struct(SwapsIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(SwapsCreateParams{}).FlattenInputs()
	createForm.SetFieldInputParam("sendAssetsId", "default", assetsOptions[0].Value)
	createForm.SetFieldInputParam("receiveAssetsId", "default", assetsOptions[1].Value)

	vueTable.SetTools(0, views.NewFormCreateButtonDialog("资产交换", SwapsCreateURL, createForm))
	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", SwapsDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(SwapsIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("type").SetSelectFormat(typeOptions)
	vueTable.GetFieldColumn("adminId").SetSelectFormat(adminOptions)
	vueTable.GetFieldColumn("sendAssetsId").SetSelectFormat(assetsOptions)
	vueTable.GetFieldColumn("receiveAssetsId").SetSelectFormat(assetsOptions)

	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(SwapsUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", SwapsUpdateURL, updateForm))

	return vueTable
}
