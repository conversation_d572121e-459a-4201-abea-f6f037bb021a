package wallets

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	BillAssetsIndexURL  = "/wallets/assets/bill/index"
	BillAssetsCreateURL = "/wallets/assets/bill/create"
	BillAssetsUpdateURL = "/wallets/assets/bill/update"
	BillAssetsDeleteURL = "/wallets/assets/bill/delete"
)

// BillAssetsIndexParams 账单资产列表参数
type BillAssetsIndexParams struct {
	AdminID    uint                   `json:"adminId" views:"label:管理;type:select"`
	UserID     uint                   `json:"userId" views:"label:账户;type:selectSearch;mask:user#username>id"`
	Name       string                 `json:"name" views:"label:名称"`
	AssetsID   uint                   `json:"assetsId" views:"label:资产;type:select"`
	Type       int8                   `json:"type" views:"label:类型;type:select"`
	CreatedAt  *model.RangeDatePicker `json:"createdAt" views:"label:时间;type:dateRange"`
	Pagination *model.Pagination      `json:"pagination" views:"-"`
}

// BillAssetsIndexRows 账单资产列表行
type BillAssetsIndexRows struct {
	ID        uint        `json:"id" column:"label:ID"`
	AdminID   uint        `json:"adminId" column:"label:管理"`
	UserName  string      `json:"username" column:"label:账户;scanField:userInfo.username" gorm:"-"`
	AssetsID  uint        `json:"assetsId" column:"label:资产"`
	Name      string      `json:"name" column:"label:名称"`
	Type      string      `json:"type" column:"label:类型"`
	Money     float64     `json:"money" column:"label:金额"`
	Balance   float64     `json:"balance" column:"label:余额"`
	CreatedAt time.Time   `json:"createdAt" column:"label:时间;type:date"`
	UserID    uint        `json:"userId"`
	UserInfo  models.User `json:"userInfo" gorm:"foreignKey:UserID"`
}

// BillAssetsIndex 账单资产列表
func BillAssetsIndex(c *context.CustomCtx, bodyParams *BillAssetsIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	data := &model.IndexData{Items: make([]*BillAssetsIndexRows, 0), Count: 0}
	db := model.NewModel()
	query := db.Equal("user_id", bodyParams.UserID).Equal("admin_id", bodyParams.AdminID).
		Like("name", bodyParams.Name).Equal("type", bodyParams.Type).Equal("assets_id", bodyParams.AssetsID).
		BetweenTime("created_at", bodyParams.CreatedAt, c.TimeZone).
		Model(&models.WalletBill{})

	query.Where("admin_id IN ?", subAdminIDs).Where("assets_id > 0").Count(&data.Count)
	query.Preload("UserInfo")
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	for _, v := range data.Items.([]*BillAssetsIndexRows) {
		v.Balance += v.Money
	}

	return c.SuccessJson(data)
}

// BillAssetsIndexConfigure 账单资产列表配置
func BillAssetsIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(BillAssetsIndexURL)
	assetsService := service.NewWalletAssetsService()
	adminService := service.NewAdminUserService()
	adminsOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	assetsOptions := assetsService.GetAssetsOptions(subAdminIDs)
	billService := service.NewWalletBillService()
	typesOptions := billService.GetBillOptions()

	vueTable.SetCommonOptions("assetsId", assetsOptions).SetCommonOptions("type", typesOptions).SetCommonOptions("adminId", adminsOptions)

	// 搜索
	searchForm := views.NewForm().Struct(BillAssetsIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(BillAssetsCreateParams{}).FlattenInputs()
	createForm.SetFieldInputParam("type", "default", typesOptions[0].Value)
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增账单", BillAssetsCreateURL, createForm))
	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", BillAssetsDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(BillAssetsIndexRows{})
	vueTable.GetFieldColumn("assetsId").SetSelectFormat(assetsOptions)
	vueTable.GetFieldColumn("type").SetSelectFormat(typesOptions)
	vueTable.GetFieldColumn("adminId").SetSelectFormat(adminsOptions)
	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(BillAssetsUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", BillAssetsUpdateURL, updateForm))

	return vueTable
}
