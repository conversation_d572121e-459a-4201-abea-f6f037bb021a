package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// TransferDelete 删除转账
func TransferDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	for _, v := range bodyParams.Ids {
		result := db.Model(&models.Transfer{}).Where("id = ?", v).Where("admin_id IN ?", subAdminIDs).Delete(&models.Transfer{})
		if result.Error != nil {
			return c.<PERSON><PERSON>r<PERSON><PERSON>(fmt.Sprintf("删除失败: %v", result.Error))
		}
	}

	return c.SuccessOk()
}
