package wallets

import (
	"fmt"
	"math"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	DepositIndexURL  = "/wallets/deposit/index"
	DepositCreateURL = "/wallets/deposit/create"
)

// DepositIndexParams 充值订单列表参数
type DepositIndexParams struct {
	AdminID    uint                   `json:"adminId" views:"label:管理;type:select"`
	UserID     uint                   `json:"userId" views:"label:用户;type:selectSearch;mask:user#username>id"`
	Status     int8                   `json:"status" views:"label:状态;type:select"`
	CreatedAt  *model.RangeDatePicker `json:"createdAt" views:"label:提交时间;type:dateRange"`
	Pagination *model.Pagination      `json:"pagination" views:"-"`
}

// DepositIndexRows 充值订单行
type DepositIndexRows struct {
	ID          uint                   `json:"id" column:"label:ID;display:true"`
	AdminID     uint                   `json:"adminId" column:"label:管理"`
	UserName    string                 `json:"userName" column:"label:用户;scanField:userInfo.username" gorm:"-"`
	BankName    string                 `json:"bankName" column:"label:银行|公链;scanField:data.data.bankName" gorm:"-"`
	BankCardNo  string                 `json:"bankCardNo" column:"label:卡号|地址;scanField:data.data.bankCardNo" gorm:"-"`
	RealName    string                 `json:"realName" column:"label:姓名|Token;scanField:data.data.realName" gorm:"-"`
	BankCode    string                 `json:"bankCode" column:"label:代号|简写;scanField:data.data.bankCode" gorm:"-"`
	BankAddress string                 `json:"bankAddress" column:"label:支付地址;scanField:data.data.bankAddress" gorm:"-"`
	Proof       string                 `json:"proof" column:"label:凭证;"`
	Money       float64                `json:"money" column:"label:金额;type:number"`
	ActualMoney float64                `json:"actualMoney" column:"label:兑换金额;type:number" gorm:"-"`
	Status      int8                   `json:"status" column:"label:状态;type:select"`
	CreatedAt   string                 `json:"createdAt" column:"label:提交时间;type:date"`
	UpdatedAt   string                 `json:"updatedAt" column:"label:更新时间;type:date"`
	SourceID    uint                   `json:"sourceId"`
	Data        models.WalletOrderData `json:"data"`
	UserID      uint                   `json:"userId"`
	UserInfo    models.User            `json:"userInfo" gorm:"foreignKey:UserID"`
}

// DepositIndex 充值订单列表
func DepositIndex(c *context.CustomCtx, bodyParams *DepositIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	data := &model.IndexData{Items: make([]*DepositIndexRows, 0), Count: 0}
	db := model.NewModel()
	query := db.Equal("admin_id", bodyParams.AdminID).
		Equal("user_id", bodyParams.UserID).
		Equal("status", bodyParams.Status).
		BetweenTime("created_at", bodyParams.CreatedAt, c.TimeZone).
		Model(&models.WalletOrder{})

	query.Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeDeposit).Where("assets_id = 0").Count(&data.Count)
	query.Preload("UserInfo")
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	for _, v := range data.Items.([]*DepositIndexRows) {
		v.ActualMoney = v.Money
		if v.Data.Currency != "" {
			assetsInfo := models.WalletAssets{}
			db.Model(&models.WalletAssets{}).Where("currency = ?", v.Data.Currency).Where("admin_id = ?", v.AdminID).Find(&assetsInfo)
			if assetsInfo.ID != 0 {
				decimals := math.Pow10(int(assetsInfo.Decimals))
				v.ActualMoney = math.Round(v.Money*assetsInfo.Rate*decimals) / decimals
			}
		}
	}

	return c.SuccessJson(data)
}

// DepositIndexConfigure 充值订单配置
func DepositIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(DepositIndexURL)
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	paymentService := service.NewWalletPaymentService()
	adminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)
	paymentOptions := paymentService.GetPaymentOptions(subAdminIDs, []int8{models.PaymentTypeBankCard, models.PaymentTypeCrypto}, []int8{models.PaymentModeDeposit})
	statusOptions := []*views.SelectOption{
		{Label: "审核", Value: models.WalletOrderStatusPending},
		{Label: "完成", Value: models.WalletOrderStatusCompleted},
		{Label: "拒绝", Value: models.WalletOrderStatusRejected},
	}
	pendingStatusOptions := []*views.SelectOption{
		{Label: "完成", Value: models.WalletOrderStatusCompleted},
		{Label: "拒绝", Value: models.WalletOrderStatusRejected},
	}
	isHintOptions := []*views.SelectOption{
		{Label: "开启", Value: model.BoolTrue},
		{Label: "关闭", Value: model.BoolFalse},
	}
	vueTable.SetCommonOptions("adminId", adminOptions).SetCommonOptions("status", statusOptions).
		SetCommonOptions("sourceId", paymentOptions).SetCommonOptions("isHint", isHintOptions).
		SetCommonOptions("pendingStatus", pendingStatusOptions)
	// 搜索
	searchForm := views.NewForm().Struct(DepositIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(DepositCreateParams{}).FlattenInputs()
	if len(paymentOptions) > 0 {
		createForm.SetFieldInputParam("sourceId", "default", paymentOptions[0].Value)
	}
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("充值订单", DepositCreateURL, createForm))
	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", OrderDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(DepositIndexRows{})
	vueTable.GetFieldColumn("proof").SetAvatarFormat("proof")
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("adminId").SetSelectFormat(adminOptions)

	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(OrderUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", OrderUpdateURL, updateForm))

	// 数据表格 - 更新状态
	statusForm := views.NewForm().Struct(OrderStatusParams{}).FlattenInputs()
	statusForm.SetFieldInputParam("pendingStatus", "default", models.WalletOrderStatusCompleted)
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("审核", OrderStatusURL, statusForm).SetDisplay(fmt.Sprintf("row.status == %d", models.WalletOrderStatusPending)))

	return vueTable
}
