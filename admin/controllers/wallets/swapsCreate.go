package wallets

import (
	"errors"
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// SwapsCreateParams 资产交换参数
type SwapsCreateParams struct {
	UserID          uint    `json:"userId" views:"label:账户;type:selectSearch;mask:user#username>id"`
	SendAssetsID    uint    `json:"sendAssetsId" views:"label:发送资产;type:select"`
	ReceiveAssetsID uint    `json:"receiveAssetsId" views:"label:接收资产;type:select"`
	SendAmount      float64 `json:"sendAmount" views:"label:金额;type:number"`
}

// SwapsCreate 资产交换
func SwapsCreate(c *context.CustomCtx, bodyParams *SwapsCreateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	db := model.NewModel()

	// 用户信息
	userInfo := &models.User{}
	result := db.Where("id = ?", bodyParams.UserID).Where("admin_id IN ?", subAdminIDs).First(userInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("获取用户信息失败: %s", result.Error.Error()))
	}

	// 相同的资产不能兑换
	if bodyParams.SendAssetsID == bodyParams.ReceiveAssetsID {
		return c.ErrorJson("发送资产和接收资产不能相同")
	}

	walletService := service.NewWalletService()
	err := db.Transaction(func(tx *gorm.DB) error {
		var receiveAmount, receiveRate float64
		switch {
		case bodyParams.SendAssetsID == 0 && bodyParams.ReceiveAssetsID > 0:
			// 扣除余额
			err := walletService.SpendBalance(tx, c.Rds, c.Lang, models.BillTypeSwapsSend, userInfo.ID, userInfo, bodyParams.SendAmount)
			if err != nil {
				return err
			}

			assetsInfo := &models.WalletAssets{}
			result := tx.Where("id = ?", bodyParams.ReceiveAssetsID).First(assetsInfo)
			if result.Error != nil {
				return errors.New("接收资产不存在")
			}

			// 按照汇率得到接收金额
			receiveRate = 1 / assetsInfo.Rate
			receiveAmount = bodyParams.SendAmount * receiveRate

			// 增加资产
			err = walletService.IncreaseAssets(tx, c.Rds, c.Lang, models.BillTypeSwapsReceive, userInfo.ID, userInfo, assetsInfo, receiveAmount)
			if err != nil {
				return err
			}
		case bodyParams.SendAssetsID > 0 && bodyParams.ReceiveAssetsID == 0:
			assetsInfo := &models.WalletAssets{}
			result := tx.Where("id = ?", bodyParams.SendAssetsID).First(assetsInfo)
			if result.Error != nil {
				return errors.New("发送资产不存在")
			}

			// 扣除资产
			err := walletService.SpendAssets(tx, c.Rds, c.Lang, models.BillTypeSwapsSend, userInfo.ID, userInfo, assetsInfo, bodyParams.SendAmount)
			if err != nil {
				return err
			}

			receiveRate = assetsInfo.Rate
			receiveAmount = bodyParams.SendAmount * receiveRate

			// 增加余额
			err = walletService.IncreaseBalance(tx, c.Rds, c.Lang, models.BillTypeSwapsReceive, userInfo.ID, userInfo, receiveAmount)
			if err != nil {
				return err
			}

		default:
			sendAssetsInfo := &models.WalletAssets{}
			result := tx.Where("id = ?", bodyParams.SendAssetsID).First(sendAssetsInfo)
			if result.Error != nil {
				return errors.New("发送资产不存在")
			}

			// 扣除发送资产
			err := walletService.SpendAssets(tx, c.Rds, c.Lang, models.BillTypeSwapsSend, userInfo.ID, userInfo, sendAssetsInfo, bodyParams.SendAmount)
			if err != nil {
				return err
			}

			receiveAssetsInfo := &models.WalletAssets{}
			result = tx.Where("id = ?", bodyParams.ReceiveAssetsID).First(receiveAssetsInfo)
			if result.Error != nil {
				return errors.New("接收资产不存在")
			}

			// 增加接收资产
			receiveRate = sendAssetsInfo.Rate / receiveAssetsInfo.Rate
			receiveAmount = bodyParams.SendAmount * receiveRate
			err = walletService.IncreaseAssets(tx, c.Rds, c.Lang, models.BillTypeSwapsReceive, userInfo.ID, userInfo, receiveAssetsInfo, receiveAmount)
			if err != nil {
				return err
			}
		}

		result := tx.Create(&models.Swaps{
			AdminID:         userInfo.AdminID,
			UserID:          userInfo.ID,
			SendAssetsID:    bodyParams.SendAssetsID,
			ReceiveAssetsID: bodyParams.ReceiveAssetsID,
			SendAmount:      bodyParams.SendAmount,
			ReceiveAmount:   receiveAmount,
			Rate:            receiveRate,
			Fee:             0,
			Status:          models.SwapsStatusCompleted,
		})
		if result.Error != nil {
			return result.Error
		}

		return nil
	})
	if err != nil {
		return c.ErrorJson(fmt.Sprintf("闪兑失败: %s", err.Error()))
	}

	return c.SuccessOk()
}
