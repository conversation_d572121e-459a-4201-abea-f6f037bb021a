package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	DepositAssetsIndexURL  = "/wallets/assets/deposit/index"
	DepositAssetsCreateURL = "/wallets/assets/deposit/create"
)

// DepositAssetsIndexParams 充值订单列表参数
type DepositAssetsIndexParams struct {
	AdminID    uint                   `json:"adminId" views:"label:管理;type:select"`
	OrderSN    string                 `json:"orderSn" views:"label:订单编号"`
	UserID     uint                   `json:"userId" views:"label:用户;type:selectSearch;mask:user#username>id"`
	SourceID   uint                   `json:"sourceId" views:"label:来源;type:select"`
	Status     int8                   `json:"status" views:"label:状态;type:select"`
	CreatedAt  *model.RangeDatePicker `json:"createdAt" views:"label:提交时间;type:dateRange"`
	Pagination *model.Pagination      `json:"pagination" views:"-"`
}

// DepositAssetsIndexRows 充值订单行
type DepositAssetsIndexRows struct {
	ID          uint                   `json:"id" column:"label:ID;display:true"`
	AdminID     uint                   `json:"adminId" column:"label:管理"`
	UserName    string                 `json:"userName" column:"label:用户;scanField:userInfo.username" gorm:"-"`
	BankName    string                 `json:"bankName" column:"label:银行|公链;scanField:data.data.bankName" gorm:"-"`
	BankCardNo  string                 `json:"bankCardNo" column:"label:卡号|地址;scanField:data.data.bankCardNo" gorm:"-"`
	RealName    string                 `json:"realName" column:"label:姓名|Token;scanField:data.data.realName" gorm:"-"`
	BankCode    string                 `json:"bankCode" column:"label:代号|简写;scanField:data.data.bankCode" gorm:"-"`
	BankAddress string                 `json:"bankAddress" column:"label:支付地址;scanField:data.data.bankAddress" gorm:"-"`
	Proof       string                 `json:"proof" column:"label:凭证"`
	OrderSN     string                 `json:"orderSn" column:"label:编号"`
	Money       float64                `json:"money" column:"label:金额;type:number"`
	Fee         float64                `json:"fee" column:"label:手续费;type:number"`
	IsHint      int8                   `json:"isHint" column:"label:提示音;type:select"`
	Status      int8                   `json:"status" column:"label:状态;type:select"`
	Reason      string                 `json:"reason" column:"label:原因"`
	CreatedAt   string                 `json:"createdAt" column:"label:提交时间;type:date"`
	UpdatedAt   string                 `json:"updatedAt" column:"label:更新时间;type:date"`
	SourceID    uint                   `json:"sourceId"`
	Data        models.WalletOrderData `json:"data"`
	UserID      uint                   `json:"userId"`
	UserInfo    models.User            `json:"userInfo" gorm:"foreignKey:UserID"`
}

// DepositAssetsIndex 充值订单列表
func DepositAssetsIndex(c *context.CustomCtx, bodyParams *DepositAssetsIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	data := &model.IndexData{Items: make([]*DepositAssetsIndexRows, 0), Count: 0}
	db := model.NewModel()
	query := db.Equal("order_sn", bodyParams.OrderSN).
		Equal("admin_id", bodyParams.AdminID).
		Equal("user_id", bodyParams.UserID).
		Equal("source_id", bodyParams.SourceID).
		Equal("status", bodyParams.Status).
		BetweenTime("created_at", bodyParams.CreatedAt, c.TimeZone).
		Model(&models.WalletOrder{})

	query.Where("admin_id IN ?", subAdminIDs).Where("type = ?", models.WalletOrderTypeAssetDeposit).Where("assets_id > 0").Count(&data.Count)
	query.Preload("UserInfo")
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}

// DepositAssetsIndexConfigure 充值订单配置
func DepositAssetsIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(DepositAssetsIndexURL)
	adminService := service.NewAdminUserService()
	adminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)
	paymentService := service.NewWalletPaymentService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	paymentOptions := paymentService.GetPaymentOptions(subAdminIDs, []int8{models.PaymentTypeCrypto}, []int8{models.PaymentModeAssetDeposit})
	statusOptions := []*views.SelectOption{
		{Label: "审核", Value: models.WalletOrderStatusPending},
		{Label: "完成", Value: models.WalletOrderStatusCompleted},
		{Label: "拒绝", Value: models.WalletOrderStatusRejected},
	}
	pendingStatusOptions := []*views.SelectOption{
		{Label: "完成", Value: models.WalletOrderStatusCompleted},
		{Label: "拒绝", Value: models.WalletOrderStatusRejected},
	}
	isHintOptions := []*views.SelectOption{
		{Label: "开启", Value: model.BoolTrue},
		{Label: "关闭", Value: model.BoolFalse},
	}
	vueTable.SetCommonOptions("status", statusOptions).SetCommonOptions("adminId", adminOptions).
		SetCommonOptions("sourceId", paymentOptions).SetCommonOptions("isHint", isHintOptions).
		SetCommonOptions("pendingStatus", pendingStatusOptions)

	// 搜索
	searchForm := views.NewForm().Struct(DepositAssetsIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(DepositAssetsCreateParams{}).FlattenInputs()
	if len(paymentOptions) > 0 {
		createForm.SetFieldInputParam("sourceId", "default", paymentOptions[0].Value)
	}
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("充值订单", DepositAssetsCreateURL, createForm))
	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", OrderDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(DepositAssetsIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("adminId").SetSelectFormat(adminOptions)
	vueTable.GetFieldColumn("isHint").SetSelectFormat(isHintOptions)
	vueTable.GetFieldColumn("proof").SetAvatarFormat("proof")
	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(OrderUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", OrderUpdateURL, updateForm))

	// 数据表格 - 更新状态
	statusForm := views.NewForm().Struct(OrderStatusParams{}).FlattenInputs()
	statusForm.SetFieldInputParam("pendingStatus", "default", models.WalletOrderStatusCompleted)
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("审核", OrderStatusURL, statusForm).SetDisplay(fmt.Sprintf("row.status == %d", models.WalletOrderStatusPending)))

	return vueTable
}
