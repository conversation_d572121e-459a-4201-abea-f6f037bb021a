package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// AssetsUpdateParams 更新资产参数
type AssetsUpdateParams struct {
	ID       uint    `json:"id" validate:"required" views:"label:ID;display:true"`
	Icon     string  `json:"icon" validate:"required" views:"label:图标;type:image"`
	Type     int8    `json:"type" validate:"required" views:"label:类型"`
	Name     string  `json:"name" validate:"required" views:"label:名称"`
	Currency string  `json:"currency" validate:"required" views:"label:货币符号"`
	Rate     float64 `json:"rate" validate:"required" views:"label:汇率;type:number"`
	Decimals uint8   `json:"decimals" validate:"required" views:"label:小数位数;type:number"`
	Network  string  `json:"network" validate:"required" views:"label:网络"`
	Status   int8    `json:"status" validate:"required" views:"label:状态;type:select"`
	Desc     string  `json:"desc" validate:"required" views:"label:描述;type:translate"`
}

// AssetsUpdate 更新资产
func AssetsUpdate(c *context.CustomCtx, bodyParams *AssetsUpdateParams) error {
	db := model.NewModel()

	assetInfo := models.WalletAssets{}
	result := db.Where("id = ?", bodyParams.ID).Where("admin_id = ?", c.Claims.MerchantID).First(&assetInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("资产不存在: %v", result.Error))
	}

	assetInfo.Icon = bodyParams.Icon
	assetInfo.Type = bodyParams.Type
	assetInfo.Name = bodyParams.Name
	assetInfo.Currency = bodyParams.Currency
	assetInfo.Rate = bodyParams.Rate
	assetInfo.Decimals = bodyParams.Decimals
	assetInfo.Network = bodyParams.Network
	assetInfo.Status = bodyParams.Status

	result = db.Save(&assetInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新失败: %v", result.Error))
	}

	return c.SuccessOk()
}
