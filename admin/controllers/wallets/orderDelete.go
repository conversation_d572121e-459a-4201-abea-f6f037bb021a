package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// 删除订单
func OrderDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	for _, v := range bodyParams.Ids {
		result := db.Where("id = ?", v).Where("status IN ?", []int8{models.WalletOrderStatusCompleted, models.WalletOrderStatusRejected}).Where("admin_id IN ?", subAdminIDs).Delete(&models.WalletOrder{})
		if result.Error != nil {
			return c.<PERSON><PERSON>r<PERSON><PERSON>(fmt.Sprintf("删除失败: %v", result.Error))
		}
	}
	return c.SuccessOk()
}
