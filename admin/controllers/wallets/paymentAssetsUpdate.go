package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// PaymentAssetsUpdateParams 更新支付资产参数
type PaymentAssetsUpdateParams struct {
	ID        uint    `json:"id" validate:"required" views:"label:ID;display:true"`
	Icon      string  `json:"icon" views:"label:图标;type:image"`
	Name      string  `json:"name" views:"label:名称"`
	Currency  string  `json:"currency" views:"label:货币符号"`
	MaxAmount float64 `json:"maxAmount" views:"label:最大金额;type:number"`
	MinAmount float64 `json:"minAmount" views:"label:最小金额;type:number"`
	StartTime string  `json:"startTime" views:"label:开始时间;type:datetime"`
	EndTime   string  `json:"endTime" views:"label:结束时间;type:datetime"`
	Fee       float64 `json:"fee" views:"label:手续费;type:number"`
	Status    int8    `json:"status" views:"label:状态;type:select"`
	IsChats   int8    `json:"isChats" views:"label:跳转客服;type:select"`
	IsProof   int8    `json:"isProof" views:"label:开启凭证;type:select"`
	Desc      string  `json:"desc" views:"label:描述说明;type:translate"`
}

// PaymentAssetsUpdate 更新支付资产
func PaymentAssetsUpdate(c *context.CustomCtx, bodyParams *PaymentAssetsUpdateParams) error {
	db := model.NewModel()

	paymentInfo := &models.WalletPayment{}
	if err := db.Where("id = ?", bodyParams.ID).Where("assets_id > 0").Where("admin_id = ?", c.Claims.MerchantID).First(paymentInfo).Error; err != nil {
		return c.ErrorJson(fmt.Sprintf("支付资产不存在: %v", err))
	}

	// 更新描述说明的健值
	bodyParams.Desc = fmt.Sprintf("walletPaymentDesc%v", paymentInfo.ID)
	if err := db.Model(&models.WalletPayment{}).
		Select("icon", "name", "currency", "max_amount", "min_amount", "start_time", "end_time", "fee", "status", "is_chats", "is_proof", "desc").
		Where("id = ?", paymentInfo.ID).Updates(bodyParams).Error; err != nil {
		return c.ErrorJson(fmt.Sprintf("更新失败: %v", err))
	}

	return c.SuccessOk()
}
