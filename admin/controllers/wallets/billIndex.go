package wallets

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	BillIndexURL  = "/wallets/bill/index"
	BillCreateURL = "/wallets/bill/create"
	BillUpdateURL = "/wallets/bill/update"
	BillDeleteURL = "/wallets/bill/delete"
)

// BillIndexParams 账单列表参数
type BillIndexParams struct {
	AdminID    uint                   `json:"adminId" views:"label:管理;type:select"`
	UserID     uint                   `json:"userId" views:"label:账户;type:selectSearch;mask:user#username>id"`
	Name       string                 `json:"name" views:"label:名称"`
	Type       int8                   `json:"type" views:"label:类型;type:select"`
	CreatedAt  *model.RangeDatePicker `json:"createdAt" views:"label:时间;type:dateRange"`
	Pagination *model.Pagination      `json:"pagination" views:"-"`
}

// BillIndexRows 账单列表行
type BillIndexRows struct {
	ID        uint        `json:"id" column:"label:ID"`
	AdminID   uint        `json:"adminId" column:"label:管理"`
	UserName  string      `json:"username" column:"label:账户;scanField:userInfo.username" gorm:"-"`
	Name      string      `json:"name" column:"label:名称"`
	Type      int8        `json:"type" column:"label:类型;type:select"`
	Money     float64     `json:"money" column:"label:金额;type:number"`
	Balance   float64     `json:"balance" column:"label:余额;type:number"`
	CreatedAt time.Time   `json:"createdAt" column:"label:时间;type:date"`
	UserID    uint        `json:"userId"`
	UserInfo  models.User `json:"userInfo" gorm:"foreignKey:UserID"`
}

// BillIndex 账单列表
func BillIndex(c *context.CustomCtx, bodyParams *BillIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	data := &model.IndexData{Items: make([]*BillIndexRows, 0), Count: 0}
	db := model.NewModel()
	query := db.Equal("user_id", bodyParams.UserID).Equal("admin_id", bodyParams.AdminID).
		Like("name", bodyParams.Name).Equal("type", bodyParams.Type).
		BetweenTime("created_at", bodyParams.CreatedAt, c.TimeZone).
		Model(&models.WalletBill{})

	query.Where("admin_id IN ?", subAdminIDs).Where("assets_id = 0").Count(&data.Count)
	query.Preload("UserInfo")
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	for _, v := range data.Items.([]*BillIndexRows) {
		v.Balance += v.Money
	}

	return c.SuccessJson(data)
}

// BillIndexConfigure 账单列表配置
func BillIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(BillIndexURL)
	billService := service.NewWalletBillService()
	adminService := service.NewAdminUserService()
	adminsOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)

	typesOptions := billService.GetBillOptions()
	vueTable.SetCommonOptions("type", typesOptions).SetCommonOptions("adminId", adminsOptions)

	// 搜索
	searchForm := views.NewForm().Struct(BillIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(BillCreateParams{}).FlattenInputs()
	createForm.SetFieldInputParam("type", "default", typesOptions[0].Value)
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增账单", BillCreateURL, createForm))
	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", BillDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(BillIndexRows{})
	vueTable.GetFieldColumn("type").SetSelectFormat(typesOptions)
	vueTable.GetFieldColumn("adminId").SetSelectFormat(adminsOptions)

	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(BillUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", BillUpdateURL, updateForm))

	return vueTable
}
