package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
)

const (
	PaymentIndexURL      = "/wallets/payment/index"
	PaymentCreateURL     = "/wallets/payment/create"
	PaymentUpdateURL     = "/wallets/payment/update"
	PaymentDeleteURL     = "/wallets/payment/delete"
	PaymentUpdateDataURL = "/wallets/payment/update/data"
	PaymentUpdateConfURL = "/wallets/payment/update/conf"
)

// PaymentIndexParams 支付列表参数
type PaymentIndexParams struct {
	Name       string            `json:"name" views:"label:名称"`
	Mode       int8              `json:"mode" views:"label:模式;type:select"`
	Type       int8              `json:"type" views:"label:类型;type:select"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// PaymentIndexRows 支付列表行
type PaymentIndexRows struct {
	ID        uint                     `json:"id" column:"label:ID"`
	Icon      string                   `json:"icon" column:"label:图标"`
	Name      string                   `json:"name" column:"label:名称"`
	Mode      int8                     `json:"mode" column:"label:模式"`
	Type      int8                     `json:"type" column:"label:类型"`
	Currency  string                   `json:"currency" column:"label:符号"`
	MaxAmount float64                  `json:"maxAmount" column:"label:最大金额"`
	MinAmount float64                  `json:"minAmount" column:"label:最小金额"`
	Fee       float64                  `json:"fee" column:"label:手续费"`
	StartTime string                   `json:"startTime" column:"label:开始时间"`
	EndTime   string                   `json:"endTime" column:"label:结束时间"`
	IsChats   int8                     `json:"isChats" column:"label:跳转客服"`
	IsProof   int8                     `json:"isProof" column:"label:开启凭证"`
	Status    int8                     `json:"status" column:"label:状态"`
	Data      models.WalletPaymentData `json:"data"`
	Conf      models.WalletPaymentConf `json:"conf"`
	Desc      string                   `json:"desc"`
}

// PaymentIndex 支付列表
func PaymentIndex(c *context.CustomCtx, bodyParams *PaymentIndexParams) error {
	db := model.NewModel()
	data := &model.IndexData{Items: make([]*PaymentIndexRows, 0), Count: 0}

	query := db.Equal("name", bodyParams.Name).
		Equal("mode", bodyParams.Mode).
		Equal("type", bodyParams.Type).
		Equal("status", bodyParams.Status).Model(&models.WalletPayment{})

	query.Where("admin_id", c.Claims.AdminID).Where("mode IN ?", []int8{models.PaymentModeDeposit, models.PaymentModeWithdrawal, models.PaymentModePay}).Count(&data.Count)
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	for _, v := range data.Items.([]*PaymentIndexRows) {
		v.Desc = fmt.Sprintf("walletPaymentDesc%v", v.ID)
	}

	return c.SuccessJson(data)
}

// PaymentIndexConfigure 支付列表配置
func PaymentIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(PaymentIndexURL)
	modeOptions := []*views.SelectOption{
		{Label: "充值", Value: models.PaymentModeDeposit},
		{Label: "提现", Value: models.PaymentModeWithdrawal},
		{Label: "支付", Value: models.PaymentModePay},
	}
	statusOptions := []*views.SelectOption{
		{Label: "启用", Value: models.PaymentStatusEnabled},
		{Label: "禁用", Value: models.PaymentStatusDisabled},
	}
	typeOptions := []*views.SelectOption{
		{Label: "数字货币", Value: models.PaymentTypeCrypto},
		{Label: "银行卡", Value: models.PaymentTypeBankCard},
		{Label: "第三方", Value: models.PaymentTypeThirdParty},
	}
	isChatsOptions := []*views.SelectOption{
		{Label: "是", Value: model.BoolTrue},
		{Label: "否", Value: model.BoolFalse},
	}
	isProofOptions := []*views.SelectOption{
		{Label: "是", Value: model.BoolTrue},
		{Label: "否", Value: model.BoolFalse},
	}
	vueTable.SetCommonOptions("mode", modeOptions).SetCommonOptions("status", statusOptions).
		SetCommonOptions("type", typeOptions).SetCommonOptions("isChats", isChatsOptions).
		SetCommonOptions("isProof", isProofOptions)

	// 搜索
	searchForm := views.NewForm().Struct(PaymentIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(PaymentCreateParams{}).FlattenInputs()
	createForm.SetFieldInputParam("mode", "default", models.PaymentModeDeposit)
	createForm.SetFieldInputParam("type", "default", models.PaymentTypeCrypto)
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增支付", PaymentCreateURL, createForm))
	if c.Claims.AdminID == models.SuperAdminID {
		deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
		vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", PaymentDeleteURL, deleteForm), "ids", "id")
	}

	// 数据表格数据
	vueTable.StructColumns(PaymentIndexRows{})
	vueTable.GetFieldColumn("icon").SetAvatarFormat("icon")
	vueTable.GetFieldColumn("mode").SetSelectFormat(modeOptions)
	vueTable.GetFieldColumn("type").SetSelectFormat(typeOptions)
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("isChats").SetSelectFormat(isChatsOptions)
	vueTable.GetFieldColumn("isProof").SetSelectFormat(isProofOptions)
	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(PaymentUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", PaymentUpdateURL, updateForm))

	// 数据表格 - 配置
	updateDataForm := views.NewForm().Struct(PaymentUpdateDataParams{}).FlattenInputs().SetChildFormFlattenInputs("data")
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("配置", PaymentUpdateDataURL, updateDataForm))

	// 数据表格 - 设置
	updateConfForm := views.NewForm().Struct(PaymentUpdateConfParams{}).FlattenInputs().SetChildFormFlattenInputs("conf")
	updateConfForm.ChildrenForm["conf"].SetFieldInputParam("paymentType", "options", typeOptions)
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("设置", PaymentUpdateConfURL, updateConfForm).SetDisplay(fmt.Sprintf("row.type == %d", models.PaymentTypeThirdParty)))

	return vueTable
}
