package wallets

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
)

const (
	AssetsIndexURL  = "/wallets/assets/index"
	AssetsCreateURL = "/wallets/assets/create"
	AssetsUpdateURL = "/wallets/assets/update"
	AssetsDeleteURL = "/wallets/assets/delete"
)

// AssetsIndexParams 资产列表参数
type AssetsIndexParams struct {
	Name       string            `json:"name" views:"label:名称"`
	Network    string            `json:"network" views:"label:网络"`
	Type       int8              `json:"type" views:"label:类型;type:select"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// AssetsIndexRows 资产列表行
type AssetsIndexRows struct {
	ID       uint    `json:"id" column:"label:ID"`
	Icon     string  `json:"icon" column:"label:图标"`
	Name     string  `json:"name" column:"label:名称"`
	Currency string  `json:"currency" column:"label:符号"`
	Rate     float64 `json:"rate" column:"label:汇率"`
	Decimals int     `json:"decimals" column:"label:精度"`
	Type     int8    `json:"type" column:"label:类型"`
	Status   int8    `json:"status" column:"label:状态"`
	Network  string  `json:"network" column:"label:网络"`
	Desc     string  `json:"desc"`
}

// AssetsIndex 资产列表
func AssetsIndex(c *context.CustomCtx, bodyParams *AssetsIndexParams) error {
	db := model.NewModel()
	data := &model.IndexData{Items: make([]*AssetsIndexRows, 0), Count: 0}

	query := db.
		Equal("name", bodyParams.Name).
		Equal("network", bodyParams.Network).
		Equal("type", bodyParams.Type).
		Equal("status", bodyParams.Status).
		Model(&models.WalletAssets{})

	query.Where("admin_id = ?", c.Claims.MerchantID).Count(&data.Count)
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}

// AssetsIndexConfigure 资产列表配置
func AssetsIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(AssetsIndexURL)
	typeOptions := []*views.SelectOption{
		{Label: "数字货币", Value: models.WalletAssetsTypeDigitalCurrency},
	}
	statusOptions := []*views.SelectOption{
		{Label: "启用", Value: models.WalletAssetsStatusEnabled},
		{Label: "禁用", Value: models.WalletAssetsStatusDisabled},
	}
	vueTable.SetCommonOptions("type", typeOptions).SetCommonOptions("status", statusOptions)

	// 搜索
	searchForm := views.NewForm().Struct(AssetsIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(AssetsCreateParams{}).FlattenInputs().SetFieldInputParam("type", "default", models.WalletAssetsTypeDigitalCurrency)
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增资产", AssetsCreateURL, createForm))
	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", AssetsDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(AssetsIndexRows{})
	vueTable.GetFieldColumn("icon").SetAvatarFormat("icon")
	vueTable.GetFieldColumn("type").SetSelectFormat(typeOptions)
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)

	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(AssetsUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", AssetsUpdateURL, updateForm))

	return vueTable
}
