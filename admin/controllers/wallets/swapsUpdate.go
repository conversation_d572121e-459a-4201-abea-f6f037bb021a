package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// SwapsUpdateParams 资产交换更新参数
type SwapsUpdateParams struct {
	ID            uint    `json:"id" views:"label:ID;display:true"`
	SendAmount    float64 `json:"sendAmount" views:"label:发送金额;type:number"`
	ReceiveAmount float64 `json:"receiveAmount" views:"label:接收金额;type:number"`
	Rate          float64 `json:"rate" views:"label:汇率;type:number"`
	Fee           float64 `json:"fee" views:"label:手续费;type:number"`
	Status        int     `json:"status" views:"label:状态;type:select"`
}

// SwapsUpdate 更新资产交换记录
func SwapsUpdate(c *context.CustomCtx, bodyParams *SwapsUpdateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	result := db.Model(&models.Swaps{}).Where("id = ?", bodyParams.ID).Where("admin_id IN ?", subAdminIDs).Updates(bodyParams)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新资产交换记录失败: %s", result.Error.Error()))
	}

	return c.SuccessOk()
}
