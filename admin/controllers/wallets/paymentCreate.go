package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// PaymentCreateParams 新增支付参数
type PaymentCreateParams struct {
	Icon     string `json:"icon" validate:"required" views:"label:图标;type:image"`
	Mode     int8   `json:"mode" validate:"required" views:"label:模式;type:select"`
	Type     int8   `json:"type" validate:"required" views:"label:类型;type:select"`
	Name     string `json:"name" validate:"required" views:"label:名称"`
	Currency string `json:"currency" validate:"required" views:"label:货币符号"`
}

// PaymentCreate 新增支付
func PaymentCreate(c *context.CustomCtx, bodyParams *PaymentCreateParams) error {
	db := model.NewModel()

	result := db.Create(&models.WalletPayment{
		AdminID:   c.Claims.AdminID,
		Name:      bodyParams.Name,
		Icon:      bodyParams.Icon,
		Currency:  bodyParams.Currency,
		Mode:      bodyParams.Mode,
		Type:      bodyParams.Type,
		Fee:       0.01,
		MinAmount: 100,
		MaxAmount: 50000,
		IsChats:   model.BoolFalse,
		Data:      models.WalletPaymentData{},
	})
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("新增失败: %v", result.Error))
	}

	return c.SuccessOk()
}
