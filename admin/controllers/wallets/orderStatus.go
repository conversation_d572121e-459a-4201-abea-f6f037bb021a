package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// 更新订单状态参数
type OrderStatusParams struct {
	ID            uint   `json:"id" validate:"required" views:"label:ID;display:true"`
	PendingStatus int8   `json:"pendingStatus" validate:"required" views:"label:状态;type:select"`
	Reason        string `json:"reason" views:"label:拒绝原因;type:textarea"`
}

// 更新订单状态
func OrderStatus(c *context.CustomCtx, bodyParams *OrderStatusParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	// 订单信息
	orderInfo := models.WalletOrder{}
	result := db.Model(&models.WalletOrder{}).Where("admin_id IN ?", subAdminIDs).Where("id = ?", bodyParams.ID).First(&orderInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("订单信息获取失败: %v", result.Error))
	}

	// 用户信息
	userInfo := &models.User{}
	result = db.Model(&models.User{}).Where("id = ?", orderInfo.UserID).First(userInfo)
	if result.Error != nil {
		return fmt.Errorf("用户信息获取失败: %v", result.Error)
	}

	// 更新订单状态
	orderInfo.Status = bodyParams.PendingStatus
	orderInfo.Reason = bodyParams.Reason
	err := db.Transaction(func(tx *gorm.DB) error {
		return service.NewWalletOrderService().UpdateOrderStaus(tx, userInfo, c.Rds, c.Lang, &orderInfo)
	})
	if err != nil {
		return c.ErrorJson(fmt.Sprintf("更新失败: %v", err))
	}

	return c.SuccessOk()
}
