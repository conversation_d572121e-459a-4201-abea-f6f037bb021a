package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"

	"gorm.io/gorm"
)

// 资产提现订单创建参数
type WithdrawAssetsCreateParams struct {
	SourceID uint    `json:"sourceId" validate:"required" views:"label:提现账户;type:selectSearch;mask:wallet_account#name>id#type=11"`
	Money    float64 `json:"money" validate:"required,gt=0" views:"label:金额;type:number"`
}

// 创建资产提现订单
func WithdrawAssetsCreate(c *context.CustomCtx, bodyParams *WithdrawAssetsCreateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	// 提现账户信息
	sourceInfo := &models.WalletAccount{}
	result := db.Model(&models.WalletAccount{}).Where("type = ?", models.AccountTypeAsset).Where("admin_id IN ?", subAdminIDs).Where("id = ?", bodyParams.SourceID).First(sourceInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("资产提现账户不存在: %v", result.Error))
	}

	// 支付方式信息
	paymentInfo := models.WalletPayment{}
	result = db.Model(&models.WalletPayment{}).Where("id = ?", sourceInfo.PaymentID).First(&paymentInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("提现账户支付方式不存在: %v", result.Error))
	}
	paymentInfo.Data = sourceInfo.Data

	// 用户信息
	userInfo := &models.User{}
	result = db.Model(&models.User{}).Where("admin_id IN ?", subAdminIDs).Where("id = ?", sourceInfo.UserID).First(userInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("用户不存在: %v", result.Error))
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		orderInfo := &models.WalletOrder{
			SourceID: sourceInfo.ID,
			AssetsID: paymentInfo.AssetsID,
			AdminID:  userInfo.AdminID,
			UserID:   userInfo.ID,
			Type:     models.WalletOrderTypeAssetWithdrawal,
			OrderSN:  utils.GenerateOrderSN(),
			Money:    bodyParams.Money,
			Fee:      paymentInfo.Fee * bodyParams.Money / 100,
			Data:     models.WalletOrderData{WalletPayment: paymentInfo},
		}
		result = tx.Create(orderInfo)
		if result.Error != nil {
			return c.ErrorJson(fmt.Sprintf("创建资产提现订单失败: %v", result.Error))
		}

		// 资产信息
		assetsInfo := &models.WalletAssets{}
		result = tx.Model(&models.WalletAssets{}).Where("id = ?", paymentInfo.AssetsID).First(assetsInfo)
		if result.Error != nil {
			return c.ErrorJson(fmt.Sprintf("资产不存在: %v", result.Error))
		}

		// 扣减资产
		walletService := service.NewWalletService()
		return walletService.SpendAssets(tx, c.Rds, c.Lang, models.BillTypeWithdrawal, orderInfo.ID, userInfo, assetsInfo, bodyParams.Money)
	})
	if err != nil {
		return c.ErrorJson(fmt.Sprintf("创建资产提现订单失败: %v", err))
	}
	return c.SuccessOk()
}
