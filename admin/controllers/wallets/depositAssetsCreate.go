package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"
)

// 资产充值订单创建参数
type DepositAssetsCreateParams struct {
	SourceID uint    `json:"sourceId" validate:"required" views:"label:来源;type:select"`
	UserID   uint    `json:"userId" validate:"required" views:"label:账户;type:selectSearch;mask:user#username>id"`
	Money    float64 `json:"money" validate:"required,gt=0" views:"label:金额;type:number"`
}

// 创建资产充值订单
func DepositAssetsCreate(c *context.CustomCtx, bodyParams *DepositAssetsCreateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	// 用户信息
	userInfo := &models.User{}
	result := db.Model(&models.User{}).Where("admin_id IN ?", subAdminIDs).Where("id = ?", bodyParams.UserID).First(userInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("用户不存在: %v", result.Error))
	}

	// 管理员信息
	adminInfo := &models.AdminUser{}
	result = db.Model(&models.AdminUser{}).Where("id = ?", userInfo.AdminID).First(adminInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("管理员不存在: %v", result.Error))
	}

	// 支付方式信息
	paymentInfo := models.WalletPayment{}
	merchantID, _ := service.NewAdminUserService().GetMerchantIDWithCache(c.Rds, adminInfo.ID)
	result = db.Model(&models.WalletPayment{}).Where("id = ?", bodyParams.SourceID).Where("mode = ?", models.PaymentModeAssetDeposit).Where("admin_id = ?", merchantID).First(&paymentInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("支付方式不存在: %v", result.Error))
	}

	result = db.Create(&models.WalletOrder{
		SourceID: paymentInfo.ID,
		AdminID:  userInfo.AdminID,
		UserID:   userInfo.ID,
		AssetsID: paymentInfo.AssetsID,
		Type:     models.WalletOrderTypeAssetDeposit,
		OrderSN:  utils.GenerateOrderSN(),
		Money:    bodyParams.Money,
		Fee:      paymentInfo.Fee * bodyParams.Money / 100,
		Data:     models.WalletOrderData{WalletPayment: paymentInfo},
	})
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("创建资产充值订单失败: %v", result.Error))
	}
	return c.SuccessOk()
}
