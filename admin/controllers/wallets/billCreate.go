package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// BillCreateParams 创建账单参数
type BillCreateParams struct {
	Type   int8    `json:"type" validate:"required" views:"label:类型;type:select"`
	UserID uint    `json:"userId" validate:"required" views:"label:账户;type:selectSearch;mask:user#username>id"`
	Amount float64 `json:"amount" validate:"required" views:"label:金额;type:number"`
}

// BillCreate 创建账单
func BillCreate(c *context.CustomCtx, bodyParams *BillCreateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	// 用户信息
	userInfo := models.User{}
	result := db.Where("admin_id IN ?", subAdminIDs).Where("id = ?", bodyParams.UserID).First(&userInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("账户不存在: %v", result.Error))
	}

	// 写入账单
	err := db.Transaction(func(tx *gorm.DB) error {
		billService := service.NewWalletBillService()
		return billService.CreateBill(tx, &userInfo, nil, bodyParams.Type, 0, bodyParams.Amount)
	})
	if err != nil {
		return c.ErrorJson(fmt.Sprintf("创建账单失败: %v", err))
	}
	return c.SuccessOk()
}
