package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// TransferUpdateParams 更新转账
type TransferUpdateParams struct {
	ID     uint    `json:"id" validate:"required" views:"label:ID;display:true"`
	Amount float64 `json:"amount" views:"label:金额;type:number"`
	Remark string  `json:"remark" views:"label:备注"`
}

// TransferUpdate 更新转账
func TransferUpdate(c *context.CustomCtx, bodyParams *TransferUpdateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	result := db.Model(&models.Transfer{}).Where("id = ?", bodyParams.ID).Where("admin_id IN ?", subAdminIDs).Updates(bodyParams)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("转账记录更新失败: %v", result.Error))
	}

	return c.SuccessOk()
}
