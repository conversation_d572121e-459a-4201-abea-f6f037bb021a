package wallets

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	TransferIndexURL  = "/wallets/transfer/index"
	TransferCreateURL = "/wallets/transfer/create"
	TransferUpdateURL = "/wallets/transfer/update"
	TransferDeleteURL = "/wallets/transfer/delete"
)

type TransferIndexParams struct {
	AdminID    uint              `json:"adminId" views:"label:管理;type:select"`
	SenderID   uint              `json:"senderId" views:"label:发送者;type:selectSearch;mask:user#username>id"`
	ReceiverID uint              `json:"receiverId" views:"label:接收者;type:selectSearch;mask:user#username>id"`
	Type       int8              `json:"type" views:"label:类型;type:select"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

type TransferIndexRows struct {
	ID           uint        `json:"id" column:"label:ID"`
	AdminID      uint        `json:"adminId" column:"label:管理"`
	SenderName   string      `json:"senderName" column:"label:发送者;scanField:senderInfo.username" gorm:"-"`
	ReceiverName string      `json:"receiverName" column:"label:接收者;scanField:receiverInfo.username" gorm:"-"`
	Type         int8        `json:"type" column:"label:类型"`
	Amount       float64     `json:"amount" column:"label:金额"`
	Remark       string      `json:"remark" column:"label:备注"`
	Status       int8        `json:"status" column:"label:状态"`
	CreatedAt    string      `json:"createdAt" column:"label:时间;type:date"`
	SenderID     uint        `json:"senderId"`
	ReceiverID   uint        `json:"receiverId"`
	SenderInfo   models.User `json:"senderInfo" gorm:"foreignKey:SenderID"`
	ReceiverInfo models.User `json:"receiverInfo" gorm:"foreignKey:ReceiverID"`
}

func TransferIndex(c *context.CustomCtx, bodyParams *TransferIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	data := &model.IndexData{Items: make([]*TransferIndexRows, 0), Count: 0}
	db := model.NewModel()
	query := db.Equal("sender_id", bodyParams.SenderID).
		Equal("admin_id", bodyParams.AdminID).
		Equal("receiver_id", bodyParams.ReceiverID).
		Equal("type", bodyParams.Type).
		Equal("status", bodyParams.Status).
		Model(&models.Transfer{})

	query.Where("admin_id IN ?", subAdminIDs).Where("assets_id = 0").Count(&data.Count)
	query.Preload("SenderInfo").Preload("ReceiverInfo")
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}

func TransferIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(TransferIndexURL)
	adminService := service.NewAdminUserService()
	adminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)
	typeOptions := []*views.SelectOption{
		{Label: "内部转账", Value: models.TransferTypeInternal},
	}
	statusOptions := []*views.SelectOption{
		{Label: "完成", Value: models.TransferStatusCompleted},
	}
	vueTable.SetCommonOptions("status", statusOptions).SetCommonOptions("type", typeOptions).SetCommonOptions("adminId", adminOptions)

	// 搜索
	searchForm := views.NewForm().Struct(TransferIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(TransferCreateParams{}).FlattenInputs()
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("转账订单", TransferCreateURL, createForm))
	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", TransferDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(TransferIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("type").SetSelectFormat(typeOptions)
	vueTable.GetFieldColumn("adminId").SetSelectFormat(adminOptions)

	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(TransferUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", TransferUpdateURL, updateForm))

	return vueTable
}
