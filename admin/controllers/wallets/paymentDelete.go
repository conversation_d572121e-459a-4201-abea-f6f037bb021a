package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// PaymentDelete 删除支付
func PaymentDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	db := model.NewModel()

	for _, v := range bodyParams.Ids {
		if err := db.Where("id = ?", v).Where("assets_id = 0").Where("admin_id IN ?", subAdminIDs).Delete(&models.WalletPayment{}).Error; err != nil {
			return c.<PERSON>rror<PERSON><PERSON>(fmt.Sprintf("删除失败: %v", err))
		}
	}

	return c.SuccessOk()
}
