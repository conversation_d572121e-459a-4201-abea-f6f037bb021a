package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// SwapsDelete 删除资产交换记录
func SwapsDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	for _, v := range bodyParams.Ids {
		result := db.Where("id = ?", v).Where("admin_id IN ?", subAdminIDs).Delete(&models.Swaps{})
		if result.Error != nil {
			return c.<PERSON>rror<PERSON>son(fmt.Sprintf("删除资产交换记录失败: %s", result.Error.Error()))
		}
	}

	return c.SuccessOk()
}
