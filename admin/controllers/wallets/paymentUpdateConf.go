package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// PaymentUpdateConfParams 支付更新设置
type PaymentUpdateConfParams struct {
	ID   uint                     `json:"id" validate:"required" views:"label:ID;display:true"`
	Conf models.WalletPaymentConf `json:"conf" views:"label:设置;type:struct"`
}

// PaymentUpdateData 支付更新数据
func PaymentUpdateConf(c *context.CustomCtx, bodyParams *PaymentUpdateConfParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	// 强制更新 Data 字段下的所有字段，包括零值
	result := db.Model(&models.WalletPayment{}).
		Where("id", bodyParams.ID).
		Where("admin_id IN ?", subAdminIDs).
		Select("conf").
		Updates(map[string]interface{}{
			"conf": bodyParams.Conf,
		})

	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新失败: %v", result.Error))
	}

	return c.SuccessOk()
}
