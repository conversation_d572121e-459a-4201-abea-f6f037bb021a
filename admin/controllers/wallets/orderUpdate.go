package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// OrderUpdateParams 订单更新参数
type OrderUpdateParams struct {
	ID     uint    `json:"id" views:"label:ID;display:true"`
	Money  float64 `json:"money" views:"label:金额;type:number"`
	Status int8    `json:"status" views:"label:状态;type:select"`
	IsHint int8    `json:"isHint" views:"label:提示音;type:select"`
	Reason string  `json:"reason" views:"label:拒绝原因;type:textarea"`
}

// OrderUpdate 订单更新
func OrderUpdate(c *context.CustomCtx, bodyParams *OrderUpdateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	result := db.Model(&models.WalletOrder{}).Where("admin_id IN ?", subAdminIDs).Where("id = ?", bodyParams.ID).Updates(bodyParams)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新失败: %v", result.Error))
	}
	return c.SuccessOk()
}
