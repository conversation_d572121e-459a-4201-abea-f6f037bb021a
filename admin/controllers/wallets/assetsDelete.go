package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// AssetsDelete 删除资产
func AssetsDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	db := model.NewModel()

	for _, v := range bodyParams.Ids {
		result := db.Where("id = ?", v).Where("admin_id = ?", c.Claims.MerchantID).Delete(&models.WalletAssets{})
		if result.Error != nil {
			return c.<PERSON><PERSON><PERSON><PERSON>(fmt.Sprintf("删除失败: %v", result.Error))
		}
	}

	return c.SuccessOk()
}
