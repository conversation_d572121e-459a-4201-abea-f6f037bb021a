package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// BillDelete 删除账单
func BillDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	for _, v := range bodyParams.Ids {
		result := db.Where("id = ?", v).Where("admin_id IN ?", subAdminIDs).Delete(&models.WalletBill{})
		if result.Error != nil {
			return c.<PERSON>(fmt.Sprintf("删除账单失败: %v", result.Error))
		}
	}

	return c.SuccessOk()
}
