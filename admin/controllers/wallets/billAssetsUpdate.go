package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// BillAssetsUpdateParams 更新账单资产参数
type BillAssetsUpdateParams struct {
	ID    uint    `json:"id" validate:"required" views:"label:ID;display:true"`
	Name  string  `json:"name" views:"label:名称"`
	Money float64 `json:"money" views:"label:金额;type:number"`
}

// BillAssetsUpdate 更新账单资产
func BillAssetsUpdate(c *context.CustomCtx, bodyParams *BillAssetsUpdateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	result := db.Model(&models.WalletBill{}).Where("id = ?", bodyParams.ID).Where("admin_id IN ?", subAdminIDs).Updates(bodyParams)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新账单失败: %v", result.Error))
	}

	return c.SuccessOk()
}
