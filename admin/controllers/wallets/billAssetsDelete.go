package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// BillAssetsDelete 删除账单资产
func BillAssetsDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	for _, v := range bodyParams.Ids {
		result := db.Where("id = ?", v).Where("admin_id IN ?", subAdminIDs).Delete(&models.WalletBill{})
		if result.Error != nil {
			return c.<PERSON>r<PERSON><PERSON>(fmt.Sprintf("删除账单资产失败: %v", result.Error))
		}
	}

	return c.SuccessOk()
}
