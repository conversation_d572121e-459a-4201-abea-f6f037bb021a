package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// PaymentUpdateDataParams 支付更新数据
type PaymentUpdateDataParams struct {
	ID   uint                     `json:"id" validate:"required" views:"label:ID;display:true"`
	Data models.WalletPaymentData `json:"data" views:"label:数据;type:struct"`
}

// PaymentUpdateData 支付更新数据
func PaymentUpdateData(c *context.CustomCtx, bodyParams *PaymentUpdateDataParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	// 强制更新 Data 字段下的所有字段，包括零值
	result := db.Model(&models.WalletPayment{}).
		Where("id", bodyParams.ID).
		Where("admin_id IN ?", subAdminIDs).
		Select("data").
		Updates(map[string]interface{}{
			"data": bodyParams.Data,
		})

	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新失败: %v", result.Error))
	}

	return c.SuccessOk()
}
