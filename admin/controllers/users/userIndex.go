package users

import (
	"fmt"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	UserIndexURL         = "/users/user/index"
	UserCreateURL        = "/users/user/create"
	UserUpdateURL        = "/users/user/update"
	UserDeleteURL        = "/users/user/delete"
	UserBalanceURL       = "/users/user/balance"
	UserFreezeBalanceURL = "/users/user/freeze"
	UserSettingsURL      = "/users/user/settings"
	UserAscriptionURL    = "/users/user/ascription"

	// BalanceFreezeDeposit 冻结充值类型
	BalanceFreezeDeposit = -1
)

// UserIndexParams 用户列表参数
type UserIndexParams struct {
	AdminID     uint                   `json:"adminId" views:"label:管理;type:select"`
	ParentID    uint                   `json:"parentId" views:"label:上级;type:selectSearch;mask:user#username>id"`
	UserID      uint                   `json:"userId" views:"label:账户;type:selectSearch;mask:user#username>id"`
	NickName    string                 `json:"nickname" views:"label:昵称"`
	Email       string                 `json:"email" views:"label:邮箱"`
	Telephone   string                 `json:"telephone" views:"label:电话"`
	InviteCode  string                 `json:"inviteCode" views:"label:邀请码"`
	Type        int8                   `json:"type" views:"label:类型;type:select"`
	Status      int8                   `json:"status" views:"label:状态;type:select"`
	CreatedAt   *model.RangeDatePicker `json:"createdAt" views:"label:注册时间;type:dateRange"`
	LastLoginAt *model.RangeDatePicker `json:"lastLoginAt" views:"label:登录时间;type:dateRange"`
	Pagination  *model.Pagination      `json:"pagination" views:"-"` // 分页参数
}

// UserIndexRows 用户列表行
type UserIndexRows struct {
	ID              int                    `json:"id" column:"label:ID"`
	AdminID         uint                   `json:"adminId" column:"label:管理"`
	Avatar          string                 `json:"avatar" column:"label:头像"`
	Username        string                 `json:"username" column:"label:账户;type:html"`
	Type            int8                   `json:"type" column:"label:类型"`
	Nickname        string                 `json:"nickname" column:"label:昵称"`
	Email           string                 `json:"email" column:"label:邮箱"`
	Telephone       string                 `json:"telephone" column:"label:电话"`
	AvailableAmount float64                `json:"availableAmount" column:"label:可用金额"`
	FrozenAmount    float64                `json:"frozenAmount" column:"label:冻结金额"`
	Score           int                    `json:"score" column:"label:信用分"`
	InviteCode      string                 `json:"inviteCode" column:"label:邀请码"`
	Status          int8                   `json:"status" column:"label:状态"`
	CreatedAt       time.Time              `json:"createdAt" column:"label:注册时间;type:date"`
	LastLoginIP     string                 `json:"lastLoginIP" column:"label:登录IP"`
	LastLoginAt     time.Time              `json:"lastLoginAt" column:"label:登录时间;type:date"`
	Sex             int8                   `json:"sex"`
	Birthday        time.Time              `json:"birthday"`
	ParentID        uint                   `json:"parentId"`
	ParentInfo      models.User            `json:"parentInfo" gorm:"foreignKey:ID;references:ParentID"`
	SettingList     []models.Setting       `json:"settingList" gorm:"foreignKey:UserID"`
	SettingValues   map[string]interface{} `json:"settingValues" gorm:"-"`
}

// UserIndex 用户列表
func UserIndex(c *context.CustomCtx, bodyParams *UserIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	data := &model.IndexData{Items: make([]*UserIndexRows, 0), Count: 0}
	db := model.NewModel()

	query := db.Equal("id", bodyParams.UserID).Equal("parent_id", bodyParams.ParentID).
		Equal("admin_id", bodyParams.AdminID).
		Equal("nickname", bodyParams.NickName).
		Equal("email", bodyParams.Email).
		Equal("telephone", bodyParams.Telephone).
		Equal("invite_code", bodyParams.InviteCode).
		Equal("type", bodyParams.Type).
		Equal("status", bodyParams.Status).
		BetweenTime("created_at", bodyParams.CreatedAt, c.TimeZone).
		BetweenTime("last_login_at", bodyParams.LastLoginAt, c.TimeZone).
		Model(&models.User{})

	query.Where("admin_id IN ?", subAdminIDs).Count(&data.Count)
	query.Preload("ParentInfo").Preload("SettingList").Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	// 设置用户设置值
	for _, item := range data.Items.([]*UserIndexRows) {
		item.SettingValues = make(map[string]interface{})
		for _, setting := range item.SettingList {
			item.SettingValues[setting.Field] = views.InputValueToInterface(setting.Type, setting.Value)
		}

		item.Username = fmt.Sprintf("<div>%s</div><div>%s</div>", item.ParentInfo.Username, item.Username)
	}

	return c.SuccessJson(data)
}

// UserIndexConfigure 用户列表配置
func UserIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	adminService := service.NewAdminUserService()
	walletBillService := service.NewWalletBillService()
	adminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)
	billTypeOptions := walletBillService.GetAmountTypeOptions()
	billTypeOptions = append(billTypeOptions, &views.SelectOption{Label: "冻结充值", Value: BalanceFreezeDeposit})

	vueTable := views.NewTable(UserIndexURL).SetAutoRefresh()
	statusOptions := []*views.SelectOption{
		{Label: "冻结", Value: models.UserStatusFrozen},
		{Label: "激活", Value: models.UserStatusActive},
	}
	typeOptions := []*views.SelectOption{
		{Label: "虚拟用户", Value: models.UserTypeVirtual},
		{Label: "普通用户", Value: models.UserTypeDefault},
	}
	sexOptions := []*views.SelectOption{
		{Label: "男", Value: models.SexMale},
		{Label: "女", Value: models.SexFemale},
		{Label: "未知", Value: models.SexUnknown},
	}

	vueTable.SetCommonOptions("status", statusOptions).SetCommonOptions("type", typeOptions).
		SetCommonOptions("adminId", adminOptions).SetCommonOptions("sex", sexOptions).
		SetCommonOptions("billType", billTypeOptions)

	// 搜索
	searchForm := views.NewForm().Struct(UserIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(UserCreateParams{}).FlattenInputs()
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("新增用户", UserCreateURL, createForm))

	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", UserDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(UserIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("type").SetSelectFormat(typeOptions)
	vueTable.GetFieldColumn("adminId").SetSelectFormat(adminOptions)
	vueTable.GetFieldColumn("avatar").SetAvatarFormat("avatar")

	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(UserUpdateParams{}).FlattenInputs().ResetInputs([][]string{
		{"id"},
		{"avatar"},
		{"nickname", "score"},
		{"email", "telephone"},
		{"password", "securityKey"},
		{"birthday", "sex"},
		{"type", "status"},
	})
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", UserUpdateURL, updateForm).SetSize(views.DialogSizeMedium))

	// 数据表格 - 归属
	ascriptionForm := views.NewForm().Struct(UserAscriptionParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("归属", UserAscriptionURL, ascriptionForm))

	// 数据表格 - 设置用户公告
	noticeInput := models.NewSettingInput(models.UserSettingsNotice, views.Input{
		Label: "首页公告",
		Type:  views.InputTypeEditor,
	}, nil)
	settingsForm := views.NewForm().Struct(UserSettingsParams{}).
		SetDefaultSettingInput(noticeInput.Field, noticeInput.Data.Input, noticeInput.Data.ChildrenForm).
		FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("公告", UserSettingsURL, settingsForm).SetSize(views.DialogSizeMedium))

	// 数据表格 - 余额
	balanceForm := views.NewForm().Struct(UserBalanceParams{}).FlattenInputs()
	balanceForm.SetFieldInputParam("billType", "default", billTypeOptions[0].Value)
	vueTable.SetColumnOptions(1, views.NewFormOptionsButtonDialog("余额", UserBalanceURL, balanceForm))

	// 数据表格 - 冻结余额
	freezeBalanceForm := views.NewForm().Struct(UserFreezeBalanceParams{}).FlattenInputs()
	vueTable.SetColumnOptions(1, views.NewFormOptionsButtonDialog("冻结", UserFreezeBalanceURL, freezeBalanceForm))

	// 数据表格 - 账单明细
	vueTable.SetColumnOptions(1, views.NewRouteButtonDialog("账单", "/wallets/bill/index?userId=row.id").SetButtonColor(views.ColorSecondary))

	// 数据表格 - 设置用户期权胜率
	futuresWinningRateInput := models.NewSettingInput(models.UserSettingsFuturesWinningRate, views.Input{
		Label: "期权胜率%",
		Type:  views.InputTypeNumber,
	}, nil)
	futuresWinningRateForm := views.NewForm().Struct(UserSettingsParams{}).
		SetDefaultSettingInput(futuresWinningRateInput.Field, futuresWinningRateInput.Data.Input, futuresWinningRateInput.Data.ChildrenForm).
		FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("胜率", UserSettingsURL, futuresWinningRateForm))

	return vueTable
}
