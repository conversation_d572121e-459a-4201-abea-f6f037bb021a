package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// BalanceBankAccountParams 余额银行卡账户
type BalanceBankAccountParams struct {
	PaymentID   uint   `json:"paymentId" validate:"required" views:"label:银行类型;type:select;scanField:balanceBankPaymentList"`
	UserID      uint   `json:"userId" validate:"required" views:"label:账户;type:selectSearch;mask:user#username>id"`
	BankName    string `json:"bankName" validate:"required" views:"label:银行名称"`
	BankCardNo  string `json:"bankCardNo" validate:"required" views:"label:银行卡号"`
	RealName    string `json:"realName" validate:"required" views:"label:真实姓名"`
	BankAddress string `json:"bankAddress" validate:"required" views:"label:银行地址"`
	BankCode    string `json:"bankCode" validate:"required" views:"label:银行代码"`
}

// BalanceAssetsAccountParams 余额资产账户
type BalanceAssetsAccountParams struct {
	PaymentID   uint   `json:"paymentId" validate:"required" views:"label:货币类型;type:select;scanField:balanceAssetsPaymentList"`
	UserID      uint   `json:"userId" validate:"required" views:"label:账户;type:selectSearch;mask:user#username>id"`
	BankName    string `json:"bankName" validate:"required" views:"label:公链名称(Ethereum);default:Ethereum"`
	BankCardNo  string `json:"bankCardNo" validate:"required" views:"label:公链地址(0x1234567890abcdef);default:0x1234567890abcdef"`
	RealName    string `json:"realName" validate:"required" views:"label:公链简写(ETH);default:ETH"`
	BankAddress string `json:"bankAddress" validate:"required" views:"label:公链标识(Erc20);default:Erc20"`
	BankCode    string `json:"bankCode" validate:"required" views:"label:公链Token(USDT);default:USDT"`
}

// AssetsAccountParams 资产账户
type AssetsAccountParams struct {
	PaymentID   uint   `json:"paymentId" validate:"required" views:"label:货币类型;type:select;scanField:assetsPaymentList"`
	UserID      uint   `json:"userId" validate:"required" views:"label:账户;type:selectSearch;mask:user#username>id"`
	BankName    string `json:"bankName" validate:"required" views:"label:公链名称(Ethereum);default:Ethereum"`
	BankCardNo  string `json:"bankCardNo" validate:"required" views:"label:公链地址(0x1234567890abcdef);default:0x1234567890abcdef"`
	RealName    string `json:"realName" validate:"required" views:"label:公链简写(ETH);default:ETH"`
	BankAddress string `json:"bankAddress" validate:"required" views:"label:公链标识(Erc20);default:Erc20"`
	BankCode    string `json:"bankCode" validate:"required" views:"label:公链Token(USDT);default:USDT"`
}

// AccountCreateParams 创建账户
type AccountCreateParams struct {
	PaymentID   uint   `json:"paymentId" validate:"required"`   //	提现方式
	UserID      uint   `json:"userId" validate:"required"`      //	用户ID
	BankName    string `json:"bankName" validate:"required"`    //	银行名称|公链名称(Ethereum)
	BankCardNo  string `json:"bankCardNo" validate:"required"`  //	银行卡号|公链地址(0x1234567890abcdef)
	RealName    string `json:"realName" validate:"required"`    //	真实姓名|公链简写(ETH)
	BankAddress string `json:"bankAddress" validate:"required"` //	银行地址|公链标识(Erc20)
	BankCode    string `json:"bankCode" validate:"required"`    //	银行代码|公链Token(USDT)
}

// AccountCreate 创建账户
func AccountCreate(c *context.CustomCtx, bodyParams *AccountCreateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	// 用户信息
	userInfo := &models.User{}
	result := db.Where("admin_id IN ?", subAdminIDs).Where("id = ?", bodyParams.UserID).First(userInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("用户不存在: %s", result.Error.Error()))
	}

	// 提现方式
	paymentInfo := &models.WalletPayment{}
	result = db.Where("admin_id = ?", c.Claims.MerchantID).Where("id = ?", bodyParams.PaymentID).First(paymentInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("提现方式不存在: %s", result.Error.Error()))
	}

	accountType := models.AccountTypeBalance
	if int8(paymentInfo.AssetsID) > 0 {
		accountType = models.AccountTypeAsset
	}

	db = model.NewModel()
	result = db.Create(&models.WalletAccount{
		AdminID:   userInfo.AdminID,
		UserID:    userInfo.ID,
		PaymentID: paymentInfo.ID,
		Name:      paymentInfo.Name + "|" + bodyParams.BankCardNo,
		Type:      accountType,
		Data: models.WalletPaymentData{
			BankName:    bodyParams.BankName,
			BankCode:    bodyParams.BankCode,
			BankAddress: bodyParams.BankAddress,
			RealName:    bodyParams.RealName,
			BankCardNo:  bodyParams.BankCardNo,
		},
	})
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("创建失败: %s", result.Error.Error()))
	}
	return c.SuccessOk()
}
