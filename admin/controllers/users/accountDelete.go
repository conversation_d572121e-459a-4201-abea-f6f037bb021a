package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// AccountDelete 删除账户
func AccountDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	for _, v := range bodyParams.Ids {
		result := db.Where("admin_id IN ?", subAdminIDs).Where("id = ?", v).Delete(&models.WalletAccount{})
		if result.Error != nil {
			return c.<PERSON><PERSON>r<PERSON><PERSON>(fmt.Sprintf("删除失败: %s", result.Error.Error()))
		}
	}

	return c.SuccessOk()
}
