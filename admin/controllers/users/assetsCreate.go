package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// AssetsCreateParams 创建用户资产参数
type AssetsCreateParams struct {
	AssetsID uint    `json:"assetsId" validate:"required" views:"label:资产;type:select"`
	UserID   uint    `json:"userId" validate:"required" views:"label:账户;type:selectSearch;mask:user#username>id"`
	Amount   float64 `json:"amount" validate:"required" views:"label:加减(含正负);type:number"`
}

// AssetsCreate 创建用户资产
func AssetsCreate(c *context.CustomCtx, bodyParams *AssetsCreateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	// 用户信息
	userInfo := &models.User{}
	result := db.Where("admin_id IN ?", subAdminIDs).Where("id = ?", bodyParams.UserID).First(userInfo)
	if result.Error != nil || userInfo.ID == 0 {
		return c.ErrorJson(fmt.Sprintf("用户不存在: %s", result.Error.Error()))
	}

	// 资产信息
	assetsInfo := &models.WalletAssets{}
	result = db.Where("id = ?", bodyParams.AssetsID).First(assetsInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("资产不存在: %s", result.Error.Error()))
	}

	walletService := service.NewWalletService()
	err := db.Transaction(func(tx *gorm.DB) error {
		if bodyParams.Amount > 0 {
			err := walletService.IncreaseAssets(tx, c.Rds, c.Lang, models.BillTypeSystemAddition, 0, userInfo, assetsInfo, bodyParams.Amount)
			return err
		} else {
			return walletService.SpendAssets(tx, c.Rds, c.Lang, models.BillTypeSystemDeduction, 0, userInfo, assetsInfo, -bodyParams.Amount)
		}
	})
	if err != nil {
		return c.ErrorJson(fmt.Sprintf("用户资产操作失败: %s", err.Error()))
	}

	return c.SuccessOk()
}
