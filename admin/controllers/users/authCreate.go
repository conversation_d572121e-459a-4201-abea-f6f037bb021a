package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// 创建用户认证
type AuthCreateParams struct {
	Photo1   string `json:"photo1" validate:"required" views:"label:证件照片1;type:image"`
	Photo2   string `json:"photo2" validate:"required" views:"label:证件照片2;type:image"`
	Type     int8   `json:"type" validate:"required" views:"label:类型;type:select"`
	UserID   int64  `json:"user_id" validate:"required" views:"label:用户;type:selectSearch;mask:user#username>id"`
	RealName string `json:"realname" validate:"required" views:"label:证件姓名"`
	IDNumber string `json:"id_number" validate:"required" views:"label:证件号码"`
}

// 创建用户认证
func AuthCreate(c *context.CustomCtx, bodyParams *AuthCreateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	// 检查账户是否存在
	userInfo := &models.User{}
	result := db.Where("id = ?", bodyParams.UserID).Where("admin_id IN ?", subAdminIDs).First(userInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("用户 %d 不存在", bodyParams.UserID))
	}

	// 创建授权
	authInfo := &models.UserAuth{
		AdminID:  c.Claims.AdminID,
		UserID:   userInfo.ID,
		Type:     bodyParams.Type,
		RealName: bodyParams.RealName,
		IDNumber: bodyParams.IDNumber,
		Photo1:   bodyParams.Photo1,
		Photo2:   bodyParams.Photo2,
	}
	result = db.Create(authInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("创建授权失败: %s", result.Error.Error()))
	}

	return c.SuccessOk()
}
