package users

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	AssetsIndexURL  = "/users/assets/index"
	AssetsCreateURL = "/users/assets/create"
	AssetsUpdateURL = "/users/assets/update"
	AssetsDeleteURL = "/users/assets/delete"
)

// AssetsIndexParams 资产列表参数
type AssetsIndexParams struct {
	AdminID    uint              `json:"adminId" views:"label:管理;type:select"`
	UserID     uint              `json:"userId" views:"label:用户;type:selectSearch;mask:user#username>id"`
	AssetsID   uint              `json:"assetsId" views:"label:资产;type:select"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// AssetsIndexRows 资产列表行
type AssetsIndexRows struct {
	ID              uint        `json:"id" column:"label:ID"`
	AdminID         uint        `json:"adminId" column:"label:管理"`
	UserName        string      `json:"username" column:"label:账户;scanField:userInfo.username"`
	AssetsID        uint        `json:"assetsId" column:"label:资产"`
	AvailableAmount float64     `json:"availableAmount" column:"label:可用金额;"`
	FrozenAmount    float64     `json:"frozenAmount" column:"label:冻结金额;"`
	Status          int8        `json:"status" column:"label:状态"`
	UserID          uint        `json:"userId"`
	UserInfo        models.User `json:"userInfo" gorm:"foreignKey:UserID"`
}

// AssetsIndex 资产列表
func AssetsIndex(c *context.CustomCtx, bodyParams *AssetsIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	data := &model.IndexData{Items: make([]*AssetsIndexRows, 0), Count: 0}

	query := db.Equal("status", bodyParams.Status).Equal("assets_id", bodyParams.AssetsID).
		Equal("user_id", bodyParams.UserID).
		Model(&models.UserAssets{})

	query.Where("admin_id IN ?", subAdminIDs).Count(&data.Count)
	query.Preload("UserInfo")
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}

// AssetsIndexConfigure 资产列表配置
func AssetsIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(AssetsIndexURL)
	assetsService := service.NewWalletAssetsService()
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	assetsOptions := assetsService.GetAssetsOptions(subAdminIDs)
	adminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)

	statusOptions := []*views.SelectOption{
		{Label: "启用", Value: models.PaymentStatusEnabled},
		{Label: "禁用", Value: models.PaymentStatusDisabled},
	}

	vueTable.SetCommonOptions("assetsId", assetsOptions).SetCommonOptions("status", statusOptions).SetCommonOptions("adminId", adminOptions)

	// 搜索
	searchForm := views.NewForm().Struct(AssetsIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(AssetsCreateParams{}).FlattenInputs()
	if len(assetsOptions) > 0 {
		createForm.SetFieldInputParam("assetsId", "default", assetsOptions[0].Value)
	}
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("资产操作", AssetsCreateURL, createForm))

	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", AssetsDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(AssetsIndexRows{})
	vueTable.GetFieldColumn("adminId").SetSelectFormat(adminOptions)
	vueTable.GetFieldColumn("assetsId").SetSelectFormat(assetsOptions)
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)

	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(AssetsUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("资产操作", AssetsUpdateURL, updateForm))

	return vueTable
}
