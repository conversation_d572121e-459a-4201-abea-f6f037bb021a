package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// AssetsUpdateParams 更新用户资产参数
type AssetsUpdateParams struct {
	ID           uint    `json:"id" validate:"required" views:"label:ID;display:true"`
	FrozenAmount float64 `json:"frozenAmount" views:"label:冻结金额;type:number"`
	Status       int8    `json:"status" views:"label:状态;type:select"`
}

// AssetsUpdate 更新用户资产
func AssetsUpdate(c *context.CustomCtx, bodyParams *AssetsUpdateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	// 当前用户资产信息
	userAssets := &models.UserAssets{}
	result := db.Where("id = ?", bodyParams.ID).Where("admin_id IN ?", subAdminIDs).First(userAssets)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("查询资产失败: %s", result.Error.Error()))
	}

	// 资产信息
	assetsInfo := &models.WalletAssets{}
	result = db.Where("id = ?", userAssets.AssetsID).First(assetsInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("查询资产失败: %s", result.Error.Error()))
	}

	// 用户信息
	userInfo := &models.User{}
	result = db.Where("id = ?", userAssets.UserID).First(userInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("查询用户失败: %s", result.Error.Error()))
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		// 更新当前用户资产信息
		result = tx.Model(&models.UserAssets{}).Where("id = ?", userAssets.ID).Updates(bodyParams)
		if result.Error != nil {
			return result.Error
		}

		return nil
	})
	if err != nil {
		return c.ErrorJson(fmt.Sprintf("用户资产操作失败: %s", err.Error()))
	}

	return c.SuccessOk()
}
