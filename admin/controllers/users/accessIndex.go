package users

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	AccessIndexURL  = "/users/access/index"
	AccessDeleteURL = "/users/access/delete"
)

// AccessIndexParams 访问记录列表参数
type AccessIndexParams struct {
	AdminID    uint                   `json:"adminId" views:"label:管理;type:select"`
	UserID     uint                   `json:"userId" views:"label:账户;type:selectSearch;mask:user#username>id"`
	Type       int8                   `json:"type" views:"label:类型;type:select"`
	Name       string                 `json:"name" views:"label:名称"`
	Method     string                 `json:"method" views:"label:方法;type:select"`
	Route      string                 `json:"route" views:"label:路由"`
	UserAgent  string                 `json:"userAgent" views:"label:信息"`
	Referer    string                 `json:"referer" views:"label:来源"`
	Params     string                 `json:"params" views:"label:参数"`
	IP         string                 `json:"ip" views:"label:IP4"`
	CreatedAt  *model.RangeDatePicker `json:"createdAt" views:"label:时间;type:dateRange"`
	Pagination *model.Pagination      `json:"pagination" views:"-"`
}

// AccessIndexRows 访问记录列表行
type AccessIndexRows struct {
	ID        uint              `json:"id" column:"label:ID"`
	AdminID   uint              `json:"adminId" column:"label:管理"`
	UserName  string            `json:"username" column:"label:账户;scanField:userInfo.username"`
	Type      int8              `json:"type" column:"label:类型"`
	Name      string            `json:"name" column:"label:名称"`
	Method    string            `json:"method" column:"label:方法"`
	Route     string            `json:"route" column:"label:路由"`
	UserAgent string            `json:"userAgent" column:"label:信息"`
	Referer   string            `json:"referer" column:"label:来源"`
	Params    string            `json:"params" column:"label:参数"`
	IP        string            `json:"ip" column:"label:IP4"`
	CreatedAt string            `json:"createdAt" column:"label:时间;type:date"`
	UserID    uint              `json:"userId"`
	UserInfo  models.User       `json:"userInfo" column:"-" gorm:"foreignKey:UserID"`
	Data      models.AccessData `json:"data"`
}

// AccessIndex 访问记录列表
func AccessIndex(c *context.CustomCtx, bodyParams *AccessIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	db := model.NewModel()

	data := &model.IndexData{Items: make([]*AccessIndexRows, 0), Count: 0}

	// 查询数据
	query := db.Equal("admin_id", bodyParams.AdminID).
		Equal("user_id", bodyParams.UserID).
		Equal("type", bodyParams.Type).
		Like("name", bodyParams.Name).
		Equal("method", bodyParams.Method).
		Equal("route", bodyParams.Route).
		Like("user_agent", bodyParams.UserAgent).
		Like("referer", bodyParams.Referer).
		Like("params", bodyParams.Params).
		Model(&models.Access{})

	query.Where("admin_id IN ?", subAdminIDs).Count(&data.Count)
	query.Scopes(bodyParams.Pagination.Scopes()).Preload("UserInfo").Find(&data.Items)

	return c.SuccessJson(data)
}

// AccessIndexConfigure 访问记录列表配置
func AccessIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(AccessIndexURL)

	adminService := service.NewAdminUserService()
	subAdminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)

	typeOptions := []*views.SelectOption{
		{Label: "刷新", Value: models.AccessTypeRefresh},
		{Label: "登录", Value: models.AccessTypeLogin},
		{Label: "登出", Value: models.AccessTypeLogout},
		{Label: "访问", Value: models.AccessTypeVisit},
	}

	methodOptions := []*views.SelectOption{
		{Label: "POST", Value: models.AccessMethodPost},
	}

	vueTable.SetCommonOptions("adminId", subAdminOptions).SetCommonOptions("type", typeOptions).SetCommonOptions("method", methodOptions)

	// 搜索表单
	searchForm := views.NewForm().Struct(AccessIndexParams{}).SetPagination(&model.Pagination{RowsPerPage: 100, SortBy: "created_at"})
	vueTable.SetSearchs(searchForm)

	// 工具栏
	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", AccessDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(AccessIndexRows{})
	vueTable.GetFieldColumn("adminId").SetSelectFormat(subAdminOptions)
	vueTable.GetFieldColumn("type").SetSelectFormat(typeOptions)
	vueTable.GetFieldColumn("method").SetSelectFormat(methodOptions)

	return vueTable
}
