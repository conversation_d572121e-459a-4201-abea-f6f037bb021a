package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// UserDelete 删除用户
func UserDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	for _, v := range bodyParams.Ids {

		err := db.Transaction(func(tx *gorm.DB) error {
			// 删除用户信息
			result := tx.Where("id = ?", v).Where("admin_id IN ?", subAdminIDs).Delete(&models.User{})
			if result.Error != nil {
				return result.Error
			}

			// 删除用户会话
			result = tx.Unscoped().Where("user_id = ?", v).Delete(&models.ChatsSessions{})
			if result.Error != nil {
				return result.Error
			}

			// 删除用户聊天信息
			result = tx.Unscoped().Where("sender_id = ? OR receiver_id = ?", v, v).Delete(&models.ChatsMessages{})
			if result.Error != nil {
				return result.Error
			}

			// 删除用户设置
			result = tx.Unscoped().Where("user_id = ?", v).Delete(&models.Setting{})
			if result.Error != nil {
				return result.Error
			}

			// 删除用户订单
			result = tx.Where("user_id = ?", v).Delete(&models.Order{})
			if result.Error != nil {
				return result.Error
			}

			// 删除用户访问记录
			result = tx.Unscoped().Where("user_id = ?", v).Delete(&models.Access{})
			if result.Error != nil {
				return result.Error
			}

			// 删除用户资产
			result = tx.Where("user_id = ?", v).Delete(&models.UserAssets{})
			if result.Error != nil {
				return result.Error
			}

			// 删除用户认证信息
			result = tx.Where("user_id = ?", v).Delete(&models.UserAuth{})
			if result.Error != nil {
				return result.Error
			}

			// 删除用户等级信息
			result = tx.Where("user_id = ?", v).Delete(&models.UserLevel{})
			if result.Error != nil {
				return result.Error
			}

			// 删除用户转账记录
			result = tx.Where("sender_id = ? OR receiver_id = ?", v, v).Delete(&models.Transfer{})
			if result.Error != nil {
				return result.Error
			}

			// 删除用户提现账户
			result = tx.Where("user_id = ?", v).Delete(&models.WalletAccount{})
			if result.Error != nil {
				return result.Error
			}

			// 删除用户提现订单
			result = tx.Where("user_id = ?", v).Delete(&models.WalletOrder{})
			if result.Error != nil {
				return result.Error
			}

			// 删除用户账单信息
			result = tx.Where("user_id = ?", v).Delete(&models.WalletBill{})
			if result.Error != nil {
				return result.Error
			}

			return nil
		})
		if err != nil {
			return c.ErrorJson(fmt.Sprintf("删除用户失败: %s", err.Error()))
		}
	}

	return c.SuccessOk()
}
