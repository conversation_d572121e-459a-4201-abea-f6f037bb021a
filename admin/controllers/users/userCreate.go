package users

import (
	"fmt"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"
)

// UserCreateParams 创建用户参数
type UserCreateParams struct {
	Username string `json:"username" validate:"required,email" views:"label:账户(邮箱)"`
	Password string `json:"password" validate:"required" views:"label:密码"`
}

// UserCreate 创建用户
func UserCreate(c *context.CustomCtx, bodyParams *UserCreateParams) error {
	db := model.NewModel()
	adminSettingService := service.NewAdminSettingService()

	// 如果当前管理不是超级管理员, 那么需要判断当前邮箱是否重复
	if c.Claims.AdminID != models.SuperAdminID {
		adminService := service.NewAdminUserService()
		adminSubIds, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.MerchantID)
		existUser := models.User{}
		db.Model(&models.User{}).Where("username = ?", bodyParams.Username).Where("admin_id IN ?", adminSubIds).Find(&existUser)
		if existUser.ID > 0 {
			return c.ErrorJson("当前邮箱已存在, 请更换邮箱")
		}
	}

	userAvatar, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, "userAvatar").ToString()
	result := db.Create(&models.User{
		AdminID:     c.Claims.AdminID,
		Username:    bodyParams.Username,
		Email:       bodyParams.Username,
		Avatar:      userAvatar,
		Nickname:    utils.GenerateRandomString(12),
		Password:    utils.EncryptPassword(bodyParams.Password),
		SecurityKey: utils.EncryptPassword(bodyParams.Password),
		InviteCode:  utils.GenerateInviteCode(6),
		Birthday:    time.Now().Add(-10 * 365 * 24 * time.Hour),
		LastLoginAt: time.Now(),
		LastLoginIP: utils.GetClientIP(c.Ctx),
	})
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("创建用户失败: %s", result.Error.Error()))
	}

	return c.SuccessOk()
}
