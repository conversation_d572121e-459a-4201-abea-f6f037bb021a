package users

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// AccessDelete 删除访问记录
func AccessDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	for _, v := range bodyParams.Ids {
		result := db.Unscoped().Where("admin_id IN ?", subAdminIDs).Where("id = ?", v).Delete(&models.Access{})
		if result.Error != nil {
			return result.Error
		}
	}

	return c.SuccessOk()
}
