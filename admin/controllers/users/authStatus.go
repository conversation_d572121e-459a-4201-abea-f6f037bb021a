package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// 更新用户认证状态参数
type AuthStatusParams struct {
	ID            uint   `json:"id" validate:"required" views:"label:ID;display:true"`
	PendingStatus int8   `json:"pendingStatus" validate:"required" views:"label:状态;type:select"`
	Reason        string `json:"reason" views:"label:拒绝原因;type:textarea"`
}

// 更新用户认证状态
func AuthStatus(c *context.CustomCtx, bodyParams *AuthStatusParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	authInfo := &models.UserAuth{}
	db := model.NewModel()
	result := db.Where("id = ?", bodyParams.ID).Where("admin_id IN ?", subAdminIDs).First(authInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("查询授权失败: %s", result.Error.Error()))
	}

	authInfo.Status = bodyParams.PendingStatus
	authInfo.Reason = bodyParams.Reason
	result = db.Save(authInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新授权状态失败: %s", result.Error.Error()))
	}

	return c.SuccessOk()
}
