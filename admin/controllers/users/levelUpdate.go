package users

import (
	"errors"
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// LevelUpdateParams 更新用户等级参数
type LevelUpdateParams struct {
	ID        uint                     `json:"id" validate:"required" views:"label:ID;display:true"`
	LevelID   uint                     `json:"levelId" views:"label:等级;type:select"`
	Status    int8                     `json:"status" views:"label:状态;type:select"`
	ExpiredAt model.GormDateTimeParams `json:"expiredAt" views:"label:到期时间;type:dateTime"`
}

// LevelUpdate 更新用户等级
func LevelUpdate(c *context.CustomCtx, bodyParams *LevelUpdateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	userLevel := &models.UserLevel{}
	result := db.Where("id = ?", bodyParams.ID).Where("admin_id IN ?", subAdminIDs).First(userLevel)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("获取用户等级失败: %s", result.Error.Error()))
	}

	updateParams := &models.UserLevel{}
	err := db.Transaction(func(tx *gorm.DB) error {
		if userLevel.LevelID != bodyParams.LevelID {
			//	等级信息
			levelInfo := &models.Level{}
			result = tx.Where("id = ?", bodyParams.LevelID).First(levelInfo)
			if result.Error != nil {
				return result.Error
			}
			if userLevel.AdminID != levelInfo.AdminID {
				return errors.New("等级不匹配")
			}
			updateParams.LevelID = bodyParams.LevelID

			// 修改这里，适应新的 UserLevelData 结构
			// 保留原有的购买类型，如果没有则默认为全额购买
			buyType := models.UserLevelBuyTypeFullAmount
			if userLevel.ID > 0 && userLevel.Data.BuyType != "" {
				buyType = userLevel.Data.BuyType
			}

			updateParams.Data = models.UserLevelData{
				Levels:  []models.Level{*levelInfo},
				BuyType: buyType,
			}

			userInfo := &models.User{}
			result = tx.Where("id = ?", userLevel.UserID).First(userInfo)
			if result.Error != nil {
				return result.Error
			}

			// 扣除当前等级对应的金额
			walletService := service.NewWalletService()
			err := walletService.SpendBalance(tx, c.Rds, c.Lang, models.BillTypeMembershipPurchase, userLevel.ID, userInfo, 0)
			if err != nil {
				return err
			}
		}

		// 更新用户等级
		updateParams.ExpiredAt = bodyParams.ExpiredAt.ToTimeZone(c.TimeZone)
		updateParams.Status = bodyParams.Status
		result = tx.Model(&models.UserLevel{}).Where("id = ?", userLevel.ID).Updates(updateParams)
		if result.Error != nil {
			return result.Error
		}

		return nil
	})
	if err != nil {
		return c.ErrorJson(fmt.Sprintf("更新用户等级失败: %s", err.Error()))
	}

	return c.SuccessOk()
}
