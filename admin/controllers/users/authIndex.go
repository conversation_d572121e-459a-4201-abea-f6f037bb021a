package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	AuthIndexURL  = "/users/auth/index"
	AuthCreateURL = "/users/auth/create"
	AuthUpdateURL = "/users/auth/update"
	AuthStatusURL = "/users/auth/status"
	AuthDeleteURL = "/users/auth/delete"
)

// 权限表格列表参数
type AuthIndexParams struct {
	AdminID    int64             `json:"adminId" views:"label:管理;type:select"`
	UserID     int64             `json:"userId" views:"label:用户;type:selectSearch;mask:user#username>id"`
	RealName   string            `json:"realName" views:"label:证件姓名"`
	IDNumber   string            `json:"idNumber" views:"label:证件号码"`
	Type       int8              `json:"type" views:"label:证件类型;type:select"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// 权限表格列表
type AuthIndexRows struct {
	ID        uint        `json:"id" column:"label:ID"`
	AdminID   int64       `json:"adminId" column:"label:管理"`
	UserName  string      `json:"username" column:"label:账户;scanField:userInfo.username"`
	Type      int8        `json:"type" column:"label:证件类型"`
	RealName  string      `json:"realName" column:"label:证件姓名"`
	IDNumber  string      `json:"idNumber" column:"label:证件号码"`
	Photo1    string      `json:"photo1" column:"label:证件照片1"`
	Photo2    string      `json:"photo2" column:"label:证件照片2"`
	Status    int8        `json:"status" column:"label:状态"`
	CreatedAt string      `json:"createdAt" column:"label:申请时间;type:date"`
	Address   string      `json:"address"`
	Photo3    string      `json:"photo3"`
	UserID    uint        `json:"userId"`
	UserInfo  models.User `json:"userInfo" gorm:"foreignKey:UserID"`
}

// 权限列表
func AuthIndex(c *context.CustomCtx, bodyParams *AuthIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	data := &model.IndexData{Items: make([]*AuthIndexRows, 0), Count: 0}

	db := model.NewModel()
	query := db.Equal("real_name", bodyParams.RealName).Equal("id_number", bodyParams.IDNumber).
		Equal("user_id", bodyParams.UserID).Equal("admin_id", bodyParams.AdminID).
		Equal("type", bodyParams.Type).Equal("status", bodyParams.Status).Model(&models.UserAuth{})

	query.Where("admin_id IN ?", subAdminIDs).Count(&data.Count)
	query.Preload("UserInfo")
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}

// 权限列表配置
func AuthIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	adminService := service.NewAdminUserService()
	adminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)

	vueTable := views.NewTable(AuthIndexURL)

	statusOptions := []*views.SelectOption{
		{Label: "审核", Value: models.UserAuthStatusPending},
		{Label: "通过", Value: models.UserAuthStatusCompleted},
		{Label: "拒绝", Value: models.UserAuthStatusRejected},
	}
	pendingStatusOptions := []*views.SelectOption{
		{Label: "通过", Value: models.UserAuthStatusCompleted},
		{Label: "拒绝", Value: models.UserAuthStatusRejected},
	}
	typeOptions := []*views.SelectOption{
		{Label: "身份证", Value: models.UserAuthTypeIDCard},
		{Label: "护照", Value: models.UserAuthTypePassport},
		{Label: "驾驶证", Value: models.UserAuthTypeDriver},
	}

	vueTable.SetCommonOptions("status", statusOptions).SetCommonOptions("type", typeOptions).
		SetCommonOptions("adminId", adminOptions).SetCommonOptions("pendingStatus", pendingStatusOptions)

	// 搜索
	searchForm := views.NewForm().Struct(AuthIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(AuthCreateParams{}).FlattenInputs()
	createForm.SetFieldInputParam("type", "default", models.UserAuthTypeIDCard)
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("用户认证", AuthCreateURL, createForm))

	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", AssetsDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(AuthIndexRows{})
	vueTable.GetFieldColumn("type").SetSelectFormat(typeOptions)
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("photo1").SetAvatarFormat("photo1")
	vueTable.GetFieldColumn("photo2").SetAvatarFormat("photo2")
	vueTable.GetFieldColumn("adminId").SetSelectFormat(adminOptions)

	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(AuthUpdateParams{}).ResetInputs([][]string{
		{"id"},
		{"photo1", "photo2", "photo3"},
		{"realName"}, {"idNumber"}, {"address"}, {"status"}, {"reason"},
	})
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", AuthUpdateURL, updateForm).SetSize(views.DialogSizeMedium))

	// 数据表格 - 更新状态
	statusForm := views.NewForm().Struct(AuthStatusParams{}).FlattenInputs()
	statusForm.SetFieldInputParam("pendingStatus", "default", models.UserAuthStatusCompleted)
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("审核", AuthStatusURL, statusForm).SetDisplay(fmt.Sprintf("row.status == %d", models.UserAuthStatusPending)))

	return vueTable
}
