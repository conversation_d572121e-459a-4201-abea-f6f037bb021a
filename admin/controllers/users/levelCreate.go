package users

import (
	"fmt"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// 创建用户等级参数
type LevelCreateParams struct {
	UserID  uint `json:"userId" validate:"required" views:"label:账户;type:selectSearch;mask:user#username>id"`
	LevelID uint `json:"levelId" validate:"required" views:"label:等级;type:select"`
}

// 创建用户等级
func LevelCreate(c *context.CustomCtx, bodyParams *LevelCreateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	// 用户信息
	userInfo := &models.User{}
	db := model.NewModel()
	result := db.Where("id = ?", bodyParams.UserID).Where("admin_id IN ?", subAdminIDs).First(userInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("查询用户失败: %s", result.Error.Error()))
	}

	// 管理信息
	adminInfo := &models.AdminUser{}
	result = db.Where("id = ?", userInfo.AdminID).First(adminInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("查询管理员失败: %s", result.Error.Error()))
	}

	// 等级信息
	levelInfo := &models.Level{}
	merchantID, _ := service.NewAdminUserService().GetMerchantIDWithCache(c.Rds, adminInfo.ID)
	result = db.Where("id = ?", bodyParams.LevelID).Where("admin_id = ?", merchantID).Where("status = ?", models.LevelStatusEnabled).First(levelInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("查询等级失败: %s", result.Error.Error()))
	}

	// 用户等级信息
	userLevel := &models.UserLevel{}
	result = db.Where("user_id = ?", userInfo.ID).Find(userLevel)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("查询用户等级失败: %s", result.Error.Error()))
	}
	if userLevel.ID > 0 {
		return c.ErrorJson("用户已存在等级")
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		userLevel.AdminID = c.Claims.AdminID
		userLevel.UserID = userInfo.ID
		userLevel.LevelID = levelInfo.ID
		userLevel.Status = models.UserLevelStatusEnabled
		userLevel.Data = models.UserLevelData{
			Levels:  []models.Level{*levelInfo},
			BuyType: models.UserLevelBuyTypeFullAmount, // 默认使用全额购买方式
		}
		userLevel.ExpiredAt = time.Now().Add(time.Duration(levelInfo.Days) * 24 * time.Hour)

		result = tx.Create(userLevel)
		if result.Error != nil {
			return c.ErrorJson(fmt.Sprintf("创建用户等级失败: %s", result.Error.Error()))
		}

		walletService := service.NewWalletService()
		return walletService.SpendBalance(tx, c.Rds, c.Lang, models.BillTypeMembershipPurchase, userLevel.ID, userInfo, 0)
	})
	if err != nil {
		return c.ErrorJson(fmt.Sprintf("创建用户等级失败: %s", err.Error()))
	}

	return c.SuccessOk()
}
