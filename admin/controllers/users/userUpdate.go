package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// UserUpdateParams 更新用户参数
type UserUpdateParams struct {
	ID          int                      `json:"id" validate:"required" views:"label:ID;display:true"`
	CountryID   uint                     `json:"-"`
	Username    string                   `json:"-"`
	Avatar      string                   `json:"avatar" views:"label:头像;type:image"`
	Nickname    string                   `json:"nickname" views:"label:昵称"`
	Email       string                   `json:"email" views:"label:邮箱"`
	Telephone   string                   `json:"telephone" views:"label:电话"`
	Score       int                      `json:"score" views:"label:信用分;type:number"`
	Birthday    model.GormDateTimeParams `json:"birthday" views:"label:生日;type:dateTime"`
	Password    model.GormPasswordParams `json:"password" views:"label:密码"`
	SecurityKey model.GormPasswordParams `json:"securityKey" views:"label:支付密码"`
	Sex         int8                     `json:"sex" views:"label:性别;type:select"`
	Type        int8                     `json:"type" views:"label:类型;type:select"`
	Status      int8                     `json:"status" views:"label:状态;type:select"`
}

// UserUpdate 更新用户
func UserUpdate(c *context.CustomCtx, bodyParams *UserUpdateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()
	userInfo := &models.User{}
	db.Where("id = ?", bodyParams.ID).Where("admin_id IN ?", subAdminIDs).Find(userInfo)

	bodyParams.Birthday = model.FormatDateTimeToRFC3339(bodyParams.Birthday, c.TimeZone)

	// 如果用户名跟邮箱一样，那么一起修改
	if userInfo.Username == userInfo.Email && bodyParams.Email != "" {
		bodyParams.Username = bodyParams.Email
	}

	// 如果手机跟用户名一样，那么一起修改
	if userInfo.Username == userInfo.Telephone && bodyParams.Telephone != "" {
		bodyParams.Username = bodyParams.Telephone
		if bodyParams.CountryID == 0 {
			countryInfo := &models.Country{}
			db.Where("admin_id = ?", c.Claims.MerchantID).Order("sort ASC").Find(countryInfo)
			bodyParams.CountryID = countryInfo.ID
		}
	}

	result := db.Model(&models.User{}).Where("id = ?", bodyParams.ID).Updates(bodyParams)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新用户失败: %s", result.Error.Error()))
	}

	return c.SuccessOk()
}
