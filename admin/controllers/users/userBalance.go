package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// UserBalanceParams 用户余额操作参数
type UserBalanceParams struct {
	ID       uint    `json:"id" validate:"required" views:"label:ID;display:true"`
	BillType int8    `json:"billType" validate:"required" views:"label:账单类型;type:select"`
	Amount   float64 `json:"amount" validate:"required" views:"label:金额;type:number"`
}

// UserBalance 用户余额操作
func UserBalance(c *context.CustomCtx, bodyParams *UserBalanceParams) error {
	adminService := service.NewAdminUserService()
	adminSettingService := service.NewAdminSettingService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	db := model.NewModel()

	if bodyParams.Amount < 0 {
		return c.ErrorJson("金额不能为负数")
	}

	// 用户信息
	userInfo := &models.User{}
	result := db.Model(&models.User{}).Where("admin_id IN ?", subAdminIDs).Where("id = ?", bodyParams.ID).First(userInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("用户不存在: %v", result.Error))
	}

	billType := bodyParams.BillType
	if bodyParams.BillType == BalanceFreezeDeposit {
		billType = models.BillTypeSystemAddition
	}
	err := db.Transaction(func(tx *gorm.DB) error {
		walletService := service.NewWalletService()
		switch billType {
		case models.BillTypeSystemAddition, models.BillTypeSystemReward, models.BillTypeRegisterReward, models.BillTypeInviteReward:
			err := walletService.IncreaseBalance(tx, c.Rds, c.Lang, billType, 0, userInfo, bodyParams.Amount)
			if err != nil {
				return err
			}

			merchantAdminId, _ := adminService.GetMerchantIDWithCache(c.Rds, userInfo.AdminID)
			// 判断是否冻结金额
			if bodyParams.BillType == BalanceFreezeDeposit {
				result := tx.Model(&models.User{}).Where("id = ?", userInfo.ID).Update("frozen_amount", gorm.Expr("frozen_amount + ?", bodyParams.Amount))
				if result.Error != nil {
					return result.Error
				}
			}

			// 判断用户是否升级
			switch billType {
			case models.BillTypeSystemAddition:
				// 判断是否充值金额升级等级
				buyLevelStr, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantAdminId, models.AdminSettingBuyLevelOptions).ToString()
				if buyLevelStr == models.UserLevelBuyDeposit {
					var sumDeposit float64
					tx.Model(&models.WalletOrder{}).Where("user_id = ? AND assets_id = 0 AND type = ? AND status = ?", userInfo.ID, models.WalletOrderTypeDeposit, models.WalletOrderStatusCompleted).Select("sum(money)").Scan(&sumDeposit)

					// 获取用户等级
					userLevel := models.UserLevel{}
					tx.Model(&models.UserLevel{}).Where("user_id = ? AND status = ?", userInfo.ID, models.UserLevelStatusEnabled).Find(&userLevel)

					var userLevelSymbol int8 = 0
					if userLevel.ID > 0 {
						latestLevel := userLevel.GetLatestLevel()
						if latestLevel != nil {
							userLevelSymbol = latestLevel.Symbol
						}
					}

					buyLevelMannerStr, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantAdminId, models.AdminSettingBuyLevelOptions2).ToString()
					// 获取大于当前用户等级, 并且大于当前总金额的等级
					systemLevel := models.Level{}
					tx.Model(&models.Level{}).Where("admin_id = ?", merchantAdminId).Where("symbol > ?", userLevelSymbol).Where("money <= ?", sumDeposit).Order("money DESC").Find(&systemLevel)
					if systemLevel.ID > 0 {
						err := models.UpdateUserLevel(tx, userInfo, &userLevel, systemLevel, buyLevelMannerStr)
						if err != nil {
							return err
						}
					}
				}
			}

			return nil
		case models.BillTypeSystemDeduction:
			return walletService.SpendBorrowBalance(tx, c.Rds, c.Lang, bodyParams.BillType, 0, userInfo, bodyParams.Amount)
		default:
			return fmt.Errorf("账单类型错误")
		}
	})
	if err != nil {
		return c.ErrorJson(fmt.Sprintf("操作失败: %v", err))
	}

	return nil
}
