package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// 删除用户认证
func AuthDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	for _, v := range bodyParams.Ids {
		result := db.Unscoped().Where("id = ?", v).Where("admin_id IN ?", subAdminIDs).Delete(&models.UserAuth{})
		if result.Error != nil {
			return c.<PERSON>rror<PERSON><PERSON>(fmt.Sprintf("删除授权失败: %s", result.Error.Error()))
		}
	}

	return c.SuccessOk()
}
