package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// UserAscriptionParams 用户归属参数
type UserAscriptionParams struct {
	ID       uint `json:"id" validate:"required" views:"label:ID;display:true"`
	AdminID  uint `json:"adminId" views:"label:管理ID;type:selectSearch;mask:admin_user#username>id"`
	ParentID uint `json:"parentId" views:"label:上级ID;type:selectSearch;mask:user#username>id"`
}

// UserAscription 用户归属
func UserAscription(c *context.CustomCtx, bodyParams *UserAscriptionParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	userInfo := &models.User{}
	result := db.Where("id = ?", bodyParams.ID).Where("admin_id IN ?", subAdminIDs).First(userInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("获取用户信息失败: %s", result.Error.Error()))
	}

	// 如果管理员存在
	if bodyParams.AdminID > 0 {
		adminInfo := &models.AdminUser{}
		result := db.Where("id = ?", bodyParams.AdminID).Where("id IN ?", subAdminIDs).First(adminInfo)
		if result.Error != nil {
			return c.ErrorJson(fmt.Sprintf("获取管理员信息失败: %s", result.Error.Error()))
		}
	}

	if bodyParams.ParentID > 0 {
		parentInfo := &models.User{}
		result := db.Where("id = ?", bodyParams.ParentID).Where("admin_id IN ?", subAdminIDs).First(parentInfo)
		if result.Error != nil {
			return c.ErrorJson(fmt.Sprintf("获取上级信息失败: %s", result.Error.Error()))
		}
	}

	result = db.Model(&models.User{}).Where("id = ?", bodyParams.ID).Updates(bodyParams)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新用户信息失败: %s", result.Error.Error()))
	}

	return c.SuccessOk()
}
