package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// 删除用户等级
func LevelDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	for _, v := range bodyParams.Ids {
		result := db.Unscoped().Where("id = ?", v).Where("admin_id IN ?", subAdminIDs).Delete(&models.UserLevel{})
		if result.Error != nil {
			return c.<PERSON>rror<PERSON>son(fmt.Sprintf("删除用户等级失败: %s", result.Error.Error()))
		}
	}

	return c.SuccessOk()
}
