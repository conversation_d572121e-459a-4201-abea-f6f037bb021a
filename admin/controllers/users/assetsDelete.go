package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// AssetsDelete 删除用户资产
func AssetsDelete(c *context.CustomCtx, bodyParams *model.DeleteParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	for _, id := range bodyParams.Ids {
		result := db.Unscoped().Where("id = ?", id).Where("admin_id IN ?", subAdminIDs).Delete(&models.UserAssets{})
		if result.Error != nil {
			return c.<PERSON>r<PERSON>(fmt.Sprintf("删除资产失败: %s", result.Error.Error()))
		}
	}

	return c.SuccessOk()
}
