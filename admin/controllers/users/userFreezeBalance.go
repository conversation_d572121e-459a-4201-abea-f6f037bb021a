package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// UserFreezeBalanceParams 用户冻结余额操作参数
type UserFreezeBalanceParams struct {
	ID     uint    `json:"id" validate:"required" views:"label:ID;display:true"`
	Amount float64 `json:"amount" views:"label:金额;type:number"`
}

// UserBalance 用户余额操作
func UserFreezeBalance(c *context.CustomCtx, bodyParams *UserFreezeBalanceParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	db := model.NewModel()

	if bodyParams.Amount < 0 {
		return c.ErrorJson("冻结金额不能为负数")
	}

	// 用户信息
	userInfo := &models.User{}
	result := db.Model(&models.User{}).Where("admin_id IN ?", subAdminIDs).Where("id = ?", bodyParams.ID).First(userInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("用户不存在: %v", result.Error))
	}

	result = db.Model(&models.User{}).Where("id = ?", userInfo.ID).Update("frozen_amount", bodyParams.Amount)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新用户冻结余额失败: %v", result.Error))
	}

	return c.SuccessOk()
}
