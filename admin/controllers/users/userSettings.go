package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

// UserSettingsParams 用户设置参数
type UserSettingsParams struct {
	ID    uint        `json:"id" validate:"required" views:"label:ID;display:true"`
	Field string      `json:"settingField" validate:"required" views:"label:字段;display:true"`
	Type  string      `json:"settingType" validate:"required" views:"label:类型;display:true"`
	Value interface{} `json:"settingValue" views:"label:配置;type:dynamic"`
}

// UserSettings 用户设置
func UserSettings(c *context.CustomCtx, bodyParams *UserSettingsParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	// 用户信息
	userInfo := models.User{}
	db := model.NewModel()
	result := db.Model(&userInfo).Where("id = ?", bodyParams.ID).Where("admin_id IN ?", subAdminIDs).First(&userInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("找不到用户信息 %s", result.Error.Error()))
	}

	userSettingInfo := models.Setting{}
	result = db.Model(&userSettingInfo).Where("field = ?", bodyParams.Field).Where("user_id = ?", userInfo.ID).Find(&userSettingInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("查询用户设置失败 %s", result.Error.Error()))
	}

	if userSettingInfo.ID == 0 {
		userSettingInfo.AdminID = userInfo.AdminID
		userSettingInfo.Field = bodyParams.Field
		userSettingInfo.Name = fmt.Sprintf("%s(%v)", bodyParams.Field, c.Claims.AdminID)
		userSettingInfo.Type = bodyParams.Type
		userSettingInfo.UserID = userInfo.ID
		userSettingInfo.Value = views.InputValueToString(bodyParams.Value)
		result = db.Create(&userSettingInfo)
	} else {
		userSettingInfo.Value = views.InputValueToString(bodyParams.Value)
		result = db.Save(&userSettingInfo)
	}

	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新用户设置失败 %s", result.Error.Error()))
	}

	return c.SuccessOk()
}
