package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// AuthUpdateParams 更新用户认证参数
type AuthUpdateParams struct {
	ID       int    `json:"id" validate:"required" views:"label:ID;display:true"`
	Photo1   string `json:"photo1" views:"label:证件照片1;type:image"`
	Photo2   string `json:"photo2" views:"label:证件照片2;type:image"`
	Photo3   string `json:"photo3" views:"label:证件照片3;type:image"`
	RealName string `json:"realName" views:"label:真实姓名"`
	IDNumber string `json:"idNumber" views:"label:身份证号"`
	Address  string `json:"address" views:"label:证件地址"`
	Status   int8   `json:"status" views:"label:状态;type:select"`
	Reason   string `json:"reason" views:"label:拒绝原因;type:textarea"`
}

// 更新用户认证
func AuthUpdate(c *context.CustomCtx, bodyParams *AuthUpdateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	authInfo := &models.UserAuth{}
	db := model.NewModel()
	result := db.Where("id = ?", bodyParams.ID).Where("admin_id IN ?", subAdminIDs).First(authInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("查询授权失败: %s", result.Error.Error()))
	}

	authInfo.RealName = bodyParams.RealName
	authInfo.IDNumber = bodyParams.IDNumber
	authInfo.Address = bodyParams.Address
	authInfo.Photo1 = bodyParams.Photo1
	authInfo.Photo2 = bodyParams.Photo2
	authInfo.Photo3 = bodyParams.Photo3
	authInfo.Status = bodyParams.Status
	authInfo.Reason = bodyParams.Reason

	result = db.Save(authInfo)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新授权失败: %s", result.Error.Error()))
	}

	return c.SuccessOk()
}
