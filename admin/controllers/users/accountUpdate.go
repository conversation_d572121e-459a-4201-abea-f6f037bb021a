package users

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// AccountUpdateParams 更新账户提现参数
type AccountUpdateParams struct {
	ID   uint                     `json:"id" validate:"required" views:"label:ID;display:true"`
	Name string                   `json:"name" views:"label:名称"`
	Data models.WalletPaymentData `json:"data" views:"label:配置;type:struct"`
}

// AccountUpdate 更新账户提现
func AccountUpdate(c *context.CustomCtx, bodyParams *AccountUpdateParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	db := model.NewModel()

	account := &models.WalletAccount{}
	result := db.Where("admin_id IN ?", subAdminIDs).Where("id = ?", bodyParams.ID).First(account)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("账户不存在: %s", result.Error.Error()))
	}

	account.Name = bodyParams.Name
	account.Data = bodyParams.Data

	result = db.Save(account)
	if result.Error != nil {
		return c.ErrorJson(fmt.Sprintf("更新失败: %s", result.Error.Error()))
	}

	return c.SuccessOk()
}
