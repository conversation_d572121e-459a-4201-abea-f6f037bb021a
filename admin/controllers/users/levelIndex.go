package users

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	LevelIndexURL  = "/users/level/index"
	LevelCreateURL = "/users/level/create"
	LevelUpdateURL = "/users/level/update"
	LevelDeleteURL = "/users/level/delete"
)

// LevelIndexParams 用户等级列表参数
type LevelIndexParams struct {
	AdminID    uint                   `json:"adminId" views:"label:管理;type:select"`
	UserID     uint                   `json:"userId" views:"label:账户;type:selectSearch;mask:user#username>id"`
	LevelID    uint                   `json:"levelId" views:"label:等级;type:select"`
	Status     int8                   `json:"status" views:"label:状态;type:select"`
	ExpiredAt  *model.RangeDatePicker `json:"expiredAt" views:"label:到期时间;type:dateRange"`
	Pagination *model.Pagination      `json:"pagination" views:"-"` // 分页参数
}

// LevelIndexRows 用户等级列表数据
type LevelIndexRows struct {
	ID        uint        `json:"id" column:"label:ID"`
	AdminID   uint        `json:"adminId" column:"label:管理"`
	UserName  string      `json:"username" column:"label:账户;scanField:userInfo.username"`
	LevelID   uint        `json:"levelId" column:"label:等级"`
	Status    int8        `json:"status" column:"label:状态"`
	ExpiredAt time.Time   `json:"expiredAt" column:"label:到期时间;type:date"`
	UserID    uint        `json:"userId"`
	UserInfo  models.User `json:"userInfo" gorm:"foreignKey:UserID"`
}

// LevelIndex 用户等级列表
func LevelIndex(c *context.CustomCtx, bodyParams *LevelIndexParams) error {
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	data := &model.IndexData{Items: make([]*LevelIndexRows, 0), Count: 0}

	db := model.NewModel()
	query := db.Equal("user_id", bodyParams.UserID).
		Equal("admin_id", bodyParams.AdminID).
		Equal("level_id", bodyParams.LevelID).
		Equal("status", bodyParams.Status).
		BetweenTime("expired_at", bodyParams.ExpiredAt, c.TimeZone).
		Model(&models.UserLevel{})

	query.Where("admin_id IN ?", subAdminIDs).Count(&data.Count)
	query.Preload("UserInfo")
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}

// LevelIndexConfigure 用户等级列表配置
func LevelIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(LevelIndexURL)

	levelService := service.NewSystemLevelService()
	adminService := service.NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.MerchantID)
	levelOptions, _ := levelService.GetLevelOptions(c.Rds, subAdminIDs)
	adminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)

	statusOptions := []*views.SelectOption{
		{Label: "禁用", Value: models.UserLevelStatusDisabled},
		{Label: "启用", Value: models.UserLevelStatusEnabled},
	}
	vueTable.SetCommonOptions("status", statusOptions).SetCommonOptions("levelId", levelOptions).SetCommonOptions("adminId", adminOptions)

	// 查询结构体, 自动转化成 inputs 设置表格数据
	searchForm := views.NewForm().Struct(LevelIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	createForm := views.NewForm().Struct(LevelCreateParams{}).FlattenInputs()
	if len(levelOptions) > 0 {
		createForm.SetFieldInputParam("levelId", "default", levelOptions[0].Value)
	}
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("添加会员", LevelCreateURL, createForm))

	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", LevelDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(LevelIndexRows{})
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("levelId").SetSelectFormat(levelOptions)
	vueTable.GetFieldColumn("adminId").SetSelectFormat(adminOptions)

	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(LevelUpdateParams{}).FlattenInputs()
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", LevelUpdateURL, updateForm))

	return vueTable
}
