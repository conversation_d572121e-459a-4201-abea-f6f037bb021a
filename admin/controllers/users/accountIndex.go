package users

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
	"zfeng/service"
)

const (
	AccountIndexURL  = "/users/account/index"
	AccountCreateURL = "/users/account/create"
	AccountUpdateURL = "/users/account/update"
	AccountDeleteURL = "/users/account/delete"
)

// AccountIndexParams 账户提现列表参数
type AccountIndexParams struct {
	Name       string            `json:"name" views:"label:名称"`
	AdminID    uint              `json:"adminId" views:"label:管理员;type:select"`
	UserID     uint              `json:"userId" views:"label:账户;type:selectSearch;mask:user#username>id"`
	PaymentID  uint              `json:"paymentId" views:"label:支付;type:select"`
	Type       int8              `json:"type" views:"label:类型;type:select"`
	Status     int8              `json:"status" views:"label:状态;type:select"`
	Pagination *model.Pagination `json:"pagination" views:"-"`
}

// AccountIndexRows 账户提现列表行
type AccountIndexRows struct {
	ID          uint                     `json:"id" column:"label:ID"`
	AdminID     uint                     `json:"adminId" column:"label:管理"`
	UserName    string                   `json:"username" column:"label:账户;scanField:userInfo.username"`
	Name        string                   `json:"name" column:"label:名称"`
	Type        int8                     `json:"type" column:"label:类型"`
	PaymentID   uint                     `json:"paymentId" column:"label:支付"`
	BankName    string                   `json:"bankName" column:"label:银行|公链;scanField:data.bankName"`
	BankCardNo  string                   `json:"bankCardNo" column:"label:卡号|地址;scanField:data.bankCardNo"`
	RealName    string                   `json:"realName" column:"label:姓名|Token;scanField:data.realName"`
	BankCode    string                   `json:"bankCode" column:"label:代号|简写;scanField:data.bankCode"`
	BankAddress string                   `json:"bankAddress" column:"label:支付地址;scanField:data.bankAddress"`
	Status      int8                     `json:"status" column:"label:状态"`
	Data        models.WalletPaymentData `json:"data"`
	UserID      uint                     `json:"userId"`
	UserInfo    models.User              `json:"userInfo" gorm:"foreignKey:UserID"`
}

// AccountIndex 账户提现列表
func AccountIndex(c *context.CustomCtx, bodyParams *AccountIndexParams) error {
	db := model.NewModel()
	data := &model.IndexData{Items: make([]*AccountIndexRows, 0), Count: 0}

	query := db.Equal("name", bodyParams.Name).
		Equal("admin_id", bodyParams.AdminID).
		Equal("user_id", bodyParams.UserID).
		Equal("payment_id", bodyParams.PaymentID).
		Equal("type", bodyParams.Type).
		Equal("status", bodyParams.Status).
		Model(&models.WalletAccount{})

	query.Where("admin_id = ?", c.Claims.AdminID).Count(&data.Count)
	query.Preload("UserInfo")
	query.Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	return c.SuccessJson(data)
}

func AccountIndexConfigure(c *context.CustomCtx, bodyParams map[string]interface{}) *views.Table {
	vueTable := views.NewTable(AccountIndexURL)
	paymentService := service.NewWalletPaymentService()
	adminService := service.NewAdminUserService()
	adminOptions := adminService.GetSubAdminOptions(c.Claims.AdminID)
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)

	// 所有余额提现方式
	balancePaymentOptions := paymentService.GetPaymentOptions(subAdminIDs, []int8{models.PaymentTypeBankCard, models.PaymentTypeCrypto}, []int8{models.PaymentModeWithdrawal})
	// 余额银行卡提现方式
	balanceBankPaymentOptions := paymentService.GetPaymentOptions(subAdminIDs, []int8{models.PaymentTypeBankCard}, []int8{models.PaymentModeWithdrawal})
	// 余额货币提现方式
	balanceAssetsPaymentOptions := paymentService.GetPaymentOptions(subAdminIDs, []int8{models.PaymentTypeCrypto}, []int8{models.PaymentModeWithdrawal})

	// 资产提现方式
	assetsPaymentOptions := paymentService.GetPaymentOptions(subAdminIDs, []int8{models.PaymentTypeBankCard, models.PaymentTypeCrypto}, []int8{models.PaymentModeAssetWithdrawal})
	paymentOptions := paymentService.GetPaymentOptions(subAdminIDs, []int8{models.PaymentTypeBankCard, models.PaymentTypeCrypto}, []int8{models.PaymentModeWithdrawal, models.PaymentModeAssetWithdrawal})
	typeOptions := []*views.SelectOption{
		{Label: "余额", Value: models.AccountTypeBalance},
		{Label: "资产", Value: models.AccountTypeAsset},
	}
	statusOptions := []*views.SelectOption{
		{Label: "启用", Value: models.PaymentStatusEnabled},
		{Label: "禁用", Value: models.PaymentStatusDisabled},
	}

	vueTable.SetCommonOptions("paymentId", paymentOptions).SetCommonOptions("status", statusOptions).SetCommonOptions("adminId", adminOptions).
		SetCommonOptions("balanceBankPaymentList", balanceBankPaymentOptions).SetCommonOptions("balanceAssetsPaymentList", balanceAssetsPaymentOptions).SetCommonOptions("assetsPaymentList", assetsPaymentOptions).
		SetCommonOptions("balancePaymentId", balancePaymentOptions).SetCommonOptions("assetsPaymentId", assetsPaymentOptions).SetCommonOptions("type", typeOptions)

	// 搜索
	searchForm := views.NewForm().Struct(AccountIndexParams{}).SetPagination(nil)
	vueTable.SetSearchs(searchForm)

	// 工具栏
	// 余额 - 银行卡账户
	createBalanceForm := views.NewForm().Struct(BalanceBankAccountParams{}).FlattenInputs()
	if len(balanceBankPaymentOptions) > 0 {
		createBalanceForm.SetFieldInputParam("paymentId", "default", balanceBankPaymentOptions[0].Value)
	}
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("银行卡[余额]", AccountCreateURL, createBalanceForm))

	// 余额 - 数字货币账户
	createAssetsBalanceForm := views.NewForm().Struct(BalanceAssetsAccountParams{}).FlattenInputs()
	if len(balanceAssetsPaymentOptions) > 0 {
		createAssetsBalanceForm.SetFieldInputParam("paymentId", "default", balanceAssetsPaymentOptions[0].Value)
	}
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("数字货币[余额]", AccountCreateURL, createAssetsBalanceForm))

	// 资产 - 数字货币账户
	createAssetsForm := views.NewForm().Struct(AssetsAccountParams{}).FlattenInputs()
	if len(assetsPaymentOptions) > 0 {
		createAssetsForm.SetFieldInputParam("paymentId", "default", assetsPaymentOptions[0].Value)
	}
	vueTable.SetTools(0, views.NewFormCreateButtonDialog("数字货币[资产]", AccountCreateURL, createAssetsForm))

	deleteForm := views.NewForm().Struct(model.DeleteParams{}).FlattenInputs()
	vueTable.SetToolsAndSelected(0, views.NewFormDeleteButtonDialog("批量删除", AccountDeleteURL, deleteForm), "ids", "id")

	// 数据表格数据
	vueTable.StructColumns(AccountIndexRows{})
	vueTable.GetFieldColumn("paymentId").SetSelectFormat(paymentOptions)
	vueTable.GetFieldColumn("status").SetSelectFormat(statusOptions)
	vueTable.GetFieldColumn("adminId").SetSelectFormat(adminOptions)
	vueTable.GetFieldColumn("type").SetSelectFormat(typeOptions)

	// 数据表格 - 更新
	updateForm := views.NewForm().Struct(AccountUpdateParams{}).FlattenInputs().SetChildFormFlattenInputs("data")
	vueTable.SetColumnOptions(0, views.NewFormOptionsButtonDialog("更新", AccountUpdateURL, updateForm))

	return vueTable
}
