package controllers

import (
	"errors"
	"strings"
	"zfeng/admin/controllers/admins"
	"zfeng/admin/controllers/chats"
	"zfeng/admin/controllers/indexs"
	"zfeng/admin/controllers/products"
	"zfeng/admin/controllers/systems"
	"zfeng/admin/controllers/users"
	"zfeng/admin/controllers/wallets"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/inits/instance"
	"zfeng/middleware"
	"zfeng/module/socket"
	"zfeng/service"

	"github.com/gofiber/fiber/v2"
	"github.com/gomodule/redigo/redis"
)

const (
	// AdminRoutePrefix 项目前缀
	AdminRoutePrefix = "/admin"
	// 版本前缀
	routePrefix = "/v1"
)

func SetupRoutes(app *fiber.App) {
	// 添加验证码相关路由
	app.Get("/", context.NewHandler(indexs.Admin)).Name("首页介绍")
	app.Get("/captcha/create", indexs.NewCaptcha).Name("生成验证码")
	app.Get("/captcha/:captchaId", indexs.ShowCaptcha).Name("显示验证码")
	app.Post("/login", context.NewHandler[indexs.LoginParams](indexs.Login)).Name("管理登录")
	app.Get("/chats/ws", instance.NewChatsSocketConn(socket.ConnTypeAdmin)).Name("管理聊天")

	// 添加 /auth 路由组，使用 JWT 中间件
	authRuoter := app.Group(routePrefix, middleware.InitJWT(middleware.AdminPrivateKey, successHandler))

	// 聊天会话
	authRuoter.Post("/chats/sessions", context.NewHandler[chats.SessionsParams](chats.Sessions)).Name("聊天会话")
	authRuoter.Post("/chats/session", context.NewHandler[chats.SessionParams](chats.Session)).Name("聊天会话信息")
	authRuoter.Post("/chats/messages", context.NewHandler[chats.MessagesParams](chats.Messages)).Name("聊天记录")
	authRuoter.Post("/chats/send", context.NewHandler[chats.SendParams](chats.Send)).Name("发送消息")
	authRuoter.Post("/chats/read", context.NewHandler[chats.ReadParams](chats.Read)).Name("阅读消息")
	authRuoter.Post("/chats/revoke", context.NewHandler[chats.RevokeParams](chats.Revoke)).Name("撤回消息")

	// 公用基础接口
	authRuoter.Post("/configure", context.NewHandler[indexs.ConfigureParams](indexs.Configure)).Name("菜单配置")
	authRuoter.Post("/init", context.NewHandler(indexs.Init)).Name("初始化数据")
	authRuoter.Post("/index", context.NewHandler(indexs.Index)).Name("控制台信息")
	authRuoter.Post("/info", context.NewHandler(indexs.Info)).Name("管理信息")
	authRuoter.Post("/update", context.NewHandler[indexs.UpdateParams](indexs.Update)).Name("更新管理信息")
	authRuoter.Post("/update/password", context.NewHandler[indexs.UpdatePasswordParams](indexs.UpdatePassword)).Name("更新管理密码")
	authRuoter.Post("/update/translate", context.NewHandler[indexs.UpdateTranslateParams](indexs.UpdateTranslate)).Name("更新语言翻译")
	authRuoter.Post("/lang/tabs", context.NewHandler(indexs.LangTabs)).Name("语言标签")
	authRuoter.Post("/upload", context.NewHandler(indexs.Upload)).Name("上传文件")
	authRuoter.Post("/options", context.NewHandler[indexs.OptionsParams](indexs.Options)).Name("获取选项")
	authRuoter.Post("/notify/info", context.NewHandler[indexs.NotifyInfoParams](indexs.NotifyInfo)).Name("通知详情")
	authRuoter.Post("/audio", context.NewHandler(indexs.Audio)).Name("提示音")

	// 管理员菜单配置
	indexs.AdminMenuConfigureFunc[admins.ManageIndexURL] = admins.ManageIndexConfigure
	indexs.AdminMenuConfigureFunc[admins.RoleIndexURL] = admins.RoleIndexConfigure
	indexs.AdminMenuConfigureFunc[admins.SettingsIndexURL] = admins.SettingsIndexConfigure
	indexs.AdminMenuConfigureFunc[admins.LogsIndexURL] = admins.LogsIndexConfigure
	indexs.AdminMenuConfigureFunc[admins.MenuIndexURL] = admins.MenuIndexConfigure
	indexs.AdminMenuConfigureFunc[systems.TranslateIndexURL] = systems.TranslateIndexConfigure
	indexs.AdminMenuConfigureFunc[systems.MenuIndexURL] = systems.MenuIndexConfigure
	indexs.AdminMenuConfigureFunc[systems.CountryIndexURL] = systems.CountryIndexConfigure
	indexs.AdminMenuConfigureFunc[systems.LangIndexURL] = systems.LangIndexConfigure
	indexs.AdminMenuConfigureFunc[systems.ArticleIndexURL] = systems.ArticleIndexConfigure
	indexs.AdminMenuConfigureFunc[systems.ChannelIndexURL] = systems.ChannelIndexConfigure
	indexs.AdminMenuConfigureFunc[systems.LevelIndexURL] = systems.LevelIndexConfigure
	indexs.AdminMenuConfigureFunc[systems.NotifyIndexURL] = systems.NotifyIndexConfigure
	indexs.AdminMenuConfigureFunc[users.AccessIndexURL] = users.AccessIndexConfigure
	indexs.AdminMenuConfigureFunc[users.AuthIndexURL] = users.AuthIndexConfigure
	indexs.AdminMenuConfigureFunc[users.AccountIndexURL] = users.AccountIndexConfigure
	indexs.AdminMenuConfigureFunc[users.AssetsIndexURL] = users.AssetsIndexConfigure
	indexs.AdminMenuConfigureFunc[users.LevelIndexURL] = users.LevelIndexConfigure
	indexs.AdminMenuConfigureFunc[users.UserIndexURL] = users.UserIndexConfigure
	indexs.AdminMenuConfigureFunc[wallets.AssetsIndexURL] = wallets.AssetsIndexConfigure
	indexs.AdminMenuConfigureFunc[wallets.PaymentIndexURL] = wallets.PaymentIndexConfigure
	indexs.AdminMenuConfigureFunc[wallets.PaymentAssetsIndexURL] = wallets.PaymentAssetsIndexConfigure
	indexs.AdminMenuConfigureFunc[wallets.BillIndexURL] = wallets.BillIndexConfigure
	indexs.AdminMenuConfigureFunc[wallets.BillAssetsIndexURL] = wallets.BillAssetsIndexConfigure
	indexs.AdminMenuConfigureFunc[wallets.DepositIndexURL] = wallets.DepositIndexConfigure
	indexs.AdminMenuConfigureFunc[wallets.DepositAssetsIndexURL] = wallets.DepositAssetsIndexConfigure
	indexs.AdminMenuConfigureFunc[wallets.WithdrawIndexURL] = wallets.WithdrawIndexConfigure
	indexs.AdminMenuConfigureFunc[wallets.WithdrawAssetsIndexURL] = wallets.WithdrawAssetsIndexConfigure
	indexs.AdminMenuConfigureFunc[wallets.TransferIndexURL] = wallets.TransferIndexConfigure
	indexs.AdminMenuConfigureFunc[wallets.TransferAssetsIndexURL] = wallets.TransferAssetsIndexConfigure
	indexs.AdminMenuConfigureFunc[wallets.SwapsIndexURL] = wallets.SwapsIndexConfigure
	indexs.AdminMenuConfigureFunc[products.CategoryIndexURL] = products.CategoryIndexConfigure
	indexs.AdminMenuConfigureFunc[products.ProductIndexURL] = products.ProductIndexConfigure
	indexs.AdminMenuConfigureFunc[products.OrderSpotIndexURL] = products.OrderSpotIndexConfigure
	indexs.AdminMenuConfigureFunc[products.CollectIndexURL] = products.CollectIndexConfigure
	indexs.AdminMenuConfigureFunc[products.OrderContractIndexURL] = products.OrderContractIndexConfigure
	indexs.AdminMenuConfigureFunc[products.OrderFuturesIndexURL] = products.OrderFuturesIndexConfigure
	indexs.AdminMenuConfigureFunc[products.OrderStakingIndexURL] = products.OrderStakingIndexConfigure

	// 管理员模块
	authRuoter.Post(admins.ManageIndexURL, context.NewHandler[admins.ManageIndexParams](admins.ManageIndex)).Name("管理列表")
	authRuoter.Post(admins.ManageCreateURL, context.NewHandler[admins.ManageCreateParams](admins.ManageCreate)).Name("新增管理")
	authRuoter.Post(admins.ManageUpdateURL, context.NewHandler[admins.ManageUpdateParams](admins.ManageUpdate)).Name("更新管理")
	authRuoter.Post(admins.ManageDeleteURL, context.NewHandler[model.DeleteParams](admins.ManageDelete)).Name("删除管理")
	authRuoter.Post(admins.ManageUpdateDataURL, context.NewHandler[admins.ManageUpdateDataParams](admins.ManageUpdateData)).Name("更新管理设置")
	authRuoter.Post(admins.ManageUpdateMerchantSettingURL, context.NewHandler[admins.ManageUpdateMerchantSettingParams](admins.ManageUpdateMerchantSetting)).Name("更新商户设置")
	authRuoter.Post(admins.ManageResetURL, context.NewHandler[admins.ManageResetParams](admins.ManageReset)).Name("重置商户数据")

	// 管理角色模块
	authRuoter.Post(admins.RoleIndexURL, context.NewHandler[admins.RoleIndexParams](admins.RoleIndex)).Name("角色列表")
	authRuoter.Post(admins.RoleCreateURL, context.NewHandler[admins.RoleCreateParams](admins.RoleCreate)).Name("新增角色")
	authRuoter.Post(admins.RoleUpdateURL, context.NewHandler[admins.RoleUpdateParams](admins.RoleUpdate)).Name("更新角色")
	authRuoter.Post(admins.RoleDeleteURL, context.NewHandler[model.DeleteParams](admins.RoleDelete)).Name("删除角色")

	// 管理设置模块
	authRuoter.Post(admins.SettingsIndexURL, context.NewHandler[admins.SettingsIndexParams](admins.SettingsIndex)).Name("商户配置列表")
	authRuoter.Post(admins.SettingsUpdateURL, context.NewHandler[admins.SettingsUpdateParams](admins.SettingsUpdate)).Name("更新商户配置")

	// 操作日志模块
	authRuoter.Post(admins.LogsIndexURL, context.NewHandler[admins.LogsIndexParams](admins.LogsIndex)).Name("操作日志列表")

	// 菜单模块
	authRuoter.Post(admins.MenuIndexURL, context.NewHandler[admins.MenuIndexParams](admins.MenuIndex)).Name("菜单列表")
	authRuoter.Post(admins.MenuUpdateURL, context.NewHandler[admins.MenuUpdateParams](admins.MenuUpdate)).Name("更新菜单")
	authRuoter.Post(admins.MenuUpdateDataURL, context.NewHandler[admins.MenuUpdateDataParams](admins.MenuUpdateData)).Name("更新菜单设置")

	// 翻译模块
	authRuoter.Post(systems.TranslateIndexURL, context.NewHandler[systems.TranslateIndexParams](systems.TranslateIndex)).Name("翻译列表")
	authRuoter.Post(systems.TranslateUpdateURL, context.NewHandler[systems.TranslateUpdateParams](systems.TranslateUpdate)).Name("更新翻译")

	// 用户菜单模块
	authRuoter.Post(systems.MenuIndexURL, context.NewHandler[systems.MenuIndexParams](systems.MenuIndex)).Name("用户菜单列表")
	authRuoter.Post(systems.MenuCreateURL, context.NewHandler[systems.MenuCreateParams](systems.MenuCreate)).Name("新增用户菜单")
	authRuoter.Post(systems.MenuDeleteURL, context.NewHandler[model.DeleteParams](systems.MenuDelete)).Name("删除用户菜单")
	authRuoter.Post(systems.MenuUpdateURL, context.NewHandler[systems.MenuUpdateParams](systems.MenuUpdate)).Name("更新用户菜单")
	authRuoter.Post(systems.MenuUpdateDataURL, context.NewHandler[systems.MenuUpdateDataParams](systems.MenuUpdateData)).Name("更新用户菜单设置")

	// 国家模块
	authRuoter.Post(systems.CountryIndexURL, context.NewHandler[systems.CountryIndexParams](systems.CountryIndex)).Name("国家列表")
	authRuoter.Post(systems.CountryCreateURL, context.NewHandler[systems.CountryCreateParams](systems.CountryCreate)).Name("新增国家")
	authRuoter.Post(systems.CountryDeleteURL, context.NewHandler[model.DeleteParams](systems.CountryDelete)).Name("删除国家")
	authRuoter.Post(systems.CountryUpdateURL, context.NewHandler[systems.CountryUpdateParams](systems.CountryUpdate)).Name("更新国家")

	// 语言模块
	authRuoter.Post(systems.LangIndexURL, context.NewHandler[systems.LangIndexParams](systems.LangIndex)).Name("语言列表")
	authRuoter.Post(systems.LangCreateURL, context.NewHandler[systems.LangCreateParams](systems.LangCreate)).Name("新增语言")
	authRuoter.Post(systems.LangDeleteURL, context.NewHandler[model.DeleteParams](systems.LangDelete)).Name("删除语言")
	authRuoter.Post(systems.LangUpdateURL, context.NewHandler[systems.LangUpdateParams](systems.LangUpdate)).Name("更新语言")
	authRuoter.Post(systems.LangSyncURL, context.NewHandler[systems.LangSyncParams](systems.LangSync)).Name("翻译同步")
	authRuoter.Post(systems.LangTranslateURL, context.NewHandler[systems.LangTranslateParams](systems.LangTranslate)).Name("翻译语言")

	// 文章模块
	authRuoter.Post(systems.ArticleIndexURL, context.NewHandler[systems.ArticleIndexParams](systems.ArticleIndex)).Name("文章列表")
	authRuoter.Post(systems.ArticleCreateURL, context.NewHandler[systems.ArticleCreateParams](systems.ArticleCreate)).Name("新增文章")
	authRuoter.Post(systems.ArticleDeleteURL, context.NewHandler[model.DeleteParams](systems.ArticleDelete)).Name("删除文章")
	authRuoter.Post(systems.ArticleUpdateURL, context.NewHandler[systems.ArticleUpdateParams](systems.ArticleUpdate)).Name("更新文章")

	// 渠道模块
	authRuoter.Post(systems.ChannelIndexURL, context.NewHandler[systems.ChannelIndexParams](systems.ChannelIndex)).Name("渠道列表")
	authRuoter.Post(systems.ChannelCreateURL, context.NewHandler[systems.ChannelCreateParams](systems.ChannelCreate)).Name("新增渠道")
	authRuoter.Post(systems.ChannelDeleteURL, context.NewHandler[model.DeleteParams](systems.ChannelDelete)).Name("删除渠道")
	authRuoter.Post(systems.ChannelUpdateURL, context.NewHandler[systems.ChannelUpdateParams](systems.ChannelUpdate)).Name("更新渠道")

	// 等级模块
	authRuoter.Post(systems.LevelIndexURL, context.NewHandler[systems.LevelIndexParams](systems.LevelIndex)).Name("等级列表")
	authRuoter.Post(systems.LevelCreateURL, context.NewHandler[systems.LevelCreateParams](systems.LevelCreate)).Name("新增等级")
	authRuoter.Post(systems.LevelDeleteURL, context.NewHandler[model.DeleteParams](systems.LevelDelete)).Name("删除等级")
	authRuoter.Post(systems.LevelUpdateURL, context.NewHandler[systems.LevelUpdateParams](systems.LevelUpdate)).Name("更新等级")

	// 通知模块
	authRuoter.Post(systems.NotifyIndexURL, context.NewHandler[systems.NotifyIndexParams](systems.NotifyIndex)).Name("通知列表")
	authRuoter.Post(systems.NotifyCreateFrontendURL, context.NewHandler[systems.NotifyCreateFrontendParams](systems.NotifyCreateFrontend)).Name("新增用户通知")
	authRuoter.Post(systems.NotifyCreateBackendURL, context.NewHandler[systems.NotifyCreateBackendParams](systems.NotifyCreateBackend)).Name("新增管理通知")
	authRuoter.Post(systems.NotifyDeleteURL, context.NewHandler[model.DeleteParams](systems.NotifyDelete)).Name("删除通知")
	authRuoter.Post(systems.NotifyUpdateURL, context.NewHandler[systems.NotifyUpdateParams](systems.NotifyUpdate)).Name("更新通知")

	// 访问记录模块
	authRuoter.Post(users.AccessIndexURL, context.NewHandler[users.AccessIndexParams](users.AccessIndex)).Name("访问记录列表")
	authRuoter.Post(users.AccessDeleteURL, context.NewHandler[model.DeleteParams](users.AccessDelete)).Name("删除访问记录")

	// 提现账户模块
	authRuoter.Post(users.AccountIndexURL, context.NewHandler[users.AccountIndexParams](users.AccountIndex)).Name("提现账户列表")
	authRuoter.Post(users.AccountCreateURL, context.NewHandler[users.AccountCreateParams](users.AccountCreate)).Name("新增提现账户")
	authRuoter.Post(users.AccountUpdateURL, context.NewHandler[users.AccountUpdateParams](users.AccountUpdate)).Name("更新提现账户")
	authRuoter.Post(users.AccountDeleteURL, context.NewHandler[model.DeleteParams](users.AccountDelete)).Name("删除提现账户")

	// 用户资产模块
	authRuoter.Post(users.AssetsIndexURL, context.NewHandler[users.AssetsIndexParams](users.AssetsIndex)).Name("用户资产列表")
	authRuoter.Post(users.AssetsCreateURL, context.NewHandler[users.AssetsCreateParams](users.AssetsCreate)).Name("资产变更")
	authRuoter.Post(users.AssetsUpdateURL, context.NewHandler[users.AssetsUpdateParams](users.AssetsUpdate)).Name("用户资产变更")
	authRuoter.Post(users.AssetsDeleteURL, context.NewHandler[model.DeleteParams](users.AssetsDelete)).Name("删除用户资产")

	// 用户认证模块
	authRuoter.Post(users.AuthIndexURL, context.NewHandler[users.AuthIndexParams](users.AuthIndex)).Name("用户认证列表")
	authRuoter.Post(users.AuthCreateURL, context.NewHandler[users.AuthCreateParams](users.AuthCreate)).Name("新增用户认证")
	authRuoter.Post(users.AuthUpdateURL, context.NewHandler[users.AuthUpdateParams](users.AuthUpdate)).Name("更新用户认证")
	authRuoter.Post(users.AuthStatusURL, context.NewHandler[users.AuthStatusParams](users.AuthStatus)).Name("更新用户认证状态")
	authRuoter.Post(users.AuthDeleteURL, context.NewHandler[model.DeleteParams](users.AuthDelete)).Name("删除用户认证")

	// 用户等级模块
	authRuoter.Post(users.LevelIndexURL, context.NewHandler[users.LevelIndexParams](users.LevelIndex)).Name("用户等级列表")
	authRuoter.Post(users.LevelCreateURL, context.NewHandler[users.LevelCreateParams](users.LevelCreate)).Name("新增用户等级")
	authRuoter.Post(users.LevelDeleteURL, context.NewHandler[model.DeleteParams](users.LevelDelete)).Name("删除用户等级")
	authRuoter.Post(users.LevelUpdateURL, context.NewHandler[users.LevelUpdateParams](users.LevelUpdate)).Name("更新用户等级")

	// 用户模块
	authRuoter.Post(users.UserIndexURL, context.NewHandler[users.UserIndexParams](users.UserIndex)).Name("用户列表")
	authRuoter.Post(users.UserCreateURL, context.NewHandler[users.UserCreateParams](users.UserCreate)).Name("新增用户")
	authRuoter.Post(users.UserUpdateURL, context.NewHandler[users.UserUpdateParams](users.UserUpdate)).Name("更新用户")
	authRuoter.Post(users.UserBalanceURL, context.NewHandler[users.UserBalanceParams](users.UserBalance)).Name("用户余额")
	authRuoter.Post(users.UserFreezeBalanceURL, context.NewHandler[users.UserFreezeBalanceParams](users.UserFreezeBalance)).Name("用户冻结余额")
	authRuoter.Post(users.UserAscriptionURL, context.NewHandler[users.UserAscriptionParams](users.UserAscription)).Name("用户归属")
	authRuoter.Post(users.UserSettingsURL, context.NewHandler[users.UserSettingsParams](users.UserSettings)).Name("用户配置")
	authRuoter.Post(users.UserDeleteURL, context.NewHandler[model.DeleteParams](users.UserDelete)).Name("删除用户")

	// 钱包资产模块
	authRuoter.Post(wallets.AssetsIndexURL, context.NewHandler[wallets.AssetsIndexParams](wallets.AssetsIndex)).Name("资产列表")
	authRuoter.Post(wallets.AssetsCreateURL, context.NewHandler[wallets.AssetsCreateParams](wallets.AssetsCreate)).Name("新增资产")
	authRuoter.Post(wallets.AssetsUpdateURL, context.NewHandler[wallets.AssetsUpdateParams](wallets.AssetsUpdate)).Name("更新资产")
	authRuoter.Post(wallets.AssetsDeleteURL, context.NewHandler[model.DeleteParams](wallets.AssetsDelete)).Name("删除资产")

	// 钱包支付模块
	authRuoter.Post(wallets.PaymentIndexURL, context.NewHandler[wallets.PaymentIndexParams](wallets.PaymentIndex)).Name("支付列表")
	authRuoter.Post(wallets.PaymentCreateURL, context.NewHandler[wallets.PaymentCreateParams](wallets.PaymentCreate)).Name("新增支付")
	authRuoter.Post(wallets.PaymentUpdateURL, context.NewHandler[wallets.PaymentUpdateParams](wallets.PaymentUpdate)).Name("更新支付")
	authRuoter.Post(wallets.PaymentUpdateDataURL, context.NewHandler[wallets.PaymentUpdateDataParams](wallets.PaymentUpdateData)).Name("更新支付数据")
	authRuoter.Post(wallets.PaymentUpdateConfURL, context.NewHandler[wallets.PaymentUpdateConfParams](wallets.PaymentUpdateConf)).Name("更新支付设置")
	authRuoter.Post(wallets.PaymentDeleteURL, context.NewHandler[model.DeleteParams](wallets.PaymentDelete)).Name("删除支付")

	// 钱包支付资产模块
	authRuoter.Post(wallets.PaymentAssetsIndexURL, context.NewHandler[wallets.PaymentAssetsIndexParams](wallets.PaymentAssetsIndex)).Name("支付资产列表")
	authRuoter.Post(wallets.PaymentAssetsCreateURL, context.NewHandler[wallets.PaymentAssetsCreateParams](wallets.PaymentAssetsCreate)).Name("新增支付资产")
	authRuoter.Post(wallets.PaymentAssetsUpdateURL, context.NewHandler[wallets.PaymentAssetsUpdateParams](wallets.PaymentAssetsUpdate)).Name("更新支付资产")
	authRuoter.Post(wallets.PaymentAssetsUpdateDataURL, context.NewHandler[wallets.PaymentAssetsUpdateDataParams](wallets.PaymentAssetsUpdateData)).Name("更新支付资产数据")
	authRuoter.Post(wallets.PaymentAssetsUpdateDataURL, context.NewHandler[wallets.PaymentAssetsUpdateConfParams](wallets.PaymentAssetsUpdateConf)).Name("更新支付资产设置")
	authRuoter.Post(wallets.PaymentAssetsDeleteURL, context.NewHandler[model.DeleteParams](wallets.PaymentAssetsDelete)).Name("删除支付资产")

	// 钱包账单模块
	authRuoter.Post(wallets.BillIndexURL, context.NewHandler[wallets.BillIndexParams](wallets.BillIndex)).Name("账单列表")
	authRuoter.Post(wallets.BillCreateURL, context.NewHandler[wallets.BillCreateParams](wallets.BillCreate)).Name("新增账单")
	authRuoter.Post(wallets.BillUpdateURL, context.NewHandler[wallets.BillUpdateParams](wallets.BillUpdate)).Name("更新账单")
	authRuoter.Post(wallets.BillDeleteURL, context.NewHandler[model.DeleteParams](wallets.BillDelete)).Name("删除账单")

	// 钱包账单资产模块
	authRuoter.Post(wallets.BillAssetsIndexURL, context.NewHandler[wallets.BillAssetsIndexParams](wallets.BillAssetsIndex)).Name("账单资产列表")
	authRuoter.Post(wallets.BillAssetsCreateURL, context.NewHandler[wallets.BillAssetsCreateParams](wallets.BillAssetsCreate)).Name("新增账单资产")
	authRuoter.Post(wallets.BillAssetsUpdateURL, context.NewHandler[wallets.BillAssetsUpdateParams](wallets.BillAssetsUpdate)).Name("更新账单资产")
	authRuoter.Post(wallets.BillAssetsDeleteURL, context.NewHandler[model.DeleteParams](wallets.BillAssetsDelete)).Name("删除账单资产")

	// 钱包订单公用模块
	authRuoter.Post(wallets.OrderUpdateURL, context.NewHandler[wallets.OrderUpdateParams](wallets.OrderUpdate)).Name("更新钱包订单")
	authRuoter.Post(wallets.OrderDeleteURL, context.NewHandler[model.DeleteParams](wallets.OrderDelete)).Name("删除钱包订单")
	authRuoter.Post(wallets.OrderStatusURL, context.NewHandler[wallets.OrderStatusParams](wallets.OrderStatus)).Name("更新钱包订单状态")

	// 钱包充值订单模块
	authRuoter.Post(wallets.DepositIndexURL, context.NewHandler[wallets.DepositIndexParams](wallets.DepositIndex)).Name("充值订单列表")
	authRuoter.Post(wallets.DepositCreateURL, context.NewHandler[wallets.DepositCreateParams](wallets.DepositCreate)).Name("新增充值订单")

	// 钱包提现订单模块
	authRuoter.Post(wallets.WithdrawIndexURL, context.NewHandler[wallets.WithdrawIndexParams](wallets.WithdrawIndex)).Name("提现订单列表")
	authRuoter.Post(wallets.WithdrawCreateURL, context.NewHandler[wallets.WithdrawCreateParams](wallets.WithdrawCreate)).Name("新增提现订单")

	// 钱包转账订单模块
	authRuoter.Post(wallets.TransferIndexURL, context.NewHandler[wallets.TransferIndexParams](wallets.TransferIndex)).Name("转账订单列表")
	authRuoter.Post(wallets.TransferCreateURL, context.NewHandler[wallets.TransferCreateParams](wallets.TransferCreate)).Name("新增转账订单")
	authRuoter.Post(wallets.TransferUpdateURL, context.NewHandler[wallets.TransferUpdateParams](wallets.TransferUpdate)).Name("更新转账订单")
	authRuoter.Post(wallets.TransferDeleteURL, context.NewHandler[model.DeleteParams](wallets.TransferDelete)).Name("删除转账订单")

	// 钱包充值订单资产模块
	authRuoter.Post(wallets.DepositAssetsIndexURL, context.NewHandler[wallets.DepositAssetsIndexParams](wallets.DepositAssetsIndex)).Name("充值订单资产列表")
	authRuoter.Post(wallets.DepositAssetsCreateURL, context.NewHandler[wallets.DepositAssetsCreateParams](wallets.DepositAssetsCreate)).Name("新增充值订单资产")

	// 钱包提现订单资产模块
	authRuoter.Post(wallets.WithdrawAssetsIndexURL, context.NewHandler[wallets.WithdrawAssetsIndexParams](wallets.WithdrawAssetsIndex)).Name("提现订单资产列表")
	authRuoter.Post(wallets.WithdrawAssetsCreateURL, context.NewHandler[wallets.WithdrawAssetsCreateParams](wallets.WithdrawAssetsCreate)).Name("新增提现订单资产")

	// 钱包转账订单资产模块
	authRuoter.Post(wallets.TransferAssetsIndexURL, context.NewHandler[wallets.TransferAssetsIndexParams](wallets.TransferAssetsIndex)).Name("转账订单资产列表")
	authRuoter.Post(wallets.TransferAssetsCreateURL, context.NewHandler[wallets.TransferAssetsCreateParams](wallets.TransferAssetsCreate)).Name("新增转账订单资产")

	// 钱包资产交换模块
	authRuoter.Post(wallets.SwapsIndexURL, context.NewHandler[wallets.SwapsIndexParams](wallets.SwapsIndex)).Name("资产交换列表")
	authRuoter.Post(wallets.SwapsCreateURL, context.NewHandler[wallets.SwapsCreateParams](wallets.SwapsCreate)).Name("新增资产交换")
	authRuoter.Post(wallets.SwapsUpdateURL, context.NewHandler[wallets.SwapsUpdateParams](wallets.SwapsUpdate)).Name("更新资产交换")
	authRuoter.Post(wallets.SwapsDeleteURL, context.NewHandler[model.DeleteParams](wallets.SwapsDelete)).Name("删除资产交换")

	// 商品分类模块
	authRuoter.Post(products.CategoryIndexURL, context.NewHandler[products.CategoryIndexParams](products.CategoryIndex)).Name("商品分类列表")
	authRuoter.Post(products.CategoryCreateURL, context.NewHandler[products.CategoryCreateParams](products.CategoryCreate)).Name("新增商品分类")
	authRuoter.Post(products.CategoryUpdateURL, context.NewHandler[products.CategoryUpdateParams](products.CategoryUpdate)).Name("更新商品分类")
	authRuoter.Post(products.CategoryDeleteURL, context.NewHandler[model.DeleteParams](products.CategoryDelete)).Name("删除商品分类")

	// 商品模块
	authRuoter.Post(products.ProductIndexURL, context.NewHandler[products.ProductIndexParams](products.ProductIndex)).Name("商品列表")
	authRuoter.Post(products.ProductCreateURL, context.NewHandler[products.ProductCreateParams](products.ProductCreate)).Name("新增商品")
	authRuoter.Post(products.ProductUpdateURL, context.NewHandler[products.ProductUpdateParams](products.ProductUpdate)).Name("更新商品")
	authRuoter.Post(products.ProductDeleteURL, context.NewHandler[model.DeleteParams](products.ProductDelete)).Name("删除商品")
	authRuoter.Post(products.ProductUpdateLineURL, context.NewHandler[products.ProductUpdateLineParams](products.ProductUpdateLine)).Name("更新商品线性数据")
	authRuoter.Post(products.ProductSettingKLineURL, context.NewHandler[products.ProductKlineSettingParams](products.ProductKlineSetting)).Name("设置商品K线数据")
	authRuoter.Post(products.ProductStakingSettingURL, context.NewHandler[products.ProductUpdateStarkingParams](products.ProductUpdateStarking)).Name("质押设置")

	// 商品扩展设置模块
	authRuoter.Post(products.ExtendSettingURL, context.NewHandler[products.ExtendSettingsParams](products.ExtendSettings)).Name("产品扩展设置")

	// 币币订单模块
	authRuoter.Post(products.OrderSpotIndexURL, context.NewHandler[products.OrderSpotIndexParams](products.OrderSpotIndex)).Name("币币订单列表")
	authRuoter.Post(products.OrderCreateURL, context.NewHandler[products.OrderCreateParams](products.OrderCreate)).Name("新增订单")
	authRuoter.Post(products.OrderUpdateURL, context.NewHandler[products.OrderUpdateParams](products.OrderUpdate)).Name("更新订单")
	authRuoter.Post(products.OrderDeleteURL, context.NewHandler[model.DeleteParams](products.OrderSpotDelete)).Name("删除订单")
	authRuoter.Post(products.OrderRevokeURL, context.NewHandler[products.OrderRevokeParams](products.OrderSpotRevoke)).Name("撤消订单")

	// 合约订单模块
	authRuoter.Post(products.OrderContractIndexURL, context.NewHandler[products.OrderContractIndexParams](products.OrderContractIndex)).Name("合约订单列表")
	authRuoter.Post(products.OrderContractCloseURL, context.NewHandler[products.OrderContractCloseParams](products.OrderContractClose)).Name("手动平仓")
	authRuoter.Post(products.OrderContractLiquidationURL, context.NewHandler[products.OrderContractLiquidationParams](products.OrderContractLiquidation)).Name("爆仓设置")

	// 期货订单模块
	authRuoter.Post(products.OrderFuturesIndexURL, context.NewHandler[products.OrderFuturesIndexParams](products.OrderFuturesIndex)).Name("期货订单列表")
	authRuoter.Post(products.OrderFuturesControlURL, context.NewHandler[products.OrderFuturesControlParams](products.OrderFuturesControl)).Name("期权控制")

	// 质押订单模块
	authRuoter.Post(products.OrderStakingIndexURL, context.NewHandler[products.OrderStakingIndexParams](products.OrderStakingIndex)).Name("质押订单列表")
	authRuoter.Post(products.OrderStakingUpdateURL, context.NewHandler[products.ProductStarkingRedemptionParams](products.ProductStarkingRedemption)).Name("质押赎回")

	// 产品收藏
	authRuoter.Post(products.CollectCreateURL, context.NewHandler[products.CollectCreateParams](products.CollectCreate)).Name("创建收藏")
	authRuoter.Post(products.CollectIndexURL, context.NewHandler[products.CollectIndexParams](products.CollectIndex)).Name("收藏列表")
	authRuoter.Post(products.CollectUpdateURL, context.NewHandler[products.CollectUpdateParams](products.CollectUpdate)).Name("修改收藏信息")
	authRuoter.Post(products.CollectDeleteURL, context.NewHandler[model.DeleteParams](products.CollectDelete)).Name("删除收藏信息")

	// 整理RABC数据
	if !fiber.IsChild() {
		service.InitRouterPermissions(routePrefix, app)
	}
}

// successHandler 处理 JWT 验证成功后的逻辑
func successHandler(rdsConn redis.Conn, claims *context.TokenClaims, c *fiber.Ctx) error {
	//	检查当前管理是否有权限
	currentPermission := service.NewAdminRbacService().GetPermissionByRoute(rdsConn, claims.AdminID, strings.TrimPrefix(c.Path(), AdminRoutePrefix+routePrefix))
	if currentPermission == nil {
		return errors.New("路由权限不存在")
	}

	// 记录管理操作日志
	service.NewAdminLogsService().Create(c, claims.AdminID, currentPermission.Child)

	return nil
}
