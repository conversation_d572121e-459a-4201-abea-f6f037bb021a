Name: "Exchange"           #项目名称
Port: "4020"            #项目端口
StaticPath: "./static"  #静态文件路径
Debug: false             #调试模式
PreFork: true           #子进程

#数据库配置
Database:
  DbName: "exchange_test"       #数据库
  Network: "tcp"        #协议
  Server: "127.0.0.1"   #地址
  Port: 3306            #端口
  User: "root"          #用户
  Pass: "12!@qwAS"    #密码
  MaxIdleConns: 10      #最大空闲连接数
  MaxOpenConns: 100     #最大打开连接数
  ConnMaxLifetime: 1h   #连接的最大生存时间
#缓存配置
Redis:
  DbName: 1             #数据库
  Network: "tcp"        #协议
  Server: "127.0.0.1"   #地址
  Port: 6379            #端口
  Pass: ""              #密码