package models

import (
	"database/sql/driver"
	"errors"
	"zfeng/core/model"

	"github.com/goccy/go-json"
	"github.com/gofiber/fiber/v2"
)

const (
	UserAssetStatusDisabled int8 = -1 // 禁用
	UserAssetStatusEnabled  int8 = 10 // 启用
)

// UserAssets 用户资产
type UserAssets struct {
	model.BaseModel
	AdminID         uint           `gorm:"type:int unsigned not null;index;comment:管理员ID" json:"adminId"`
	UserID          uint           `gorm:"type:int unsigned not null;uniqueIndex:idx_user_asset;comment:用户ID" json:"userId"`
	AssetsID        uint           `gorm:"type:int unsigned not null;uniqueIndex:idx_user_asset;comment:资产ID" json:"assetsId"`
	FrozenAmount    float64        `gorm:"type:decimal(16,4) not null;default:0;comment:冻结金额" json:"frozenAmount"`
	AvailableAmount float64        `gorm:"type:decimal(16,4) not null;default:0;comment:可用金额" json:"availableAmount"`
	Status          int8           `gorm:"type:tinyint not null;default:10;index;comment:状态(-1:禁用,10:启用)" json:"status"`
	Data            UserAssetsData `gorm:"type:json;comment:数据" json:"data"`
}

// UserAssetsInfo 资产信息
type UserAssetsInfo struct {
	ID              uint    `json:"id"`              // 资产ID
	AssetsID        int     `json:"assetsId"`        // 产品资产id
	Icon            string  `json:"icon" gorm:"-"`   //	图标
	AvailableAmount float64 `json:"availableAmount"` // 资产余额
}

func (u UserAssetsInfo) TableName() string {
	return "user_assets"
}

// UserAssetsData 用户资产数据
type UserAssetsData struct {
}

// Value implements the driver.Valuer interface
func (d UserAssetsData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *UserAssetsData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

func init() {
	// 只允许主进程运行
	if fiber.IsChild() {
		return
	}

	db := model.NewModel()

	// Create the table if it doesn't exist
	if err := db.AutoMigrate(&UserAssets{}); err != nil {
		panic("Failed to auto migrate UserAssets table: " + err.Error())
	}
}
