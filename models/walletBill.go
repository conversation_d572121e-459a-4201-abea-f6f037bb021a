package models

import (
	"database/sql/driver"
	"errors"
	"time"
	"zfeng/core/model"
	"zfeng/core/views"

	"github.com/goccy/go-json"
	"github.com/gofiber/fiber/v2"
)

// WalletBillTypePrefix 钱包账单类型前缀
const WalletBillTypePrefix = "walletBillType"

const (
	// 信用（传入）账单类型
	BillTypeDeposit            int8 = 1  // 充值
	BillTypeSystemReward       int8 = 2  // 系统奖励
	BillTypeRegisterReward     int8 = 3  // 注册奖励
	BillTypeInviteReward       int8 = 4  // 邀请奖励
	BillTypeDistributionReward int8 = 5  // 分销奖励
	BillTypeBalanceUnfreeze    int8 = 6  // 余额解冻
	BillTypeSystemAddition     int8 = 7  // 系统加款
	BillTypeWithdrawalReject   int8 = 8  // 提现拒绝
	BillTypeProductRefund      int8 = 9  // 产品退款
	BillTypeProductEarnings    int8 = 10 // 产品收益
	BillTypeTransferReceive    int8 = 11 // 转账接收
	BillTypeSwapsReceive       int8 = 12 // 闪兑接收

	// 借记（支出）账单类型
	BillTypeWithdrawal         int8 = -1 // 提现
	BillTypeSystemDeduction    int8 = -2 // 系统扣款
	BillTypeProductPurchase    int8 = -3 // 购买产品
	BillTypeMembershipPurchase int8 = -4 // 购买会员
	BillTypeBalanceFreeze      int8 = -5 // 余额冻结
	BillTypeTransferSend       int8 = -6 // 转账发送
	BillTypeSwapsSend          int8 = -7 // 闪兑发送
)

// BillTypeNames 账单类型名称
var BillTypeNames = map[int8]*views.SelectOption{
	BillTypeDeposit:            {Label: "充值", Value: BillTypeDeposit},
	BillTypeSystemReward:       {Label: "系统奖励", Value: BillTypeSystemReward},
	BillTypeRegisterReward:     {Label: "注册奖励", Value: BillTypeRegisterReward},
	BillTypeInviteReward:       {Label: "邀请奖励", Value: BillTypeInviteReward},
	BillTypeDistributionReward: {Label: "分销奖励", Value: BillTypeDistributionReward},
	BillTypeBalanceUnfreeze:    {Label: "余额解冻", Value: BillTypeBalanceUnfreeze},
	BillTypeSystemAddition:     {Label: "系统加款", Value: BillTypeSystemAddition},
	BillTypeWithdrawalReject:   {Label: "提现拒绝", Value: BillTypeWithdrawalReject},
	BillTypeProductRefund:      {Label: "产品退款", Value: BillTypeProductRefund},
	BillTypeProductEarnings:    {Label: "产品收益", Value: BillTypeProductEarnings},
	BillTypeTransferReceive:    {Label: "转账接收", Value: BillTypeTransferReceive},
	BillTypeSwapsReceive:       {Label: "闪兑接收", Value: BillTypeSwapsReceive},
	BillTypeWithdrawal:         {Label: "提现", Value: BillTypeWithdrawal},
	BillTypeSystemDeduction:    {Label: "系统扣款", Value: BillTypeSystemDeduction},
	BillTypeProductPurchase:    {Label: "购买产品", Value: BillTypeProductPurchase},
	BillTypeMembershipPurchase: {Label: "购买会员", Value: BillTypeMembershipPurchase},
	BillTypeBalanceFreeze:      {Label: "余额冻结", Value: BillTypeBalanceFreeze},
	BillTypeTransferSend:       {Label: "转账发送", Value: BillTypeTransferSend},
	BillTypeSwapsSend:          {Label: "闪兑发送", Value: BillTypeSwapsSend},
}

// GetWalletBillCheckboxOptions 获取钱包账单类型选项
func GetWalletBillCheckboxOptions() []*views.CheckboxOption {
	checkboxes := make([]*views.CheckboxOption, 0)
	for _, v := range BillTypeNames {
		checkboxes = append(checkboxes, &views.CheckboxOption{
			Label:   v.Label,
			Value:   v.Value,
			Checked: true,
		})
	}
	return checkboxes
}

// WalletBill 钱包账单
type WalletBill struct {
	model.BaseModel
	AdminID  uint           `gorm:"type:int unsigned not null;index;comment:管理ID" json:"adminId"`
	UserID   uint           `gorm:"type:int unsigned not null;index;comment:用户ID" json:"userId"`
	AssetsID uint           `gorm:"type:int unsigned not null;index;comment:资产ID" json:"assetsId"`
	SourceID uint           `gorm:"type:int unsigned not null;index;comment:来源ID" json:"sourceId"`
	Type     int8           `gorm:"type:tinyint not null;index;comment:类型" json:"type"`
	Name     string         `gorm:"type:varchar(60) not null;index;comment:名称" json:"name"`
	Money    float64        `gorm:"type:decimal(16,4) not null;comment:金额" json:"money"`
	Balance  float64        `gorm:"type:decimal(16,4) not null;comment:余额" json:"balance"`
	Desc     string         `gorm:"type:varchar(255);comment:描述" json:"desc"`
	Data     WalletBillData `gorm:"type:json;comment:数据" json:"data"`
}

// WalletBillDailyStats 钱包账单每日统计
type WalletBillDailyStats struct {
	Date    time.Time `json:"date"`
	Money   float64   `json:"money"`   // 已经乘以汇率的金额
	Balance float64   `json:"balance"` // 已经乘以汇率的余额
}

// WalletBillData 钱包账单数据
type WalletBillData struct {
}

// Value implements the driver.Valuer interface
func (d WalletBillData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *WalletBillData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

func init() {
	// 只允许主进程运行
	if fiber.IsChild() {
		return
	}

	db := model.NewModel()

	// Create the table if it doesn't exist
	if err := db.AutoMigrate(&WalletBill{}); err != nil {
		panic("Failed to auto migrate WalletBill table: " + err.Error())
	}
}
