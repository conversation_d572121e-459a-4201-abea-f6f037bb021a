package models

import (
	"database/sql/driver"
	"errors"
	"time"
	"zfeng/core/model"

	"github.com/goccy/go-json"
	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

const (
	UserLevelStatusDisabled int8   = -1        // 禁用
	UserLevelStatusExpired  int8   = 10        // 过期
	UserLevelStatusEnabled  int8   = 20        // 激活
	UserLevelBuyBalance     string = "balance" // 余额购买
	UserLevelBuyDeposit     string = "deposit" // 充值升级

	UserLevelBuyTypeFullAmount     string = "fullAmount"     // 全额购买
	UserLevelBuyTypeDifference     string = "difference"     // 补差价购买
	UserLevelBuyTypeClassification string = "classification" // 按等级分类购买
)

// UserLevel 会员表
type UserLevel struct {
	model.BaseModel
	AdminID   uint          `gorm:"type:int unsigned not null;index;comment:管理ID" json:"adminId"`
	UserID    uint          `gorm:"type:int unsigned not null;index;comment:用户ID" json:"userId"`
	LevelID   uint          `gorm:"type:int unsigned not null;index;comment:等级ID" json:"levelId"`
	Status    int8          `gorm:"type:tinyint not null;default:20;index;comment:状态(-1:禁用,10:过期,20:激活)" json:"status"`
	Data      UserLevelData `gorm:"type:json;comment:等级信息" json:"data"`
	ExpiredAt time.Time     `gorm:"type:datetime(3);index;comment:过期时间" json:"expiredAt"`
}

// UserLevelData 用户等级数据
type UserLevelData struct {
	Levels  []Level `json:"levels"`  // 使用切片存储多个等级
	BuyType string  `json:"buyType"` // 购买类型
}

// Scan implements sql.Scanner interface
func (u *UserLevelData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, &u)
}

// Value implements driver.Valuer interface
func (u UserLevelData) Value() (driver.Value, error) {
	return json.Marshal(u)
}

func init() {
	// 只允许主进程运行
	if fiber.IsChild() {
		return
	}

	db := model.NewModel()

	// Create the table if it doesn't exist
	if err := db.AutoMigrate(&UserLevel{}); err != nil {
		panic("Failed to auto migrate UserLevel table: " + err.Error())
	}
}

// UpdateUserLevel 更新用户等级
func UpdateUserLevel(tx *gorm.DB, userInfo *User, userLevel *UserLevel, systemLevel Level, buyType string) error {
	userLevel.LevelID = systemLevel.ID

	// 根据购买类型处理等级数据
	switch buyType {
	case UserLevelBuyTypeFullAmount, UserLevelBuyTypeDifference:
		// 全额购买和补差价购买：替换所有等级
		userLevel.Data = UserLevelData{
			Levels:  []Level{systemLevel},
			BuyType: buyType,
		}
	case UserLevelBuyTypeClassification:
		// 按等级分类购买：添加新等级或更新已有等级
		if userLevel.ID > 0 {
			// 已有等级记录，检查是否已包含该等级
			found := false
			for i, level := range userLevel.Data.Levels {
				if level.ID == systemLevel.ID {
					// 更新已有等级
					userLevel.Data.Levels[i] = systemLevel
					found = true
					break
				}
			}
			if !found {
				// 添加新等级
				userLevel.Data.Levels = append(userLevel.Data.Levels, systemLevel)
			}
			userLevel.Data.BuyType = buyType
		} else {
			// 新建等级记录
			userLevel.Data = UserLevelData{
				Levels:  []Level{systemLevel},
				BuyType: buyType,
			}
		}
	default:
		// 默认使用全额购买
		userLevel.Data = UserLevelData{
			Levels:  []Level{systemLevel},
			BuyType: UserLevelBuyTypeFullAmount,
		}
	}

	userLevel.Status = UserLevelStatusEnabled
	userLevel.ExpiredAt = time.Now().AddDate(0, 0, systemLevel.Days)

	var result *gorm.DB
	if userLevel.ID > 0 {
		result = tx.Save(userLevel)
		if result.Error != nil {
			return result.Error
		}
	} else {
		userLevel.AdminID = userInfo.AdminID
		userLevel.UserID = userInfo.ID
		result = tx.Create(userLevel)
		if result.Error != nil {
			return result.Error
		}
	}
	return result.Error
}

// GetLatestLevel 获取用户最新购买的等级
func (u *UserLevel) GetLatestLevel() *Level {
	if len(u.Data.Levels) == 0 {
		return nil
	}

	// 直接返回切片中的最后一个元素
	latestLevel := u.Data.Levels[len(u.Data.Levels)-1]
	return &latestLevel
}
