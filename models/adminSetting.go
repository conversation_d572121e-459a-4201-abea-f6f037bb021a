package models

import (
	"database/sql/driver"
	"errors"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/core/views/vues"
	"zfeng/utils"

	"github.com/goccy/go-json"
	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

const (
	AdminSettingGroupDefault  uint = 1  // 默认分组
	AdminSettingGroupBasic    uint = 10 // 基础分组
	AdminSettingGroupHome     uint = 11 // 首页分组
	AdminSettingGroupWallet   uint = 12 // 钱包分组
	AdminSettingGroupTemplate uint = 13 // 模版分组
	AdminSettingGroupProducts uint = 4  // 产品分组

	AdminSettingTypeInput int8 = 1 // Input类型
)

// AdminSettingGroupOptions 管理设置分组Options
var AdminSettingGroupOptions = []*views.SelectOption{
	{Label: "默认配置", Value: AdminSettingGroupDefault},
	{Label: "基础配置", Value: AdminSettingGroupBasic},
	{Label: "首页配置", Value: AdminSettingGroupHome},
	{Label: "钱包配置", Value: AdminSettingGroupWallet},
	{Label: "模版配置", Value: AdminSettingGroupTemplate},
	{Label: "产品配置", Value: AdminSettingGroupProducts},
}

// AdminSetting 管理设置表
type AdminSetting struct {
	model.BaseModel
	AdminID uint             `gorm:"type:int unsigned not null;uniqueIndex:idx_admin_field;comment:管理ID" json:"adminId"`
	GroupID uint             `gorm:"type:int unsigned not null;index;comment:分组ID" json:"groupId"`
	Name    string           `gorm:"type:varchar(60) not null;comment:名称" json:"name"`
	Type    int8             `gorm:"type:tinyint not null;default:1;comment:类型(1:Input类型)" json:"type"`
	Field   string           `gorm:"type:varchar(60) not null;uniqueIndex:idx_admin_field;comment:键名" json:"field"`
	Value   string           `gorm:"type:text;comment:键值" json:"value"`
	Data    AdminSettingData `gorm:"type:json;comment:配置" json:"data,omitempty"`
}

// AdminSettingData 管理设置配置数据
type AdminSettingData struct {
	Input        views.Input            `json:"input"`
	ChildrenForm map[string]*views.Form `json:"childrenForm"`
}

// Value implements the driver.Valuer interface
func (d AdminSettingData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *AdminSettingData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

func init() {
	// 只允许主进程运行
	if fiber.IsChild() {
		return
	}

	db := model.NewModel()

	// Create the table if it doesn't exist
	if err := db.AutoMigrate(&AdminSetting{}); err != nil {
		panic("Failed to auto migrate AdminSetting table: " + err.Error())
	}

	// Initialize admin settings
	if err := InitAdminSettings(db.DB); err != nil {
		panic("Failed to initialize admin settings: " + err.Error())
	}
}

// InitAdminSettings 初始化管理配置
func InitAdminSettings(db *gorm.DB) error {
	var count int64
	if err := db.Model(&AdminSetting{}).Count(&count).Error; err != nil {
		return err
	}
	if count == 0 {
		// 默认配置
		settings := []AdminSetting{
			// AdminSettingGroupDefault 默认分组
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupDefault,
				Name:    "前端模版[商户设置]",
				Type:    AdminSettingTypeInput,
				Field:   "merchantTemplate",
				Value:   "default",
				Data: AdminSettingData{Input: views.Input{Label: "前端模版", Field: "valueInterface", Type: views.InputTypeSelect, Options: []*views.SelectOption{
					{Label: "默认模版", Value: "default"}, {Label: "测试模版", Value: "example"},
				}}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupDefault,
				Name:    "代理数量[商户设置]",
				Type:    AdminSettingTypeInput,
				Field:   "merchantAgentNums",
				Value:   "10",
				Data:    AdminSettingData{Input: views.Input{Label: "代理数量", Field: "valueInterface", Type: views.InputTypeNumber}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupDefault,
				Name:    "设备数量[商户设置]",
				Type:    AdminSettingTypeInput,
				Field:   "merchantDeviceNums",
				Value:   "5",
				Data:    AdminSettingData{Input: views.Input{Label: "设备数量", Field: "valueInterface", Type: views.InputTypeNumber}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupDefault,
				Name:    "过期时间[商户设置]",
				Type:    AdminSettingTypeInput,
				Field:   "merchantTokenExpire",
				Value:   utils.StructToString(&views.FormatTimePicker{Type: views.TimeTypeMonth, Value: 1}),
				Data: AdminSettingData{
					Input: views.Input{Label: "过期时间", Field: "valueInterface", Type: views.InputTypeStruct},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {Inputs: [][]*views.Input{
							{
								{Label: "类型", Field: "type", Type: views.InputTypeSelect, Options: views.FormatTimePickerOptions},
								{Label: "时间", Field: "value", Type: views.InputTypeNumber},
							},
						}},
					},
				},
			},
		}
		// 首页配置
		settings = append(settings, []AdminSetting{

			// AdminSettingGroupBasic 基础分组
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "站点Logo",
				Type:    AdminSettingTypeInput,
				Field:   "siteLogo",
				Value:   "/logo.png",
				Data:    AdminSettingData{Input: views.Input{Label: "站点Logo", Field: "valueInterface", Type: views.InputTypeImage}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "站点图标",
				Type:    AdminSettingTypeInput,
				Field:   "siteIcon",
				Value:   "/logo.png",
				Data:    AdminSettingData{Input: views.Input{Label: "站点图标", Field: "valueInterface", Type: views.InputTypeImage}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "站点名称",
				Type:    AdminSettingTypeInput,
				Field:   "siteName",
				Value:   "BaJie",
				Data:    AdminSettingData{Input: views.Input{Label: "站点名称", Field: "valueInterface", Type: views.InputTypeText}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "登录背景图",
				Type:    AdminSettingTypeInput,
				Field:   "loginImage",
				Value:   "/images/hero-node.png",
				Data:    AdminSettingData{Input: views.Input{Label: "登录背景图", Field: "valueInterface", Type: views.InputTypeImage}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "默认头像",
				Type:    AdminSettingTypeInput,
				Field:   "userAvatar",
				Value:   "/userAvatar.png",
				Data:    AdminSettingData{Input: views.Input{Label: "默认头像", Field: "valueInterface", Type: views.InputTypeImage}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "客服问候语",
				Type:    AdminSettingTypeInput,
				Field:   "chatsGreeting",
				Value:   "chatsFirstGreeting",
				Data:    AdminSettingData{Input: views.Input{Label: "客服问候语", Field: "valueInterface", Type: views.InputTypeText}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "站内转账[余额]提示语",
				Type:    AdminSettingTypeInput,
				Field:   "transferBalanceTips",
				Value:   "transferBalanceTips",
				Data:    AdminSettingData{Input: views.Input{Label: "站内转账[余额]提示语", Field: "valueInterface", Type: views.InputTypeTranslate}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "冻结弹窗提示语",
				Type:    AdminSettingTypeInput,
				Field:   "freezeTips",
				Value:   "freezeTips",
				Data:    AdminSettingData{Input: views.Input{Label: "冻结弹窗提示语", Field: "valueInterface", Type: views.InputTypeTranslate}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "信用分过低弹窗提示语",
				Type:    AdminSettingTypeInput,
				Field:   "tooLowScoreTips",
				Value:   "tooLowScoreTips",
				Data:    AdminSettingData{Input: views.Input{Label: "信用分过低弹窗提示语", Field: "valueInterface", Type: views.InputTypeTranslate}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "时区",
				Type:    AdminSettingTypeInput,
				Field:   "siteTimezone",
				Value:   "Asia/Shanghai",
				Data: AdminSettingData{Input: views.Input{Label: "时区", Field: "valueInterface", Type: views.InputTypeSelect, Options: []*views.SelectOption{
					{Label: "上海", Value: "Asia/Shanghai"},
					{Label: "纽约", Value: "America/New_York"},
					{Label: "伦敦", Value: "Europe/London"},
					{Label: "东京", Value: "Asia/Tokyo"},
					{Label: "巴黎", Value: "Europe/Paris"},
					{Label: "悉尼", Value: "Australia/Sydney"},
					{Label: "莫斯科", Value: "Europe/Moscow"},
					{Label: "迪拜", Value: "Asia/Dubai"},
					{Label: "洛杉矶", Value: "America/Los_Angeles"},
				}}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "站点颜色",
				Type:    AdminSettingTypeInput,
				Field:   AdminSettingThemeColor,
				Value:   AdminSettingThemeColorWhite,
				Data: AdminSettingData{Input: views.Input{Label: "站点颜色", Field: "valueInterface", Type: views.InputTypeSelect, Options: []*views.SelectOption{
					{Label: "主题自动", Value: AdminSettingThemeColorAuto},
					{Label: "主题白色", Value: AdminSettingThemeColorWhite},
					{Label: "主题黑色", Value: AdminSettingThemeColorBlack},
				}}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "等级购买配置A",
				Type:    AdminSettingTypeInput,
				Field:   AdminSettingBuyLevelOptions,
				Value:   UserLevelBuyBalance,
				Data: AdminSettingData{Input: views.Input{Label: "等级购买配置", Field: "valueInterface", Type: views.InputTypeSelect, Options: []*views.SelectOption{
					{Label: "余额购买", Value: UserLevelBuyBalance}, {Label: "充值升级", Value: UserLevelBuyDeposit},
				}}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "等级购买配置B",
				Type:    AdminSettingTypeInput,
				Field:   AdminSettingBuyLevelOptions2,
				Value:   UserLevelBuyTypeFullAmount,
				Data: AdminSettingData{Input: views.Input{Label: "等级购买配置", Field: "valueInterface", Type: views.InputTypeSelect, Options: []*views.SelectOption{
					{Label: "全额购买", Value: UserLevelBuyTypeFullAmount}, {Label: "等差购买", Value: UserLevelBuyTypeDifference}, {Label: "分类购买", Value: UserLevelBuyTypeClassification},
				}}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "最低信用分",
				Type:    AdminSettingTypeInput,
				Field:   "siteMinCreditScore",
				Value:   "60",
				Data:    AdminSettingData{Input: views.Input{Label: "最低信用分", Field: "valueInterface", Type: views.InputTypeNumber}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "提示音配置",
				Type:    AdminSettingTypeInput,
				Field:   "siteSound",
				Value: utils.StructToString([]*views.CheckboxOption{
					{Label: "余额充值", Value: "balanceDeposit", Checked: true},
					{Label: "资产充值", Value: "assetsDeposit", Checked: true},
					{Label: "余额提现", Value: "balanceWithdraw", Checked: true},
					{Label: "资产提现", Value: "assetsWithdraw", Checked: true},
					{Label: "用户认证", Value: "userAuth", Checked: true},
				}),
				Data: AdminSettingData{Input: views.Input{Label: "提示音", Field: "valueInterface", Type: views.InputTypeCheckbox, Options: []*views.CheckboxOption{
					{Label: "余额充值", Value: "balanceDeposit", Checked: false}, {Label: "资产充值", Value: "assetsDeposit", Checked: false},
					{Label: "余额提现", Value: "balanceWithdraw", Checked: false}, {Label: "资产提现", Value: "assetsWithdraw", Checked: false},
					{Label: "用户认证", Value: "userAuth", Checked: false},
				}}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "用户状态配置",
				Type:    AdminSettingTypeInput,
				Field:   "siteUserStatus",
				Value: utils.StructToString([]*views.CheckboxOption{
					{Label: "冻结禁止登录", Value: "freezeLogin", Checked: true},
					{Label: "冻结禁止提现", Value: "freezeWithdraw", Checked: true},
					{Label: "冻结禁止提现", Value: "freezeWithdraw", Checked: true},
					{Label: "冻结禁止订单", Value: "freezeOrder", Checked: true},
					{Label: "信用分过低禁止提现", Value: "creditWithdraw", Checked: true},
					{Label: "信用分过低禁止订单", Value: "creditOrder", Checked: true},
					{Label: "冻结禁止转账", Value: "freezeTransfer", Checked: true},
					{Label: "信用分过低禁止转账", Value: "creditTransfer", Checked: true},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "用户状态", Field: "valueInterface", Type: views.InputTypeCheckbox, Options: []*views.CheckboxOption{
						{Label: "冻结禁止登录", Value: "freezeLogin", Checked: false},
						{Label: "冻结禁止提现", Value: "freezeWithdraw", Checked: false}, {Label: "冻结禁止订单", Value: "freezeOrder", Checked: false},
						{Label: "信用分禁止提现", Value: "creditWithdraw", Checked: false}, {Label: "信用分禁止订单", Value: "creditOrder", Checked: false},
						{Label: "冻结禁止转账", Value: "freezeTransfer", Checked: false}, {Label: "信用分禁止转账", Value: "creditTransfer", Checked: false},
					}},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "冻结余额配置",
				Type:    AdminSettingTypeInput,
				Field:   AdminSettingFreezeBalanceField,
				Value: utils.StructToString([]*views.CheckboxOption{
					{Label: "奖励冻结", Value: AdminSettingFreezeBalanceRewardFreeze, Checked: false},
					{Label: "注册冻结", Value: AdminSettingFreezeBalanceSignupFreeze, Checked: false},
					{Label: "邀请冻结", Value: AdminSettingFreezeBalanceInviteFreeze, Checked: false},
					{Label: "订单解冻", Value: AdminSettingUnfreezeBalanceOrder, Checked: false},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "冻结余额配置", Field: "valueInterface", Type: views.InputTypeCheckbox, Options: []*views.CheckboxOption{
						{Label: "奖励冻结", Value: AdminSettingFreezeBalanceRewardFreeze, Checked: false},
						{Label: "注册冻结", Value: AdminSettingFreezeBalanceSignupFreeze, Checked: false},
						{Label: "邀请冻结", Value: AdminSettingFreezeBalanceInviteFreeze, Checked: false},
						{Label: "订单解冻", Value: AdminSettingUnfreezeBalanceOrder, Checked: false},
					}},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "APP文件",
				Type:    AdminSettingTypeInput,
				Field:   "siteAppFile",
				Value:   `{"ios":"/app/ios.apk","android":"/app/android.apk"}`,
				Data: AdminSettingData{
					Input: views.Input{Label: "APP文件", Field: "valueInterface", Type: views.InputTypeStruct},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{{Label: "IOS", Field: "ios", Type: views.InputTypeFile}},
								{{Label: "Android", Field: "android", Type: views.InputTypeFile}},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "底部[社区]",
				Type:    AdminSettingTypeInput,
				Field:   "siteFooterCommunity",
				Value:   `[{"icon":"/social/discord.png","link":"","target":"_self"},{"icon":"/social/telegram.png","link":"","target":"_self"},{"icon":"/social/tiktok.png","link":"","target":"_self"},{"icon":"/social/facebook.png","link":"","target":"_self"},{"icon":"/social/twitter.png","link":"","target":"_self"},{"icon":"/social/reddit.png","link":"","target":"_self"},{"icon":"/social/instagram.png","link":"","target":"_self"},{"icon":"/social/youtube.png","link":"","target":"_self"}]`,
				Data: AdminSettingData{
					Input: views.Input{Label: "底部[社区]", Field: "valueInterface", Type: views.InputTypeSlice},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "图标", Field: "icon", Type: views.InputTypeImage},
									{Label: "链接", Field: "link", Type: views.InputTypeText},
									{Label: "方式", Field: "target", Type: views.InputTypeSelect, Options: []*views.SelectOption{
										{Label: "内部", Value: "_self"}, {Label: "外部", Value: "_blank"},
									}},
								},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "底部[关于我们]",
				Type:    AdminSettingTypeInput,
				Field:   "siteFooterAboutUs",
				Value:   `[{"name":"articleTitle_aboutUs","link":"/article/aboutUs","target":"_self"},{"name":"articleTitle_contactUs","link":"/article/contactUs","target":"_self"},{"name":"articleTitle_privacyPolicy","link":"/article/privacyPolicy","target":"_self"},{"name":"articleTitle_termsOfService","link":"/article/termsOfService","target":"_self"},{"name":"articleTitle_missionAndVision","link":"/article/missionAndVision","target":"_self"},{"name":"articleTitle_teamCulture","link":"/article/teamCulture","target":"_self"},{"name":"articleTitle_joinUs","link":"/article/joinUs","target":"_self"}]`,
				Data: AdminSettingData{
					Input: views.Input{Label: "底部[关于我们]", Field: "valueInterface", Type: views.InputTypeSlice},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "名称", Field: "name", Type: views.InputTypeTranslate},
									{Label: "链接", Field: "link", Type: views.InputTypeText},
									{Label: "方式", Field: "target", Type: views.InputTypeSelect, Options: []*views.SelectOption{
										{Label: "内部", Value: "_self"}, {Label: "外部", Value: "_blank"},
									}},
								},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "底部[服务]",
				Type:    AdminSettingTypeInput,
				Field:   "siteFooterService",
				Value:   `[{"name":"articleTitle_registerTerms","link":"/article/registerTerms","target":"_self"},{"name":"articleTitle_userGuidelines","link":"/article/userGuidelines","target":"_self"},{"name":"articleTitle_accountSecurity","link":"/article/accountSecurity","target":"_self"},{"name":"articleTitle_paymentMethods","link":"/article/paymentMethods","target":"_self"},{"name":"articleTitle_legalLiability","link":"/article/legalLiability","target":"_self"},{"name":"articleTitle_disclaimer","link":"/article/disclaimer","target":"_self"}]`,
				Data: AdminSettingData{
					Input: views.Input{Label: "底部[服务]", Field: "valueInterface", Type: views.InputTypeSlice},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "名称", Field: "name", Type: views.InputTypeTranslate},
									{Label: "链接", Field: "link", Type: views.InputTypeText},
									{Label: "方式", Field: "target", Type: views.InputTypeSelect, Options: []*views.SelectOption{
										{Label: "内部", Value: "_self"}, {Label: "外部", Value: "_blank"},
									}},
								},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "底部[支持]",
				Type:    AdminSettingTypeInput,
				Field:   "siteFooterSupport",
				Value:   `[{"name":"articleTitle_faq","link":"/article/faq","target":"_self"},{"name":"articleTitle_techSupport","link":"/article/techSupport","target":"_self"},{"name":"articleTitle_tutorial","link":"/article/tutorial","target":"_self"},{"name":"articleTitle_feedback","link":"/article/feedback","target":"_self"},{"name":"articleTitle_download","link":"/article/download","target":"_self"},{"name":"articleTitle_afterSale","link":"/article/afterSale","target":"_self"}]`,
				Data: AdminSettingData{
					Input: views.Input{Label: "底部[支持]", Field: "valueInterface", Type: views.InputTypeSlice},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "名称", Field: "name", Type: views.InputTypeTranslate},
									{Label: "链接", Field: "link", Type: views.InputTypeText},
									{Label: "方式", Field: "target", Type: views.InputTypeSelect, Options: []*views.SelectOption{
										{Label: "内部", Value: "_self"}, {Label: "外部", Value: "_blank"},
									}},
								},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "底部[产品]",
				Type:    AdminSettingTypeInput,
				Field:   "siteFooterProduct",
				Value:   `[{"name":"articleTitle_latestProducts","link":"/article/latestProducts","target":"_self"},{"name":"articleTitle_evaluation","link":"/article/evaluation","target":"_self"},{"name":"articleTitle_productCatalog","link":"/article/productCatalog","target":"_self"},{"name":"articleTitle_crossBorderPayment","link":"/article/crossBorderPayment","target":"_self"}]`,
				Data: AdminSettingData{
					Input: views.Input{Label: "底部[产品]", Field: "valueInterface", Type: views.InputTypeSlice},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "名称", Field: "name", Type: views.InputTypeTranslate},
									{Label: "链接", Field: "link", Type: views.InputTypeText},
									{Label: "方式", Field: "target", Type: views.InputTypeSelect, Options: []*views.SelectOption{
										{Label: "内部", Value: "_self"}, {Label: "外部", Value: "_blank"},
									}},
								},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "帮助中心",
				Type:    AdminSettingTypeInput,
				Field:   "siteHelpCenter",
				Value:   `[{"name":"articleTitle_helperAccount", "content": "articleContent_helperAccount", "link":"/article/helperAccount","target":"_self"},{"name":"articleTitle_helperPayment", "content": "articleContent_helperPayment", "link":"/article/helperPayment","target":"_self"},{"name":"articleTitle_helperSecurity", "content": "articleContent_helperSecurity", "link":"/article/helperSecurity","target":"_self"},{"name":"articleTitle_helperService", "content": "articleContent_helperService", "link":"/article/helperService","target":"_self"},{"name":"articleTitle_helperFaq", "content": "articleContent_helperFaq", "link":"/article/helperFaq","target":"_self"}]`,
				Data: AdminSettingData{
					Input: views.Input{Label: "帮助中心", Field: "valueInterface", Type: views.InputTypeSlice},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "名称", Field: "name", Type: views.InputTypeTranslate},
									{Label: "内容", Field: "content", Type: views.InputTypeTranslate},
									{Label: "链接", Field: "link", Type: views.InputTypeText},
									{Label: "方式", Field: "target", Type: views.InputTypeSelect, Options: []*views.SelectOption{
										{Label: "内部", Value: "_self"}, {Label: "外部", Value: "_blank"},
									}},
								},
							},
						},
					},
				},
			},

			// AdminSettingGroupHome 首页分组
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupHome,
				Name:    "站点公告",
				Type:    AdminSettingTypeInput,
				Field:   "siteNotice",
				Value:   "settingNotice",
				Data:    AdminSettingData{Input: views.Input{Label: "站点公告", Field: "valueInterface", Type: views.InputTypeTranslate}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupHome,
				Name:    "站点信息",
				Type:    AdminSettingTypeInput,
				Field:   "siteIntroduce",
				Value:   "settingIntroduce",
				Data:    AdminSettingData{Input: views.Input{Label: "站点信息", Field: "valueInterface", Type: views.InputTypeTranslate}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "站点版权",
				Type:    AdminSettingTypeInput,
				Field:   "siteCopyright",
				Value:   "Copyright © 2024 BaJie. All rights reserved.",
				Data:    AdminSettingData{Input: views.Input{Label: "站点版权", Field: "valueInterface", Type: views.InputTypeTextarea}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupBasic,
				Name:    "统计代码",
				Type:    AdminSettingTypeInput,
				Field:   "statisCode",
				Value:   "",
				Data:    AdminSettingData{Input: views.Input{Label: "统计代码", Field: "valueInterface", Type: views.InputTypeTextarea}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupHome,
				Name:    "首页轮播图",
				Type:    AdminSettingTypeInput,
				Field:   "siteBanner",
				Value:   `["/images/home_back_mobile.png","/images/home_back_mobile1.png","/images/home_back_mobile2.png"]`,
				Data:    AdminSettingData{Input: views.Input{Label: "首页轮播图", Field: "valueInterface", Type: views.InputTypeMultipleImage}},
			},
		}...)

		// 钱包配置
		settings = append(settings, []AdminSetting{
			// AdminSettingGroupWallet 钱包分组
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupWallet,
				Name:    "钱包账单",
				Type:    AdminSettingTypeInput,
				Field:   "walletBillOptions",
				Value:   utils.StructToString(GetWalletBillCheckboxOptions()),
				Data: AdminSettingData{
					Input: views.Input{Label: "钱包账单", Field: "valueInterface", Type: views.InputTypeCheckbox, Options: GetWalletBillCheckboxOptions()},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupWallet,
				Name:    "闪兑手续费",
				Type:    AdminSettingTypeInput,
				Field:   "walletExchangeFee",
				Value:   "0.01",
				Data:    AdminSettingData{Input: views.Input{Label: "闪兑手续费", Field: "valueInterface", Type: views.InputTypeNumber}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupWallet,
				Name:    "推荐奖励配置",
				Type:    AdminSettingTypeInput,
				Field:   "walletRecommendReward",
				Value:   utils.StructToString(&AdminSettingInviteRegisterReward{Register: 10, Invite: 10}),
				Data: AdminSettingData{
					Input: views.Input{Label: "推荐奖励", Field: "valueInterface", Type: views.InputTypeStruct},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{{Label: "注册奖励", Field: "register", Type: views.InputTypeNumber}},
								{{Label: "邀请奖励", Field: "invite", Type: views.InputTypeNumber}},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupWallet,
				Name:    "分销奖励配置",
				Type:    AdminSettingTypeInput,
				Field:   "walletDistributionReward",
				Value: utils.StructToString([]*AdminSettingDistributionReward{
					{Level: 1, Type: BillTypeProductPurchase, Rate: 30},
					{Level: 1, Type: BillTypeProductEarnings, Rate: 30},
					{Level: 2, Type: BillTypeProductPurchase, Rate: 20},
					{Level: 2, Type: BillTypeProductEarnings, Rate: 20},
					{Level: 3, Type: BillTypeProductPurchase, Rate: 10},
					{Level: 3, Type: BillTypeProductEarnings, Rate: 10},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "分销奖励", Field: "valueInterface", Type: views.InputTypeSlice},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "分销级数", Field: "level", Type: views.InputTypeNumber},
									{Label: "账单类型", Field: "type", Type: views.InputTypeSelect, Options: []*views.SelectOption{
										{Label: "购买产品", Value: BillTypeProductPurchase}, {Label: "产品收益", Value: BillTypeProductEarnings},
									}},
									{Label: "收益比例(%)", Field: "rate", Type: views.InputTypeNumber},
								},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupWallet,
				Name:    "提现账户数量",
				Type:    AdminSettingTypeInput,
				Field:   "walletAccountNums",
				Value:   "3",
				Data:    AdminSettingData{Input: views.Input{Label: "钱包余额", Field: "valueInterface", Type: views.InputTypeNumber}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupWallet,
				Name:    "钱包汇率",
				Type:    AdminSettingTypeInput,
				Field:   "walletRate",
				Value:   "1",
				Data:    AdminSettingData{Input: views.Input{Label: "钱包汇率", Field: "valueInterface", Type: views.InputTypeNumber}},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupWallet,
				Name:    "提现审核最多订单数",
				Type:    AdminSettingTypeInput,
				Field:   "walletWithdrawPendingNums",
				Value:   "3",
				Data: AdminSettingData{
					Input: views.Input{Label: "提现审核最多订单数", Field: "valueInterface", Type: views.InputTypeNumber},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupWallet,
				Name:    "提现次数",
				Type:    AdminSettingTypeInput,
				Field:   "walletWithdrawNums",
				Value:   `{"day": 1, "nums": 1}`,
				Data: AdminSettingData{
					Input: views.Input{Label: "提现次数", Field: "valueInterface", Type: views.InputTypeStruct},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{{Label: "每天", Field: "day", Type: views.InputTypeNumber}},
								{{Label: "次数", Field: "nums", Type: views.InputTypeNumber}},
							},
						},
					},
				},
			},
		}...)
		// 模版配置
		settings = append(settings, []AdminSetting{
			// AdminSettingGroupTemplate 模版分组
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupTemplate,
				Name:    "基础模版配置",
				Type:    AdminSettingTypeInput,
				Field:   AdminSettingBasic,
				Value: utils.StructToString([]*views.CheckboxOption{
					{Label: "首页是否登录", Value: AdminSettingBasicShowHome, Checked: false},
					{Label: "显示首页客服", Value: AdminSettingBasicShowChats, Checked: true},
					{Label: "显示资产", Value: AdminSettingBasicShowAssets, Checked: true},
					{Label: "显示冻结余额", Value: AdminSettingBasicShowFreezeBalance, Checked: false},
					{Label: "显示站内转账", Value: AdminSettingBasicShowTransfer, Checked: true},
					{Label: "显示等级", Value: AdminSettingBasicShowLevel, Checked: true},
					{Label: "显示购买等级", Value: AdminSettingBasicShowBuyLevel, Checked: true},
					{Label: "显示积分", Value: AdminSettingBasicShowScore, Checked: true},
					{Label: "显示认证", Value: AdminSettingBasicShowAuth, Checked: true},
					{Label: "显示认证查看", Value: AdminSettingBasicShowAuthView, Checked: true},
					{Label: "显示提现账户编辑", Value: AdminSettingBasicShowWithdrawAccountEdit, Checked: true},
					{Label: "显示提现账户删除", Value: AdminSettingBasicShowWithdrawAccountDelete, Checked: true},
					{Label: "显示提现账户号码", Value: AdminSettingBasicShowWithdrawAccountNumber, Checked: true},
					{Label: "显示修改密码", Value: AdminSettingBasicShowModifyPassword, Checked: true},
					{Label: "显示修改提现密码", Value: AdminSettingBasicShowModifyWithdrawPassword, Checked: true},
					{Label: "显示绑定邮箱", Value: AdminSettingBasicShowBindEmail, Checked: true},
					{Label: "显示绑定手机", Value: AdminSettingBasicShowBindTelephone, Checked: true},
					{Label: "显示提现账户[更新｜删除]提现密码", Value: AdminSettingBasicShowWithdrawAccountPassword, Checked: false},
					{Label: "显示充值[提交]提现密码", Value: AdminSettingBasicShowDepositPassword, Checked: false},
					{Label: "显示提现[提交]提现密码", Value: AdminSettingBasicShowWithdrawPassword, Checked: true},
					{Label: "显示转账[提交]提现密码", Value: AdminSettingBasicShowTransferPassword, Checked: true},
					{Label: "显示兑换[提交]提现密码", Value: AdminSettingBasicShowSwapsPassword, Checked: true},
					{Label: "显示购买等级[提交]提现密码", Value: AdminSettingBasicShowBuyLevelPassword, Checked: true},
					{Label: "显示购买订单[提交]提现密码", Value: AdminSettingBasicShowBuyOrderPassword, Checked: false},
					{Label: "显示金额单位在右侧", Value: AdminSettingBasicShowAmountSymbolRight, Checked: false},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "基础模版配置", Field: "valueInterface", Type: views.InputTypeCheckbox, Options: []*views.CheckboxOption{
						{Label: "首页是否登录", Value: AdminSettingBasicShowHome, Checked: false},
						{Label: "显示首页客服", Value: AdminSettingBasicShowChats, Checked: false},
						{Label: "显示资产", Value: AdminSettingBasicShowAssets, Checked: false},
						{Label: "显示冻结余额", Value: AdminSettingBasicShowFreezeBalance, Checked: false},
						{Label: "显示站内转账", Value: AdminSettingBasicShowTransfer, Checked: true},
						{Label: "显示等级", Value: AdminSettingBasicShowLevel, Checked: false},
						{Label: "显示等级购买", Value: AdminSettingBasicShowBuyLevel, Checked: false},
						{Label: "显示积分", Value: AdminSettingBasicShowScore, Checked: false},
						{Label: "显示认证", Value: AdminSettingBasicShowAuth, Checked: false},
						{Label: "显示认证查看", Value: AdminSettingBasicShowAuthView, Checked: false},
						{Label: "显示提现账户编辑", Value: AdminSettingBasicShowWithdrawAccountEdit, Checked: false},
						{Label: "显示提现账户删除", Value: AdminSettingBasicShowWithdrawAccountDelete, Checked: false},
						{Label: "显示提现账户号码", Value: AdminSettingBasicShowWithdrawAccountNumber, Checked: false},
						{Label: "显示修改密码", Value: AdminSettingBasicShowModifyPassword, Checked: false},
						{Label: "显示修改提现密码", Value: AdminSettingBasicShowModifyWithdrawPassword, Checked: false},
						{Label: "显示绑定邮箱", Value: AdminSettingBasicShowBindEmail, Checked: false},
						{Label: "显示绑定手机", Value: AdminSettingBasicShowBindTelephone, Checked: false},
						{Label: "显示提现账户[更新｜删除]提现密码", Value: AdminSettingBasicShowWithdrawAccountPassword, Checked: false},
						{Label: "显示充值[提交]提现密码", Value: AdminSettingBasicShowDepositPassword, Checked: false},
						{Label: "显示提现[提交]提现密码", Value: AdminSettingBasicShowWithdrawPassword, Checked: false},
						{Label: "显示转账[提交]提现密码", Value: AdminSettingBasicShowTransferPassword, Checked: false},
						{Label: "显示兑换[提交]提现密码", Value: AdminSettingBasicShowSwapsPassword, Checked: false},
						{Label: "显示购买等级[提交]提现密码", Value: AdminSettingBasicShowBuyLevelPassword, Checked: false},
						{Label: "显示购买订单[提交]提现密码", Value: AdminSettingBasicShowBuyOrderPassword, Checked: false},
						{Label: "显示金额单位在右侧", Value: AdminSettingBasicShowAmountSymbolRight, Checked: false},
					}},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupTemplate,
				Name:    "主题配置",
				Type:    AdminSettingTypeInput,
				Field:   "topicSettings",
				Value: utils.StructToString([]*views.CheckboxOption{
					{Label: "是否为暗色", Value: "isDark", Checked: false},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "基础模版配置", Field: "valueInterface", Type: views.InputTypeCheckbox, Options: []*views.CheckboxOption{
						{Label: "是否为暗色", Value: "isDark", Checked: false},
					}},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupTemplate,
				Name:    "用户名登录模版",
				Type:    AdminSettingTypeInput,
				Field:   "usernameLoginTemplate",
				Value: utils.StructToString([][]*vues.Input{
					{{Label: "username", Field: "username", Type: vues.InputTypeText, Style: "width: 100%;", Display: true}},
					{{Label: "password", Field: "password", Type: vues.InputTypePassword, Style: "width: 100%;", Display: true}},
					{{Label: "captcha", Field: "captcha", Type: vues.InputTypeCaptcha, Style: "width: 100%;", Display: true}},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "用户名登录模版", Field: "valueInterface", Type: views.InputTypeSlice, Readonly: true},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "标题", Field: "label", Type: views.InputTypeTranslate},
									{Label: "只读", Field: "readonly", Type: views.InputTypeToggle},
									{Label: "隐藏", Field: "display", Type: views.InputTypeToggle},
									{Label: "验证", Field: "rules", Type: views.InputTypeText},
									{Label: "格式", Field: "mask", Type: views.InputTypeText},
								},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupTemplate,
				Name:    "邮箱登录模版",
				Type:    AdminSettingTypeInput,
				Field:   "emailLoginTemplate",
				Value: utils.StructToString([][]*vues.Input{
					{{Label: "email", Field: "email", Type: vues.InputTypeText, Style: "width: 100%;"}},
					{{Label: "password", Field: "password", Type: vues.InputTypePassword, Style: "width: 100%;"}},
					{{Label: "captcha", Field: "captcha", Type: vues.InputTypeCaptcha, Style: "width: 100%;"}},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "邮箱登录模版", Field: "valueInterface", Type: views.InputTypeSlice, Readonly: true},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "标题", Field: "label", Type: views.InputTypeTranslate},
									{Label: "只读", Field: "readonly", Type: views.InputTypeToggle},
									{Label: "隐藏", Field: "display", Type: views.InputTypeToggle},
									{Label: "验证", Field: "rules", Type: views.InputTypeText},
									{Label: "格式", Field: "mask", Type: views.InputTypeText},
								},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupTemplate,
				Name:    "手机登录模版",
				Type:    AdminSettingTypeInput,
				Field:   "phoneLoginTemplate",
				Value: utils.StructToString([][]*vues.Input{
					{{Label: "telephone", Field: "telephone", Type: vues.InputTypeTelephone, Style: "width: 100%;"}},
					{{Label: "password", Field: "password", Type: vues.InputTypePassword, Style: "width: 100%;"}},
					{{Label: "captcha", Field: "captcha", Type: vues.InputTypeCaptcha, Style: "width: 100%;"}},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "手机登录模版", Field: "valueInterface", Type: views.InputTypeSlice, Readonly: true},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "标题", Field: "label", Type: views.InputTypeTranslate},
									{Label: "只读", Field: "readonly", Type: views.InputTypeToggle},
									{Label: "隐藏", Field: "display", Type: views.InputTypeToggle},
									{Label: "验证", Field: "rules", Type: views.InputTypeText},
									{Label: "格式", Field: "mask", Type: views.InputTypeText},
								},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupTemplate,
				Name:    "用户名注册模版",
				Type:    AdminSettingTypeInput,
				Field:   "usernameRegisterTemplate",
				Value: utils.StructToString([][]*vues.Input{
					{{Label: "username", Field: "username", Type: vues.InputTypeText, Style: "width: 100%;", Display: true}},
					{{Label: "nickname", Field: "nickname", Type: vues.InputTypeText, Display: true, Style: "width: 100%;"}},
					{{Label: "sex", Field: "sex", Type: vues.InputTypeSelect, Options: []*views.SelectOption{{Label: "sexMale", Value: SexMale}, {Label: "sexFemale", Value: SexFemale}}, Display: true, Style: "width: 100%;"}},
					{{Label: "password", Field: "password", Type: vues.InputTypePassword, Style: "width: 100%;", Display: true}},
					{{Label: "confirmPassword", Field: "confirmPassword", Type: vues.InputTypePassword, Display: true, Rules: "(val) => {return val == formParams.value.password || $i18n.t('passwordNotMatch')}", Style: "width: 100%;"}},
					{{Label: "securityKey", Field: "securityKey", Type: vues.InputTypePassword, Display: true, Style: "width: 100%;"}},
					{{Label: "confirmSecurityKey", Field: "confirmSecurityKey", Type: vues.InputTypePassword, Display: true, Rules: "(val) => {return val == formParams.value.securityKey || $i18n.t('securityKeyNotMatch')}", Style: "width: 100%;"}},
					{{Label: "inviteCode", Field: "inviteCode", Type: vues.InputTypeText, Display: true, Style: "width: 100%;"}},
					{{Label: "captcha", Field: "captcha", Type: vues.InputTypeCaptcha, Style: "width: 100%;", Display: true}},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "用户名注册模版", Field: "valueInterface", Type: views.InputTypeSlice, Readonly: true},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "标题", Field: "label", Type: views.InputTypeTranslate},
									{Label: "只读", Field: "readonly", Type: views.InputTypeToggle},
									{Label: "隐藏", Field: "display", Type: views.InputTypeToggle},
									{Label: "验证", Field: "rules", Type: views.InputTypeText},
									{Label: "格式", Field: "mask", Type: views.InputTypeText},
								},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupTemplate,
				Name:    "邮箱注册模版",
				Type:    AdminSettingTypeInput,
				Field:   "emailRegisterTemplate",
				Value: utils.StructToString([][]*vues.Input{
					{{Label: "email", Field: "email", Type: vues.InputTypeText, Style: "width: 100%;"}},
					{{Label: "nickname", Field: "nickname", Type: vues.InputTypeText, Display: true, Style: "width: 100%;"}},
					{{Label: "sex", Field: "sex", Type: vues.InputTypeSelect, Options: []*views.SelectOption{{Label: "sexMale", Value: SexMale}, {Label: "sexFemale", Value: SexFemale}}, Display: true, Style: "width: 100%;"}},
					{{Label: "password", Field: "password", Type: vues.InputTypePassword, Style: "width: 100%;"}},
					{{Label: "confirmPassword", Field: "confirmPassword", Type: vues.InputTypePassword, Display: true, Rules: "(val) => {return val == formParams.value.password || $i18n.t('passwordNotMatch')}", Style: "width: 100%;"}},
					{{Label: "securityKey", Field: "securityKey", Type: vues.InputTypePassword, Display: true, Style: "width: 100%;"}},
					{{Label: "confirmSecurityKey", Field: "confirmSecurityKey", Type: vues.InputTypePassword, Display: true, Rules: "(val) => {return val == formParams.value.securityKey || $i18n.t('securityKeyNotMatch')}", Style: "width: 100%;"}},
					{{Label: "inviteCode", Field: "inviteCode", Type: vues.InputTypeText, Display: true, Style: "width: 100%;"}},
					{{Label: "captcha", Field: "captcha", Type: vues.InputTypeCaptcha, Style: "width: 100%;"}},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "邮箱注册模版", Field: "valueInterface", Type: views.InputTypeSlice, Readonly: true},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "标题", Field: "label", Type: views.InputTypeTranslate},
									{Label: "只读", Field: "readonly", Type: views.InputTypeToggle},
									{Label: "隐藏", Field: "display", Type: views.InputTypeToggle},
									{Label: "验证", Field: "rules", Type: views.InputTypeText},
									{Label: "格式", Field: "mask", Type: views.InputTypeText},
								},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupTemplate,
				Name:    "手机注册模版",
				Type:    AdminSettingTypeInput,
				Field:   "phoneRegisterTemplate",
				Value: utils.StructToString([][]*vues.Input{
					{{Label: "telephone", Field: "telephone", Type: vues.InputTypeTelephone, Style: "width: 100%;"}},
					{{Label: "nickname", Field: "nickname", Type: vues.InputTypeText, Display: true, Style: "width: 100%;"}},
					{{Label: "sex", Field: "sex", Type: vues.InputTypeSelect, Options: []*views.SelectOption{{Label: "sexMale", Value: SexMale}, {Label: "sexFemale", Value: SexFemale}}, Display: true, Style: "width: 100%;"}},
					{{Label: "password", Field: "password", Type: vues.InputTypePassword, Style: "width: 100%;"}},
					{{Label: "confirmPassword", Field: "confirmPassword", Type: vues.InputTypePassword, Display: true, Rules: "(val) => {return val == formParams.value.password || $i18n.t('passwordNotMatch')}", Style: "width: 100%;"}},
					{{Label: "securityKey", Field: "securityKey", Type: vues.InputTypePassword, Display: true, Style: "width: 100%;"}},
					{{Label: "confirmSecurityKey", Field: "confirmSecurityKey", Type: vues.InputTypePassword, Display: true, Rules: "(val) => {return val == formParams.value.securityKey || $i18n.t('securityKeyNotMatch')}", Style: "width: 100%;"}},
					{{Label: "inviteCode", Field: "inviteCode", Type: vues.InputTypeText, Display: true, Style: "width: 100%;"}},
					{{Label: "captcha", Field: "captcha", Type: vues.InputTypeCaptcha, Style: "width: 100%;"}},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "手机注册模版", Field: "valueInterface", Type: views.InputTypeSlice, Readonly: true},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "标题", Field: "label", Type: views.InputTypeTranslate},
									{Label: "只读", Field: "readonly", Type: views.InputTypeToggle},
									{Label: "隐藏", Field: "display", Type: views.InputTypeToggle},
									{Label: "验证", Field: "rules", Type: views.InputTypeText},
									{Label: "格式", Field: "mask", Type: views.InputTypeText},
								},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupTemplate,
				Name:    "账户设置模版",
				Type:    AdminSettingTypeInput,
				Field:   "userSettingTemplate",
				Value: utils.StructToString([][]*vues.Input{
					{{Icon: "/inputs/avatar.png", Label: "avatar", Small: "avatarSmall", Field: "avatar", Type: vues.InputTypeAvatar, Style: "width: 100%;"}},
					{{Icon: "/inputs/nickname.png", Label: "nickname", Small: "nicknameSmall", Field: "nickname", Type: vues.InputTypeText, Style: "width: 100%;"}},
					{{Icon: "/inputs/sex.png", Label: "sex", Small: "sexSmall", Field: "sex", Type: vues.InputTypeSelect, Options: []*views.SelectOption{
						{Label: "sexMale", Value: SexMale},
						{Label: "sexFemale", Value: SexFemale},
						{Label: "sexUnknown", Value: SexUnknown},
					}, Style: "width: 100%;"}},
					{{Icon: "/inputs/birthday.png", Label: "birthday", Small: "birthdaySmall", Field: "birthday", Type: vues.InputTypeDatePicker, Style: "width: 100%;"}},
					{{Icon: "/inputs/introduction.png", Label: "introduction", Small: "introductionSmall", Field: "desc", Type: vues.InputTypeTextarea, Style: "width: 100%;"}},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "账户设置模版", Field: "valueInterface", Type: views.InputTypeSlice, Readonly: true},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "图标", Field: "icon", Type: views.InputTypeIcon},
									{Label: "标题", Field: "label", Type: views.InputTypeTranslate},
									{Label: "只读", Field: "readonly", Type: views.InputTypeToggle},
									{Label: "隐藏", Field: "display", Type: views.InputTypeToggle},
									{Label: "验证", Field: "rules", Type: views.InputTypeText},
									{Label: "格式", Field: "mask", Type: views.InputTypeText},
								},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupTemplate,
				Name:    "身份认证模版",
				Type:    AdminSettingTypeInput,
				Field:   "userAuthTemplate",
				Value: utils.StructToString([][]*vues.Input{
					{{Label: "idName", Field: "realName", Type: vues.InputTypeText, Style: "width: 100%;"}},
					{{Label: "idNumber", Field: "idNumber", Type: vues.InputTypeText, Style: "width: 100%;"}},
					{{Label: "idAddress", Field: "address", Type: vues.InputTypeTextarea, Style: "width: 100%;"}},
					{
						{Label: "idPhoto1", Field: "photo1", Type: vues.InputTypeImage},
						{Label: "idPhoto2", Field: "photo2", Type: vues.InputTypeImage},
						{Label: "idPhoto3", Field: "photo3", Type: vues.InputTypeImage},
					},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "身份认证模版", Field: "valueInterface", Type: views.InputTypeSlice, Readonly: true},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "标题", Field: "label", Type: views.InputTypeTranslate},
									{Label: "只读", Field: "readonly", Type: views.InputTypeToggle},
									{Label: "隐藏", Field: "display", Type: views.InputTypeToggle},
									{Label: "验证", Field: "rules", Type: views.InputTypeText},
									{Label: "格式", Field: "mask", Type: views.InputTypeText},
								},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupTemplate,
				Name:    "提现账户银行卡模版",
				Type:    AdminSettingTypeInput,
				Field:   "withdrawAccountBankTemplate",
				Value: utils.StructToString([][]*vues.Input{
					{{Label: "accountBankName", Field: "bankName", Type: vues.InputTypeText, Style: "width: 100%;"}},
					{{Label: "accountBankCardNo", Field: "bankCardNo", Type: vues.InputTypeText, Style: "width: 100%;"}},
					{{Label: "accountRealName", Field: "realName", Type: vues.InputTypeText, Style: "width: 100%;"}},
					{{Label: "accountBankAddress", Field: "bankAddress", Type: vues.InputTypeText, Style: "width: 100%;"}},
					{{Label: "accountBankCode", Field: "bankCode", Type: vues.InputTypeText, Style: "width: 100%;"}},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "提现账户银行卡模版", Field: "valueInterface", Type: views.InputTypeSlice, Readonly: true},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "标题", Field: "label", Type: views.InputTypeTranslate},
									{Label: "只读", Field: "readonly", Type: views.InputTypeToggle},
									{Label: "隐藏", Field: "display", Type: views.InputTypeToggle},
									{Label: "验证", Field: "rules", Type: views.InputTypeText},
									{Label: "格式", Field: "mask", Type: views.InputTypeText},
								},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupTemplate,
				Name:    "提现账户数字货币模版",
				Type:    AdminSettingTypeInput,
				Field:   "withdrawAccountAssetTemplate",
				Value: utils.StructToString([][]*vues.Input{
					{{Label: "accountTokenName", Field: "bankName", Readonly: true, Type: vues.InputTypeText, Style: "width: 100%;"}},
					{{Label: "accountTokenAddress", Field: "bankCardNo", Type: vues.InputTypeText, Style: "width: 100%;"}},
					{{Label: "accountToken", Field: "realName", Readonly: true, Type: vues.InputTypeText, Style: "width: 100%;"}},
					{{Label: "accountTokenDesc", Field: "bankAddress", Readonly: true, Type: vues.InputTypeText, Style: "width: 100%;"}},
					{{Label: "accountTokenSymbol", Field: "bankCode", Readonly: true, Type: vues.InputTypeText, Style: "width: 100%;"}},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "提现账户数字货币模版", Field: "valueInterface", Type: views.InputTypeSlice, Readonly: true},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "标题", Field: "label", Type: views.InputTypeTranslate},
									{Label: "只读", Field: "readonly", Type: views.InputTypeToggle},
									{Label: "隐藏", Field: "display", Type: views.InputTypeToggle},
									{Label: "验证", Field: "rules", Type: views.InputTypeText},
									{Label: "格式", Field: "mask", Type: views.InputTypeText},
								},
							},
						},
					},
				},
			},
		}...)
		// 追加产品配置
		settings = append(settings, []AdminSetting{
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupProducts,
				Name:    "首选产品",
				Type:    AdminSettingTypeInput,
				Field:   "preferredProducts",
				Value: utils.StructToString(&PreferredProducts{
					Forex:   "usdmxn:cur",
					Futures: "xauusd:cur",
					Spot:    "BTC-USDT",
					Staking: "BTC",
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "首选货币", Field: "valueInterface", Type: views.InputTypeStruct},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{{Label: "首选合约产品", Field: "forex", Type: views.InputTypeText}},
								{{Label: "首选期货产品", Field: "futures", Type: views.InputTypeText}},
								{{Label: "首选币币产品", Field: "spot", Type: views.InputTypeText}},
								{{Label: "首选质押产品", Field: "staking", Type: views.InputTypeText}},
							},
						},
					},
				},
			},
			{
				AdminID: SuperAdminID,
				GroupID: AdminSettingGroupProducts,
				Name:    "期权收益率",
				Type:    AdminSettingTypeInput,
				Field:   "futuresRate",
				Value: utils.StructToString([]FuturesRate{
					{Label: "30s", Second: 30, Value: 12, MinAmount: 1},
					{Label: "60s", Second: 60, Value: 37, MinAmount: 1},
					{Label: "90s", Second: 90, Value: 48, MinAmount: 1},
					{Label: "120s", Second: 120, Value: 53, MinAmount: 1},
					{Label: "240s", Second: 240, Value: 8, MinAmount: 1},
				}),
				Data: AdminSettingData{
					Input: views.Input{Label: "期权收益率", Field: "valueInterface", Type: views.InputTypeSlice},
					ChildrenForm: map[string]*views.Form{
						"valueInterface": {
							Inputs: [][]*views.Input{
								{
									{Label: "标题", Field: "label", Type: views.InputTypeText},
									{Label: "秒数", Field: "second", Type: views.InputTypeNumber},
									{Label: "收益率", Field: "value", Type: views.InputTypeNumber},
									{Label: "最小金额", Field: "minAmount", Type: views.InputTypeNumber}},
							},
						},
					},
				},
			},
		}...)

		return db.CreateInBatches(settings, len(settings)).Error
	}

	return nil
}
