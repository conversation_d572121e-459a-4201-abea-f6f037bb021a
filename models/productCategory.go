package models

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"github.com/goccy/go-json"
	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
	"zfeng/core/model"
)

const (
	// CategoryTranslatePrefix 分类翻译前缀
	CategoryTranslatePrefix = "categoryName_%s"

	// Category Types
	CategoryTypeDefault int8 = 1  //	数字货币
	CategoryTypeSpot    int8 = 11 //	加密货币
	CategoryTypeForex   int8 = 21 //	外汇
	CategoryTypeFutures int8 = 31 // 	期货
	CategoryTypeStaking int8 = 41 //	质押

	// Category Status
	CategoryStatusDisabled int8 = -1 // 禁用
	CategoryStatusEnabled  int8 = 10 // 启用
)

// Category 商品分类表
type Category struct {
	model.BaseModel
	ParentID uint         `gorm:"type:int unsigned not null;index;comment:父级ID" json:"parentId"`
	AdminID  uint         `gorm:"type:int unsigned not null;uniqueIndex:idx_admin_symbol;comment:管理ID" json:"adminId"`
	Symbol   string       `gorm:"type:varchar(60) not null;uniqueIndex:idx_admin_symbol;comment:标识" json:"symbol"`
	Type     int8         `gorm:"type:tinyint not null;default:1;index;comment:类型(1:商品类型)" json:"type"`
	Name     string       `gorm:"type:varchar(60) not null;index;comment:名称" json:"name"`
	Icon     string       `gorm:"type:varchar(255);comment:图标" json:"icon"`
	Sort     int16        `gorm:"type:smallint not null;default:99;index;comment:排序" json:"sort"`
	Status   int8         `gorm:"type:tinyint not null;default:10;index;comment:状态(-1:禁用,10:启用)" json:"status"`
	Desc     string       `gorm:"type:text;comment:描述" json:"desc"`
	Data     CategoryData `gorm:"type:json;comment:数据" json:"data"`
}

// CategoryDisplayData 分类显示数据
type CategoryDisplayData struct {
	ID       uint                   `json:"id"`
	AdminID  uint                   `json:"adminId"`
	Symbol   string                 `json:"symbol"`
	Type     int8                   `json:"type"`
	Name     string                 `json:"name"`
	Icon     string                 `json:"icon"`
	Data     CategoryData           `json:"data"`
	Desc     string                 `json:"desc"`
	Children []*CategoryDisplayData `json:"children" gorm:"-"`
}

var CategoryStrategy = map[string]bool{"Monday": true, "Tuesday": true, "Wednesday": true, "Thursday": true, "Friday": true, "Saturday": true, "Sunday": true}

// CategoryData 分类附加数据
type CategoryData struct {
	Strategy map[string]bool
}

// Value implements the driver.Valuer interface
func (d CategoryData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *CategoryData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

func init() {
	// 只允许主进程运行
	if fiber.IsChild() {
		return
	}

	db := model.NewModel()

	// Create the table if it doesn't exist
	if err := db.AutoMigrate(&Category{}); err != nil {
		panic("Failed to auto migrate Category table: " + err.Error())
	}

	// Initialize system menus
	if err := InitProductCategorys(db.DB); err != nil {
		panic("Failed to initialize product categorys: " + err.Error())
	}
}

// InitProductCategorys 初始化分类
func InitProductCategorys(db *gorm.DB) error {
	// Check if the table is empty
	var count int64
	db.Model(&Category{}).Count(&count)
	// If the table is empty, add initial data

	if count == 0 {
		categors := initCategorys()
		for _, v := range categors {
			v.Name = fmt.Sprintf(CategoryTranslatePrefix, v.Symbol)
		}
		return db.CreateInBatches(categors, len(categors)).Error
	}
	return nil
}

func initCategorys() []*Category {
	return []*Category{
		{BaseModel: model.BaseModel{ID: 1}, ParentID: 0, AdminID: SuperAdminID, Name: "加密货币", Symbol: "cryptocurrency", Icon: "/icons/cryptocurrency.png", Type: CategoryTypeSpot, Data: CategoryData{Strategy: CategoryStrategy}},
		{BaseModel: model.BaseModel{ID: 100}, ParentID: 1, AdminID: SuperAdminID, Name: "现货", Symbol: "spotGoods", Icon: "/icons/spotGoods.png", Type: CategoryTypeSpot, Data: CategoryData{Strategy: CategoryStrategy}},
		{BaseModel: model.BaseModel{ID: 1000}, ParentID: 100, AdminID: SuperAdminID, Name: "USDT", Symbol: "spotGoodsUSDT", Icon: "/icons/spotGoodsUSDT.png", Status: ChannelStatusDisabled, Type: CategoryTypeSpot, Data: CategoryData{Strategy: CategoryStrategy}},
		{BaseModel: model.BaseModel{ID: 1100}, ParentID: 100, AdminID: SuperAdminID, Name: "USD", Symbol: "spotGoodsUSD", Icon: "/icons/spotGoodsUSD.png", Type: CategoryTypeSpot, Data: CategoryData{Strategy: CategoryStrategy}},

		{BaseModel: model.BaseModel{ID: 2}, ParentID: 1, AdminID: SuperAdminID, Name: "合约", Symbol: "contract", Type: CategoryTypeForex, Icon: "/icons/contract.png", Data: CategoryData{Strategy: CategoryStrategy}},
		{BaseModel: model.BaseModel{ID: 200}, ParentID: 2, AdminID: SuperAdminID, Name: "USDT本位", Symbol: "contractUSDTMargined", Icon: "/icons/contractUSDTMargined.png", Status: ChannelStatusDisabled, Type: CategoryTypeForex, Data: CategoryData{Strategy: CategoryStrategy}},
		{BaseModel: model.BaseModel{ID: 300}, ParentID: 2, AdminID: SuperAdminID, Name: "USD", Symbol: "contractUSDMargined", Icon: "/icons/contractUSDMargined.png", Type: CategoryTypeForex, Data: CategoryData{Strategy: CategoryStrategy}},

		{BaseModel: model.BaseModel{ID: 3}, ParentID: 1, AdminID: SuperAdminID, Name: "期权", Symbol: "options", Type: CategoryTypeFutures, Icon: "/icons/options.png", Data: CategoryData{Strategy: CategoryStrategy}},
		{BaseModel: model.BaseModel{ID: 400}, ParentID: 3, AdminID: SuperAdminID, Name: "USDT", Symbol: "optionsUSDT", Icon: "/icons/optionsUSDT.png", Status: ChannelStatusDisabled, Type: CategoryTypeFutures, Data: CategoryData{Strategy: CategoryStrategy}},
		{BaseModel: model.BaseModel{ID: 500}, ParentID: 3, AdminID: SuperAdminID, Name: "USD", Symbol: "optionsUSD", Icon: "/icons/optionsUSD.png", Type: CategoryTypeFutures, Data: CategoryData{Strategy: CategoryStrategy}},

		{BaseModel: model.BaseModel{ID: 4}, ParentID: 0, AdminID: SuperAdminID, Name: "外汇", Symbol: "forex", Icon: "/icons/forex.png", Type: CategoryTypeFutures, Data: CategoryData{Strategy: CategoryStrategy}},
		{BaseModel: model.BaseModel{ID: 600}, ParentID: 4, AdminID: SuperAdminID, Name: "USD", Symbol: "forexUSD", Icon: "/icons/forexUSD.png", Type: CategoryTypeFutures, Data: CategoryData{Strategy: CategoryStrategy}},

		{BaseModel: model.BaseModel{ID: 5}, ParentID: 0, AdminID: SuperAdminID, Name: "期货", Symbol: "futures", Icon: "/icons/futures.png", Type: CategoryTypeFutures, Data: CategoryData{Strategy: CategoryStrategy}},
		{BaseModel: model.BaseModel{ID: 700}, ParentID: 5, AdminID: SuperAdminID, Name: "金属", Symbol: "metal", Icon: "/icons/metal.png", Type: CategoryTypeFutures, Data: CategoryData{Strategy: CategoryStrategy}},
		{BaseModel: model.BaseModel{ID: 800}, ParentID: 5, AdminID: SuperAdminID, Name: "能源", Symbol: "energy", Icon: "/icons/energy.png", Type: CategoryTypeFutures, Data: CategoryData{Strategy: CategoryStrategy}},

		{BaseModel: model.BaseModel{ID: 6}, ParentID: 0, AdminID: SuperAdminID, Name: "质押", Symbol: "staking", Icon: "/icons/staking.png", Status: ChannelStatusEnabled, Type: CategoryTypeStaking, Data: CategoryData{Strategy: CategoryStrategy}},
		{BaseModel: model.BaseModel{ID: 900}, ParentID: 6, AdminID: SuperAdminID, Name: "USD", Symbol: "stakingUSD", Icon: "/icons/stakingUSD.png", Status: ChannelStatusEnabled, Type: CategoryTypeStaking, Data: CategoryData{Strategy: CategoryStrategy}},
	}
}
