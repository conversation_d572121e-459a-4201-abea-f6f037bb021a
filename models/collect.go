package models

import (
	"github.com/gofiber/fiber/v2"
	"zfeng/core/model"
)

const (
	CollectStatusEnabled = 10 //	收藏
	CollectStatusDisable = -1 //	取消收藏
)

// Collect 产品收藏表
type Collect struct {
	model.BaseModel
	AdminId   uint   `gorm:"type: int unsigned not null; comment: 管理员ID" json:"adminId"`
	ProductId uint   `gorm:"type: int unsigned not null; comment: 产品ID" json:"productId"`
	UserId    uint   `gorm:"type: int unsigned not null; comment: 用户ID" json:"userId"`
	Data      string `gorm:"type: text; comment: 数据" json:"data"`
	Status    int    `gorm:"type: tinyint not null; default: 10; comment: 状态 -1删除 10收藏" json:"status"`
}

func init() {
	// 只允许主进程运行
	if fiber.IsChild() {
		return
	}

	db := model.NewModel()

	// Create the table if it doesn't exist
	if err := db.AutoMigrate(&Collect{}); err != nil {
		panic("Failed to auto migrate Collect table: " + err.<PERSON>rror())
	}
}
