package models

import (
	"database/sql/driver"
	"errors"
	"zfeng/core/model"

	"github.com/goccy/go-json"
	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

const (
	MenuIconTypeDark   = "dark"   //	暗色图标
	MenuIconTypeLight  = "light"  //	亮色图标
	MenuIconTypeActive = "active" //	激活图标

	// MenuNameTranslatePrefix 菜单名称翻译前缀
	MenuNameTranslatePrefix = "menu_%s"

	// Menu types
	MenuTypeMobileNavigationBar  int8 = 1  // 移动菜单导航
	MenuTypeCommonUserMenu       int8 = 11 // 用户更多菜单
	MenuTypeCommonUserMoreMenu   int8 = 12 // 用户更多菜单
	MenuTypeCommonMoreMenu       int8 = 13 // 公用更多菜单
	MenuTypeCommonWalletMenu     int8 = 14 // 公用钱包菜单
	MenuTypeDesktopNavigationBar int8 = 21 // 电脑菜单导航

	MenuTargetSelf  = "_self"  //	当前窗口
	MenuTargetBlank = "_blank" //	新窗口

	// Menu statuses
	MenuStatusDisabled int8 = -1 // 禁用
	MenuStatusEnabled  int8 = 10 // 启用
)

// Menu 前台菜单
type Menu struct {
	model.BaseModel
	AdminID  uint     `gorm:"type:int unsigned not null;index;comment:管理ID" json:"adminId"`
	ParentID uint     `gorm:"type:int unsigned not null;index;comment:父级ID" json:"parentId"`
	Name     string   `gorm:"type:varchar(50) not null;comment:名称" json:"name"`
	Route    string   `gorm:"type:varchar(100) not null;comment:路由" json:"route"`
	Sort     uint8    `gorm:"type:tinyint unsigned not null;default:99;index;comment:排序" json:"sort"`
	Type     int8     `gorm:"type:tinyint not null;default:1;index;comment:类型(1:导航菜单,11:用户菜单,21:快捷菜单)" json:"type"`
	Status   int8     `gorm:"type:tinyint not null;default:10;index;comment:状态(-1:禁用,10:启用)" json:"status"`
	Data     MenuData `gorm:"type:json;comment:数据" json:"data,omitempty"`
}

type MenuDisplayData struct {
	ID       uint               `json:"id"`                //	ID
	Name     string             `json:"name"`              //	名称
	Route    string             `json:"route"`             //	路由
	Data     MenuData           `json:"data"`              //	数据
	Children []*MenuDisplayData `json:"children" gorm:"-"` //	下级
}

// MenuData 前台菜单数据
type MenuData struct {
	Target     string `json:"target" views:"label:打开方式;type:select"`   // 打开方式 (_self, _blank)
	Label      string `json:"label" views:"label:翻译;type:translate"`   //	翻译
	Small      string `json:"small" views:"label:翻译;type:translate"`   //	副标题翻译
	Class      string `json:"class" views:"label:自定义CSS类"`             // 自定义CSS类
	DarkIcon   string `json:"darkIcon" views:"label:暗色图标;type:icon"`   // 暗色图标
	LightIcon  string `json:"lightIcon" views:"label:亮色图标;type:icon"`  // 亮色图标
	ActiveIcon string `json:"activeIcon" views:"label:激活图标;type:icon"` // 激活图标
}

// Value implements the driver.Valuer interface
func (d MenuData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *MenuData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

func init() {
	// 只允许主进程运行
	if fiber.IsChild() {
		return
	}

	db := model.NewModel()

	// Create the table if it doesn't exist
	if err := db.AutoMigrate(&Menu{}); err != nil {
		panic("Failed to auto migrate Menu table: " + err.Error())
	}

	// Initialize system menus
	if err := InitSystemMenus(db.DB); err != nil {
		panic("Failed to initialize system menus: " + err.Error())
	}
}

// InitSystemMenus initializes the default system menus
func InitSystemMenus(db *gorm.DB) error {
	var count int64
	if err := db.Model(&Menu{}).Count(&count).Error; err != nil {
		return err
	}

	if count == 0 {
		menus := initSystemsMenus()
		return db.CreateInBatches(menus, len(menus)).Error
	}

	return nil
}

func initSystemsMenus() []Menu {
	menus := []Menu{
		// 手机端导航菜单
		{AdminID: SuperAdminID, ParentID: 0, Name: "首页", Route: "/", Sort: 1, Type: MenuTypeMobileNavigationBar, Status: MenuStatusEnabled, Data: MenuData{Label: "menuHome", DarkIcon: "/menus/dark/home.svg", LightIcon: "/menus/light/home.svg", ActiveIcon: "/menus/active/home.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "行情", Route: "/market", Sort: 2, Type: MenuTypeMobileNavigationBar, Status: MenuStatusEnabled, Data: MenuData{Label: "market", DarkIcon: "/menus/dark/market.svg", LightIcon: "/menus/light/market.svg", ActiveIcon: "/menus/active/market.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "交易", Route: "/trade", Sort: 3, Type: MenuTypeMobileNavigationBar, Status: MenuStatusEnabled, Data: MenuData{Label: "trade", DarkIcon: "/menus/dark/trade.svg", LightIcon: "/menus/light/trade.svg", ActiveIcon: "/menus/active/trade.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "理财", Route: "/staking", Sort: 4, Type: MenuTypeMobileNavigationBar, Status: MenuStatusEnabled, Data: MenuData{Label: "manage", DarkIcon: "/menus/dark/manage.svg", LightIcon: "/menus/light/manage.svg", ActiveIcon: "/menus/active/manage.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "资产", Route: "/wallets", Sort: 5, Type: MenuTypeMobileNavigationBar, Status: MenuStatusEnabled, Data: MenuData{Label: "walletAssets", DarkIcon: "/menus/dark/assets.svg", LightIcon: "/menus/light/assets.svg", ActiveIcon: "/menus/active/assets.svg", Target: "_self", Class: ""}},

		// 电脑端导航菜单
		{AdminID: SuperAdminID, ParentID: 0, Name: "行情", Route: "/market", Sort: 1, Type: MenuTypeDesktopNavigationBar, Status: MenuStatusEnabled, Data: MenuData{Label: "market", DarkIcon: "/menus/dark/market.svg", LightIcon: "/menus/light/market.svg", ActiveIcon: "/menus/active/market.svg", Target: "_self", Class: ""}},
		{BaseModel: model.BaseModel{ID: 50}, AdminID: SuperAdminID, ParentID: 0, Name: "交易", Route: "/spot", Sort: 2, Type: MenuTypeDesktopNavigationBar, Status: MenuStatusEnabled, Data: MenuData{Label: "trade", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 50, Name: "现货", Route: "/spot", Sort: 1, Type: MenuTypeDesktopNavigationBar, Status: MenuStatusEnabled, Data: MenuData{Label: "spot", DarkIcon: "/menus/spot.svg", LightIcon: "/menus/spot.svg", Target: "_self", Class: "", Small: "spotSmall"}},
		{AdminID: SuperAdminID, ParentID: 50, Name: "合约", Route: "/contract", Sort: 2, Type: MenuTypeDesktopNavigationBar, Status: MenuStatusEnabled, Data: MenuData{Label: "contract", DarkIcon: "/menus/contract.svg", LightIcon: "/menus/contract.svg", Target: "_self", Class: "", Small: "contractSmall"}},
		{AdminID: SuperAdminID, ParentID: 50, Name: "期权", Route: "/option", Sort: 3, Type: MenuTypeDesktopNavigationBar, Status: MenuStatusEnabled, Data: MenuData{Label: "option", DarkIcon: "/menus/option.svg", LightIcon: "/menus/option.svg", Target: "_self", Class: "", Small: "optionSmall"}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "理财", Route: "/staking", Sort: 3, Type: MenuTypeDesktopNavigationBar, Status: MenuStatusEnabled, Data: MenuData{Label: "manage", DarkIcon: "/menus/dark/manage.svg", LightIcon: "/menus/light/manage.svg", ActiveIcon: "/menus/active/manage.svg", Target: "_self", Class: ""}},

		// 我的菜单 - 总览｜用户信息
		{BaseModel: model.BaseModel{ID: 99}, AdminID: SuperAdminID, ParentID: 0, Name: "总览", Route: "/users", Sort: 1, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuOverview", DarkIcon: "/menus/dark/overview.svg", LightIcon: "/menus/light/overview.svg", ActiveIcon: "/menus/active/overview.svg", Target: "_self", Class: ""}},

		// 我的菜单 - 钱包
		{BaseModel: model.BaseModel{ID: 100}, AdminID: SuperAdminID, ParentID: 0, Name: "钱包", Route: "/wallets", Sort: 1, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuWallet", DarkIcon: "/menus/dark/wallet.svg", LightIcon: "/menus/light/wallet.svg", ActiveIcon: "/menus/active/wallet.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 100, Name: "钱包总览", Route: "/wallets", Sort: 1, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuWalletInfo", DarkIcon: "/menus/dark/wallet.svg", LightIcon: "/menus/light/wallet.svg", ActiveIcon: "/menus/active/wallet.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 100, Name: "账单明细", Route: "/wallets/bill", Sort: 2, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuWalletBill", DarkIcon: "/menus/dark/bill.svg", LightIcon: "/menus/light/bill.svg", ActiveIcon: "/menus/active/bill.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 100, Name: "站内转账", Route: "/wallets/transfer", Sort: 3, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuWalletTransfer", DarkIcon: "/menus/dark/transfer.svg", LightIcon: "/menus/light/transfer.svg", ActiveIcon: "/menus/active/transfer.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 100, Name: "资产兑换", Route: "/wallets/swaps", Sort: 4, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuAssetsSwaps", DarkIcon: "/menus/dark/exchange.svg", LightIcon: "/menus/light/exchange.svg", ActiveIcon: "/menus/active/exchange.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 100, Name: "提现账户", Route: "/wallets/account", Sort: 5, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuWalletAccount", DarkIcon: "/menus/dark/card.svg", LightIcon: "/menus/light/card.svg", ActiveIcon: "/menus/active/card.svg", Target: "_self", Class: ""}},

		// 我的菜单 - 订单
		{BaseModel: model.BaseModel{ID: 110}, AdminID: SuperAdminID, ParentID: 0, Name: "订单", Route: "/spot/order", Sort: 2, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuOrder", DarkIcon: "/menus/dark/order.svg", LightIcon: "/menus/light/order.svg", ActiveIcon: "/menus/active/order.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 110, Name: "现货订单", Route: "/spot/order", Sort: 1, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuSpotOrder", DarkIcon: "/menus/dark/spot_order.svg", LightIcon: "/menus/light/spot_order.svg", ActiveIcon: "/menus/active/spot_order.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 110, Name: "合约订单", Route: "/contract/order", Sort: 2, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuContractOrder", DarkIcon: "/menus/dark/contract_order.svg", LightIcon: "/menus/light/contract_order.svg", ActiveIcon: "/menus/active/contract_order.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 110, Name: "期货订单", Route: "/option/order", Sort: 3, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuOptionOrder", DarkIcon: "/menus/dark/option_order.svg", LightIcon: "/menus/light/option_order.svg", ActiveIcon: "/menus/active/option_order.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 110, Name: "质押订单", Route: "/staking/order", Sort: 4, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuStakingOrder", DarkIcon: "/menus/dark/staking_order.svg", LightIcon: "/menus/light/staking_order.svg", ActiveIcon: "/menus/active/staking_order.svg", Target: "_self", Class: ""}},

		// 我的菜单 - 账户
		{BaseModel: model.BaseModel{ID: 120}, AdminID: SuperAdminID, ParentID: 0, Name: "账户", Route: "/users/setting", Sort: 3, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuAccount", DarkIcon: "/menus/dark/account.png", LightIcon: "/menus/light/account.png", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 120, Name: "会员权益", Route: "/users/level", Sort: 1, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuLevel", DarkIcon: "/menus/dark/level.png", LightIcon: "/menus/light/level.png", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 120, Name: "认证管理", Route: "/users/auth", Sort: 2, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuAuth", DarkIcon: "/menus/dark/auth.png", LightIcon: "/menus/light/auth.png", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 120, Name: "邀请奖励", Route: "/users/invite", Sort: 3, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuInvite", DarkIcon: "/menus/dark/invite.png", LightIcon: "/menus/light/invite.png", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 120, Name: "我的团队", Route: "/users/teams", Sort: 4, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuTeam", DarkIcon: "/menus/dark/team.png", LightIcon: "/menus/light/team.png", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 120, Name: "账户安全", Route: "/users/safety", Sort: 5, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuSafety", DarkIcon: "/menus/dark/safety.png", LightIcon: "/menus/light/safety.png", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 120, Name: "账户设置", Route: "/users/setting", Sort: 6, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuSetting", DarkIcon: "/menus/dark/setting.png", LightIcon: "/menus/light/setting.png", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 120, Name: "消息通知", Route: "/users/notice", Sort: 7, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuNotice", DarkIcon: "/menus/dark/notice.png", LightIcon: "/menus/light/notice.png", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 120, Name: "帮助中心", Route: "/helpers", Sort: 8, Type: MenuTypeCommonUserMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuHelpers", DarkIcon: "/menus/dark/robot.png", LightIcon: "/menus/light/robot.png", Target: "_self", Class: ""}},

		// 我的菜单 -  快捷菜单
		{AdminID: SuperAdminID, ParentID: 0, Name: "充值", Route: "/wallets/deposit", Sort: 1, Type: MenuTypeCommonUserMoreMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuDeposit", DarkIcon: "/menus/dark/deposit.svg", LightIcon: "/menus/light/deposit.svg", ActiveIcon: "/menus/active/deposit.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "提现", Route: "/wallets/withdraw", Sort: 2, Type: MenuTypeCommonUserMoreMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuWithdraw", DarkIcon: "/menus/dark/withdrawal.svg", LightIcon: "/menus/light/withdrawal.svg", ActiveIcon: "/menus/active/withdrawal.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "转账", Route: "/wallets/transfer", Sort: 3, Type: MenuTypeCommonUserMoreMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuTransfer", DarkIcon: "/menus/dark/transfer.svg", LightIcon: "/menus/light/transfer.svg", ActiveIcon: "/menus/active/transfer.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "客服", Route: "/chats", Sort: 4, Type: MenuTypeCommonUserMoreMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuChats", DarkIcon: "/menus/dark/online.svg", LightIcon: "/menus/light/online.svg", ActiveIcon: "/menus/active/online.svg", Target: "_self", Class: ""}},

		// 公用钱包菜单 - 充值｜提现｜转账｜兑换
		{AdminID: SuperAdminID, ParentID: 0, Name: "充值", Route: "/wallets/deposit", Sort: 1, Type: MenuTypeCommonWalletMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuDeposit", DarkIcon: "/menus/dark/deposit.svg", LightIcon: "/menus/light/deposit.svg", ActiveIcon: "/menus/active/deposit.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "提现", Route: "/wallets/withdraw", Sort: 2, Type: MenuTypeCommonWalletMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuWithdraw", DarkIcon: "/menus/dark/withdrawal.svg", LightIcon: "/menus/light/withdrawal.svg", ActiveIcon: "/menus/active/withdrawal.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "转账", Route: "/wallets/transfer", Sort: 3, Type: MenuTypeCommonWalletMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuTransfer", DarkIcon: "/menus/dark/transfer.svg", LightIcon: "/menus/light/transfer.svg", ActiveIcon: "/menus/active/transfer.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "兑换", Route: "/wallets/swaps", Sort: 4, Type: MenuTypeCommonWalletMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuSwaps", DarkIcon: "/menus/dark/exchange.svg", LightIcon: "/menus/light/exchange.svg", ActiveIcon: "/menus/active/exchange.svg", Target: "_self", Class: ""}},

		// 更多菜单
		{AdminID: SuperAdminID, ParentID: 0, Name: "充值", Route: "/wallets/deposit", Sort: 1, Type: MenuTypeCommonMoreMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuDeposit", DarkIcon: "/menus/dark/deposit.svg", LightIcon: "/menus/light/deposit.svg", ActiveIcon: "/menus/active/deposit.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "提现", Route: "/wallets/withdraw", Sort: 2, Type: MenuTypeCommonMoreMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuWithdraw", DarkIcon: "/menus/dark/withdraw.svg", LightIcon: "/menus/light/withdraw.svg", ActiveIcon: "/menus/active/withdraw.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "实名认证", Route: "/users/auth", Sort: 3, Type: MenuTypeCommonMoreMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuAuth", DarkIcon: "/menus/dark/real_name.svg", LightIcon: "/menus/light/real_name.svg", ActiveIcon: "/menus/active/real_name.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "邀请奖励", Route: "/users/invite", Sort: 4, Type: MenuTypeCommonMoreMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuInvite", DarkIcon: "/menus/dark/share.svg", LightIcon: "/menus/light/share.svg", ActiveIcon: "/menus/active/share.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "帮助中心", Route: "/helpers", Sort: 5, Type: MenuTypeCommonMoreMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuHelps", DarkIcon: "/menus/dark/info.svg", LightIcon: "/menus/light/info.svg", ActiveIcon: "/menus/active/info.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "APP下载", Route: "/download", Sort: 6, Type: MenuTypeCommonMoreMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuDownload", DarkIcon: "/menus/dark/download.svg", LightIcon: "/menus/light/download.svg", ActiveIcon: "/menus/active/download.svg", Target: "_self", Class: ""}},
		{AdminID: SuperAdminID, ParentID: 0, Name: "在线客服", Route: "/chats", Sort: 7, Type: MenuTypeCommonMoreMenu, Status: MenuStatusEnabled, Data: MenuData{Label: "menuChats", DarkIcon: "/menus/dark/online.svg", LightIcon: "/menus/light/online.svg", ActiveIcon: "/menus/active/online.svg", Target: "_self", Class: ""}},
	}

	return menus
}
