package models

import (
	"database/sql/driver"
	"errors"
	"zfeng/core/model"

	"github.com/goccy/go-json"
	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

const (
	// Payment Types
	PaymentTypeBankCard   int8 = 1  //	银行卡
	PaymentTypeCrypto     int8 = 11 //	数字货币
	PaymentTypeThirdParty int8 = 21 //	第三方支付

	// Payment Modes
	PaymentModeDeposit         int8 = 1  //	充值
	PaymentModeAssetDeposit    int8 = 2  // 资产充值
	PaymentModeWithdrawal      int8 = 11 // 提现
	PaymentModeAssetWithdrawal int8 = 12 // 资产提现
	PaymentModePay             int8 = 21 // 支付

	// Payment Status
	PaymentStatusDisabled int8 = -1 // 禁用
	PaymentStatusEnabled  int8 = 10 // 激活
)

// WalletPayment 钱包支付管理
type WalletPayment struct {
	model.BaseModel
	AdminID   uint              `gorm:"type:int unsigned not null;index;comment:管理ID" json:"adminId"`
	AssetsID  uint              `gorm:"type:int unsigned not null;default:0;index;comment:资产ID" json:"assetsId"`
	Name      string            `gorm:"type:varchar(60) not null;index;comment:名称" json:"name"`
	Icon      string            `gorm:"type:varchar(255) not null;comment:图标" json:"icon"`
	Currency  string            `gorm:"type:varchar(60) not null;comment:货币符号" json:"currency"`
	Type      int8              `gorm:"type:tinyint not null;default:1;index;comment:类型(1:银行卡,11:数字货币,21:三方支付)" json:"type"`
	Mode      int8              `gorm:"type:tinyint not null;default:1;index;comment:模式(1:充值,2:资产充值,11:提现,12:资产提现,21:支付)" json:"mode"`
	MinAmount float64           `gorm:"type:decimal(16,2) not null;default:1;comment:最小金额" json:"minAmount"`
	MaxAmount float64           `gorm:"type:decimal(16,2) not null;default:1000000;comment:最大金额" json:"maxAmount"`
	StartTime string            `gorm:"type:varchar(60) not null;default:'00:00:00';comment:开始时间" json:"startTime"`
	EndTime   string            `gorm:"type:varchar(60) not null;default:'23:59:59';comment:结束时间" json:"endTime"`
	FixedFee  float64           `gorm:"type:decimal(16,4) not null;default:0;comment:固定手续费" json:"fixedFee"`
	Fee       float64           `gorm:"type:decimal(16,4) not null;default:0;comment:手续费" json:"fee"`
	Level     int8              `gorm:"type:tinyint not null;default:0;comment:等级" json:"level"`
	Status    int8              `gorm:"type:tinyint not null;default:10;index;comment:状态(-1:禁用,10:启用)" json:"status"`
	Sort      int16             `gorm:"type:smallint not null;default:99;index;comment:排序" json:"sort"`
	IsProof   int8              `gorm:"type:tinyint not null;default:2;comment:是否需要凭证(1:是,2:否)" json:"isProof"`
	IsChats   int8              `gorm:"type:tinyint not null;default:2;comment:客服(1:真,2:假)" json:"isChats"`
	Data      WalletPaymentData `gorm:"type:json;comment:数据" json:"data"`
	Conf      WalletPaymentConf `gorm:"type:json;comment:配置" json:"conf"`
	Desc      string            `gorm:"type:varchar(255);comment:描述" json:"desc"`
}

// WalletPaymentConf 配置信息
type WalletPaymentConf struct {
	ID          string `json:"id" views:"label:标识符号"`                       //  项目标识符号
	AppKey      string `json:"appKey" views:"label:应用ID"`                   //   应用ID
	AppSecret   string `json:"appSecret" views:"label:应用密钥"`                //	应用密钥
	PaymentType int8   `json:"paymentType" views:"label:支付类型;type:select;"` //	支付类型
	GatewayUrl  string `json:"baseUrl" views:"label:网关地址"`                  //	网关地址
	ReutrnUrl   string `json:"reutrnUrl" views:"label:返回地址"`                //	返回地址
	NotifyUrl   string `json:"notifyUrl" views:"label:通知地址"`                //	通知地址
}

// Value implements the driver.Valuer interface
func (w WalletPaymentConf) Value() (driver.Value, error) {
	return json.Marshal(w)
}

// Scan implements the sql.Scanner interface
func (w *WalletPaymentConf) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &w)
}

// WalletPaymentData 钱包支付额外数据
type WalletPaymentData struct {
	BankName    string `json:"bankName" views:"label:名称｜公链"`    // 银行名称｜公链名称(Ethereum)
	BankAddress string `json:"bankAddress" views:"label:支付地址"`  // 银行地址｜公链标识(Erc20)
	RealName    string `json:"realName" views:"label:姓名|Token"` // 真实姓名｜公链Token(USDT)
	BankCardNo  string `json:"bankCardNo" views:"label:卡号|地址"`  // 银行卡号｜公链地址(0x**********abcdef)
	BankCode    string `json:"bankCode" views:"label:代号|简写"`    // 银行代号｜公链简写(ETH)
}

// Value implements the driver.Valuer interface
func (d WalletPaymentData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *WalletPaymentData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

func init() {
	// 只允许主进程运行
	if fiber.IsChild() {
		return
	}

	db := model.NewModel()

	// Create the table if it doesn't exist
	if err := db.AutoMigrate(&WalletPayment{}); err != nil {
		panic("Failed to auto migrate WalletPayment table: " + err.Error())
	}

	// Initialize wallet payments
	if err := InitWalletPayments(db.DB); err != nil {
		panic("Failed to initialize wallet payments: " + err.Error())
	}
}

// InitWalletPayments initializes the default wallet payments
func InitWalletPayments(db *gorm.DB) error {
	var count int64
	if err := db.Model(&WalletPayment{}).Count(&count).Error; err != nil {
		return err
	}

	if count == 0 {
		payments := []WalletPayment{
			{
				AdminID:   SuperAdminID,
				AssetsID:  0,
				Name:      "Bank",
				Icon:      "/icons/bank.png",
				Currency:  "USD",
				Type:      PaymentTypeBankCard,
				Mode:      PaymentModeDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 100,
				MaxAmount: 100000,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Example Bank",
					BankCode:    "EXBK",
					RealName:    "John Doe",
					BankCardNo:  "**********",
					BankAddress: "123 Example St, City, Country",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  0,
				Name:      "TRX-USDT",
				Icon:      "/icons/usdt.png",
				Currency:  "USDT",
				Type:      PaymentTypeThirdParty,
				Mode:      PaymentModeDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 10,
				MaxAmount: 1000000,
				FixedFee:  1.5,
				Fee:       0, // Adjust fee as needed
				Desc:      "",
				Conf:      WalletPaymentConf{PaymentType: PaymentTypeCrypto},
				Data: WalletPaymentData{
					BankName:    "Tron",
					BankCode:    "TRC20",
					RealName:    "USDT",
					BankCardNo:  "Third-party platforms",
					BankAddress: "upay.com",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  0,
				Name:      "USDT",
				Icon:      "/icons/usdt.png",
				Currency:  "USDT",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 10,
				MaxAmount: 1000000,
				Fee:       0, // Adjust fee as needed
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Ethereum",
					BankCode:    "USDT",
					RealName:    "Tether",
					BankCardNo:  "0x****************************************",
					BankAddress: "ERC20",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  0,
				Name:      "Bank",
				Icon:      "/icons/bank.png",
				Currency:  "USD",
				Type:      PaymentTypeBankCard,
				Mode:      PaymentModeWithdrawal,
				Status:    PaymentStatusEnabled,
				MinAmount: 100,
				MaxAmount: 50000,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "",
					BankCode:    "",
					RealName:    "",
					BankCardNo:  "",
					BankAddress: "",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  0,
				Name:      "USDT",
				Icon:      "/icons/usdt.png",
				Currency:  "USDT",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeWithdrawal,
				Status:    PaymentStatusEnabled,
				MinAmount: 20,
				MaxAmount: 500000,
				Fee:       0, // Adjust fee as needed
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Ethereum",
					BankCode:    "USDT",
					RealName:    "Tether",
					BankCardNo:  "",
					BankAddress: "ERC20",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  1,
				Name:      "BTC",
				Icon:      "/icons/btc.png",
				Currency:  "BTC",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 0.001,
				MaxAmount: 100,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Bitcoin",
					BankCode:    "BTC",
					RealName:    "Bitcoin",
					BankCardNo:  "******************************************",
					BankAddress: "Bitcoin",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  1,
				Name:      "BTC",
				Icon:      "/icons/btc.png",
				Currency:  "BTC",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetWithdrawal,
				Status:    PaymentStatusEnabled,
				MinAmount: 0.001,
				MaxAmount: 10,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Bitcoin",
					BankCode:    "BTC",
					RealName:    "Bitcoin",
					BankCardNo:  "",
					BankAddress: "Bitcoin",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  2,
				Name:      "ETH",
				Icon:      "/icons/eth.png",
				Currency:  "ETH",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 0.01,
				MaxAmount: 1000,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Ethereum",
					BankCode:    "ETH",
					RealName:    "Ethereum",
					BankCardNo:  "******************************************",
					BankAddress: "Ethereum",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  2,
				Name:      "ETH",
				Icon:      "/icons/eth.png",
				Currency:  "ETH",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetWithdrawal,
				Status:    PaymentStatusEnabled,
				MinAmount: 0.01,
				MaxAmount: 100,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Ethereum",
					BankCode:    "ETH",
					RealName:    "Ethereum",
					BankCardNo:  "",
					BankAddress: "Ethereum",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  3,
				Name:      "USDT",
				Icon:      "/icons/usdt.png",
				Currency:  "USDT",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 10,
				MaxAmount: 1000000,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Ethereum",
					BankCode:    "USDT",
					RealName:    "Tether",
					BankCardNo:  "******************************************",
					BankAddress: "ERC20",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  3,
				Name:      "USDT",
				Icon:      "/icons/usdt.png",
				Currency:  "USDT",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetWithdrawal,
				Status:    PaymentStatusEnabled,
				MinAmount: 10,
				MaxAmount: 100000,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Ethereum",
					BankCode:    "USDT",
					RealName:    "Tether",
					BankCardNo:  "",
					BankAddress: "ERC20",
				},
			},
			// Add similar structures for USDC, BNB, SOL, TRX, XRP
			// Example for USDC:
			{
				AdminID:   SuperAdminID,
				AssetsID:  4,
				Name:      "USDC",
				Icon:      "/icons/usdc.png",
				Currency:  "USDC",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 10,
				MaxAmount: 1000000,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Ethereum",
					BankCode:    "USDC",
					RealName:    "USDC Coin",
					BankCardNo:  "******************************************",
					BankAddress: "ERC20",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  4,
				Name:      "USDC",
				Icon:      "/icons/usdc.png",
				Currency:  "USDC",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetWithdrawal,
				Status:    PaymentStatusEnabled,
				MinAmount: 10,
				MaxAmount: 100000,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Ethereum",
					BankCode:    "USDC",
					RealName:    "USDC Coin",
					BankCardNo:  "",
					BankAddress: "ERC20",
				},
			},
			// Continue with similar structures for BNB, SOL, TRX, XRP
			{
				AdminID:   SuperAdminID,
				AssetsID:  5,
				Name:      "BNB",
				Icon:      "/icons/bnb.png",
				Currency:  "BNB",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 0.1,
				MaxAmount: 10000,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Binance",
					BankCode:    "BNB",
					RealName:    "BNB",
					BankCardNo:  "bnb1jxfh2g85q3v0tdq56fnevx6xcxtcnhtsmcu64m",
					BankAddress: "BEP20",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  5,
				Name:      "BNB",
				Icon:      "/icons/bnb.png",
				Currency:  "BNB",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetWithdrawal,
				Status:    PaymentStatusEnabled,
				MinAmount: 0.1,
				MaxAmount: 1000,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Binance",
					BankCode:    "BNB",
					RealName:    "BNB",
					BankCardNo:  "",
					BankAddress: "BEP20",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  6,
				Name:      "SOL",
				Icon:      "/icons/sol.png",
				Currency:  "SOL",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 1,
				MaxAmount: 100000,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Solana",
					BankCode:    "SOL",
					RealName:    "Solana",
					BankCardNo:  "7v91N7iZ9mNicL8WfG6cgSCKyRXydQjLh6UYBWwm6y1Q",
					BankAddress: "Solana",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  6,
				Name:      "SOL",
				Icon:      "/icons/sol.png",
				Currency:  "SOL",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetWithdrawal,
				Status:    PaymentStatusEnabled,
				MinAmount: 1,
				MaxAmount: 10000,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Solana",
					BankCode:    "SOL",
					RealName:    "Solana",
					BankCardNo:  "",
					BankAddress: "Solana",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  7,
				Name:      "TRX",
				Icon:      "/icons/trx.png",
				Currency:  "TRX",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 100,
				MaxAmount: ********,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "TRON",
					BankCode:    "TRX",
					RealName:    "TRX",
					BankCardNo:  "TJYeasTPDdxYbsqd4Q5ci2ETpW5khfvjZj",
					BankAddress: "TRC20",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  7,
				Name:      "TRX",
				Icon:      "/icons/trx.png",
				Currency:  "TRX",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetWithdrawal,
				Status:    PaymentStatusEnabled,
				MinAmount: 100,
				MaxAmount: 1000000,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "TRON",
					BankCode:    "TRX",
					RealName:    "TRX",
					BankCardNo:  "",
					BankAddress: "TRC20",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  8,
				Name:      "XRP",
				Icon:      "/icons/xrp.png",
				Currency:  "XRP",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 10,
				MaxAmount: 1000000,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Ripple",
					BankCode:    "XRP",
					RealName:    "XRP",
					BankCardNo:  "rEb8TK3gBgk5auZkwc6sHnwrGVJH8DuaLh",
					BankAddress: "XRPL",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  8,
				Name:      "XRP",
				Icon:      "/icons/xrp.png",
				Currency:  "XRP",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetWithdrawal,
				Status:    PaymentStatusEnabled,
				MinAmount: 10,
				MaxAmount: 100000,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "Ripple",
					BankCode:    "XRP",
					RealName:    "XRP",
					BankCardNo:  "",
					BankAddress: "XRPL",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  0,
				Name:      "PayPal",
				Icon:      "/icons/paypal.png",
				Currency:  "USD", // 为标准货币代码
				Type:      PaymentTypeThirdParty,
				Mode:      PaymentModePay,
				Status:    PaymentStatusEnabled,
				MinAmount: 1,
				MaxAmount: ********00,
				FixedFee:  1.5,
				Fee:       0,
				Desc:      "",
				Conf:      WalletPaymentConf{},
				Data: WalletPaymentData{
					BankName:    "PayPal",                  // 添加支付服务商名称
					BankCode:    "PP",                      // 标识
					RealName:    "PayPal Payment",          // 支付服务描述
					BankCardNo:  "https://www.paypal.com/", // 使用国际版网址
					BankAddress: "<EMAIL>",  // 添加商户 PayPal 邮箱
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  9, // Cardano的资产ID
				Name:      "ADA",
				Icon:      "/icons/ada.png",
				Currency:  "ADA",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 1,
				MaxAmount: 100000,
				Fee:       0.01,
				Desc:      "Cardano deposit",
				Data: WalletPaymentData{
					BankName:    "Cardano",
					BankCode:    "ADA",
					RealName:    "ADA Deposit",
					BankCardNo:  "addr1q9xyz**********abcdef", // 示例Cardano地址
					BankAddress: "Cardano",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  9, // Cardano的资产ID
				Name:      "ADA",
				Icon:      "/icons/ada.png",
				Currency:  "ADA",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetWithdrawal,
				Status:    PaymentStatusEnabled,
				MinAmount: 1,
				MaxAmount: 50000,
				Fee:       0.01,
				Desc:      "Cardano withdrawal",
				Data: WalletPaymentData{
					BankName:    "Cardano",
					BankCode:    "ADA",
					RealName:    "ADA Withdrawal",
					BankCardNo:  "addr1q9xyz**********abcdef", // 示例Cardano地址（同样可以配置为实际提币地址）
					BankAddress: "Cardano",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  10, // Cosmos的资产ID
				Name:      "ATOM",
				Icon:      "/icons/atom.png",
				Currency:  "ATOM",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 1,
				MaxAmount: 100000,
				Fee:       0.01,
				Desc:      "Cosmos deposit",
				Data: WalletPaymentData{
					BankName:    "Cosmos",
					BankCode:    "ATOM",
					RealName:    "ATOM Deposit",
					BankCardNo:  "cosmos1xyz**********abcdef", // 示例Cosmos地址
					BankAddress: "Cosmos",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  10, // Cosmos的资产ID
				Name:      "ATOM",
				Icon:      "/icons/atom.png",
				Currency:  "ATOM",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetWithdrawal, // 提币模式
				Status:    PaymentStatusEnabled,       // 启用状态
				MinAmount: 1,                          // 最小提币金额
				MaxAmount: 50000,                      // 最大提币金额
				Fee:       0.01,                       // 提币手续费
				Desc:      "Cosmos withdrawal",
				Data: WalletPaymentData{
					BankName:    "Cosmos",
					BankCode:    "ATOM",
					RealName:    "ATOM Withdrawal",
					BankCardNo:  "cosmos1q9xyz**********abcdef", // 示例Cosmos地址
					BankAddress: "Cosmos Hub Network",           // 网络或地址说明
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  11, // Dogecoin的资产ID
				Name:      "DOGE",
				Icon:      "/icons/doge.png",
				Currency:  "DOGE",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 10,
				MaxAmount: 1000000,
				Fee:       0.01,
				Desc:      "Dogecoin deposit",
				Data: WalletPaymentData{
					BankName:    "Dogecoin",
					BankCode:    "DOGE",
					RealName:    "DOGE Deposit",
					BankCardNo:  "Dxyz**********abcdef", // 示例Dogecoin地址
					BankAddress: "Dogecoin",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  11, // Dogecoin的资产ID
				Name:      "DOGE",
				Icon:      "/icons/doge.png",
				Currency:  "DOGE",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetWithdrawal,
				Status:    PaymentStatusEnabled,
				MinAmount: 10,
				MaxAmount: 500000,
				Fee:       0.01,
				Desc:      "Dogecoin withdrawal",
				Data: WalletPaymentData{
					BankName:    "Dogecoin",
					BankCode:    "DOGE",
					RealName:    "DOGE Withdrawal",
					BankCardNo:  "D9xyz**********abcdef**********abc",
					BankAddress: "Dogecoin Network",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  12, // Ethereum Classic的资产ID
				Name:      "ETC",
				Icon:      "/icons/etc.png",
				Currency:  "ETC",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 0.1,
				MaxAmount: 10000,
				Fee:       0.01,
				Desc:      "Ethereum Classic deposit",
				Data: WalletPaymentData{
					BankName:    "Ethereum Classic",
					BankCode:    "ETC",
					RealName:    "ETC Deposit",
					BankCardNo:  "0x**********abcdef**********abcdef", // 示例ETC地址
					BankAddress: "Ethereum Classic",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  12, // Ethereum Classic的资产ID
				Name:      "ETC",
				Icon:      "/icons/etc.png",
				Currency:  "ETC",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetWithdrawal,
				Status:    PaymentStatusEnabled,
				MinAmount: 0.1,
				MaxAmount: 5000,
				Fee:       0.01,
				Desc:      "Ethereum Classic withdrawal",
				Data: WalletPaymentData{
					BankName:    "Ethereum Classic",
					BankCode:    "ETC",
					RealName:    "ETC Withdrawal",
					BankCardNo:  "******************************************",
					BankAddress: "Ethereum Classic Network",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  13, // Litecoin的资产ID
				Name:      "LTC",
				Icon:      "/icons/ltc.png",
				Currency:  "LTC",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 0.1,
				MaxAmount: 10000,
				Fee:       0.01,
				Desc:      "Litecoin deposit",
				Data: WalletPaymentData{
					BankName:    "Litecoin",
					BankCode:    "LTC",
					RealName:    "LTC Deposit",
					BankCardNo:  "Lxyz**********abcdef", // 示例Litecoin地址
					BankAddress: "Litecoin",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  13, // Litecoin的资产ID
				Name:      "LTC",
				Icon:      "/icons/ltc.png",
				Currency:  "LTC",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetWithdrawal, // 提币模式
				Status:    PaymentStatusEnabled,       // 启用状态
				MinAmount: 0.1,                        // 最小提币金额
				MaxAmount: 5000,                       // 最大提币金额
				Fee:       0.01,                       // 提币手续费
				Desc:      "Litecoin withdrawal",
				Data: WalletPaymentData{
					BankName:    "Litecoin",
					BankCode:    "LTC",
					RealName:    "LTC Withdrawal",
					BankCardNo:  "LTC1q9xyz**********abcdef**********abc", // 示例Litecoin地址
					BankAddress: "Litecoin Network",                       // 网络名称或说明
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  14, // Uniswap的资产ID
				Name:      "UNI",
				Icon:      "/icons/uni.png",
				Currency:  "UNI",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 1,
				MaxAmount: 100000,
				Fee:       0.01,
				Desc:      "Uniswap deposit",
				Data: WalletPaymentData{
					BankName:    "Uniswap",
					BankCode:    "UNI",
					RealName:    "UNI Deposit",
					BankCardNo:  "0x5678**********abcdef**********abcdef", // 示例UNI地址
					BankAddress: "Ethereum",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  14, // Uniswap的资产ID
				Name:      "UNI",
				Icon:      "/icons/uni.png",
				Currency:  "UNI",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetWithdrawal,
				Status:    PaymentStatusEnabled,
				MinAmount: 1,
				MaxAmount: 50000,
				Fee:       0.01,
				Desc:      "Uniswap withdrawal",
				Data: WalletPaymentData{
					BankName:    "Uniswap",
					BankCode:    "UNI",
					RealName:    "UNI Withdrawal",
					BankCardNo:  "0xabc**********abcdef**********abcdef1234",
					BankAddress: "Uniswap Network",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  15, // TrumpCoin的资产ID
				Name:      "TRUMP",
				Icon:      "/icons/trump.png",
				Currency:  "UNI",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetDeposit,
				Status:    PaymentStatusEnabled,
				MinAmount: 1,
				MaxAmount: 100000,
				Fee:       0.01,
				Desc:      "Ethereum deposit",
				Data: WalletPaymentData{
					BankName:    "TrumpCoin",
					BankCode:    "TRUMP",
					RealName:    "TRUMP Deposit",
					BankCardNo:  "0xs7sa345sfabcdesaf34567890abcdef", // 示例UNI地址
					BankAddress: "Ethereum",
				},
			},
			{
				AdminID:   SuperAdminID,
				AssetsID:  15, // TrumpCoin 的资产ID
				Name:      "TRUMP",
				Icon:      "/icons/trump.png",
				Currency:  "UNI",
				Type:      PaymentTypeCrypto,
				Mode:      PaymentModeAssetWithdrawal,
				Status:    PaymentStatusEnabled,
				MinAmount: 1,
				MaxAmount: 50000,
				Fee:       0.01,
				Desc:      "Ethereum withdrawal",
				Data: WalletPaymentData{
					BankName:    "TrumpCoin",
					BankCode:    "TRUMP",
					RealName:    "TRUMP Withdrawal",
					BankCardNo:  "0xabwrew3sd5erter89sfbcdef12gds67890abcdef1234",
					BankAddress: "Ethereum Network",
				},
			},
		}

		return db.CreateInBatches(payments, len(payments)).Error
	}

	return nil
}
