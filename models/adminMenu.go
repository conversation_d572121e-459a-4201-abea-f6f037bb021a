package models

import (
	"database/sql/driver"
	"errors"
	"zfeng/core/model"

	"github.com/goccy/go-json"
	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// AdminMenuStatus 管理员菜单状态常量
const (
	AdminMenuStatusDisabled int8 = -1 // 禁用
	AdminMenuStatusEnabled  int8 = 10 // 启用
)

// AdminMenu 管理员菜单
type AdminMenu struct {
	model.BaseModel
	ParentID uint          `gorm:"type:int unsigned not null;comment:父级ID" json:"parentId"`
	Name     string        `gorm:"type:varchar(50) not null;comment:名称" json:"name"`
	Route    string        `gorm:"type:varchar(100);default:null;unique;comment:路径" json:"route"`
	Sort     uint8         `gorm:"type:tinyint unsigned not null;default:99;comment:排序" json:"sort"`
	Status   int8          `gorm:"type:tinyint not null;default:10;comment:状态(10:启用,-1:禁用)" json:"status"`
	Data     AdminMenuData `gorm:"type:json;comment:数据" json:"data,omitempty"`
}

// ToDisplayData 将 AdminMenu 转换为 AdminMenuDisplayData
func (m *AdminMenu) ToDisplayData() *AdminMenuDisplayData {
	return &AdminMenuDisplayData{
		ID:       m.ID,
		Name:     m.Name,
		Route:    m.Route,
		Sort:     m.Sort,
		Status:   m.Status,
		Data:     m.Data,
		Children: make([]*AdminMenuDisplayData, 0),
	}
}

// AdminMenuDisplayData 管理菜单显示数据
type AdminMenuDisplayData struct {
	ID       uint                    `json:"id"`
	Name     string                  `json:"name"`
	Route    string                  `json:"route"`
	Sort     uint8                   `json:"sort"`
	Status   int8                    `json:"status"`
	Data     AdminMenuData           `json:"data"`
	Children []*AdminMenuDisplayData `json:"children"`
}

// AdminMenuData 管理菜单配置
type AdminMenuData struct {
	Icon    string `json:"icon" views:"label:内置图标"`    // 图标
	VueFile string `json:"vueFile" views:"label:视图文件"` // vue文件
}

// Value implements the driver.Valuer interface
func (d AdminMenuData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *AdminMenuData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

func init() {
	// 只允许主进程运行
	if fiber.IsChild() {
		return
	}

	db := model.NewModel()

	// Create the table if it doesn't exist
	if err := db.AutoMigrate(&AdminMenu{}); err != nil {
		panic("Failed to auto migrate AdminMenu table: " + err.Error())
	}

	// Initialize admin menus
	if err := InitAdminMenus(db.DB); err != nil {
		panic("Failed to initialize admin menus: " + err.Error())
	}
}

// InitAdminMenus 初始化管理菜单数据
func InitAdminMenus(db *gorm.DB) error {
	var count int64
	if err := db.Model(&AdminMenu{}).Count(&count).Error; err != nil {
		return err
	}

	if count == 0 {
		menuData := []*AdminMenuDisplayData{
			// 用户管理
			{
				Name:   "用户管理",
				Sort:   1,
				Status: AdminMenuStatusEnabled,
				Data: AdminMenuData{
					Icon:    "group",
					VueFile: "",
				},
				Children: []*AdminMenuDisplayData{
					{
						Name:   "用户列表",
						Route:  "/users/user/index",
						Sort:   1,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "people",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "用户资产",
						Route:  "/users/assets/index",
						Sort:   2,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "account_balance_wallet",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "认证管理",
						Route:  "/users/auth/index",
						Sort:   3,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "verified_user",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "会员列表",
						Route:  "/users/level/index",
						Sort:   4,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "star",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "访问记录",
						Route:  "/users/access/index",
						Sort:   5,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "history",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "提现账户",
						Route:  "/users/account/index",
						Sort:   6,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "account_balance",
							VueFile: "table.vue",
						},
					},
				},
			},

			// 产品管理
			{
				Name:   "产品管理",
				Sort:   2,
				Status: AdminMenuStatusEnabled,
				Data: AdminMenuData{
					Icon:    "inventory_2",
					VueFile: "",
				},
				Children: []*AdminMenuDisplayData{
					{
						Name:   "币币订单",
						Route:  "/products/order/spot/index",
						Sort:   1,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "currency_exchange",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "合约订单",
						Route:  "/products/order/contract/index",
						Sort:   2,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "candlestick_chart",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "期货订单",
						Route:  "/products/order/futures/index",
						Sort:   3,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "trending_up",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "质押订单",
						Route:  "/products/order/staking/index",
						Sort:   3,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "lock_clock",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "产品列表",
						Route:  "/products/product/index",
						Sort:   10,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "list",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "产品分类",
						Route:  "/products/category/index",
						Sort:   11,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "category",
							VueFile: "table.vue",
						},
					},
				},
			},

			// 资产管理
			{
				Name:   "资产管理",
				Route:  "",
				Sort:   3,
				Status: AdminMenuStatusEnabled,
				Data: AdminMenuData{
					Icon:    "account_balance_wallet",
					VueFile: "",
				},
				Children: []*AdminMenuDisplayData{
					{
						Name:   "充值订单",
						Route:  "/wallets/assets/deposit/index",
						Sort:   1,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "add_card",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "提现订单",
						Route:  "/wallets/assets/withdraw/index",
						Sort:   2,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "money_off",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "转账记录",
						Route:  "/wallets/assets/transfer/index",
						Sort:   3,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "swap_horiz",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "闪兑记录",
						Route:  "/wallets/exchange/index",
						Sort:   4,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "currency_exchange",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "账单资产",
						Route:  "/wallets/assets/bill/index",
						Sort:   5,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "receipt",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "资产列表",
						Route:  "/wallets/assets/index",
						Sort:   5,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "list",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "支付管理",
						Route:  "/wallets/assets/payment/index",
						Sort:   6,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "payment",
							VueFile: "table.vue",
						},
					},
				},
			},

			// 财务管理
			{
				Name:   "财务管理",
				Sort:   4,
				Status: AdminMenuStatusEnabled,
				Data: AdminMenuData{
					Icon:    "payments",
					VueFile: "",
				},
				Children: []*AdminMenuDisplayData{
					{
						Name:   "充值订单",
						Route:  "/wallets/deposit/index",
						Sort:   1,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "add_card",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "提现订单",
						Route:  "/wallets/withdraw/index",
						Sort:   2,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "money_off",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "转账记录",
						Route:  "/wallets/transfer/index",
						Sort:   3,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "swap_horiz",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "账单记录",
						Route:  "/wallets/bill/index",
						Sort:   4,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "receipt",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "支付管理",
						Route:  "/wallets/payment/index",
						Sort:   5,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "payment",
							VueFile: "table.vue",
						},
					},
				},
			},

			// 后台管理
			{
				Name:   "后台管理",
				Sort:   90,
				Status: AdminMenuStatusEnabled,
				Data: AdminMenuData{
					Icon:    "admin_panel_settings",
					VueFile: "",
				},
				Children: []*AdminMenuDisplayData{
					{
						Name:   "管理列表",
						Route:  "/admins/manage/index",
						Sort:   1,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "manage_accounts",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "参数配置",
						Route:  "/admins/settings/index",
						Sort:   2,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "tune",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "角色列表",
						Route:  "/admins/role/index",
						Sort:   2,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "security",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "菜单管理",
						Route:  "/admins/menu/index",
						Sort:   3,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "menu",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "操作日志",
						Route:  "/admins/logs/index",
						Sort:   4,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "assignment",
							VueFile: "table.vue",
						},
					},
				},
			},

			// 系统配置
			{
				Name:   "系统配置",
				Sort:   91,
				Status: AdminMenuStatusEnabled,
				Data: AdminMenuData{
					Icon:    "settings",
					VueFile: "",
				},
				Children: []*AdminMenuDisplayData{
					{
						Name:   "前台菜单",
						Route:  "/systems/menu/index",
						Sort:   1,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "menu",
							VueFile: "table.vue",
						},
					},

					{
						Name:   "翻译配置",
						Route:  "/systems/translate/index",
						Sort:   3,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "translate",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "文章配置",
						Route:  "/systems/article/index",
						Sort:   4,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "article",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "国家配置",
						Route:  "/systems/country/index",
						Sort:   5,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "public",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "语言配置",
						Route:  "/systems/lang/index",
						Sort:   6,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "language",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "渠道管理",
						Route:  "/systems/channel/index",
						Sort:   7,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "device_hub",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "等级配置",
						Route:  "/systems/level/index",
						Sort:   8,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "star_border",
							VueFile: "table.vue",
						},
					},
					{
						Name:   "通知配置",
						Route:  "/systems/notify/index",
						Sort:   9,
						Status: AdminMenuStatusEnabled,
						Data: AdminMenuData{
							Icon:    "notifications",
							VueFile: "table.vue",
						},
					},
				},
			},
		}

		menus := make([]*AdminMenu, 0)
		var nextID uint = 1
		var createMenu func(menu *AdminMenuDisplayData, parentID uint) *AdminMenu
		createMenu = func(menu *AdminMenuDisplayData, parentID uint) *AdminMenu {
			adminMenu := &AdminMenu{
				BaseModel: model.BaseModel{ID: nextID},
				ParentID:  parentID,
				Name:      menu.Name,
				Route:     menu.Route,
				Sort:      menu.Sort,
				Status:    menu.Status,
				Data:      menu.Data,
			}
			menus = append(menus, adminMenu)
			nextID++

			for _, child := range menu.Children {
				createMenu(child, adminMenu.ID)
			}

			return adminMenu
		}

		for _, menu := range menuData {
			createMenu(menu, 0)
		}

		return db.Create(&menus).Error
	}

	return nil
}
