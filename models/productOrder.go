package models

import (
	"database/sql/driver"
	"errors"
	"fmt"
	"time"
	"zfeng/core/model"

	"github.com/goccy/go-json"

	"github.com/gofiber/fiber/v2"
)

const (
	// Order Types
	ProductOrderTypeSpot     = 1 // 币币
	ProductOrderTypeContract = 2 // 合约
	ProductOrderTypeFutures  = 3 // 期货
	ProductOrderTypeStaking  = 4 // 质押

	ProductOrderSideBuy  int8 = 1 // 买入
	ProductOrderSideSell int8 = 2 // 卖出

	ProductOrderModeLimit  int8 = 1 // 限价
	ProductOrderModeMarket int8 = 2 // 市价

	// Order Status
	ProductOrderStatusCancelled int8 = -1 // 取消
	ProductOrderStatusWaiting   int8 = 10 // 等待
	ProductOrderStatusRunning   int8 = 11 // 运行
	ProductOrderStatusCompleted int8 = 20 // 完成
	ProductOrderStatusLiquidate      = -2 // 状态爆仓
	ProductOrderStatusLose           = 1  // 状态输
	ProductOrderStatusWin            = 2  // 状态赢
)

// Order represents a product order in the system
type Order struct {
	model.BaseModel
	AdminID   uint      `gorm:"type:int unsigned not null;index;comment:管理员ID" json:"adminId"`
	UserID    uint      `gorm:"type:int unsigned not null;index;comment:用户ID" json:"userId"`
	ProductID uint      `gorm:"type:int unsigned not null;index;comment:产品ID" json:"productId"`
	OrderSN   string    `gorm:"type:varchar(64);not null;uniqueIndex;comment:订单编号" json:"orderSn"`
	Money     float64   `gorm:"type:decimal(20,6) not null;comment:金额" json:"money"`
	Fee       float64   `gorm:"type:decimal(20,6) not null;comment:手续费" json:"fee"`
	Nums      float64   `gorm:"type:decimal(20,6) not null;comment:数量" json:"nums"`
	Type      int8      `gorm:"type:tinyint unsigned not null;default:1;index;comment:类型(1币币 2合约 3期货 4量化交易期权 5矿)" json:"type"`
	Side      int8      `gorm:"type:tinyint unsigned not null;default:1;index;comment:方向(1:买入,2:卖出)" json:"side"`
	Mode      int8      `gorm:"type:tinyint unsigned not null;default:1;index;comment:模式(1:限价,2:市价)" json:"mode"`
	Status    int8      `gorm:"type:tinyint not null;default:10;index;comment:订单状态(-1:取消,10:等待,11:运行,20:完成)" json:"status"`
	Data      OrderData `gorm:"type:json;comment:数据" json:"data"`
	ExpiredAt time.Time `gorm:"type:datetime(3);index;default:null;comment:过期时间" json:"expiredAt"`
}

// OrderInfo 产品信息
type OrderInfo struct {
	Order
	UserInfo    *User    `json:"userInfo" gorm:"foreignKey:UserID"`
	ProductInfo *Product `json:"productInfo" gorm:"foreignKey:ProductID"`
}

func (o *OrderInfo) TableName() string {
	return "order"
}

// increase 涨幅比
func (o *OrderInfo) increase(lastPrice float64) float64 {
	increase := (lastPrice - o.Data.Price) / o.Data.Price * float64(o.Data.Index)
	if o.Side == ProductOrderSideSell {
		increase = -increase
	}
	return increase
}

// IsLiquidate 是否爆仓
func (o *OrderInfo) IsLiquidate(lastPrice float64) bool {
	return o.increase(lastPrice) >= 1
}

// IsStopLoss 是否止损
func (o *OrderInfo) IsStopLoss(lastPrice float64) bool {
	// 设置有止损价 && ((方向买 && 最新价 <= 止损价) || (方向卖 && 最新价 >= 止损价))
	if (o.Data.StopPrice > 0) && ((o.Side == ProductOrderSideBuy && lastPrice <= o.Data.StopPrice) || (o.Side == ProductOrderSideSell && lastPrice >= o.Data.StopPrice)) {
		return true
	}
	return false
}

// IsTakeProfit 是否止盈
func (o *OrderInfo) IsTakeProfit(lastPrice float64) bool {
	// 设置有止赢价 && ((方向买 && 最新价 => 止损价) || (方向卖 && 最新价 <= 止损价))
	if (o.Data.TakePrice > 0) && ((o.Side == ProductOrderSideBuy && lastPrice >= o.Data.TakePrice) || (o.Side == ProductOrderSideSell && lastPrice <= o.Data.TakePrice)) {
		return true
	}
	return false
}

// sumAmount 未去除手续费价格
func (o *OrderInfo) sumAmount(lastPrice float64) float64 {
	sumAmount := o.Money + (o.Money * o.increase(lastPrice))
	return sumAmount
}

// Amount 最终价格
func (o *OrderInfo) Amount(lastPrice float64) float64 {
	amount := o.sumAmount(lastPrice) - o.AmountFee(lastPrice)
	return amount
}

// AmountFee 手续费
func (o *OrderInfo) AmountFee(lastPrice float64) float64 {
	sumAmount := o.sumAmount(lastPrice)
	fee := sumAmount * o.ProductInfo.Fee / 100
	return fee
}

// OrderData 产品订单数据属性
type OrderData struct {
	Price           float64         `json:"price"`     // 买入单价 质押=今日收益
	Amount          float64         `json:"amount"`    // 合约=结算金额 期货=结算金额 质押=结算金额
	Index           int             `json:"index"`     //	合约=倍数 期货=索引 质押=索引
	Second          int             `json:"second"`    //	期货=秒数
	Rate            float64         `json:"rate"`      //	期货=收益率
	TakePrice       float64         `json:"takePrice"` //	合约=止盈价格
	StopPrice       float64         `json:"stopPrice"` //	合约=止损价格
	SellPrice       float64         `json:"sellPrice"` //	合约=卖出单价
	WhichDay        float64         `json:"whichDay"`  // 质押=第几期
	StakingStrategy StakingStrategy `json:"stakingStrategy"`
}

func (_OrderData OrderData) Value() (driver.Value, error) {
	return json.Marshal(_OrderData)
}
func (_OrderData *OrderData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New(fmt.Sprint("Failed to scan OrderData value:", value))
	}
	return json.Unmarshal(bytes, &_OrderData)
}

func init() {
	// 只允许主进程运行
	if fiber.IsChild() {
		return
	}

	db := model.NewModel()

	// Create the table if it doesn't exist
	if err := db.AutoMigrate(&Order{}); err != nil {
		panic("Failed to auto migrate Order table: " + err.Error())
	}
}
