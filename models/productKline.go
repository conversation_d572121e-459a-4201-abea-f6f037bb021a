package models

import (
	"github.com/gofiber/fiber/v2"
	"time"
	"zfeng/core/model"
)

// ProductKline K线图数据
type ProductKline struct {
	model.BaseModel
	ProductSymbol string    `gorm:"type:varchar(64) not null;uniqueIndex:idx_product_symbol_created_at;comment:产品标识" json:"productSymbol"` // 产品标识
	OpenPrice     float64   `gorm:"type:decimal(16,8) not null;default:0;comment:开盘价格" json:"openPrice"`                                   // 开盘价格
	HighPrice     float64   `gorm:"type:decimal(16,8) not null;default:0;comment:最高价格" json:"highPrice"`                                   // 最高价格
	LowsPrice     float64   `gorm:"type:decimal(16,8) not null;default:0;comment:最低价格" json:"lowsPrice"`                                   // 最低价格
	ClosePrice    float64   `gorm:"type:decimal(16,8) not null;default:0;comment:收盘价格" json:"closePrice"`                                  // 收盘价格
	Vol           float64   `gorm:"type:decimal(16,8) not null;default:0;comment:交易量" json:"vol"`                                          // 交易量
	Amount        float64   `gorm:"type:decimal(16,8) not null;default:0;comment:成交额" json:"amount"`                                       // 成交额
	CreatedAt     time.Time `gorm:"autoCreateTime;uniqueIndex:idx_product_symbol_created_at" json:"createdAt"`                             //	创建时间
}

type ProductLineChart struct {
	ID        uint64    `json:"id"`
	Name      uint64    `json:"name"`
	OpenPrice float64   `json:"openPrice"`
	CreatedAt time.Time `json:"created_at"`
}

func init() {
	// 只允许主进程运行
	if fiber.IsChild() {
		return
	}

	db := model.NewModel()

	// Create the table if it doesn't exist
	if err := db.AutoMigrate(&ProductKline{}); err != nil {
		panic("Failed to auto migrate Product Kline table: " + err.Error())
	}
}
