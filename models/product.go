package models

import (
	"database/sql/driver"
	"errors"
	"time"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/module/exchange/interfaces"

	"github.com/goccy/go-json"
	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

const (
	// 翻译键名
	ProductTranslateName = "productName"
	// 翻译键名
	ProductTranslateDesc = "productDesc"

	// Product Types
	ProductTypeOKEX      int8 = 1  //	OKE产品
	ProductTypeTrading   int8 = 11 //	Trading产品
	ProductTypeIeForex   int8 = 21 //	外汇产品
	ProductTypeCustomize int8 = 31 // 自定义产品
	ProductTypeStaking   int8 = 41 //	质押产品

	// Product Status
	ProductStatusDisabled int8 = -1 // 禁用
	ProductStatusEnabled  int8 = 10 // 启用

	minute int = 60
	hour       = minute * 60
	day        = hour * 24
)

var (
	// BarsOKEX 时间粒度
	BarsOKEX = []*Bar{
		{Label: "1m", Val: "1m", Interval: minute},
		{Label: "3m", Val: "3m", Interval: minute * 3},
		{Label: "5m", Val: "5m", Interval: minute * 5},
		{Label: "15m", Val: "15m", Interval: minute * 15},
		{Label: "30m", Val: "30m", Interval: minute * 30},
		{Label: "1H", Val: "1H", Interval: hour},
		{Label: "2H", Val: "2H", Interval: hour * 2},
		{Label: "4H", Val: "4H", Interval: hour * 4},
	}

	// BarsIeforex 易汇时间粒度
	BarsIeforex = []*Bar{
		{Label: "5m", Val: "5", Interval: minute * 5},
		{Label: "15m", Val: "15", Interval: minute * 15},
		{Label: "30m", Val: "30", Interval: minute * 30},
		{Label: "1H", Val: "60", Interval: hour},
		{Label: "4H", Val: "240", Interval: hour * 4},
		{Label: "1D", Val: "1D", Interval: day},
		{Label: "1W", Val: "1W", Interval: day * 7},
		{Label: "1M", Val: "1M", Interval: day * 30},
	}

	// BarsTrading Trading时间粒度
	BarsTrading = []*Bar{
		{Label: "1m", Val: "1m", Interval: minute},
		{Label: "5m", Val: "5m", Interval: minute * 5},
		{Label: "15m", Val: "15m", Interval: minute * 15},
		{Label: "1H", Val: "1h", Interval: hour},
		{Label: "1D", Val: "1d", Interval: day},
		{Label: "1W", Val: "1w", Interval: day * 7},
		{Label: "1M", Val: "1month", Interval: day * 30},
	}

	// BarsCustomize 自定义产品时间粒度
	BarsCustomize = []*Bar{
		{Label: "1m", Val: "1", Interval: minute},
		{Label: "5m", Val: "5", Interval: minute * 5},
		{Label: "15m", Val: "15", Interval: minute * 15},
		{Label: "30m", Val: "30", Interval: minute * 30},
	}

	StakingStrategyData = []*StakingStrategy{
		{FormatTimePicker: views.FormatTimePicker{Value: 0, Type: views.TimeTypeYear}, Rate: 3, RateCycle: 0},
		{FormatTimePicker: views.FormatTimePicker{Value: 1, Type: views.TimeTypeHour}, Rate: 5, RateCycle: 1},
		{FormatTimePicker: views.FormatTimePicker{Value: 4, Type: views.TimeTypeHour}, Rate: 7, RateCycle: 2},
		{FormatTimePicker: views.FormatTimePicker{Value: 1, Type: views.TimeTypeDay}, Rate: 9, RateCycle: 4},
		{FormatTimePicker: views.FormatTimePicker{Value: 30, Type: views.TimeTypeDay}, Rate: 11, RateCycle: 30},
		{FormatTimePicker: views.FormatTimePicker{Value: 60, Type: views.TimeTypeDay}, Rate: 13, RateCycle: 60},
	}
)

// Product 产品表
type Product struct {
	model.BaseModel
	AdminID     uint                  `gorm:"type:int unsigned not null;index;comment:管理员ID" json:"adminId"`
	CategoryID  uint                  `gorm:"type:int unsigned not null;index;comment:类目ID" json:"categoryId"`
	AssetsID    uint                  `gorm:"type:int unsigned not null;index;comment:资产ID" json:"assetsId"`
	Name        string                `gorm:"type:varchar(64) not null;index;comment:产品名称" json:"name"`
	Symbol      string                `gorm:"type:varchar(64) not null;index;comment:产品标识" json:"symbol"`
	Images      model.GormStringSlice `gorm:"type:text;comment:产品图片" json:"images"`
	Money       float64               `gorm:"type:decimal(16,2) not null;comment:金额" json:"money"`
	Type        int8                  `gorm:"type:tinyint not null;default:1;index;comment:类型(1:默认)" json:"type"`
	Sort        int16                 `gorm:"type:smallint not null;default:99;index;comment:排序" json:"sort"`
	Recommended int8                  `gorm:"type:tinyint not null;default:2;index;comment:是否推荐(1:真,2:假)" json:"recommended"`
	IsTranslate int8                  `gorm:"type:tinyint not null;default:2;index;comment:是否翻译(1:是,2:否)" json:"isTranslate"`
	Status      int8                  `gorm:"type:tinyint not null;default:10;index;comment:状态(-1:禁用,10:启用)" json:"status"`
	Fee         float64               `gorm:"type:decimal(20,6) not null;comment:手续费" json:"fee"`
	Desc        string                `gorm:"type:text;comment:产品描述" json:"desc"`
	Data        ProductData           `gorm:"type:json;comment:配置" json:"data"`
}

// ProductInfo 产品信息
type ProductInfo struct {
	ID               uint                     `json:"id"`                    // 产品Id
	Name             string                   `json:"name"`                  // 产品名
	Symbol           string                   `json:"symbol"`                // 产品标识
	AssetsID         uint                     `json:"assetsId"`              // 产品资产id
	CategoryId       uint                     `json:"categoryId"`            // 产品分类
	Images           model.GormStringSlice    `json:"images"`                // 产品图片
	Type             int8                     `json:"type"`                  // 产品类型
	CategoryType     int8                     `json:"categoryType"`          // 产品分类类型
	IsTranslate      int8                     `json:"isTranslate"`           // 产品分类类型
	IsCollect        bool                     `json:"isCollect"`             // 是否收藏
	Desc             string                   `json:"desc"`                  // 产品描述
	Sort             int16                    `json:"sort"`                  // 排序字段
	Fee              float64                  `json:"fee"`                   // 手续费
	Data             ProductData              `json:"data"`                  // 产品数据
	CreatedAt        time.Time                `json:"createdAt"`             //	创建时间
	UpdatedAt        time.Time                `json:"updatedAt"`             //	更新时间
	AssetsInfo       AssetsInfo               `json:"assets" gorm:"-"`       // 资产信息
	SymbolAssetsInfo AssetsInfo               `json:"symbolAssets" gorm:"-"` // 标识资产信息
	Ticker           interfaces.Tickers       `json:"ticker" gorm:"-"`       // 行情数据
	KlineAttrs       []*interfaces.KlineAttrs `json:"klineAttrs" gorm:"-"`   // kline数据
}

func (p *ProductInfo) TableName() string {
	return "product"
}

// ProductData 产品数据
type ProductData struct {
	Bars                []*Bar             `json:"bars"`
	StakingStrategy     []*StakingStrategy `json:"stakingStrategy"`
	SymbolAssetsID      uint               `json:"symbolAssetsId"`
	SymbolAssetsDecimal int                `json:"symbolAssetsDecimal"`
	AssetsDecimal       int                `json:"assetsDecimal"` // 资产精度
	MinMoney            float64            `json:"minMoney"`      // 最大金额
}

// Value implements the driver.Valuer interface
func (d ProductData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *ProductData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

// StakingStrategy 质押策略
type StakingStrategy struct {
	views.FormatTimePicker
	Rate      float64 `json:"rate"`      //	每期收益
	RateCycle float64 `json:"rateCycle"` //	收益周期
}

// Bar 时间粒度
type Bar struct {
	Label    string `json:"label"`
	Val      string `json:"value"`
	Interval int    `json:"interval"`
}

func init() {
	// 只允许主进程运行
	if fiber.IsChild() {
		return
	}

	db := model.NewModel()

	// Create the table if it doesn't exist
	if err := db.AutoMigrate(&Product{}); err != nil {
		panic("Failed to auto migrate Product table: " + err.Error())
	}

	// Initialize system menus
	if err := InitProductProducts(db.DB); err != nil {
		panic("Failed to initialize product products: " + err.Error())
	}
}

func InitProductProducts(db *gorm.DB) error {
	// Check if the table is empty
	var count int64
	db.Model(&Product{}).Count(&count)

	// If the table is empty, add initial data
	if count == 0 {
		// 现货USDT产品
		initialProducts := []*Product{
			{AdminID: SuperAdminID, CategoryID: 1000, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "BTC/USDT", Symbol: "BTC-USDT", Images: model.GormStringSlice{"/icons/btc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 6, SymbolAssetsID: 1, Bars: BarsOKEX, MinMoney: 0}, Desc: "比特币(BTC)是一种点对点加密货币，旨在充当独立于任何中央机构的一种交易手段。 BTC可以安全，可验证和不变的方式进行电子现金转移。\nBTC于2009年推出，是通过在交易信息广播到比特币网络中的所有节点之前加盖交易时间戳的\"第一种解决双重支出问题的虚拟数字货币\"。比特币协议通过blockchain网络结构为拜占庭容错问题提供了解决方案，该概念最初由Stuart Haber和W. Scott Stornetta创建1991年。\nBitcoin的白皮书于由个人或一个团体以化名\"Satoshi Nakamoto\"于2008年发表，其基本身份尚未得到验证。\n比特币协议使用基于SHA-256d的工作量证明(PoW)算法达成网络共识。其网络的目标出块时间为10分钟，最大供应量为2100万个代币，代币的产生速率不断下降。为了防止拥堵时间的波动，通过基于过去2016年拥堵时间的算法重新调整了网络的出块难度。\n区块大小上限为1兆字节，比特币协议同时支持Lightning Network支付渠道的多层基础结构，以及隔离见证，这是一个增加功能的软分叉作为网络可扩展性的解决方案。\n比特币最大的用途之一是作为线上和线下购买商品和服务的支付媒介。超过 15,000 家企业积极接受比特币，包括微软、星巴克、家得宝、Etsy、新蛋、AT&T、赛百味、汉堡王、肯德基、维珍银河、达拉斯小牛队、挪威航空、特拉瓦拉和必胜客。\n尽管比特币的受欢迎程度和价值多年来都有了巨大增长，同时它也面临着许多批评。一些人认为它不像传统货币那样安全，因为政府或金融机构不支持它。另一些人则声称，比特币实际上并没有用于任何真正的交易，而是像股票或商品一样进行交易。最后，一些批评人士断言，开采比特币所需的能量值不了报酬，而且这个过程最终可能会破坏环境。\n那么，比特币是如何安全地促进交易的呢？比特币网络以区块链的方式运行，这是一个所有比特币交易的公共分类账。它不断增长，“完成块”添加到它与新的录音集。每个块包含前一个块的加密散列、时间戳和交易数据。比特币节点 (使用比特币网络的计算机) 使用区块链来区分合法的比特币交易和试图重新消费已经在其他地方消费过的比特币的行为，这种做法被称为双重消费 (双花)。\n比特币的设计是就为了抵抗审查。比特币交易记录在公共区块链上，可以提高透明度，防止一方控制网络。这使得政府或金融机构很难控制或干预比特币网络或交易。\n新的比特币是通过一种被称为挖矿的计算哈希值过程产生的。当矿工在区块链上验证并记录交易时，他们将获得比特币奖励。矿工使用特殊的软件来解决数学问题，并获得一定数量的比特币作为回报。这激励人们挖矿，并有助于确保新比特币的创造是可预测和公平的。每个区块奖励的比特币数量随着时间的推移而减少，因为网络调整了新区块添加到区块链的速率。目前，矿工每挖出一个区块可以获得 3.125 个比特币的奖励。\n在进行交易之前，你需要一个比特币钱包。比特币钱包是你储存比特币的地方。你可以用这个钱包收发比特币。你可以通过在数字货币交易所 (如八戒交易所) 设立账户或通过专门的提供商获得比特币钱包。\n当你想进行支付时，你只需将比特币发送到收件人的钱包地址，然后由矿工验证交易并记录在区块链上。比特币交易快速、廉价、安全。"},
			{AdminID: SuperAdminID, CategoryID: 1000, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "ETH/USDT", Symbol: "ETH-USDT", Images: model.GormStringSlice{"/icons/eth.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 4, SymbolAssetsID: 2, Bars: BarsOKEX, MinMoney: 0}, Desc: "以太坊是一个去中心化的区块链网络，其数字货币以太币 (ETH) 被广泛应用于创建和部署去中心化应用和智能合约，同时还可作为一种数字支付方式。\n\n作为市值第二大的数字货币，以太坊通过引入智能合约功能改变了行业格局，让用户和开发者能够进入去中心化金融 (DeFi) 等新兴领域。以太坊推动了价值数十亿美元的行业的发展，包括 DeFi、“边玩边赚”数字货币游戏和热门的 NFT 市场等。\n\n以太币 (ETH) 是以太坊区块链的原生代币，用于支付网络上的交易手续费，提供 PoS 共识机制下的质押奖励，并作为 NFT 等数字资产的货币交换媒介。\n\n以太坊引入了 ERC-20、ERC-721 和 ERC-1155 等代币标准，促进了同质化和非同质化代币的产生。这些标准在各个行业的增长中发挥了重要作用，尤其是 NFT 市场。\n\n以太坊虚拟机 (EVM) 是以太坊区块链的重要组成部分，为以太坊账户和智能合约提供了一个运行环境。它充当一个分布式计算机，执行着由运行以太坊客户端的互连节点维护的数百万个项目。\n\n上海升级为以太坊平台带来了一系列技术提升。其中引入的一项主要功能是用户能够访问和取消质押以太坊代币。这些代币此前由信标链的验证者锁定在智能合约中。\n\n上海升级之前，如果用户要成为采用权益证明(PoS)的以太坊区块链验证者，则需将代币锁定在智能合约中，从而无法自由访问或转账这些代币。但最新升级后，以太坊用户现在可以选择按需取消质押代币，从而能更自如地掌控个人资产。\n"},
			{AdminID: SuperAdminID, CategoryID: 1000, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "SOL/USDT", Symbol: "SOL-USDT", Images: model.GormStringSlice{"/icons/sol.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 6, Bars: BarsOKEX, MinMoney: 0}, Desc: "Solana是一个独立的L1区块链，它拥有底层智能合约协议，速度快、效率高。自2020年推出以来，Solana网络一直被视为以太坊的竞争对手，人们甚至戏称其为“以太坊杀手”。 \n\nSolana网络可扩展性强、费率低且速度快，成为了许多非同质化代币(NFT)去中心化应用程序 (DApp)、去中心化金融 (DeFi)和数字区块链支付生态系统的首选。\n\nSolana网络采用历史证明(PoH)技术，专注于提升交易吞吐量和处理速度。Solana声称每秒可处理大约65,000笔交易，交易速度业内领先。\n\nSolana区块链线性散列交易以创建所有网络活动的可验证顺序，从而实现快速交易。因此，它无需依赖区块创建者的时间戳，网络验证者也无需检查交易是否以正确的顺序进行。\n\nSolana网络由系统工程师兼计算机程序员Anatoly Yakovenko和现任Solana首席运营官Raj Gokal于2017年联合创立。Solana网络在PoH计时机制上运行，该机制是在目前采用权益证明算法的共识层之前部署的。\n\nSolana的原生代币SOL主要用于质押以支持验证过程，还可用于支付点对点(C2C)交易费用。Solana通过无限代币供应以最大限度地实现这些功能。然而，为维持其同比通货膨胀率，Solana会销毁每笔交易费用中50%的SOL。\n\n而剩余50%的交易费用会奖励给交易验证者。只要持有足够的SOL，任何人均可成为网络验证者或验证者委托人，为运行该独立区块链所需的共识过程提供支持。在该机制下，质押SOL以支持Solana区块链的用户可赚取奖励。Solana价格在币安上实时更新。\n\n2022年8月3日早些时候，Solana网络出现了重大漏洞。运行在Solana网络上的去中心化钱包应用Phantom和Slope在社交媒体上公布称，它们遭到了黑客攻击。\n\n这些黑客盗取了约8,000个钱包中总价值约800万美元的SOL。据发现，被盗钱包均与iOS和Android移动操作系统中的其他应用程序进行了交互。关于黑客攻击来源以及被盗钱包所有者是否会得到补偿，目前仍未可知。\n"},
			{AdminID: SuperAdminID, CategoryID: 1000, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "TRX/USDT", Symbol: "TRX-USDT", Images: model.GormStringSlice{"/icons/trx.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 7, Bars: BarsOKEX, MinMoney: 0}, Desc: "波场TRON以推动互联网去中心化为己任，致力于为去中心化互联网搭建基础设施。旗下的TRON协议是全球最大的基于区块链的去中心化应用操作系统协 议之一，为协议上的去中心化应用运行提供高吞吐，高扩展，高可靠性的底层公链支持。波场TRON还通过创新的可插拔智能合约平台为以太坊智能合约提供更好的兼容性。\n自2018年7月24日起，TRON收购了位于旧金山的互联网技术公司BitTorrent Inc.。 BitTorrent Inc.设计的分布式技术能够有效扩展，保持智能化，并使创作者和消费者能够控制其内容和数据。 每个月有超过1.7亿人使用BitTorrent Inc.开发的产品。 BitTorrent Inc.的协议每天可以传输全球40％的互联网流量。"},
			{AdminID: SuperAdminID, CategoryID: 1000, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "XRP/USDT", Symbol: "XRP-USDT", Images: model.GormStringSlice{"/icons/xrp.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 8, Bars: BarsOKEX, MinMoney: 0}, Desc: "XRP 是 XRP 账本（XRPL） 区块链上的原生数字资产，最初是为支付而构建的XRP 主要促进网络上的交易，并在 XRP 账本的原生 DEX 中桥接货币。XRP 是 XRP 账本 原生的数字资产，XRP 账本是一种开源、无需许可和去中心化的区块链技术XRP 创建于 2012 年，专门用于支付，可以在 3-5 秒内结算公开账目上的交易，使用受信任的验证者网络来验证账本上的交易。XRP 可以直接发送，无需中央中介，使其成为快速有效地桥接两种不同货币的便捷工具。它在公开市场上自由交换，并在现实世界中用于实现跨境支付和微交易。XRP 还可用于兑换不同的货币和访问加密流动性\nXRPL 还具有第一个内置于协议中的去中心化交易所 （DEX） 和自定义代币化功能。自 2012 年以来，XRPL 一直可靠运行，已关闭了 7000 万个分类账。"},
			{AdminID: SuperAdminID, CategoryID: 1000, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "ADA/USDT", Symbol: "ADA-USDT", Images: model.GormStringSlice{"/icons/ada.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 9, Bars: BarsOKEX, MinMoney: 0}, Desc: "Cardano 是一个区块链平台，旨在改进以太坊和比特币的功能。它基于多种设计组件，包括dApp开发平台，支持多资产的分类账和可验证的智能合约。它使用节能且可扩展的权益证明共识机制。 ADA 是为网络提供动力并实现治理的加密货币。 Cardano 由专家团队采用同行评审和循证方法开发。 Cardano支持智能合约和去中心化应用，并经历了多次升级以增强其能力。"},
			{AdminID: SuperAdminID, CategoryID: 1000, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "ATOM/USDT", Symbol: "ATOM-USDT", Images: model.GormStringSlice{"/icons/atom.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 10, Bars: BarsOKEX, MinMoney: 0}, Desc: "Cosmos项目是由Tendermint团队于2016年发起具有高度前瞻创新性的区块链跨链项目。Cosmos项目的目标是通过实现代币转移这样一个基础的功能最终实现“区块链的互联网”，并构造一个深度集成的代币经济生态系统。\nCosmos 网络由许多独立的并行区块链（称为区域）组成，每个区块均由 Tendermint 之类的经典拜占庭容错 (BFT) 共识协议提供支持 (已被 ErisDB 等平台使用)。某些区域相对于其他区域充当集线器，从而允许许多区域通过共享的集线器进行互操作。该架构是比特币侧链概念的一个更通用的应用，使用经典的 BFT 和权益证明算法，而不是工作量证明。 Cosmos 可以与其他多种应用程序和加密货币进行互操作，而其他区块链则无法做到这一点。通过创建一个新区域，您可以将任何区块链系统插入 Cosmos 集线器，并在这些区域之间来回传递令牌，而无需中介。"},
			{AdminID: SuperAdminID, CategoryID: 1000, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "DOGE/USDT", Symbol: "DOGE-USDT", Images: model.GormStringSlice{"/icons/doge.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 11, Bars: BarsOKEX, MinMoney: 0}, Desc: "Dogecoin 是一种基于 Shiba Inu (柴犬) 模因的点对点加密货币，在社交媒体上迅速走红。起初，Dogecoin 是作为对当时推出的其他加密货币项目的调侃而创建的，但它迅速发展出一批忠实的粉丝群体，并围绕这个代币发现和开发了新的应用场景。它被视为第一个“模因币”。狗狗币值得注意的一个方面是，它借用了现已解散的Luckycoin所使用的基于scrypt的工作量证明(PoW)共识算法，该算法借用了大部分莱特币的技术。尽管最初开发的本地代币 DOGE 没有特定的实用性，但多年来作为数字货币的使用逐渐增加。截至 2022 年 9 月，全球有超过 2,000 家商户接受 DOGE 作为支付方式。"},
			{AdminID: SuperAdminID, CategoryID: 1000, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "ETC/USDT", Symbol: "ETC-USDT", Images: model.GormStringSlice{"/icons/etc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 12, Bars: BarsOKEX, MinMoney: 0}, Desc: "以太坊经典（Ethereum）是一个去中心化智能合约支持的网络，旨在成为一个全球支付系统。起源于以太坊区块链网络，以太坊经典使用工作量证明共识机制（POW），支持去中心化应用。以太坊经典是在最初的以太坊区块链因为第一次攻击而分裂后出现的。一群坚持区块链核心价值的开发者组成Ethereum Classic，发行独立的加密货币ETC;作为一种完整的货币，吸引了之前错失投资机会的一批人，而且与原有以太坊的相似之处，也是一种坚守以太坊模式的方式。"},
			{AdminID: SuperAdminID, CategoryID: 1000, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "LTC/USDT", Symbol: "LTC-USDT", Images: model.GormStringSlice{"/icons/ltc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 13, Bars: BarsOKEX, MinMoney: 0}, Desc: "Litecoin（LTC）于2011年作为比特币网络的一个分叉开发，旨在解决比特币的一些限制。它旨在提供一个分散的点对点货币，具有更快的交易处理时间和更低的费用。Litecoin受到了比特币（BTC）的启发，并且在技术上具有相同的实现原理，莱特币的创造和转让基于一种开源的加密协议，不受到任何中央机构的管理。莱特币旨在改进比特币。\nLitecoin的主要目标之一是防止比特币中观察到的挖掘集中化。它通过使用称为Scrypt的不同算法来实现这一目标，这使得挖掘更加占用内存和更慢。尽管矿场最终获得了对Litecoin挖矿的控制权，但Litecoin的重点已转向成为一个高效的支付系统。\n"},
			{AdminID: SuperAdminID, CategoryID: 1000, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "UNI/USDT", Symbol: "UNI-USDT", Images: model.GormStringSlice{"/icons/uni.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 14, Bars: BarsOKEX, MinMoney: 0}, Desc: "Uniswap 是一个去中心化的加密货币交易协议 DEX，构建在以太坊区块链上。它的第一个版本(Uniswap v1)自2018年11月开始运行(在DevCon 4上发布)，而第二个版本(Uniswap v2)于2020年5月发布。它允许用户以对等的方式直接交易以太坊及其他 ERC-20 代币，无需传统的交易所中介。Uniswap 采用自动化做市商模型，通过流动性池和智能合约来自动匹配买卖订单，提供了高度的流动性和即时的交易体验，同时为用户提供了参与流动性提供和获取奖励的机会。"},
			{AdminID: SuperAdminID, CategoryID: 1000, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "TRUMP/USDT", Symbol: "TRUMP-USDT", Images: model.GormStringSlice{"/icons/trump.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 15, Bars: BarsOKEX, MinMoney: 0}, Desc: "TrumpCoin 是一个去中心化的加密货币，基于以太坊区块链构建。它旨在提供安全、透明和无需许可的交易方式，允许用户自由地在全球范围内转移价值。TrumpCoin 采用智能合约技术，确保所有交易的可信度，并支持流动性提供、质押等 DeFi 生态功能。作为 ERC-20 代币，TrumpCoin 兼容以太坊上的各种 DEX 和钱包，为用户带来便捷的交易体验和资产管理能力。"},
		}

		// 现货USD产品
		initialProducts = append(initialProducts, []*Product{
			{AdminID: SuperAdminID, CategoryID: 1100, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "BTC/USD", Symbol: "BTC-USDT", Images: model.GormStringSlice{"/icons/btc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 6, SymbolAssetsID: 1, Bars: BarsOKEX, MinMoney: 0}, Desc: "比特币(BTC)是一种点对点加密货币，旨在充当独立于任何中央机构的一种交易手段。 BTC可以安全，可验证和不变的方式进行电子现金转移。\nBTC于2009年推出，是通过在交易信息广播到比特币网络中的所有节点之前加盖交易时间戳的\"第一种解决双重支出问题的虚拟数字货币\"。比特币协议通过blockchain网络结构为拜占庭容错问题提供了解决方案，该概念最初由Stuart Haber和W. Scott Stornetta创建1991年。\nBitcoin的白皮书于由个人或一个团体以化名\"Satoshi Nakamoto\"于2008年发表，其基本身份尚未得到验证。\n比特币协议使用基于SHA-256d的工作量证明(PoW)算法达成网络共识。其网络的目标出块时间为10分钟，最大供应量为2100万个代币，代币的产生速率不断下降。为了防止拥堵时间的波动，通过基于过去2016年拥堵时间的算法重新调整了网络的出块难度。\n区块大小上限为1兆字节，比特币协议同时支持Lightning Network支付渠道的多层基础结构，以及隔离见证，这是一个增加功能的软分叉作为网络可扩展性的解决方案。\n比特币最大的用途之一是作为线上和线下购买商品和服务的支付媒介。超过 15,000 家企业积极接受比特币，包括微软、星巴克、家得宝、Etsy、新蛋、AT&T、赛百味、汉堡王、肯德基、维珍银河、达拉斯小牛队、挪威航空、特拉瓦拉和必胜客。\n尽管比特币的受欢迎程度和价值多年来都有了巨大增长，同时它也面临着许多批评。一些人认为它不像传统货币那样安全，因为政府或金融机构不支持它。另一些人则声称，比特币实际上并没有用于任何真正的交易，而是像股票或商品一样进行交易。最后，一些批评人士断言，开采比特币所需的能量值不了报酬，而且这个过程最终可能会破坏环境。\n那么，比特币是如何安全地促进交易的呢？比特币网络以区块链的方式运行，这是一个所有比特币交易的公共分类账。它不断增长，“完成块”添加到它与新的录音集。每个块包含前一个块的加密散列、时间戳和交易数据。比特币节点 (使用比特币网络的计算机) 使用区块链来区分合法的比特币交易和试图重新消费已经在其他地方消费过的比特币的行为，这种做法被称为双重消费 (双花)。\n比特币的设计是就为了抵抗审查。比特币交易记录在公共区块链上，可以提高透明度，防止一方控制网络。这使得政府或金融机构很难控制或干预比特币网络或交易。\n新的比特币是通过一种被称为挖矿的计算哈希值过程产生的。当矿工在区块链上验证并记录交易时，他们将获得比特币奖励。矿工使用特殊的软件来解决数学问题，并获得一定数量的比特币作为回报。这激励人们挖矿，并有助于确保新比特币的创造是可预测和公平的。每个区块奖励的比特币数量随着时间的推移而减少，因为网络调整了新区块添加到区块链的速率。目前，矿工每挖出一个区块可以获得 3.125 个比特币的奖励。\n在进行交易之前，你需要一个比特币钱包。比特币钱包是你储存比特币的地方。你可以用这个钱包收发比特币。你可以通过在数字货币交易所 (如八戒交易所) 设立账户或通过专门的提供商获得比特币钱包。\n当你想进行支付时，你只需将比特币发送到收件人的钱包地址，然后由矿工验证交易并记录在区块链上。比特币交易快速、廉价、安全。"},
			{AdminID: SuperAdminID, CategoryID: 1100, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "ETH/USD", Symbol: "ETH-USDT", Images: model.GormStringSlice{"/icons/eth.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 4, SymbolAssetsID: 2, Bars: BarsOKEX, MinMoney: 0}, Desc: "以太坊是一个去中心化的区块链网络，其数字货币以太币 (ETH) 被广泛应用于创建和部署去中心化应用和智能合约，同时还可作为一种数字支付方式。\n\n作为市值第二大的数字货币，以太坊通过引入智能合约功能改变了行业格局，让用户和开发者能够进入去中心化金融 (DeFi) 等新兴领域。以太坊推动了价值数十亿美元的行业的发展，包括 DeFi、“边玩边赚”数字货币游戏和热门的 NFT 市场等。\n\n以太币 (ETH) 是以太坊区块链的原生代币，用于支付网络上的交易手续费，提供 PoS 共识机制下的质押奖励，并作为 NFT 等数字资产的货币交换媒介。\n\n以太坊引入了 ERC-20、ERC-721 和 ERC-1155 等代币标准，促进了同质化和非同质化代币的产生。这些标准在各个行业的增长中发挥了重要作用，尤其是 NFT 市场。\n\n以太坊虚拟机 (EVM) 是以太坊区块链的重要组成部分，为以太坊账户和智能合约提供了一个运行环境。它充当一个分布式计算机，执行着由运行以太坊客户端的互连节点维护的数百万个项目。\n\n上海升级为以太坊平台带来了一系列技术提升。其中引入的一项主要功能是用户能够访问和取消质押以太坊代币。这些代币此前由信标链的验证者锁定在智能合约中。\n\n上海升级之前，如果用户要成为采用权益证明(PoS)的以太坊区块链验证者，则需将代币锁定在智能合约中，从而无法自由访问或转账这些代币。但最新升级后，以太坊用户现在可以选择按需取消质押代币，从而能更自如地掌控个人资产。\n"},
			{AdminID: SuperAdminID, CategoryID: 1100, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "SOL/USD", Symbol: "SOL-USDT", Images: model.GormStringSlice{"/icons/sol.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 6, Bars: BarsOKEX, MinMoney: 0}, Desc: "Solana是一个独立的L1区块链，它拥有底层智能合约协议，速度快、效率高。自2020年推出以来，Solana网络一直被视为以太坊的竞争对手，人们甚至戏称其为“以太坊杀手”。 \n\nSolana网络可扩展性强、费率低且速度快，成为了许多非同质化代币(NFT)去中心化应用程序 (DApp)、去中心化金融 (DeFi)和数字区块链支付生态系统的首选。\n\nSolana网络采用历史证明(PoH)技术，专注于提升交易吞吐量和处理速度。Solana声称每秒可处理大约65,000笔交易，交易速度业内领先。\n\nSolana区块链线性散列交易以创建所有网络活动的可验证顺序，从而实现快速交易。因此，它无需依赖区块创建者的时间戳，网络验证者也无需检查交易是否以正确的顺序进行。\n\nSolana网络由系统工程师兼计算机程序员Anatoly Yakovenko和现任Solana首席运营官Raj Gokal于2017年联合创立。Solana网络在PoH计时机制上运行，该机制是在目前采用权益证明算法的共识层之前部署的。\n\nSolana的原生代币SOL主要用于质押以支持验证过程，还可用于支付点对点(C2C)交易费用。Solana通过无限代币供应以最大限度地实现这些功能。然而，为维持其同比通货膨胀率，Solana会销毁每笔交易费用中50%的SOL。\n\n而剩余50%的交易费用会奖励给交易验证者。只要持有足够的SOL，任何人均可成为网络验证者或验证者委托人，为运行该独立区块链所需的共识过程提供支持。在该机制下，质押SOL以支持Solana区块链的用户可赚取奖励。Solana价格在币安上实时更新。\n\n2022年8月3日早些时候，Solana网络出现了重大漏洞。运行在Solana网络上的去中心化钱包应用Phantom和Slope在社交媒体上公布称，它们遭到了黑客攻击。\n\n这些黑客盗取了约8,000个钱包中总价值约800万美元的SOL。据发现，被盗钱包均与iOS和Android移动操作系统中的其他应用程序进行了交互。关于黑客攻击来源以及被盗钱包所有者是否会得到补偿，目前仍未可知。\n"},
			{AdminID: SuperAdminID, CategoryID: 1100, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "XRP/USD", Symbol: "XRP-USDT", Images: model.GormStringSlice{"/icons/xrp.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 8, Bars: BarsOKEX, MinMoney: 0}, Desc: "XRP 是 XRP 账本（XRPL） 区块链上的原生数字资产，最初是为支付而构建的XRP 主要促进网络上的交易，并在 XRP 账本的原生 DEX 中桥接货币。XRP 是 XRP 账本 原生的数字资产，XRP 账本是一种开源、无需许可和去中心化的区块链技术XRP 创建于 2012 年，专门用于支付，可以在 3-5 秒内结算公开账目上的交易，使用受信任的验证者网络来验证账本上的交易。XRP 可以直接发送，无需中央中介，使其成为快速有效地桥接两种不同货币的便捷工具。它在公开市场上自由交换，并在现实世界中用于实现跨境支付和微交易。XRP 还可用于兑换不同的货币和访问加密流动性\nXRPL 还具有第一个内置于协议中的去中心化交易所 （DEX） 和自定义代币化功能。自 2012 年以来，XRPL 一直可靠运行，已关闭了 7000 万个分类账。"},
			{AdminID: SuperAdminID, CategoryID: 1100, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "DOGE/USD", Symbol: "DOGE-USDT", Images: model.GormStringSlice{"/icons/doge.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 11, Bars: BarsOKEX, MinMoney: 0}, Desc: "Dogecoin 是一种基于 Shiba Inu (柴犬) 模因的点对点加密货币，在社交媒体上迅速走红。起初，Dogecoin 是作为对当时推出的其他加密货币项目的调侃而创建的，但它迅速发展出一批忠实的粉丝群体，并围绕这个代币发现和开发了新的应用场景。它被视为第一个“模因币”。狗狗币值得注意的一个方面是，它借用了现已解散的Luckycoin所使用的基于scrypt的工作量证明(PoW)共识算法，该算法借用了大部分莱特币的技术。尽管最初开发的本地代币 DOGE 没有特定的实用性，但多年来作为数字货币的使用逐渐增加。截至 2022 年 9 月，全球有超过 2,000 家商户接受 DOGE 作为支付方式。"},
			{AdminID: SuperAdminID, CategoryID: 1100, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "ETC/USD", Symbol: "ETC-USDT", Images: model.GormStringSlice{"/icons/etc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 12, Bars: BarsOKEX, MinMoney: 0}, Desc: "以太坊经典（Ethereum）是一个去中心化智能合约支持的网络，旨在成为一个全球支付系统。起源于以太坊区块链网络，以太坊经典使用工作量证明共识机制（POW），支持去中心化应用。以太坊经典是在最初的以太坊区块链因为第一次攻击而分裂后出现的。一群坚持区块链核心价值的开发者组成Ethereum Classic，发行独立的加密货币ETC;作为一种完整的货币，吸引了之前错失投资机会的一批人，而且与原有以太坊的相似之处，也是一种坚守以太坊模式的方式。"},
			{AdminID: SuperAdminID, CategoryID: 1100, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "LTC/USD", Symbol: "LTC-USDT", Images: model.GormStringSlice{"/icons/ltc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 13, Bars: BarsOKEX, MinMoney: 0}, Desc: "Litecoin（LTC）于2011年作为比特币网络的一个分叉开发，旨在解决比特币的一些限制。它旨在提供一个分散的点对点货币，具有更快的交易处理时间和更低的费用。Litecoin受到了比特币（BTC）的启发，并且在技术上具有相同的实现原理，莱特币的创造和转让基于一种开源的加密协议，不受到任何中央机构的管理。莱特币旨在改进比特币。\nLitecoin的主要目标之一是防止比特币中观察到的挖掘集中化。它通过使用称为Scrypt的不同算法来实现这一目标，这使得挖掘更加占用内存和更慢。尽管矿场最终获得了对Litecoin挖矿的控制权，但Litecoin的重点已转向成为一个高效的支付系统。\n"},
			{AdminID: SuperAdminID, CategoryID: 1100, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "TRUMP/USD", Symbol: "TRUMP-USDT", Images: model.GormStringSlice{"/icons/trump.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 15, Bars: BarsOKEX, MinMoney: 0}, Desc: "TrumpCoin 是一个去中心化的加密货币，基于以太坊区块链构建。它旨在提供安全、透明和无需许可的交易方式，允许用户自由地在全球范围内转移价值。TrumpCoin 采用智能合约技术，确保所有交易的可信度，并支持流动性提供、质押等 DeFi 生态功能。作为 ERC-20 代币，TrumpCoin 兼容以太坊上的各种 DEX 和钱包，为用户带来便捷的交易体验和资产管理能力。"},
		}...)

		// 合约USDT产品
		initialProducts = append(initialProducts, []*Product{
			{AdminID: SuperAdminID, CategoryID: 200, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "BTCUSDT", Symbol: "BTC-USDT", Images: model.GormStringSlice{"/icons/btc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 6, SymbolAssetsID: 1, Bars: BarsOKEX, MinMoney: 0}, Desc: "比特币(BTC)是一种点对点加密货币，旨在充当独立于任何中央机构的一种交易手段。 BTC可以安全，可验证和不变的方式进行电子现金转移。\nBTC于2009年推出，是通过在交易信息广播到比特币网络中的所有节点之前加盖交易时间戳的\"第一种解决双重支出问题的虚拟数字货币\"。比特币协议通过blockchain网络结构为拜占庭容错问题提供了解决方案，该概念最初由Stuart Haber和W. Scott Stornetta创建1991年。\nBitcoin的白皮书于由个人或一个团体以化名\"Satoshi Nakamoto\"于2008年发表，其基本身份尚未得到验证。\n比特币协议使用基于SHA-256d的工作量证明(PoW)算法达成网络共识。其网络的目标出块时间为10分钟，最大供应量为2100万个代币，代币的产生速率不断下降。为了防止拥堵时间的波动，通过基于过去2016年拥堵时间的算法重新调整了网络的出块难度。\n区块大小上限为1兆字节，比特币协议同时支持Lightning Network支付渠道的多层基础结构，以及隔离见证，这是一个增加功能的软分叉作为网络可扩展性的解决方案。\n比特币最大的用途之一是作为线上和线下购买商品和服务的支付媒介。超过 15,000 家企业积极接受比特币，包括微软、星巴克、家得宝、Etsy、新蛋、AT&T、赛百味、汉堡王、肯德基、维珍银河、达拉斯小牛队、挪威航空、特拉瓦拉和必胜客。\n尽管比特币的受欢迎程度和价值多年来都有了巨大增长，同时它也面临着许多批评。一些人认为它不像传统货币那样安全，因为政府或金融机构不支持它。另一些人则声称，比特币实际上并没有用于任何真正的交易，而是像股票或商品一样进行交易。最后，一些批评人士断言，开采比特币所需的能量值不了报酬，而且这个过程最终可能会破坏环境。\n那么，比特币是如何安全地促进交易的呢？比特币网络以区块链的方式运行，这是一个所有比特币交易的公共分类账。它不断增长，“完成块”添加到它与新的录音集。每个块包含前一个块的加密散列、时间戳和交易数据。比特币节点 (使用比特币网络的计算机) 使用区块链来区分合法的比特币交易和试图重新消费已经在其他地方消费过的比特币的行为，这种做法被称为双重消费 (双花)。\n比特币的设计是就为了抵抗审查。比特币交易记录在公共区块链上，可以提高透明度，防止一方控制网络。这使得政府或金融机构很难控制或干预比特币网络或交易。\n新的比特币是通过一种被称为挖矿的计算哈希值过程产生的。当矿工在区块链上验证并记录交易时，他们将获得比特币奖励。矿工使用特殊的软件来解决数学问题，并获得一定数量的比特币作为回报。这激励人们挖矿，并有助于确保新比特币的创造是可预测和公平的。每个区块奖励的比特币数量随着时间的推移而减少，因为网络调整了新区块添加到区块链的速率。目前，矿工每挖出一个区块可以获得 3.125 个比特币的奖励。\n在进行交易之前，你需要一个比特币钱包。比特币钱包是你储存比特币的地方。你可以用这个钱包收发比特币。你可以通过在数字货币交易所 (如八戒交易所) 设立账户或通过专门的提供商获得比特币钱包。\n当你想进行支付时，你只需将比特币发送到收件人的钱包地址，然后由矿工验证交易并记录在区块链上。比特币交易快速、廉价、安全。"},
			{AdminID: SuperAdminID, CategoryID: 200, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "ETHUSDT", Symbol: "ETH-USDT", Images: model.GormStringSlice{"/icons/eth.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 4, SymbolAssetsID: 2, Bars: BarsOKEX, MinMoney: 0}, Desc: "以太坊是一个去中心化的区块链网络，其数字货币以太币 (ETH) 被广泛应用于创建和部署去中心化应用和智能合约，同时还可作为一种数字支付方式。\n\n作为市值第二大的数字货币，以太坊通过引入智能合约功能改变了行业格局，让用户和开发者能够进入去中心化金融 (DeFi) 等新兴领域。以太坊推动了价值数十亿美元的行业的发展，包括 DeFi、“边玩边赚”数字货币游戏和热门的 NFT 市场等。\n\n以太币 (ETH) 是以太坊区块链的原生代币，用于支付网络上的交易手续费，提供 PoS 共识机制下的质押奖励，并作为 NFT 等数字资产的货币交换媒介。\n\n以太坊引入了 ERC-20、ERC-721 和 ERC-1155 等代币标准，促进了同质化和非同质化代币的产生。这些标准在各个行业的增长中发挥了重要作用，尤其是 NFT 市场。\n\n以太坊虚拟机 (EVM) 是以太坊区块链的重要组成部分，为以太坊账户和智能合约提供了一个运行环境。它充当一个分布式计算机，执行着由运行以太坊客户端的互连节点维护的数百万个项目。\n\n上海升级为以太坊平台带来了一系列技术提升。其中引入的一项主要功能是用户能够访问和取消质押以太坊代币。这些代币此前由信标链的验证者锁定在智能合约中。\n\n上海升级之前，如果用户要成为采用权益证明(PoS)的以太坊区块链验证者，则需将代币锁定在智能合约中，从而无法自由访问或转账这些代币。但最新升级后，以太坊用户现在可以选择按需取消质押代币，从而能更自如地掌控个人资产。\n"},
			{AdminID: SuperAdminID, CategoryID: 200, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "SOLUSDT", Symbol: "SOL-USDT", Images: model.GormStringSlice{"/icons/sol.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 6, Bars: BarsOKEX, MinMoney: 0}, Desc: "Solana是一个独立的L1区块链，它拥有底层智能合约协议，速度快、效率高。自2020年推出以来，Solana网络一直被视为以太坊的竞争对手，人们甚至戏称其为“以太坊杀手”。 \n\nSolana网络可扩展性强、费率低且速度快，成为了许多非同质化代币(NFT)去中心化应用程序 (DApp)、去中心化金融 (DeFi)和数字区块链支付生态系统的首选。\n\nSolana网络采用历史证明(PoH)技术，专注于提升交易吞吐量和处理速度。Solana声称每秒可处理大约65,000笔交易，交易速度业内领先。\n\nSolana区块链线性散列交易以创建所有网络活动的可验证顺序，从而实现快速交易。因此，它无需依赖区块创建者的时间戳，网络验证者也无需检查交易是否以正确的顺序进行。\n\nSolana网络由系统工程师兼计算机程序员Anatoly Yakovenko和现任Solana首席运营官Raj Gokal于2017年联合创立。Solana网络在PoH计时机制上运行，该机制是在目前采用权益证明算法的共识层之前部署的。\n\nSolana的原生代币SOL主要用于质押以支持验证过程，还可用于支付点对点(C2C)交易费用。Solana通过无限代币供应以最大限度地实现这些功能。然而，为维持其同比通货膨胀率，Solana会销毁每笔交易费用中50%的SOL。\n\n而剩余50%的交易费用会奖励给交易验证者。只要持有足够的SOL，任何人均可成为网络验证者或验证者委托人，为运行该独立区块链所需的共识过程提供支持。在该机制下，质押SOL以支持Solana区块链的用户可赚取奖励。Solana价格在币安上实时更新。\n\n2022年8月3日早些时候，Solana网络出现了重大漏洞。运行在Solana网络上的去中心化钱包应用Phantom和Slope在社交媒体上公布称，它们遭到了黑客攻击。\n\n这些黑客盗取了约8,000个钱包中总价值约800万美元的SOL。据发现，被盗钱包均与iOS和Android移动操作系统中的其他应用程序进行了交互。关于黑客攻击来源以及被盗钱包所有者是否会得到补偿，目前仍未可知。\n"},
			{AdminID: SuperAdminID, CategoryID: 200, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "TRXUSDT", Symbol: "TRX-USDT", Images: model.GormStringSlice{"/icons/trx.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 7, Bars: BarsOKEX, MinMoney: 0}, Desc: "波场TRON以推动互联网去中心化为己任，致力于为去中心化互联网搭建基础设施。旗下的TRON协议是全球最大的基于区块链的去中心化应用操作系统协 议之一，为协议上的去中心化应用运行提供高吞吐，高扩展，高可靠性的底层公链支持。波场TRON还通过创新的可插拔智能合约平台为以太坊智能合约提供更好的兼容性。\n自2018年7月24日起，TRON收购了位于旧金山的互联网技术公司BitTorrent Inc.。 BitTorrent Inc.设计的分布式技术能够有效扩展，保持智能化，并使创作者和消费者能够控制其内容和数据。 每个月有超过1.7亿人使用BitTorrent Inc.开发的产品。 BitTorrent Inc.的协议每天可以传输全球40％的互联网流量。"},
			{AdminID: SuperAdminID, CategoryID: 200, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "XRPUSDT", Symbol: "XRP-USDT", Images: model.GormStringSlice{"/icons/xrp.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 8, Bars: BarsOKEX, MinMoney: 0}, Desc: "XRP 是 XRP 账本（XRPL） 区块链上的原生数字资产，最初是为支付而构建的XRP 主要促进网络上的交易，并在 XRP 账本的原生 DEX 中桥接货币。XRP 是 XRP 账本 原生的数字资产，XRP 账本是一种开源、无需许可和去中心化的区块链技术XRP 创建于 2012 年，专门用于支付，可以在 3-5 秒内结算公开账目上的交易，使用受信任的验证者网络来验证账本上的交易。XRP 可以直接发送，无需中央中介，使其成为快速有效地桥接两种不同货币的便捷工具。它在公开市场上自由交换，并在现实世界中用于实现跨境支付和微交易。XRP 还可用于兑换不同的货币和访问加密流动性\nXRPL 还具有第一个内置于协议中的去中心化交易所 （DEX） 和自定义代币化功能。自 2012 年以来，XRPL 一直可靠运行，已关闭了 7000 万个分类账。"},
			{AdminID: SuperAdminID, CategoryID: 200, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "ADAUSDT", Symbol: "ADA-USDT", Images: model.GormStringSlice{"/icons/ada.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 9, Bars: BarsOKEX, MinMoney: 0}, Desc: "Cardano 是一个区块链平台，旨在改进以太坊和比特币的功能。它基于多种设计组件，包括dApp开发平台，支持多资产的分类账和可验证的智能合约。它使用节能且可扩展的权益证明共识机制。 ADA 是为网络提供动力并实现治理的加密货币。 Cardano 由专家团队采用同行评审和循证方法开发。 Cardano支持智能合约和去中心化应用，并经历了多次升级以增强其能力。"},
			{AdminID: SuperAdminID, CategoryID: 200, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "ATOMUSDT", Symbol: "ATOM-USDT", Images: model.GormStringSlice{"/icons/atom.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 10, Bars: BarsOKEX, MinMoney: 0}, Desc: "Cosmos项目是由Tendermint团队于2016年发起具有高度前瞻创新性的区块链跨链项目。Cosmos项目的目标是通过实现代币转移这样一个基础的功能最终实现“区块链的互联网”，并构造一个深度集成的代币经济生态系统。\nCosmos 网络由许多独立的并行区块链（称为区域）组成，每个区块均由 Tendermint 之类的经典拜占庭容错 (BFT) 共识协议提供支持 (已被 ErisDB 等平台使用)。某些区域相对于其他区域充当集线器，从而允许许多区域通过共享的集线器进行互操作。该架构是比特币侧链概念的一个更通用的应用，使用经典的 BFT 和权益证明算法，而不是工作量证明。 Cosmos 可以与其他多种应用程序和加密货币进行互操作，而其他区块链则无法做到这一点。通过创建一个新区域，您可以将任何区块链系统插入 Cosmos 集线器，并在这些区域之间来回传递令牌，而无需中介。"},
			{AdminID: SuperAdminID, CategoryID: 200, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "DOGEUSDT", Symbol: "DOGE-USDT", Images: model.GormStringSlice{"/icons/doge.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 11, Bars: BarsOKEX, MinMoney: 0}, Desc: "Dogecoin 是一种基于 Shiba Inu (柴犬) 模因的点对点加密货币，在社交媒体上迅速走红。起初，Dogecoin 是作为对当时推出的其他加密货币项目的调侃而创建的，但它迅速发展出一批忠实的粉丝群体，并围绕这个代币发现和开发了新的应用场景。它被视为第一个“模因币”。狗狗币值得注意的一个方面是，它借用了现已解散的Luckycoin所使用的基于scrypt的工作量证明(PoW)共识算法，该算法借用了大部分莱特币的技术。尽管最初开发的本地代币 DOGE 没有特定的实用性，但多年来作为数字货币的使用逐渐增加。截至 2022 年 9 月，全球有超过 2,000 家商户接受 DOGE 作为支付方式。"},
			{AdminID: SuperAdminID, CategoryID: 200, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "ETCUSDT", Symbol: "ETC-USDT", Images: model.GormStringSlice{"/icons/etc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 12, Bars: BarsOKEX, MinMoney: 0}, Desc: "以太坊经典（Ethereum）是一个去中心化智能合约支持的网络，旨在成为一个全球支付系统。起源于以太坊区块链网络，以太坊经典使用工作量证明共识机制（POW），支持去中心化应用。以太坊经典是在最初的以太坊区块链因为第一次攻击而分裂后出现的。一群坚持区块链核心价值的开发者组成Ethereum Classic，发行独立的加密货币ETC;作为一种完整的货币，吸引了之前错失投资机会的一批人，而且与原有以太坊的相似之处，也是一种坚守以太坊模式的方式。"},
			{AdminID: SuperAdminID, CategoryID: 200, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "LTCUSDT", Symbol: "LTC-USDT", Images: model.GormStringSlice{"/icons/ltc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 13, Bars: BarsOKEX, MinMoney: 0}, Desc: "Litecoin（LTC）于2011年作为比特币网络的一个分叉开发，旨在解决比特币的一些限制。它旨在提供一个分散的点对点货币，具有更快的交易处理时间和更低的费用。Litecoin受到了比特币（BTC）的启发，并且在技术上具有相同的实现原理，莱特币的创造和转让基于一种开源的加密协议，不受到任何中央机构的管理。莱特币旨在改进比特币。\nLitecoin的主要目标之一是防止比特币中观察到的挖掘集中化。它通过使用称为Scrypt的不同算法来实现这一目标，这使得挖掘更加占用内存和更慢。尽管矿场最终获得了对Litecoin挖矿的控制权，但Litecoin的重点已转向成为一个高效的支付系统。\n"},
			{AdminID: SuperAdminID, CategoryID: 200, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "UNIUSDT", Symbol: "UNI-USDT", Images: model.GormStringSlice{"/icons/uni.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 14, Bars: BarsOKEX, MinMoney: 0}, Desc: "Uniswap 是一个去中心化的加密货币交易协议 DEX，构建在以太坊区块链上。它的第一个版本(Uniswap v1)自2018年11月开始运行(在DevCon 4上发布)，而第二个版本(Uniswap v2)于2020年5月发布。它允许用户以对等的方式直接交易以太坊及其他 ERC-20 代币，无需传统的交易所中介。Uniswap 采用自动化做市商模型，通过流动性池和智能合约来自动匹配买卖订单，提供了高度的流动性和即时的交易体验，同时为用户提供了参与流动性提供和获取奖励的机会。"},
			{AdminID: SuperAdminID, CategoryID: 200, Type: ProductTypeOKEX, AssetsID: 3, Fee: 0.01, Name: "TRUMPUSDT", Symbol: "TRUMP-USDT", Images: model.GormStringSlice{"/icons/trump.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 15, Bars: BarsOKEX, MinMoney: 0}, Desc: "TrumpCoin 是一个去中心化的加密货币，基于以太坊区块链构建。它旨在提供安全、透明和无需许可的交易方式，允许用户自由地在全球范围内转移价值。TrumpCoin 采用智能合约技术，确保所有交易的可信度，并支持流动性提供、质押等 DeFi 生态功能。作为 ERC-20 代币，TrumpCoin 兼容以太坊上的各种 DEX 和钱包，为用户带来便捷的交易体验和资产管理能力。"},
		}...)

		// 合约USD产品
		initialProducts = append(initialProducts, []*Product{
			{AdminID: SuperAdminID, CategoryID: 300, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "BTCUSD", Symbol: "BTC-USDT", Images: model.GormStringSlice{"/icons/btc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 6, SymbolAssetsID: 1, Bars: BarsOKEX, MinMoney: 0}, Desc: "比特币(BTC)是一种点对点加密货币，旨在充当独立于任何中央机构的一种交易手段。 BTC可以安全，可验证和不变的方式进行电子现金转移。\nBTC于2009年推出，是通过在交易信息广播到比特币网络中的所有节点之前加盖交易时间戳的\"第一种解决双重支出问题的虚拟数字货币\"。比特币协议通过blockchain网络结构为拜占庭容错问题提供了解决方案，该概念最初由Stuart Haber和W. Scott Stornetta创建1991年。\nBitcoin的白皮书于由个人或一个团体以化名\"Satoshi Nakamoto\"于2008年发表，其基本身份尚未得到验证。\n比特币协议使用基于SHA-256d的工作量证明(PoW)算法达成网络共识。其网络的目标出块时间为10分钟，最大供应量为2100万个代币，代币的产生速率不断下降。为了防止拥堵时间的波动，通过基于过去2016年拥堵时间的算法重新调整了网络的出块难度。\n区块大小上限为1兆字节，比特币协议同时支持Lightning Network支付渠道的多层基础结构，以及隔离见证，这是一个增加功能的软分叉作为网络可扩展性的解决方案。\n比特币最大的用途之一是作为线上和线下购买商品和服务的支付媒介。超过 15,000 家企业积极接受比特币，包括微软、星巴克、家得宝、Etsy、新蛋、AT&T、赛百味、汉堡王、肯德基、维珍银河、达拉斯小牛队、挪威航空、特拉瓦拉和必胜客。\n尽管比特币的受欢迎程度和价值多年来都有了巨大增长，同时它也面临着许多批评。一些人认为它不像传统货币那样安全，因为政府或金融机构不支持它。另一些人则声称，比特币实际上并没有用于任何真正的交易，而是像股票或商品一样进行交易。最后，一些批评人士断言，开采比特币所需的能量值不了报酬，而且这个过程最终可能会破坏环境。\n那么，比特币是如何安全地促进交易的呢？比特币网络以区块链的方式运行，这是一个所有比特币交易的公共分类账。它不断增长，“完成块”添加到它与新的录音集。每个块包含前一个块的加密散列、时间戳和交易数据。比特币节点 (使用比特币网络的计算机) 使用区块链来区分合法的比特币交易和试图重新消费已经在其他地方消费过的比特币的行为，这种做法被称为双重消费 (双花)。\n比特币的设计是就为了抵抗审查。比特币交易记录在公共区块链上，可以提高透明度，防止一方控制网络。这使得政府或金融机构很难控制或干预比特币网络或交易。\n新的比特币是通过一种被称为挖矿的计算哈希值过程产生的。当矿工在区块链上验证并记录交易时，他们将获得比特币奖励。矿工使用特殊的软件来解决数学问题，并获得一定数量的比特币作为回报。这激励人们挖矿，并有助于确保新比特币的创造是可预测和公平的。每个区块奖励的比特币数量随着时间的推移而减少，因为网络调整了新区块添加到区块链的速率。目前，矿工每挖出一个区块可以获得 3.125 个比特币的奖励。\n在进行交易之前，你需要一个比特币钱包。比特币钱包是你储存比特币的地方。你可以用这个钱包收发比特币。你可以通过在数字货币交易所 (如八戒交易所) 设立账户或通过专门的提供商获得比特币钱包。\n当你想进行支付时，你只需将比特币发送到收件人的钱包地址，然后由矿工验证交易并记录在区块链上。比特币交易快速、廉价、安全。"},
			{AdminID: SuperAdminID, CategoryID: 300, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "ETHUSD", Symbol: "ETH-USDT", Images: model.GormStringSlice{"/icons/eth.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 4, SymbolAssetsID: 2, Bars: BarsOKEX, MinMoney: 0}, Desc: "以太坊是一个去中心化的区块链网络，其数字货币以太币 (ETH) 被广泛应用于创建和部署去中心化应用和智能合约，同时还可作为一种数字支付方式。\n\n作为市值第二大的数字货币，以太坊通过引入智能合约功能改变了行业格局，让用户和开发者能够进入去中心化金融 (DeFi) 等新兴领域。以太坊推动了价值数十亿美元的行业的发展，包括 DeFi、“边玩边赚”数字货币游戏和热门的 NFT 市场等。\n\n以太币 (ETH) 是以太坊区块链的原生代币，用于支付网络上的交易手续费，提供 PoS 共识机制下的质押奖励，并作为 NFT 等数字资产的货币交换媒介。\n\n以太坊引入了 ERC-20、ERC-721 和 ERC-1155 等代币标准，促进了同质化和非同质化代币的产生。这些标准在各个行业的增长中发挥了重要作用，尤其是 NFT 市场。\n\n以太坊虚拟机 (EVM) 是以太坊区块链的重要组成部分，为以太坊账户和智能合约提供了一个运行环境。它充当一个分布式计算机，执行着由运行以太坊客户端的互连节点维护的数百万个项目。\n\n上海升级为以太坊平台带来了一系列技术提升。其中引入的一项主要功能是用户能够访问和取消质押以太坊代币。这些代币此前由信标链的验证者锁定在智能合约中。\n\n上海升级之前，如果用户要成为采用权益证明(PoS)的以太坊区块链验证者，则需将代币锁定在智能合约中，从而无法自由访问或转账这些代币。但最新升级后，以太坊用户现在可以选择按需取消质押代币，从而能更自如地掌控个人资产。\n"},
			{AdminID: SuperAdminID, CategoryID: 300, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "SOLUSD", Symbol: "SOL-USDT", Images: model.GormStringSlice{"/icons/sol.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 6, Bars: BarsOKEX, MinMoney: 0}, Desc: "Solana是一个独立的L1区块链，它拥有底层智能合约协议，速度快、效率高。自2020年推出以来，Solana网络一直被视为以太坊的竞争对手，人们甚至戏称其为“以太坊杀手”。 \n\nSolana网络可扩展性强、费率低且速度快，成为了许多非同质化代币(NFT)去中心化应用程序 (DApp)、去中心化金融 (DeFi)和数字区块链支付生态系统的首选。\n\nSolana网络采用历史证明(PoH)技术，专注于提升交易吞吐量和处理速度。Solana声称每秒可处理大约65,000笔交易，交易速度业内领先。\n\nSolana区块链线性散列交易以创建所有网络活动的可验证顺序，从而实现快速交易。因此，它无需依赖区块创建者的时间戳，网络验证者也无需检查交易是否以正确的顺序进行。\n\nSolana网络由系统工程师兼计算机程序员Anatoly Yakovenko和现任Solana首席运营官Raj Gokal于2017年联合创立。Solana网络在PoH计时机制上运行，该机制是在目前采用权益证明算法的共识层之前部署的。\n\nSolana的原生代币SOL主要用于质押以支持验证过程，还可用于支付点对点(C2C)交易费用。Solana通过无限代币供应以最大限度地实现这些功能。然而，为维持其同比通货膨胀率，Solana会销毁每笔交易费用中50%的SOL。\n\n而剩余50%的交易费用会奖励给交易验证者。只要持有足够的SOL，任何人均可成为网络验证者或验证者委托人，为运行该独立区块链所需的共识过程提供支持。在该机制下，质押SOL以支持Solana区块链的用户可赚取奖励。Solana价格在币安上实时更新。\n\n2022年8月3日早些时候，Solana网络出现了重大漏洞。运行在Solana网络上的去中心化钱包应用Phantom和Slope在社交媒体上公布称，它们遭到了黑客攻击。\n\n这些黑客盗取了约8,000个钱包中总价值约800万美元的SOL。据发现，被盗钱包均与iOS和Android移动操作系统中的其他应用程序进行了交互。关于黑客攻击来源以及被盗钱包所有者是否会得到补偿，目前仍未可知。\n"},
			{AdminID: SuperAdminID, CategoryID: 300, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "TRXUSD", Symbol: "TRX-USDT", Images: model.GormStringSlice{"/icons/trx.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 7, Bars: BarsOKEX, MinMoney: 0}, Desc: "波场TRON以推动互联网去中心化为己任，致力于为去中心化互联网搭建基础设施。旗下的TRON协议是全球最大的基于区块链的去中心化应用操作系统协 议之一，为协议上的去中心化应用运行提供高吞吐，高扩展，高可靠性的底层公链支持。波场TRON还通过创新的可插拔智能合约平台为以太坊智能合约提供更好的兼容性。\n自2018年7月24日起，TRON收购了位于旧金山的互联网技术公司BitTorrent Inc.。 BitTorrent Inc.设计的分布式技术能够有效扩展，保持智能化，并使创作者和消费者能够控制其内容和数据。 每个月有超过1.7亿人使用BitTorrent Inc.开发的产品。 BitTorrent Inc.的协议每天可以传输全球40％的互联网流量。"},
			{AdminID: SuperAdminID, CategoryID: 300, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "XRPUSD", Symbol: "XRP-USDT", Images: model.GormStringSlice{"/icons/xrp.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 8, Bars: BarsOKEX, MinMoney: 0}, Desc: "XRP 是 XRP 账本（XRPL） 区块链上的原生数字资产，最初是为支付而构建的XRP 主要促进网络上的交易，并在 XRP 账本的原生 DEX 中桥接货币。XRP 是 XRP 账本 原生的数字资产，XRP 账本是一种开源、无需许可和去中心化的区块链技术XRP 创建于 2012 年，专门用于支付，可以在 3-5 秒内结算公开账目上的交易，使用受信任的验证者网络来验证账本上的交易。XRP 可以直接发送，无需中央中介，使其成为快速有效地桥接两种不同货币的便捷工具。它在公开市场上自由交换，并在现实世界中用于实现跨境支付和微交易。XRP 还可用于兑换不同的货币和访问加密流动性\nXRPL 还具有第一个内置于协议中的去中心化交易所 （DEX） 和自定义代币化功能。自 2012 年以来，XRPL 一直可靠运行，已关闭了 7000 万个分类账。"},
			{AdminID: SuperAdminID, CategoryID: 300, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "ADAUSD", Symbol: "ADA-USDT", Images: model.GormStringSlice{"/icons/ada.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 9, Bars: BarsOKEX, MinMoney: 0}, Desc: "Cardano 是一个区块链平台，旨在改进以太坊和比特币的功能。它基于多种设计组件，包括dApp开发平台，支持多资产的分类账和可验证的智能合约。它使用节能且可扩展的权益证明共识机制。 ADA 是为网络提供动力并实现治理的加密货币。 Cardano 由专家团队采用同行评审和循证方法开发。 Cardano支持智能合约和去中心化应用，并经历了多次升级以增强其能力。"},
			{AdminID: SuperAdminID, CategoryID: 300, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "ATOMUSD", Symbol: "ATOM-USDT", Images: model.GormStringSlice{"/icons/atom.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 10, Bars: BarsOKEX, MinMoney: 0}, Desc: "Cosmos项目是由Tendermint团队于2016年发起具有高度前瞻创新性的区块链跨链项目。Cosmos项目的目标是通过实现代币转移这样一个基础的功能最终实现“区块链的互联网”，并构造一个深度集成的代币经济生态系统。\nCosmos 网络由许多独立的并行区块链（称为区域）组成，每个区块均由 Tendermint 之类的经典拜占庭容错 (BFT) 共识协议提供支持 (已被 ErisDB 等平台使用)。某些区域相对于其他区域充当集线器，从而允许许多区域通过共享的集线器进行互操作。该架构是比特币侧链概念的一个更通用的应用，使用经典的 BFT 和权益证明算法，而不是工作量证明。 Cosmos 可以与其他多种应用程序和加密货币进行互操作，而其他区块链则无法做到这一点。通过创建一个新区域，您可以将任何区块链系统插入 Cosmos 集线器，并在这些区域之间来回传递令牌，而无需中介。"},
			{AdminID: SuperAdminID, CategoryID: 300, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "DOGEUSD", Symbol: "DOGE-USDT", Images: model.GormStringSlice{"/icons/doge.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 11, Bars: BarsOKEX, MinMoney: 0}, Desc: "Dogecoin 是一种基于 Shiba Inu (柴犬) 模因的点对点加密货币，在社交媒体上迅速走红。起初，Dogecoin 是作为对当时推出的其他加密货币项目的调侃而创建的，但它迅速发展出一批忠实的粉丝群体，并围绕这个代币发现和开发了新的应用场景。它被视为第一个“模因币”。狗狗币值得注意的一个方面是，它借用了现已解散的Luckycoin所使用的基于scrypt的工作量证明(PoW)共识算法，该算法借用了大部分莱特币的技术。尽管最初开发的本地代币 DOGE 没有特定的实用性，但多年来作为数字货币的使用逐渐增加。截至 2022 年 9 月，全球有超过 2,000 家商户接受 DOGE 作为支付方式。"},
			{AdminID: SuperAdminID, CategoryID: 300, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "ETCUSD", Symbol: "ETC-USDT", Images: model.GormStringSlice{"/icons/etc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 12, Bars: BarsOKEX, MinMoney: 0}, Desc: "以太坊经典（Ethereum）是一个去中心化智能合约支持的网络，旨在成为一个全球支付系统。起源于以太坊区块链网络，以太坊经典使用工作量证明共识机制（POW），支持去中心化应用。以太坊经典是在最初的以太坊区块链因为第一次攻击而分裂后出现的。一群坚持区块链核心价值的开发者组成Ethereum Classic，发行独立的加密货币ETC;作为一种完整的货币，吸引了之前错失投资机会的一批人，而且与原有以太坊的相似之处，也是一种坚守以太坊模式的方式。"},
			{AdminID: SuperAdminID, CategoryID: 300, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "LTCUSD", Symbol: "LTC-USDT", Images: model.GormStringSlice{"/icons/ltc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 13, Bars: BarsOKEX, MinMoney: 0}, Desc: "Litecoin（LTC）于2011年作为比特币网络的一个分叉开发，旨在解决比特币的一些限制。它旨在提供一个分散的点对点货币，具有更快的交易处理时间和更低的费用。Litecoin受到了比特币（BTC）的启发，并且在技术上具有相同的实现原理，莱特币的创造和转让基于一种开源的加密协议，不受到任何中央机构的管理。莱特币旨在改进比特币。\nLitecoin的主要目标之一是防止比特币中观察到的挖掘集中化。它通过使用称为Scrypt的不同算法来实现这一目标，这使得挖掘更加占用内存和更慢。尽管矿场最终获得了对Litecoin挖矿的控制权，但Litecoin的重点已转向成为一个高效的支付系统。\n"},
			{AdminID: SuperAdminID, CategoryID: 300, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "UNIUSD", Symbol: "UNI-USDT", Images: model.GormStringSlice{"/icons/uni.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 14, Bars: BarsOKEX, MinMoney: 0}, Desc: "Uniswap 是一个去中心化的加密货币交易协议 DEX，构建在以太坊区块链上。它的第一个版本(Uniswap v1)自2018年11月开始运行(在DevCon 4上发布)，而第二个版本(Uniswap v2)于2020年5月发布。它允许用户以对等的方式直接交易以太坊及其他 ERC-20 代币，无需传统的交易所中介。Uniswap 采用自动化做市商模型，通过流动性池和智能合约来自动匹配买卖订单，提供了高度的流动性和即时的交易体验，同时为用户提供了参与流动性提供和获取奖励的机会。"},
			{AdminID: SuperAdminID, CategoryID: 300, Type: ProductTypeOKEX, AssetsID: 0, Fee: 0.01, Name: "TRUMPUSD", Symbol: "TRUMP-USDT", Images: model.GormStringSlice{"/icons/trump.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 15, Bars: BarsOKEX, MinMoney: 0}, Desc: "TrumpCoin 是一个去中心化的加密货币，基于以太坊区块链构建。它旨在提供安全、透明和无需许可的交易方式，允许用户自由地在全球范围内转移价值。TrumpCoin 采用智能合约技术，确保所有交易的可信度，并支持流动性提供、质押等 DeFi 生态功能。作为 ERC-20 代币，TrumpCoin 兼容以太坊上的各种 DEX 和钱包，为用户带来便捷的交易体验和资产管理能力。"},
		}...)

		// 期权USDT
		initialProducts = append(initialProducts, []*Product{
			{AdminID: SuperAdminID, CategoryID: 400, Type: ProductTypeOKEX, AssetsID: 3, Name: "BTCUSDT", Symbol: "BTC-USDT", Images: model.GormStringSlice{"/icons/btc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 6, SymbolAssetsID: 1, Bars: BarsOKEX, MinMoney: 0}, Desc: "比特币(BTC)是一种点对点加密货币，旨在充当独立于任何中央机构的一种交易手段。 BTC可以安全，可验证和不变的方式进行电子现金转移。\nBTC于2009年推出，是通过在交易信息广播到比特币网络中的所有节点之前加盖交易时间戳的\"第一种解决双重支出问题的虚拟数字货币\"。比特币协议通过blockchain网络结构为拜占庭容错问题提供了解决方案，该概念最初由Stuart Haber和W. Scott Stornetta创建1991年。\nBitcoin的白皮书于由个人或一个团体以化名\"Satoshi Nakamoto\"于2008年发表，其基本身份尚未得到验证。\n比特币协议使用基于SHA-256d的工作量证明(PoW)算法达成网络共识。其网络的目标出块时间为10分钟，最大供应量为2100万个代币，代币的产生速率不断下降。为了防止拥堵时间的波动，通过基于过去2016年拥堵时间的算法重新调整了网络的出块难度。\n区块大小上限为1兆字节，比特币协议同时支持Lightning Network支付渠道的多层基础结构，以及隔离见证，这是一个增加功能的软分叉作为网络可扩展性的解决方案。\n比特币最大的用途之一是作为线上和线下购买商品和服务的支付媒介。超过 15,000 家企业积极接受比特币，包括微软、星巴克、家得宝、Etsy、新蛋、AT&T、赛百味、汉堡王、肯德基、维珍银河、达拉斯小牛队、挪威航空、特拉瓦拉和必胜客。\n尽管比特币的受欢迎程度和价值多年来都有了巨大增长，同时它也面临着许多批评。一些人认为它不像传统货币那样安全，因为政府或金融机构不支持它。另一些人则声称，比特币实际上并没有用于任何真正的交易，而是像股票或商品一样进行交易。最后，一些批评人士断言，开采比特币所需的能量值不了报酬，而且这个过程最终可能会破坏环境。\n那么，比特币是如何安全地促进交易的呢？比特币网络以区块链的方式运行，这是一个所有比特币交易的公共分类账。它不断增长，“完成块”添加到它与新的录音集。每个块包含前一个块的加密散列、时间戳和交易数据。比特币节点 (使用比特币网络的计算机) 使用区块链来区分合法的比特币交易和试图重新消费已经在其他地方消费过的比特币的行为，这种做法被称为双重消费 (双花)。\n比特币的设计是就为了抵抗审查。比特币交易记录在公共区块链上，可以提高透明度，防止一方控制网络。这使得政府或金融机构很难控制或干预比特币网络或交易。\n新的比特币是通过一种被称为挖矿的计算哈希值过程产生的。当矿工在区块链上验证并记录交易时，他们将获得比特币奖励。矿工使用特殊的软件来解决数学问题，并获得一定数量的比特币作为回报。这激励人们挖矿，并有助于确保新比特币的创造是可预测和公平的。每个区块奖励的比特币数量随着时间的推移而减少，因为网络调整了新区块添加到区块链的速率。目前，矿工每挖出一个区块可以获得 3.125 个比特币的奖励。\n在进行交易之前，你需要一个比特币钱包。比特币钱包是你储存比特币的地方。你可以用这个钱包收发比特币。你可以通过在数字货币交易所 (如八戒交易所) 设立账户或通过专门的提供商获得比特币钱包。\n当你想进行支付时，你只需将比特币发送到收件人的钱包地址，然后由矿工验证交易并记录在区块链上。比特币交易快速、廉价、安全。"},
			{AdminID: SuperAdminID, CategoryID: 400, Type: ProductTypeOKEX, AssetsID: 3, Name: "ETHUSDT", Symbol: "ETH-USDT", Images: model.GormStringSlice{"/icons/eth.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 4, SymbolAssetsID: 2, Bars: BarsOKEX, MinMoney: 0}, Desc: "以太坊是一个去中心化的区块链网络，其数字货币以太币 (ETH) 被广泛应用于创建和部署去中心化应用和智能合约，同时还可作为一种数字支付方式。\n\n作为市值第二大的数字货币，以太坊通过引入智能合约功能改变了行业格局，让用户和开发者能够进入去中心化金融 (DeFi) 等新兴领域。以太坊推动了价值数十亿美元的行业的发展，包括 DeFi、“边玩边赚”数字货币游戏和热门的 NFT 市场等。\n\n以太币 (ETH) 是以太坊区块链的原生代币，用于支付网络上的交易手续费，提供 PoS 共识机制下的质押奖励，并作为 NFT 等数字资产的货币交换媒介。\n\n以太坊引入了 ERC-20、ERC-721 和 ERC-1155 等代币标准，促进了同质化和非同质化代币的产生。这些标准在各个行业的增长中发挥了重要作用，尤其是 NFT 市场。\n\n以太坊虚拟机 (EVM) 是以太坊区块链的重要组成部分，为以太坊账户和智能合约提供了一个运行环境。它充当一个分布式计算机，执行着由运行以太坊客户端的互连节点维护的数百万个项目。\n\n上海升级为以太坊平台带来了一系列技术提升。其中引入的一项主要功能是用户能够访问和取消质押以太坊代币。这些代币此前由信标链的验证者锁定在智能合约中。\n\n上海升级之前，如果用户要成为采用权益证明(PoS)的以太坊区块链验证者，则需将代币锁定在智能合约中，从而无法自由访问或转账这些代币。但最新升级后，以太坊用户现在可以选择按需取消质押代币，从而能更自如地掌控个人资产。\n"},
			{AdminID: SuperAdminID, CategoryID: 400, Type: ProductTypeOKEX, AssetsID: 3, Name: "SOLUSDT", Symbol: "SOL-USDT", Images: model.GormStringSlice{"/icons/sol.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 6, Bars: BarsOKEX, MinMoney: 0}, Desc: "Solana是一个独立的L1区块链，它拥有底层智能合约协议，速度快、效率高。自2020年推出以来，Solana网络一直被视为以太坊的竞争对手，人们甚至戏称其为“以太坊杀手”。 \n\nSolana网络可扩展性强、费率低且速度快，成为了许多非同质化代币(NFT)去中心化应用程序 (DApp)、去中心化金融 (DeFi)和数字区块链支付生态系统的首选。\n\nSolana网络采用历史证明(PoH)技术，专注于提升交易吞吐量和处理速度。Solana声称每秒可处理大约65,000笔交易，交易速度业内领先。\n\nSolana区块链线性散列交易以创建所有网络活动的可验证顺序，从而实现快速交易。因此，它无需依赖区块创建者的时间戳，网络验证者也无需检查交易是否以正确的顺序进行。\n\nSolana网络由系统工程师兼计算机程序员Anatoly Yakovenko和现任Solana首席运营官Raj Gokal于2017年联合创立。Solana网络在PoH计时机制上运行，该机制是在目前采用权益证明算法的共识层之前部署的。\n\nSolana的原生代币SOL主要用于质押以支持验证过程，还可用于支付点对点(C2C)交易费用。Solana通过无限代币供应以最大限度地实现这些功能。然而，为维持其同比通货膨胀率，Solana会销毁每笔交易费用中50%的SOL。\n\n而剩余50%的交易费用会奖励给交易验证者。只要持有足够的SOL，任何人均可成为网络验证者或验证者委托人，为运行该独立区块链所需的共识过程提供支持。在该机制下，质押SOL以支持Solana区块链的用户可赚取奖励。Solana价格在币安上实时更新。\n\n2022年8月3日早些时候，Solana网络出现了重大漏洞。运行在Solana网络上的去中心化钱包应用Phantom和Slope在社交媒体上公布称，它们遭到了黑客攻击。\n\n这些黑客盗取了约8,000个钱包中总价值约800万美元的SOL。据发现，被盗钱包均与iOS和Android移动操作系统中的其他应用程序进行了交互。关于黑客攻击来源以及被盗钱包所有者是否会得到补偿，目前仍未可知。\n"},
			{AdminID: SuperAdminID, CategoryID: 400, Type: ProductTypeOKEX, AssetsID: 3, Name: "TRXUSDT", Symbol: "TRX-USDT", Images: model.GormStringSlice{"/icons/trx.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 7, Bars: BarsOKEX, MinMoney: 0}, Desc: "波场TRON以推动互联网去中心化为己任，致力于为去中心化互联网搭建基础设施。旗下的TRON协议是全球最大的基于区块链的去中心化应用操作系统协 议之一，为协议上的去中心化应用运行提供高吞吐，高扩展，高可靠性的底层公链支持。波场TRON还通过创新的可插拔智能合约平台为以太坊智能合约提供更好的兼容性。\n自2018年7月24日起，TRON收购了位于旧金山的互联网技术公司BitTorrent Inc.。 BitTorrent Inc.设计的分布式技术能够有效扩展，保持智能化，并使创作者和消费者能够控制其内容和数据。 每个月有超过1.7亿人使用BitTorrent Inc.开发的产品。 BitTorrent Inc.的协议每天可以传输全球40％的互联网流量。"},
			{AdminID: SuperAdminID, CategoryID: 400, Type: ProductTypeOKEX, AssetsID: 3, Name: "XRPUSDT", Symbol: "XRP-USDT", Images: model.GormStringSlice{"/icons/xrp.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 8, Bars: BarsOKEX, MinMoney: 0}, Desc: "XRP 是 XRP 账本（XRPL） 区块链上的原生数字资产，最初是为支付而构建的XRP 主要促进网络上的交易，并在 XRP 账本的原生 DEX 中桥接货币。XRP 是 XRP 账本 原生的数字资产，XRP 账本是一种开源、无需许可和去中心化的区块链技术XRP 创建于 2012 年，专门用于支付，可以在 3-5 秒内结算公开账目上的交易，使用受信任的验证者网络来验证账本上的交易。XRP 可以直接发送，无需中央中介，使其成为快速有效地桥接两种不同货币的便捷工具。它在公开市场上自由交换，并在现实世界中用于实现跨境支付和微交易。XRP 还可用于兑换不同的货币和访问加密流动性\nXRPL 还具有第一个内置于协议中的去中心化交易所 （DEX） 和自定义代币化功能。自 2012 年以来，XRPL 一直可靠运行，已关闭了 7000 万个分类账。"},
			{AdminID: SuperAdminID, CategoryID: 400, Type: ProductTypeOKEX, AssetsID: 3, Name: "ADAUSDT", Symbol: "ADA-USDT", Images: model.GormStringSlice{"/icons/ada.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 9, Bars: BarsOKEX, MinMoney: 0}, Desc: "Cardano 是一个区块链平台，旨在改进以太坊和比特币的功能。它基于多种设计组件，包括dApp开发平台，支持多资产的分类账和可验证的智能合约。它使用节能且可扩展的权益证明共识机制。 ADA 是为网络提供动力并实现治理的加密货币。 Cardano 由专家团队采用同行评审和循证方法开发。 Cardano支持智能合约和去中心化应用，并经历了多次升级以增强其能力。"},
			{AdminID: SuperAdminID, CategoryID: 400, Type: ProductTypeOKEX, AssetsID: 3, Name: "ATOMUSDT", Symbol: "ATOM-USDT", Images: model.GormStringSlice{"/icons/atom.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 10, Bars: BarsOKEX, MinMoney: 0}, Desc: "Cosmos项目是由Tendermint团队于2016年发起具有高度前瞻创新性的区块链跨链项目。Cosmos项目的目标是通过实现代币转移这样一个基础的功能最终实现“区块链的互联网”，并构造一个深度集成的代币经济生态系统。\nCosmos 网络由许多独立的并行区块链（称为区域）组成，每个区块均由 Tendermint 之类的经典拜占庭容错 (BFT) 共识协议提供支持 (已被 ErisDB 等平台使用)。某些区域相对于其他区域充当集线器，从而允许许多区域通过共享的集线器进行互操作。该架构是比特币侧链概念的一个更通用的应用，使用经典的 BFT 和权益证明算法，而不是工作量证明。 Cosmos 可以与其他多种应用程序和加密货币进行互操作，而其他区块链则无法做到这一点。通过创建一个新区域，您可以将任何区块链系统插入 Cosmos 集线器，并在这些区域之间来回传递令牌，而无需中介。"},
			{AdminID: SuperAdminID, CategoryID: 400, Type: ProductTypeOKEX, AssetsID: 3, Name: "DOGEUSDT", Symbol: "DOGE-USDT", Images: model.GormStringSlice{"/icons/doge.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 11, Bars: BarsOKEX, MinMoney: 0}, Desc: "Dogecoin 是一种基于 Shiba Inu (柴犬) 模因的点对点加密货币，在社交媒体上迅速走红。起初，Dogecoin 是作为对当时推出的其他加密货币项目的调侃而创建的，但它迅速发展出一批忠实的粉丝群体，并围绕这个代币发现和开发了新的应用场景。它被视为第一个“模因币”。狗狗币值得注意的一个方面是，它借用了现已解散的Luckycoin所使用的基于scrypt的工作量证明(PoW)共识算法，该算法借用了大部分莱特币的技术。尽管最初开发的本地代币 DOGE 没有特定的实用性，但多年来作为数字货币的使用逐渐增加。截至 2022 年 9 月，全球有超过 2,000 家商户接受 DOGE 作为支付方式。"},
			{AdminID: SuperAdminID, CategoryID: 400, Type: ProductTypeOKEX, AssetsID: 3, Name: "ETCUSDT", Symbol: "ETC-USDT", Images: model.GormStringSlice{"/icons/etc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 12, Bars: BarsOKEX, MinMoney: 0}, Desc: "以太坊经典（Ethereum）是一个去中心化智能合约支持的网络，旨在成为一个全球支付系统。起源于以太坊区块链网络，以太坊经典使用工作量证明共识机制（POW），支持去中心化应用。以太坊经典是在最初的以太坊区块链因为第一次攻击而分裂后出现的。一群坚持区块链核心价值的开发者组成Ethereum Classic，发行独立的加密货币ETC;作为一种完整的货币，吸引了之前错失投资机会的一批人，而且与原有以太坊的相似之处，也是一种坚守以太坊模式的方式。"},
			{AdminID: SuperAdminID, CategoryID: 400, Type: ProductTypeOKEX, AssetsID: 3, Name: "LTCUSDT", Symbol: "LTC-USDT", Images: model.GormStringSlice{"/icons/ltc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 13, Bars: BarsOKEX, MinMoney: 0}, Desc: "Litecoin（LTC）于2011年作为比特币网络的一个分叉开发，旨在解决比特币的一些限制。它旨在提供一个分散的点对点货币，具有更快的交易处理时间和更低的费用。Litecoin受到了比特币（BTC）的启发，并且在技术上具有相同的实现原理，莱特币的创造和转让基于一种开源的加密协议，不受到任何中央机构的管理。莱特币旨在改进比特币。\nLitecoin的主要目标之一是防止比特币中观察到的挖掘集中化。它通过使用称为Scrypt的不同算法来实现这一目标，这使得挖掘更加占用内存和更慢。尽管矿场最终获得了对Litecoin挖矿的控制权，但Litecoin的重点已转向成为一个高效的支付系统。\n"},
			{AdminID: SuperAdminID, CategoryID: 400, Type: ProductTypeOKEX, AssetsID: 3, Name: "UNIUSDT", Symbol: "UNI-USDT", Images: model.GormStringSlice{"/icons/uni.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 14, Bars: BarsOKEX, MinMoney: 0}, Desc: "Uniswap 是一个去中心化的加密货币交易协议 DEX，构建在以太坊区块链上。它的第一个版本(Uniswap v1)自2018年11月开始运行(在DevCon 4上发布)，而第二个版本(Uniswap v2)于2020年5月发布。它允许用户以对等的方式直接交易以太坊及其他 ERC-20 代币，无需传统的交易所中介。Uniswap 采用自动化做市商模型，通过流动性池和智能合约来自动匹配买卖订单，提供了高度的流动性和即时的交易体验，同时为用户提供了参与流动性提供和获取奖励的机会。"},
			{AdminID: SuperAdminID, CategoryID: 400, Type: ProductTypeOKEX, AssetsID: 3, Name: "TRUMPUSDT", Symbol: "TRUMP-USDT", Images: model.GormStringSlice{"/icons/trump.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 15, Bars: BarsOKEX, MinMoney: 0}, Desc: "TrumpCoin 是一个去中心化的加密货币，基于以太坊区块链构建。它旨在提供安全、透明和无需许可的交易方式，允许用户自由地在全球范围内转移价值。TrumpCoin 采用智能合约技术，确保所有交易的可信度，并支持流动性提供、质押等 DeFi 生态功能。作为 ERC-20 代币，TrumpCoin 兼容以太坊上的各种 DEX 和钱包，为用户带来便捷的交易体验和资产管理能力。"},
		}...)

		// 期权USD
		initialProducts = append(initialProducts, []*Product{
			{AdminID: SuperAdminID, CategoryID: 500, Type: ProductTypeOKEX, AssetsID: 0, Name: "BTCUSD", Symbol: "BTC-USDT", Images: model.GormStringSlice{"/icons/btc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 6, SymbolAssetsID: 1, Bars: BarsOKEX, MinMoney: 0}, Desc: "比特币(BTC)是一种点对点加密货币，旨在充当独立于任何中央机构的一种交易手段。 BTC可以安全，可验证和不变的方式进行电子现金转移。\nBTC于2009年推出，是通过在交易信息广播到比特币网络中的所有节点之前加盖交易时间戳的\"第一种解决双重支出问题的虚拟数字货币\"。比特币协议通过blockchain网络结构为拜占庭容错问题提供了解决方案，该概念最初由Stuart Haber和W. Scott Stornetta创建1991年。\nBitcoin的白皮书于由个人或一个团体以化名\"Satoshi Nakamoto\"于2008年发表，其基本身份尚未得到验证。\n比特币协议使用基于SHA-256d的工作量证明(PoW)算法达成网络共识。其网络的目标出块时间为10分钟，最大供应量为2100万个代币，代币的产生速率不断下降。为了防止拥堵时间的波动，通过基于过去2016年拥堵时间的算法重新调整了网络的出块难度。\n区块大小上限为1兆字节，比特币协议同时支持Lightning Network支付渠道的多层基础结构，以及隔离见证，这是一个增加功能的软分叉作为网络可扩展性的解决方案。\n比特币最大的用途之一是作为线上和线下购买商品和服务的支付媒介。超过 15,000 家企业积极接受比特币，包括微软、星巴克、家得宝、Etsy、新蛋、AT&T、赛百味、汉堡王、肯德基、维珍银河、达拉斯小牛队、挪威航空、特拉瓦拉和必胜客。\n尽管比特币的受欢迎程度和价值多年来都有了巨大增长，同时它也面临着许多批评。一些人认为它不像传统货币那样安全，因为政府或金融机构不支持它。另一些人则声称，比特币实际上并没有用于任何真正的交易，而是像股票或商品一样进行交易。最后，一些批评人士断言，开采比特币所需的能量值不了报酬，而且这个过程最终可能会破坏环境。\n那么，比特币是如何安全地促进交易的呢？比特币网络以区块链的方式运行，这是一个所有比特币交易的公共分类账。它不断增长，“完成块”添加到它与新的录音集。每个块包含前一个块的加密散列、时间戳和交易数据。比特币节点 (使用比特币网络的计算机) 使用区块链来区分合法的比特币交易和试图重新消费已经在其他地方消费过的比特币的行为，这种做法被称为双重消费 (双花)。\n比特币的设计是就为了抵抗审查。比特币交易记录在公共区块链上，可以提高透明度，防止一方控制网络。这使得政府或金融机构很难控制或干预比特币网络或交易。\n新的比特币是通过一种被称为挖矿的计算哈希值过程产生的。当矿工在区块链上验证并记录交易时，他们将获得比特币奖励。矿工使用特殊的软件来解决数学问题，并获得一定数量的比特币作为回报。这激励人们挖矿，并有助于确保新比特币的创造是可预测和公平的。每个区块奖励的比特币数量随着时间的推移而减少，因为网络调整了新区块添加到区块链的速率。目前，矿工每挖出一个区块可以获得 3.125 个比特币的奖励。\n在进行交易之前，你需要一个比特币钱包。比特币钱包是你储存比特币的地方。你可以用这个钱包收发比特币。你可以通过在数字货币交易所 (如八戒交易所) 设立账户或通过专门的提供商获得比特币钱包。\n当你想进行支付时，你只需将比特币发送到收件人的钱包地址，然后由矿工验证交易并记录在区块链上。比特币交易快速、廉价、安全。"},
			{AdminID: SuperAdminID, CategoryID: 500, Type: ProductTypeOKEX, AssetsID: 0, Name: "ETHUSD", Symbol: "ETH-USDT", Images: model.GormStringSlice{"/icons/eth.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 4, SymbolAssetsID: 2, Bars: BarsOKEX, MinMoney: 0}, Desc: "以太坊是一个去中心化的区块链网络，其数字货币以太币 (ETH) 被广泛应用于创建和部署去中心化应用和智能合约，同时还可作为一种数字支付方式。\n\n作为市值第二大的数字货币，以太坊通过引入智能合约功能改变了行业格局，让用户和开发者能够进入去中心化金融 (DeFi) 等新兴领域。以太坊推动了价值数十亿美元的行业的发展，包括 DeFi、“边玩边赚”数字货币游戏和热门的 NFT 市场等。\n\n以太币 (ETH) 是以太坊区块链的原生代币，用于支付网络上的交易手续费，提供 PoS 共识机制下的质押奖励，并作为 NFT 等数字资产的货币交换媒介。\n\n以太坊引入了 ERC-20、ERC-721 和 ERC-1155 等代币标准，促进了同质化和非同质化代币的产生。这些标准在各个行业的增长中发挥了重要作用，尤其是 NFT 市场。\n\n以太坊虚拟机 (EVM) 是以太坊区块链的重要组成部分，为以太坊账户和智能合约提供了一个运行环境。它充当一个分布式计算机，执行着由运行以太坊客户端的互连节点维护的数百万个项目。\n\n上海升级为以太坊平台带来了一系列技术提升。其中引入的一项主要功能是用户能够访问和取消质押以太坊代币。这些代币此前由信标链的验证者锁定在智能合约中。\n\n上海升级之前，如果用户要成为采用权益证明(PoS)的以太坊区块链验证者，则需将代币锁定在智能合约中，从而无法自由访问或转账这些代币。但最新升级后，以太坊用户现在可以选择按需取消质押代币，从而能更自如地掌控个人资产。\n"},
			{AdminID: SuperAdminID, CategoryID: 500, Type: ProductTypeOKEX, AssetsID: 0, Name: "SOLUSD", Symbol: "SOL-USDT", Images: model.GormStringSlice{"/icons/sol.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 6, Bars: BarsOKEX, MinMoney: 0}, Desc: "Solana是一个独立的L1区块链，它拥有底层智能合约协议，速度快、效率高。自2020年推出以来，Solana网络一直被视为以太坊的竞争对手，人们甚至戏称其为“以太坊杀手”。 \n\nSolana网络可扩展性强、费率低且速度快，成为了许多非同质化代币(NFT)去中心化应用程序 (DApp)、去中心化金融 (DeFi)和数字区块链支付生态系统的首选。\n\nSolana网络采用历史证明(PoH)技术，专注于提升交易吞吐量和处理速度。Solana声称每秒可处理大约65,000笔交易，交易速度业内领先。\n\nSolana区块链线性散列交易以创建所有网络活动的可验证顺序，从而实现快速交易。因此，它无需依赖区块创建者的时间戳，网络验证者也无需检查交易是否以正确的顺序进行。\n\nSolana网络由系统工程师兼计算机程序员Anatoly Yakovenko和现任Solana首席运营官Raj Gokal于2017年联合创立。Solana网络在PoH计时机制上运行，该机制是在目前采用权益证明算法的共识层之前部署的。\n\nSolana的原生代币SOL主要用于质押以支持验证过程，还可用于支付点对点(C2C)交易费用。Solana通过无限代币供应以最大限度地实现这些功能。然而，为维持其同比通货膨胀率，Solana会销毁每笔交易费用中50%的SOL。\n\n而剩余50%的交易费用会奖励给交易验证者。只要持有足够的SOL，任何人均可成为网络验证者或验证者委托人，为运行该独立区块链所需的共识过程提供支持。在该机制下，质押SOL以支持Solana区块链的用户可赚取奖励。Solana价格在币安上实时更新。\n\n2022年8月3日早些时候，Solana网络出现了重大漏洞。运行在Solana网络上的去中心化钱包应用Phantom和Slope在社交媒体上公布称，它们遭到了黑客攻击。\n\n这些黑客盗取了约8,000个钱包中总价值约800万美元的SOL。据发现，被盗钱包均与iOS和Android移动操作系统中的其他应用程序进行了交互。关于黑客攻击来源以及被盗钱包所有者是否会得到补偿，目前仍未可知。\n"},
			{AdminID: SuperAdminID, CategoryID: 500, Type: ProductTypeOKEX, AssetsID: 0, Name: "TRXUSD", Symbol: "TRX-USDT", Images: model.GormStringSlice{"/icons/trx.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 7, Bars: BarsOKEX, MinMoney: 0}, Desc: "波场TRON以推动互联网去中心化为己任，致力于为去中心化互联网搭建基础设施。旗下的TRON协议是全球最大的基于区块链的去中心化应用操作系统协 议之一，为协议上的去中心化应用运行提供高吞吐，高扩展，高可靠性的底层公链支持。波场TRON还通过创新的可插拔智能合约平台为以太坊智能合约提供更好的兼容性。\n自2018年7月24日起，TRON收购了位于旧金山的互联网技术公司BitTorrent Inc.。 BitTorrent Inc.设计的分布式技术能够有效扩展，保持智能化，并使创作者和消费者能够控制其内容和数据。 每个月有超过1.7亿人使用BitTorrent Inc.开发的产品。 BitTorrent Inc.的协议每天可以传输全球40％的互联网流量。"},
			{AdminID: SuperAdminID, CategoryID: 500, Type: ProductTypeOKEX, AssetsID: 0, Name: "XRPUSD", Symbol: "XRP-USDT", Images: model.GormStringSlice{"/icons/xrp.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 8, Bars: BarsOKEX, MinMoney: 0}, Desc: "XRP 是 XRP 账本（XRPL） 区块链上的原生数字资产，最初是为支付而构建的XRP 主要促进网络上的交易，并在 XRP 账本的原生 DEX 中桥接货币。XRP 是 XRP 账本 原生的数字资产，XRP 账本是一种开源、无需许可和去中心化的区块链技术XRP 创建于 2012 年，专门用于支付，可以在 3-5 秒内结算公开账目上的交易，使用受信任的验证者网络来验证账本上的交易。XRP 可以直接发送，无需中央中介，使其成为快速有效地桥接两种不同货币的便捷工具。它在公开市场上自由交换，并在现实世界中用于实现跨境支付和微交易。XRP 还可用于兑换不同的货币和访问加密流动性\nXRPL 还具有第一个内置于协议中的去中心化交易所 （DEX） 和自定义代币化功能。自 2012 年以来，XRPL 一直可靠运行，已关闭了 7000 万个分类账。"},
			{AdminID: SuperAdminID, CategoryID: 500, Type: ProductTypeOKEX, AssetsID: 0, Name: "ADAUSD", Symbol: "ADA-USDT", Images: model.GormStringSlice{"/icons/ada.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 9, Bars: BarsOKEX, MinMoney: 0}, Desc: "Cardano 是一个区块链平台，旨在改进以太坊和比特币的功能。它基于多种设计组件，包括dApp开发平台，支持多资产的分类账和可验证的智能合约。它使用节能且可扩展的权益证明共识机制。 ADA 是为网络提供动力并实现治理的加密货币。 Cardano 由专家团队采用同行评审和循证方法开发。 Cardano支持智能合约和去中心化应用，并经历了多次升级以增强其能力。"},
			{AdminID: SuperAdminID, CategoryID: 500, Type: ProductTypeOKEX, AssetsID: 0, Name: "ATOMUSD", Symbol: "ATOM-USDT", Images: model.GormStringSlice{"/icons/atom.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 10, Bars: BarsOKEX, MinMoney: 0}, Desc: "Cosmos项目是由Tendermint团队于2016年发起具有高度前瞻创新性的区块链跨链项目。Cosmos项目的目标是通过实现代币转移这样一个基础的功能最终实现“区块链的互联网”，并构造一个深度集成的代币经济生态系统。\nCosmos 网络由许多独立的并行区块链（称为区域）组成，每个区块均由 Tendermint 之类的经典拜占庭容错 (BFT) 共识协议提供支持 (已被 ErisDB 等平台使用)。某些区域相对于其他区域充当集线器，从而允许许多区域通过共享的集线器进行互操作。该架构是比特币侧链概念的一个更通用的应用，使用经典的 BFT 和权益证明算法，而不是工作量证明。 Cosmos 可以与其他多种应用程序和加密货币进行互操作，而其他区块链则无法做到这一点。通过创建一个新区域，您可以将任何区块链系统插入 Cosmos 集线器，并在这些区域之间来回传递令牌，而无需中介。"},
			{AdminID: SuperAdminID, CategoryID: 500, Type: ProductTypeOKEX, AssetsID: 0, Name: "DOGEUSD", Symbol: "DOGE-USDT", Images: model.GormStringSlice{"/icons/doge.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 11, Bars: BarsOKEX, MinMoney: 0}, Desc: "Dogecoin 是一种基于 Shiba Inu (柴犬) 模因的点对点加密货币，在社交媒体上迅速走红。起初，Dogecoin 是作为对当时推出的其他加密货币项目的调侃而创建的，但它迅速发展出一批忠实的粉丝群体，并围绕这个代币发现和开发了新的应用场景。它被视为第一个“模因币”。狗狗币值得注意的一个方面是，它借用了现已解散的Luckycoin所使用的基于scrypt的工作量证明(PoW)共识算法，该算法借用了大部分莱特币的技术。尽管最初开发的本地代币 DOGE 没有特定的实用性，但多年来作为数字货币的使用逐渐增加。截至 2022 年 9 月，全球有超过 2,000 家商户接受 DOGE 作为支付方式。"},
			{AdminID: SuperAdminID, CategoryID: 500, Type: ProductTypeOKEX, AssetsID: 0, Name: "ETCUSD", Symbol: "ETC-USDT", Images: model.GormStringSlice{"/icons/etc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 12, Bars: BarsOKEX, MinMoney: 0}, Desc: "以太坊经典（Ethereum）是一个去中心化智能合约支持的网络，旨在成为一个全球支付系统。起源于以太坊区块链网络，以太坊经典使用工作量证明共识机制（POW），支持去中心化应用。以太坊经典是在最初的以太坊区块链因为第一次攻击而分裂后出现的。一群坚持区块链核心价值的开发者组成Ethereum Classic，发行独立的加密货币ETC;作为一种完整的货币，吸引了之前错失投资机会的一批人，而且与原有以太坊的相似之处，也是一种坚守以太坊模式的方式。"},
			{AdminID: SuperAdminID, CategoryID: 500, Type: ProductTypeOKEX, AssetsID: 0, Name: "LTCUSD", Symbol: "LTC-USDT", Images: model.GormStringSlice{"/icons/ltc.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 13, Bars: BarsOKEX, MinMoney: 0}, Desc: "Litecoin（LTC）于2011年作为比特币网络的一个分叉开发，旨在解决比特币的一些限制。它旨在提供一个分散的点对点货币，具有更快的交易处理时间和更低的费用。Litecoin受到了比特币（BTC）的启发，并且在技术上具有相同的实现原理，莱特币的创造和转让基于一种开源的加密协议，不受到任何中央机构的管理。莱特币旨在改进比特币。\nLitecoin的主要目标之一是防止比特币中观察到的挖掘集中化。它通过使用称为Scrypt的不同算法来实现这一目标，这使得挖掘更加占用内存和更慢。尽管矿场最终获得了对Litecoin挖矿的控制权，但Litecoin的重点已转向成为一个高效的支付系统。\n"},
			{AdminID: SuperAdminID, CategoryID: 500, Type: ProductTypeOKEX, AssetsID: 0, Name: "UNIUSD", Symbol: "UNI-USDT", Images: model.GormStringSlice{"/icons/uni.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 14, Bars: BarsOKEX, MinMoney: 0}, Desc: "Uniswap 是一个去中心化的加密货币交易协议 DEX，构建在以太坊区块链上。它的第一个版本(Uniswap v1)自2018年11月开始运行(在DevCon 4上发布)，而第二个版本(Uniswap v2)于2020年5月发布。它允许用户以对等的方式直接交易以太坊及其他 ERC-20 代币，无需传统的交易所中介。Uniswap 采用自动化做市商模型，通过流动性池和智能合约来自动匹配买卖订单，提供了高度的流动性和即时的交易体验，同时为用户提供了参与流动性提供和获取奖励的机会。"},
			{AdminID: SuperAdminID, CategoryID: 500, Type: ProductTypeOKEX, AssetsID: 0, Name: "TRUMPUSDT", Symbol: "TRUMP-USDT", Images: model.GormStringSlice{"/icons/trump.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 15, Bars: BarsOKEX, MinMoney: 0}, Desc: "TrumpCoin 是一个去中心化的加密货币，基于以太坊区块链构建。它旨在提供安全、透明和无需许可的交易方式，允许用户自由地在全球范围内转移价值。TrumpCoin 采用智能合约技术，确保所有交易的可信度，并支持流动性提供、质押等 DeFi 生态功能。作为 ERC-20 代币，TrumpCoin 兼容以太坊上的各种 DEX 和钱包，为用户带来便捷的交易体验和资产管理能力。"},
		}...)

		// 期货【易汇外汇】
		initialProducts = append(initialProducts, []*Product{
			{AdminID: SuperAdminID, CategoryID: 600, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "eur/usd", Symbol: "eurusd:cur", Images: model.GormStringSlice{"/icons/eur_usd.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."}, // 欧元/美元
			{AdminID: SuperAdminID, CategoryID: 600, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "gbp/usd", Symbol: "gbpusd:cur", Images: model.GormStringSlice{"/icons/gbp_usd.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."}, // 英镑/美元
			{AdminID: SuperAdminID, CategoryID: 600, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "aud/usd", Symbol: "audusd:cur", Images: model.GormStringSlice{"/icons/aud_usd.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."}, // 澳元/美元
			{AdminID: SuperAdminID, CategoryID: 600, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "nzd/usd", Symbol: "nzdusd:cur", Images: model.GormStringSlice{"/icons/nzd_usd.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."}, // 纽约/美元
			{AdminID: SuperAdminID, CategoryID: 600, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "usd/jpy", Symbol: "usdjpy:cur", Images: model.GormStringSlice{"/icons/usd_jpy.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."}, // 美元/日元
			{AdminID: SuperAdminID, CategoryID: 600, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "usd/cny", Symbol: "usdcny:cur", Images: model.GormStringSlice{"/icons/usd_cny.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."}, // 美元/人民币
			{AdminID: SuperAdminID, CategoryID: 600, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "usd/chf", Symbol: "usdchf:cur", Images: model.GormStringSlice{"/icons/usd_chf.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."}, // 美元/瑞士法郎
			{AdminID: SuperAdminID, CategoryID: 600, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "jpy/usd", Symbol: "jpyusd:cur", Images: model.GormStringSlice{"/icons/jpy_usd.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."}, // 日元/美元
			{AdminID: SuperAdminID, CategoryID: 600, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "usd/mxn", Symbol: "usdmxn:cur", Images: model.GormStringSlice{"/icons/usd_mxn.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."}, // 美元/墨西哥比索
		}...)

		// 期货原料【易汇贵金属】
		initialProducts = append(initialProducts, []*Product{
			{AdminID: SuperAdminID, CategoryID: 700, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "gold", Symbol: "xauusd:cur", Images: model.GormStringSlice{"/icons/gold.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."},          // 黄金
			{AdminID: SuperAdminID, CategoryID: 700, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "silver", Symbol: "xagusd:cur", Images: model.GormStringSlice{"/icons/silver.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."},      // 白银
			{AdminID: SuperAdminID, CategoryID: 700, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "copper", Symbol: "hg1:com", Images: model.GormStringSlice{"/icons/platinum.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."},       // 铜
			{AdminID: SuperAdminID, CategoryID: 700, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "platinum", Symbol: "xptusd:cur", Images: model.GormStringSlice{"/icons/palladium.png"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."}, // 白金
		}...)

		// 期货原料【易汇能源】
		initialProducts = append(initialProducts, []*Product{
			{AdminID: SuperAdminID, CategoryID: 800, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "soybeans", Symbol: "cl1:com", Images: model.GormStringSlice{"/icons/soybeans.jpg"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."},               // 美国大豆
			{AdminID: SuperAdminID, CategoryID: 800, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "brent", Symbol: "co1:com", Images: model.GormStringSlice{"/icons/brent.jpg"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."},                     // 布伦特原油
			{AdminID: SuperAdminID, CategoryID: 800, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "gasoline", Symbol: "xb1:com", Images: model.GormStringSlice{"/icons/gasoline.jpg"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."},               // 汽油
			{AdminID: SuperAdminID, CategoryID: 800, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "naturalGas", Symbol: "ng1:com", Images: model.GormStringSlice{"/icons/natural_gas.jpg"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."},          // 天然气
			{AdminID: SuperAdminID, CategoryID: 800, Type: ProductTypeTrading, AssetsID: 0, IsTranslate: model.BoolTrue, Name: "u.k.naturalgas", Symbol: "nguk:com", Images: model.GormStringSlice{"/icons/u_k_natural_gas.jpg"}, Data: ProductData{AssetsDecimal: 4, SymbolAssetsDecimal: 2, Bars: BarsTrading, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary."}, // 英国天然气
		}...)

		// 质押USDT
		initialProducts = append(initialProducts, []*Product{
			{AdminID: SuperAdminID, CategoryID: 900, Type: ProductTypeStaking, AssetsID: 0, Name: "BTC", Symbol: "BTC", Images: model.GormStringSlice{"/icons/btc.png"}, Data: ProductData{StakingStrategy: StakingStrategyData, AssetsDecimal: 6, SymbolAssetsDecimal: 2, SymbolAssetsID: 1, Bars: BarsOKEX, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary.\n"},
			{AdminID: SuperAdminID, CategoryID: 900, Type: ProductTypeStaking, AssetsID: 0, Name: "ETH", Symbol: "ETH", Images: model.GormStringSlice{"/icons/eth.png"}, Data: ProductData{StakingStrategy: StakingStrategyData, AssetsDecimal: 4, SymbolAssetsDecimal: 2, SymbolAssetsID: 2, Bars: BarsOKEX, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary.\n"},
			{AdminID: SuperAdminID, CategoryID: 900, Type: ProductTypeStaking, AssetsID: 0, Name: "SOL", Symbol: "SOL", Images: model.GormStringSlice{"/icons/sol.png"}, Data: ProductData{StakingStrategy: StakingStrategyData, AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 6, Bars: BarsOKEX, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary.\n"},
			{AdminID: SuperAdminID, CategoryID: 900, Type: ProductTypeStaking, AssetsID: 0, Name: "USD", Symbol: "USD", Images: model.GormStringSlice{"/icons/usd.png"}, Data: ProductData{StakingStrategy: StakingStrategyData, AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 0, Bars: BarsOKEX, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary.\n"},
		}...)

		// 自定义产品
		initialProducts = append(initialProducts, []*Product{
			{AdminID: SuperAdminID, CategoryID: 1100, Type: ProductTypeCustomize, AssetsID: 0, Name: "PIGSY/USD", Symbol: "PIGSY-USD", Images: model.GormStringSlice{"/icons/pigsy.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 16, Bars: BarsCustomize, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary.\n"},
			{AdminID: SuperAdminID, CategoryID: 300, Type: ProductTypeCustomize, AssetsID: 0, Name: "PIGSY/USD", Symbol: "PIGSY-USD", Images: model.GormStringSlice{"/icons/pigsy.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 16, Bars: BarsCustomize, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary.\n"},
			{AdminID: SuperAdminID, CategoryID: 500, Type: ProductTypeCustomize, AssetsID: 0, Name: "PIGSYUSD", Symbol: "PIGSY-USD", Images: model.GormStringSlice{"/icons/pigsy.png"}, Data: ProductData{AssetsDecimal: 2, SymbolAssetsDecimal: 2, SymbolAssetsID: 16, Bars: BarsCustomize, MinMoney: 0}, Desc: "There is no more information at the moment, please contact customer service if necessary.\n"},
		}...)

		for _, product := range initialProducts {
			if err := db.Create(&product).Error; err != nil {
				panic("Failed to create initial product: " + err.Error())
			}
		}
	}

	return nil
}
