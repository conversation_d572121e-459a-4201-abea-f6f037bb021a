package models

import (
	"database/sql/driver"
	"errors"
	"zfeng/core/model"

	"github.com/goccy/go-json"
	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

const (
	CountryStatusDisabled int8 = -1 // 禁用
	CountryStatusEnabled  int8 = 10 // 启用
)

// Country 系统国家
type Country struct {
	model.BaseModel
	AdminID uint        `gorm:"type:int unsigned;uniqueIndex:idx_admin_iso2;not null;comment:管理ID" json:"adminId"`
	Name    string      `gorm:"type:varchar(60);not null;index;comment:国家名称" json:"name"`
	Alias   string      `gorm:"type:varchar(60);not null;comment:国家别名" json:"alias"`
	Icon    string      `gorm:"type:varchar(255);not null;comment:国旗图标URL" json:"icon"`
	ISO2    string      `gorm:"type:char(2);not null;uniqueIndex:idx_admin_iso2;comment:ISO 3166-1 alpha-2代码" json:"iso2"`
	Sort    int8        `gorm:"type:tinyint;not null;default:99;index;comment:排序" json:"sort"`
	Code    string      `gorm:"type:varchar(10);not null;comment:国家区号" json:"code"`
	Status  int8        `gorm:"type:tinyint;not null;default:10;index;comment:状态" json:"status"`
	Data    CountryData `gorm:"type:json;comment:数据" json:"data"`
}

// CountryDisplayData 国家显示数据
type CountryDisplayData struct {
	ID    uint   `json:"id"`    //	国家ID
	Alias string `json:"alias"` //	国家名称
	Icon  string `json:"icon"`  //	国家图标
	ISO2  string `json:"iso2"`  //	国家ISO2
	Code  string `json:"code"`  //	电话区号
}

// CountryData 国家数据
type CountryData struct {
}

// Value implements the driver.Valuer interface
func (d CountryData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements the sql.Scanner interface
func (d *CountryData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &d)
}

func init() {
	// 只允许主进程运行
	if fiber.IsChild() {
		return
	}

	db := model.NewModel()

	// Create the table if it doesn't exist
	if err := db.AutoMigrate(&Country{}); err != nil {
		panic("Failed to auto migrate Country table: " + err.Error())
	}

	// Initialize system countries
	if err := InitSystemCountries(db.DB); err != nil {
		panic("Failed to initialize system countries: " + err.Error())
	}
}

// InitSystemCountries initializes the default system countries
func InitSystemCountries(db *gorm.DB) error {
	var count int64
	if err := db.Model(&Country{}).Count(&count).Error; err != nil {
		return err
	}

	if count == 0 {
		countries := []Country{
			{AdminID: SuperAdminID, Name: "中国", Alias: "China", Code: "86", Icon: "/country/china.png", ISO2: "CN", Sort: 1, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "美国", Alias: "United States", Code: "1", Icon: "/country/usa.png", ISO2: "US", Sort: 2, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "日本", Alias: "Japan", Code: "81", Icon: "/country/japan.png", ISO2: "JP", Sort: 3, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "韩国", Alias: "South Korea", Code: "82", Icon: "/country/south_korea.png", ISO2: "KR", Sort: 4, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "英国", Alias: "United Kingdom", Code: "44", Icon: "/country/uk.png", ISO2: "GB", Sort: 5, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "德国", Alias: "Germany", Code: "49", Icon: "/country/germany.png", ISO2: "DE", Sort: 6, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "法国", Alias: "France", Code: "33", Icon: "/country/france.png", ISO2: "FR", Sort: 7, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "加拿大", Alias: "Canada", Code: "1", Icon: "/country/canada.png", ISO2: "CA", Sort: 8, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "澳大利亚", Alias: "Australia", Code: "61", Icon: "/country/australia.png", ISO2: "AU", Sort: 9, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "新加坡", Alias: "Singapore", Code: "65", Icon: "/country/singapore.png", ISO2: "SG", Sort: 10, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "香港", Alias: "Hong kong", Code: "852", Icon: "/country/hongkong.png", ISO2: "HK", Sort: 11, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "台湾", Alias: "Taiwan", Code: "886", Icon: "/country/taiwan.png", ISO2: "TW", Sort: 12, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "澳门", Alias: "Macao", Code: "853", Icon: "/country/macao.png", ISO2: "MO", Sort: 13, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "印度", Alias: "India", Code: "91", Icon: "/country/india.png", ISO2: "IN", Sort: 14, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "俄罗斯", Alias: "Russia", Code: "7", Icon: "/country/russia.png", ISO2: "RU", Sort: 15, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "蒙古", Alias: "Mongolia", Code: "976", Icon: "/country/mongolia.png", ISO2: "MN", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "朝鲜", Alias: "North Korea", Code: "850", Icon: "/country/north_korea.png", ISO2: "KP", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "菲律宾", Alias: "Philippines", Code: "63", Icon: "/country/philippines.png", ISO2: "PH", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "越南", Alias: "Vietnam", Code: "84", Icon: "/country/vietnam.png", ISO2: "VN", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "老挝", Alias: "Laos", Code: "856", Icon: "/country/laos.png", ISO2: "LA", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "柬埔寨", Alias: "Cambodia", Code: "855", Icon: "/country/cambodia.png", ISO2: "KH", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "缅甸", Alias: "Myanmar", Code: "95", Icon: "/country/myanmar.png", ISO2: "MM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "泰国", Alias: "Thailand", Code: "66", Icon: "/country/thailand.png", ISO2: "TH", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "马来西亚", Alias: "Malaysia", Code: "60", Icon: "/country/malaysia.png", ISO2: "MY", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "文莱", Alias: "Brunei", Code: "673", Icon: "/country/brunei.png", ISO2: "BN", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "印度尼西亚", Alias: "Indonesia", Code: "62", Icon: "/country/indonesia.png", ISO2: "ID", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "尼泊尔", Alias: "Nepal", Code: "977", Icon: "/country/nepal.png", ISO2: "NP", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "不丹", Alias: "Bhutan", Code: "975", Icon: "/country/bhutan.png", ISO2: "BT", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "孟加拉国", Alias: "Bangladesh", Code: "880", Icon: "/country/bangladesh.png", ISO2: "BD", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "巴基斯坦", Alias: "Pakistan", Code: "92", Icon: "/country/pakistan.png", ISO2: "PK", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "斯里兰卡", Alias: "SriLanka", Code: "94", Icon: "/country/sri_lanka.png", ISO2: "LK", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "马尔代夫", Alias: "Maldives", Code: "960", Icon: "/country/maldives.png", ISO2: "MV", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "哈萨克斯坦", Alias: "Kazakhstan", Code: "7", Icon: "/country/kazakhstan.png", ISO2: "KZ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "乌兹别克斯坦", Alias: "Uzbekistan", Code: "998", Icon: "/country/uzbekistan.png", ISO2: "UZ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "土库曼斯坦", Alias: "Turkmenistan", Code: "993", Icon: "/country/turkmenistan.png", ISO2: "TM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "阿富汗", Alias: "Afghanistan", Code: "93", Icon: "/country/afghanistan.png", ISO2: "AF", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "伊拉克", Alias: "Iraq", Code: "964", Icon: "/country/iraq.png", ISO2: "IQ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "叙利亚", Alias: "Syria", Code: "963", Icon: "/country/syria.png", ISO2: "SY", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "黎巴嫩", Alias: "Lebanon", Code: "961", Icon: "/country/lebanon.png", ISO2: "LB", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "以色列", Alias: "Israel", Code: "972", Icon: "/country/israel.png", ISO2: "IL", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "巴勒斯坦", Alias: "Palestine", Code: "970", Icon: "/country/palestine.png", ISO2: "PS", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "沙特阿拉伯", Alias: "SaudiArabia", Code: "966", Icon: "/country/saudi_arabia.png", ISO2: "SA", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "巴林", Alias: "Bahrain", Code: "973", Icon: "/country/bahrain.png", ISO2: "BH", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "科威特", Alias: "Kuwait", Code: "965", Icon: "/country/kuwait.png", ISO2: "KW", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "阿拉伯联合酋长国", Alias: "United Arab Emirates", Code: "971", Icon: "/country/united_arab_emirates.png", ISO2: "AE", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "阿曼", Alias: "Oman", Code: "968", Icon: "/country/oman.png", ISO2: "OM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "亚美尼亚", Alias: "Armenia", Code: "374", Icon: "/country/armenia.png", ISO2: "AM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "阿塞拜疆", Alias: "Azerbaijan", Code: "994", Icon: "/country/azerbaijan.png", ISO2: "AZ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "土耳其", Alias: "Turkey", Code: "90", Icon: "/country/turkey.png", ISO2: "TR", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "塞浦路斯", Alias: "Cyprus", Code: "357", Icon: "/country/cyprus.png", ISO2: "CY", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "阿尔巴尼亚", Alias: "Albania", Code: "355", Icon: "/country/albania.png", ISO2: "AL", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "阿尔及利亚", Alias: "Algeria", Code: "213", Icon: "/country/algeria.png", ISO2: "DZ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "阿根廷", Alias: "Argentina", Code: "54", Icon: "/country/argentina.png", ISO2: "AR", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "埃及", Alias: "Egypt", Code: "20", Icon: "/country/egypt.png", ISO2: "EG", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "爱尔兰", Alias: "Ireland", Code: "353", Icon: "/country/ireland.png", ISO2: "IE", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "安道尔", Alias: "Andorra", Code: "376", Icon: "/country/andorra.png", ISO2: "AD", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "安哥拉", Alias: "Angola", Code: "244", Icon: "/country/angola.png", ISO2: "AO", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "安圭拉", Alias: "Anguilla", Code: "1-264", Icon: "/country/anguilla.png", ISO2: "AI", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "安提瓜和巴布达", Alias: "Antigua and Barbuda", Code: "1-268", Icon: "/country/antigua_and_barbuda.png", ISO2: "AG", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "奥兰群岛", Alias: "Åland Islands", Code: "358", Icon: "/country/aland_islands.png", ISO2: "AX", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "奥地利", Alias: "Austria", Code: "43", Icon: "/country/austria.png", ISO2: "AT", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "巴巴多斯", Alias: "Barbados", Code: "1-246", Icon: "/country/barbados.png", ISO2: "BB", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "巴布亚新几内亚国旗", Alias: "Papua New Guinea", Code: "675", Icon: "/country/papua_new_guinea.png", ISO2: "PG", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "巴哈马", Alias: "Bahamas", Code: "1-242", Icon: "/country/bahamas.png", ISO2: "BS", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "巴拉圭", Alias: "Paraguay", Code: "595", Icon: "/country/paraguay.png", ISO2: "PY", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "巴拿马", Alias: "Panama", Code: "507", Icon: "/country/panama.png", ISO2: "PA", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "巴西", Alias: "Brazil", Code: "55", Icon: "/country/brazil.png", ISO2: "BR", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "白俄罗斯", Alias: "Belarus", Code: "375", Icon: "/country/belarus.png", ISO2: "BY", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "百慕大", Alias: "Bermuda", Code: "1-441", Icon: "/country/bermuda.png", ISO2: "BM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "北马里亚纳群岛", Alias: "Northern Mariana Islands", Code: "1-670", Icon: "/country/northern_mariana.png", ISO2: "MP", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "北马其顿", Alias: "Republika Makedonija", Code: "389", Icon: "/country/republika_makedonija.png", ISO2: "MK", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "贝宁", Alias: "Benin", Code: "229", Icon: "/country/benin.png", ISO2: "BJ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "比利时", Alias: "Belgium", Code: "32", Icon: "/country/belgium.png", ISO2: "BE", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "秘鲁", Alias: "Peru", Code: "51", Icon: "/country/peru.png", ISO2: "PE", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "冰岛", Alias: "Iceland", Code: "354", Icon: "/country/iceland.png", ISO2: "IS", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "波多黎各", Alias: "Puerto Rico", Code: "1-939", Icon: "/country/puerto_rico.png", ISO2: "PR", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "波兰", Alias: "Poland", Code: "48", Icon: "/country/poland.png", ISO2: "PL", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "波斯尼亚和黑塞哥维那", Alias: "Bosnia and Herzegovina", Code: "287", Icon: "/country/bosnia_and_herzegovina.png", ISO2: "BA", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "玻利维亚", Alias: "Bolivia", Code: "591", Icon: "/country/bolivia.png", ISO2: "BO", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "伯利兹", Alias: "Belize", Code: "501", Icon: "/country/belize.png", ISO2: "BZ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "布基纳法索", Alias: "Burkina Faso", Code: "226", Icon: "/country/burkina_faso.png", ISO2: "BF", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "布隆迪", Alias: "Burundi", Code: "257", Icon: "/country/burundi.png", ISO2: "BI", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "布维岛", Alias: "Bouvet Island", Code: "359", Icon: "/country/bouvet_island.png", ISO2: "BV", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "赤道几内亚", Alias: "Equatorial Guinea", Code: "240", Icon: "/country/equatorial_guinea.png", ISO2: "GQ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "丹麦", Alias: "Denmark", Code: "45", Icon: "/country/denmark.png", ISO2: "DK", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "东帝汶", Alias: "East Timor, Timor-Leste", Code: "257", Icon: "/country/east_timor.png", ISO2: "TL", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "多哥", Alias: "Togo", Code: "228", Icon: "/country/togo.png", ISO2: "TG", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "多米尼加共和国", Alias: "Dominican Republic", Code: "1-809", Icon: "/country/dominican_republic.png", ISO2: "DO", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "多米尼克", Alias: "Dominica", Code: "1-767", Icon: "/country/dominica.png", ISO2: "DM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "厄瓜多尔", Alias: "Ecuador", Code: "593", Icon: "/country/ecuador.png", ISO2: "EC", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "厄立特里亚", Alias: "Eritrea", Code: "291", Icon: "/country/eritrea.png", ISO2: "ER", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "斐济", Alias: "Fiji", Code: "679", Icon: "/country/fiji.png", ISO2: "FJ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "芬兰", Alias: "Finland", Code: "358", Icon: "/country/finland.png", ISO2: "FI", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "佛得角", Alias: "Cape Verde", Code: "238", Icon: "/country/cape_verde.png", ISO2: "CV", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "福克兰群岛", Alias: "Falkland Islands", Code: "500", Icon: "/country/falkland_islands.png", ISO2: "FK", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "冈比亚", Alias: "Gambia", Code: "220", Icon: "/country/gambia.png", ISO2: "GM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "刚果共和国", Alias: "Congo, Republic of the", Code: "257", Icon: "/country/congo.png", ISO2: "CG", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "刚果民主共和国", Alias: "Congo, Democratic Republic of the", Code: "243", Icon: "/country/congo_democratic.png", ISO2: "CD", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "哥伦比亚", Alias: "Colombia", Code: "57", Icon: "/country/colombia.png", ISO2: "CO", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "哥斯达黎加", Alias: "Costa Rica", Code: "506", Icon: "/country/costa_rica.png", ISO2: "CR", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "格林纳达", Alias: "Grenada", Code: "1-473", Icon: "/country/grenada.png", ISO2: "GD", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "格陵兰", Alias: "Greenland", Code: "299", Icon: "/country/greenland.png", ISO2: "GL", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "格鲁吉亚", Alias: "Georgia", Code: "995", Icon: "/country/georgia.png", ISO2: "GE", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "根西", Alias: "Guernsey", Code: "44-1481", Icon: "/country/guernsey.png", ISO2: "GG", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "古巴", Alias: "Cuba", Code: "53", Icon: "/country/cuba.png", ISO2: "CU", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "瓜德罗普", Alias: "Guadeloupe", Code: "590", Icon: "/country/gadeloupe.png", ISO2: "GP", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "关岛", Alias: "Guam", Code: "1-671", Icon: "/country/guam.png", ISO2: "GU", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "圭亚那", Alias: "Guyana", Code: "592", Icon: "/country/guyana.png", ISO2: "GY", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "海地", Alias: "Haiti", Code: "509", Icon: "/country/haiti.png", ISO2: "HT", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "荷兰", Alias: "Netherlands", Code: "31", Icon: "/country/netherlands.png", ISO2: "NL", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "赫德岛和麦克唐纳", Alias: "Heard Island and McDonald Islands", Code: "", Icon: "/country/heard_island.png", ISO2: "HM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "洪都拉斯", Alias: "Honduras", Code: "504", Icon: "/country/honduras.png", ISO2: "HN", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "基里巴斯", Alias: "Kiribati", Code: "686", Icon: "/country/kiribati.png", ISO2: "KI", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "吉布提", Alias: "Djibouti", Code: "253", Icon: "/country/djibouti.png", ISO2: "DJ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "吉尔吉斯斯坦", Alias: "Kyrgyzstan", Code: "996", Icon: "/country/kyrgyzstan.png", ISO2: "KG", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "几内亚", Alias: "Guinea", Code: "224", Icon: "/country/guinea.png", ISO2: "GN", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "加纳", Alias: "Ghana", Code: "233", Icon: "/country/ghana.png", ISO2: "GH", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "加蓬", Alias: "Gabon", Code: "241", Icon: "/country/gabon.png", ISO2: "GA", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "捷克", Alias: "Czech Republic", Code: "420", Icon: "/country/czech.png", ISO2: "CZ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "津巴布韦", Alias: "Zimbabwe", Code: "263", Icon: "/country/zimbabwe.png", ISO2: "ZW", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "喀麦隆", Alias: "Cameroon", Code: "237", Icon: "/country/cameroon.png", ISO2: "CM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "卡塔尔", Alias: "Qatar", Code: "974", Icon: "/country/qatar.png", ISO2: "QA", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "开曼群岛", Alias: "Cayman Islands)", Code: "1-345", Icon: "/country/cayman_islands.png", ISO2: "KY", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "科科斯（基林）群岛", Alias: "Cocos (Keeling) Islands", Code: "61", Icon: "/country/cocos_keeling.png", ISO2: "CC", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "科摩罗", Alias: "Comoros", Code: "269", Icon: "/country/comoros.png", ISO2: "KM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "科索沃", Alias: "Kosovo", Code: "383", Icon: "/country/kosovo.png", ISO2: "XK", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "科特迪瓦", Alias: "Côte d'Ivoire", Code: "225", Icon: "/country/ivory_coast.png", ISO2: "CI", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "克罗地亚", Alias: "Croatia", Code: "385", Icon: "/country/croatia.png", ISO2: "HR", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "肯尼亚", Alias: "Kenya", Code: "254", Icon: "/country/kenya.png", ISO2: "KE", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "库克群岛", Alias: "Cook Islands", Code: "682", Icon: "/country/cook_islands.png", ISO2: "CK", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "库拉索", Alias: "Curaçao", Code: "599", Icon: "/country/curacao.png", ISO2: "CW", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "拉脱维亚", Alias: "Latvia", Code: "371", Icon: "/country/latvia.png", ISO2: "LV", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "莱索托", Alias: "Lesotho", Code: "266", Icon: "/country/lesotho.png", ISO2: "LS", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "立陶宛", Alias: "Lithuania", Code: "370", Icon: "/country/lithuania.png", ISO2: "LT", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "利比里亚", Alias: "Liberia", Code: "231", Icon: "/country/liberia.png", ISO2: "LR", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "利比亚", Alias: "Libya", Code: "218", Icon: "/country/libya.png", ISO2: "LY", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "列支敦斯登", Alias: "Liechtenstein", Code: "423", Icon: "/country/liechtenstein.png", ISO2: "LI", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "留尼汪", Alias: "Réunion", Code: "262", Icon: "/country/réunion.png", ISO2: "RE", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "卢森堡", Alias: "Luxembourg", Code: "352", Icon: "/country/luxembourg.png", ISO2: "LU", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "卢旺达", Alias: "Rwanda", Code: "250", Icon: "/country/rwanda.png", ISO2: "RW", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "罗马尼亚", Alias: "Romania", Code: "40", Icon: "/country/romania.png", ISO2: "RO", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "马达加斯加", Alias: "Madagascar", Code: "261", Icon: "/country/madagascar.png", ISO2: "MG", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "马恩岛", Alias: "Isle of Man", Code: "44-1624", Icon: "/country/isle_of_man.png", ISO2: "IM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "匈牙利", Alias: "Hungary", Code: "36", Icon: "/country/hungary.png", ISO2: "HU", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "牙买加", Alias: "Jamaica", Code: "1-876", Icon: "/country/jamaica.png", ISO2: "JM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "也门", Alias: "Yemen", Code: "967", Icon: "/country/yemen.png", ISO2: "YE", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "伊朗", Alias: "Iran", Code: "98", Icon: "/country/iran.png", ISO2: "IR", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "意大利", Alias: "Italy", Code: "39", Icon: "/country/italy.png", ISO2: "IT", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "英属维尔京群岛", Alias: "British Virgin Islands", Code: "1-284", Icon: "/country/british_virgin_islands.png", ISO2: "VG", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "英属印度洋领地", Alias: "British Indian Ocean Territory", Code: "246", Icon: "/country/british_indian_ocean_territory.png", ISO2: "IO", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "约旦", Alias: "Jordan", Code: "962", Icon: "/country/jordan.png", ISO2: "JO", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "泽西", Alias: "Jersey", Code: "44-1534", Icon: "/country/jersey.png", ISO2: "JE", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "乍得", Alias: "Chad", Code: "235", Icon: "/country/chad.png", ISO2: "TD", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "直布罗陀", Alias: "Gibraltar", Code: "350", Icon: "/country/gibraltar.png", ISO2: "GI", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "智利", Alias: "Chile", Code: "56", Icon: "/country/chile.png", ISO2: "CL", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "中非共和国", Alias: "Central African Republic", Code: "236", Icon: "/country/central_africa.png", ISO2: "CF", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "马耳他", Alias: "Malta", Code: "44-1624", Icon: "/country/malta.png", ISO2: "MT", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "马拉维", Alias: "Malawi", Code: "265", Icon: "/country/malawi.png", ISO2: "MW", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "马里", Alias: "Mali", Code: "356", Icon: "/country/mali.png", ISO2: "ML", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "马绍尔群岛", Alias: "Marshall Islands", Code: "692", Icon: "/country/marshall_islands.png", ISO2: "MH", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "马约特", Alias: "Mayotte", Code: "262", Icon: "/country/mayotte.png", ISO2: "YT", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "马提尼克", Alias: "Martinique", Code: "596", Icon: "/country/martinique.png", ISO2: "MQ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "毛里求斯", Alias: "Mauritius", Code: "230", Icon: "/country/mauritius.png", ISO2: "MU", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "毛里塔尼亚", Alias: "Mauritania", Code: "222", Icon: "/country/mauritania.png", ISO2: "MR", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "美属萨摩亚", Alias: "American Samoa", Code: "1-684", Icon: "/country/american_samoa.png", ISO2: "AS", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "蒙特内哥", Alias: "Montenegro", Code: "382", Icon: "/country/montenegro.png", ISO2: "ME", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "蒙特塞拉特", Alias: "Montserrat", Code: "1-664", Icon: "/country/montserrat.png", ISO2: "MS", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "密克罗尼西亚", Alias: "Micronesia", Code: "691", Icon: "/country/micronesia.png", ISO2: "FM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "摩尔多瓦", Alias: "Moldova", Code: "373", Icon: "/country/moldova.png", ISO2: "MD", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "摩洛哥", Alias: "Morocco", Code: "212", Icon: "/country/morocco.png", ISO2: "MA", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "摩纳哥", Alias: "Monaco", Code: "377", Icon: "/country/monaco.png", ISO2: "MC", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "莫桑比克", Alias: "Mozambique", Code: "258", Icon: "/country/mozambique.png", ISO2: "MZ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "墨西哥", Alias: "Mexico", Code: "52", Icon: "/country/mexico.png", ISO2: "MX", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "纳米比亚", Alias: "Namibia", Code: "264", Icon: "/country/namibia.png", ISO2: "NA", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "奈及利亚", Alias: "Nigeria", Code: "234", Icon: "/country/nigeria.png", ISO2: "NG", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "南非", Alias: "South Africa", Code: "27", Icon: "/country/south_africa.png", ISO2: "ZA", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "南乔治亚与南桑威奇群岛", Alias: "South Georgia and the South Sandwich Islands", Code: "44", Icon: "/country/south_georgia.png", ISO2: "GS", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "瑙鲁", Alias: "Nauru", Code: "674", Icon: "/country/nauru.png", ISO2: "NR", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "尼加拉瓜", Alias: "Nicaragua", Code: "505", Icon: "/country/nicaragua.png", ISO2: "NI", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "尼日尔", Alias: "Niger", Code: "227", Icon: "/country/niger.png", ISO2: "NE", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "纽埃", Alias: "Niue", Code: "683", Icon: "/country/niue.png", ISO2: "NU", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "挪威", Alias: "Norway", Code: "47", Icon: "/country/norway.png", ISO2: "NO", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "帕劳", Alias: "Palau", Code: "680", Icon: "/country/palau.png", ISO2: "PW", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "皮特凯恩群岛", Alias: "Pitcairn Islands", Code: "64", Icon: "/country/pitcairn_islands.png", ISO2: "PN", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "葡萄牙", Alias: "Portugal", Code: "351", Icon: "/country/portugal.png", ISO2: "PT", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "瑞典", Alias: "Sweden", Code: "46", Icon: "/country/sweden.png", ISO2: "SE", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "瑞士", Alias: "Switzerland", Code: "41", Icon: "/country/switzerland.png", ISO2: "CH", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "萨摩亚", Alias: "Samoa", Code: "685", Icon: "/country/samoa.png", ISO2: "WS", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "塞尔维亚", Alias: "Serbia", Code: "381", Icon: "/country/serbia.png", ISO2: "RS", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "塞拉利昂", Alias: "Sierra Leone", Code: "232", Icon: "/country/sierra_leone.png", ISO2: "SL", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "塞内加尔", Alias: "Senegal", Code: "221", Icon: "/country/senegal.png", ISO2: "SN", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "塞舌尔", Alias: "Seychelles", Code: "248", Icon: "/country/seychelles.png", ISO2: "SC", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "赞比亚", Alias: "Zambia", Code: "260", Icon: "/country/zambia.png", ISO2: "ZM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "圣巴泰勒米", Alias: "Saint Barthélemy", Code: "590", Icon: "/country/saint_barthelemy.png", ISO2: "BL", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "圣诞岛", Alias: "Christmas Island", Code: "61-8-9164", Icon: "/country/christmas_island.png", ISO2: "CX", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "圣多美和普林西比", Alias: "São Tomé and Príncipe", Code: "260", Icon: "/country/sao_tome_and_principe.png", ISO2: "ST", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "圣赫勒拿", Alias: "Saint Helena", Code: "290", Icon: "/country/saint_helena.png", ISO2: "SH", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "圣马丁", Alias: "Saint Martin", Code: "590", Icon: "/country/st_martin.png", ISO2: "MF", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "圣基茨和尼维斯", Alias: "Saint Kitts and Nevis", Code: "1-869", Icon: "/country/saint_kitts_and_nevis.png", ISO2: "KN", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "圣卢西亚", Alias: "Saint Lucia", Code: "1-758", Icon: "/country/saint_lucia.png", ISO2: "LC", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "圣马力诺", Alias: "San Marino", Code: "378", Icon: "/country/san_marino.png", ISO2: "SM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "圣皮埃尔和密克隆", Alias: "Saint Pierre and Miquelon", Code: "508", Icon: "/country/zambia.png", ISO2: "PM", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "圣文森特和格林纳丁斯", Alias: "Saint Vincent and the Grenadines", Code: "1-784", Icon: "/country/saint_vincent_and_the_grenadines.png", ISO2: "VC", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "斯威士兰", Alias: "Kingdom of Eswatini", Code: "268", Icon: "/country/kingdom_of_eswatini.png", ISO2: "SZ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "斯洛伐克", Alias: "Slovakia", Code: "421", Icon: "/country/slovakia.png", ISO2: "SK", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "斯瓦尔巴群岛", Alias: "Svalbard", Code: "47-79", Icon: "/country/svalbard.png", ISO2: "SJ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "苏丹", Alias: "Sudan", Code: "249", Icon: "/country/sudan.png", ISO2: "SD", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "苏里南", Alias: "Suriname", Code: "597", Icon: "/country/suriname.png", ISO2: "SR", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "所罗门群岛", Alias: "Solomon Islands", Code: "677", Icon: "/country/solomon_islands.png", ISO2: "SB", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "索马里", Alias: "Somalia", Code: "252", Icon: "/country/somalia.png", ISO2: "SO", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "塔吉克斯坦", Alias: "Tajikistan", Code: "992", Icon: "/country/tajikistan.png", ISO2: "TJ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "坦桑尼亚", Alias: "Tanzania", Code: "255", Icon: "/country/tanzania.png", ISO2: "TZ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "汤加", Alias: "Tonga", Code: "676", Icon: "/country/tonga.png", ISO2: "TO", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "特克斯和凯科斯群岛", Alias: "Turks and Caicos Islands", Code: "1-649", Icon: "/country/turks_and_caicos_islands.png", ISO2: "TC", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "突尼斯", Alias: "Tunisia", Code: "216", Icon: "/country/tunisia.png", ISO2: "TN", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "图瓦卢", Alias: "Tuvalu", Code: "688", Icon: "/country/tuvalu.png", ISO2: "TV", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "托克劳", Alias: "Tokelau", Code: "690", Icon: "/country/tokelau.png", ISO2: "TK", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "瓦利斯和富图纳", Alias: "Wallis and Futuna", Code: "681", Icon: "/country/wallis_and_futuna.png", ISO2: "WF", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "瓦努阿图", Alias: "Vanuatu", Code: "678", Icon: "/country/vanuatu.png", ISO2: "VU", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "危地马拉", Alias: "Guatemala", Code: "502", Icon: "/country/guatemala.png", ISO2: "GT", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "委内瑞拉", Alias: "Venezuela", Code: "58", Icon: "/country/venezuela.png", ISO2: "VE", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "乌干达", Alias: "Uganda", Code: "256", Icon: "/country/uganda.png", ISO2: "UG", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "乌克兰", Alias: "Ukraine", Code: "380", Icon: "/country/ukraine.png", ISO2: "UA", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "乌拉圭", Alias: "Uruguay", Code: "598", Icon: "/country/uruguay.png", ISO2: "UY", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "西班牙", Alias: "Spain", Code: "34", Icon: "/country/spain.png", ISO2: "ES", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "希腊", Alias: "Greece", Code: "30", Icon: "/country/greece.png", ISO2: "GR", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
			{AdminID: SuperAdminID, Name: "新西兰", Alias: "New Zealand", Code: "64", Icon: "/country/new_zealand.png", ISO2: "NZ", Sort: 99, Status: CountryStatusEnabled, Data: CountryData{}},
		}

		if err := db.Create(&countries).Error; err != nil {
			return err
		}
	}

	return nil
}
