package models

const (
	// 购买等级配置A
	AdminSettingBuyLevelOptions = "buyLevelOptions"
	// 购买等级配置B
	AdminSettingBuyLevelOptions2 = "buyLevelOptions2"
	// 站点主题色配置
	AdminSettingThemeColor = "themeColor"
	// 站点主题色配置 自动
	AdminSettingThemeColorAuto = "auto"
	// 站点主题色配置 白色
	AdminSettingThemeColorWhite = "white"
	// 站点主题色配置 黑色
	AdminSettingThemeColorBlack = "black"

	// 冻结金额配置
	AdminSettingFreezeBalanceField        = "freezeBalance"
	AdminSettingUnfreezeBalanceOrder      = "unfreezeOrder" // 解冻订单
	AdminSettingFreezeBalanceRewardFreeze = "rewardFreeze"  // 奖励冻结
	AdminSettingFreezeBalanceSignupFreeze = "signupFreeze"  // 注册冻结
	AdminSettingFreezeBalanceInviteFreeze = "inviteFreeze"  // 邀请冻结

	// 基础模版配置
	AdminSettingBasic                            = "basicSettings"
	AdminSettingBasicShowHome                    = "showHome"                    //首页是否登录
	AdminSettingBasicShowChats                   = "showChats"                   //	显示首页客服
	AdminSettingBasicShowAssets                  = "showAssets"                  //	显示资产
	AdminSettingBasicShowFreezeBalance           = "showFreezeBalance"           //	显示冻结余额
	AdminSettingBasicShowTransfer                = "showTransfer"                //	显示站内转账
	AdminSettingBasicShowLevel                   = "showLevel"                   //	显示等级
	AdminSettingBasicShowBuyLevel                = "showBuyLevel"                //	显示购买等级
	AdminSettingBasicShowScore                   = "showScore"                   //	显示积分
	AdminSettingBasicShowAuth                    = "showAuth"                    //	显示认证
	AdminSettingBasicShowAuthView                = "showAuthView"                //	显示认证查看
	AdminSettingBasicShowWithdrawAccountEdit     = "showWithdrawAccountEdit"     //	显示提现账户编辑
	AdminSettingBasicShowWithdrawAccountDelete   = "showWithdrawAccountDelete"   //	显示提现账户删除
	AdminSettingBasicShowWithdrawAccountNumber   = "showWithdrawAccountNumber"   //	显示提现账户号码
	AdminSettingBasicShowModifyPassword          = "showModifyPassword"          //	显示修改密码
	AdminSettingBasicShowModifyWithdrawPassword  = "showModifyWithdrawPassword"  //	显示修改提现密码
	AdminSettingBasicShowBindEmail               = "showBindEmail"               //	显示绑定邮箱
	AdminSettingBasicShowBindTelephone           = "showBindTelephone"           //	显示绑定手机
	AdminSettingBasicShowWithdrawAccountPassword = "showWithdrawAccountPassword" //	显示提现账户[更新｜删除]提现密码
	AdminSettingBasicShowDepositPassword         = "showDepositPassword"         // 显示充值[提交]提现密码
	AdminSettingBasicShowWithdrawPassword        = "showWithdrawPassword"        //	显示提现[提交]提现密码
	AdminSettingBasicShowTransferPassword        = "showTransferPassword"        // 显示转账[提交]提现密码
	AdminSettingBasicShowSwapsPassword           = "showSwapsPassword"           //	显示兑换[提交]提现密码
	AdminSettingBasicShowBuyLevelPassword        = "showBuyLevelPassword"        //	显示购买等级[提交]提现密码
	AdminSettingBasicShowBuyOrderPassword        = "showBuyOrderPassword"        // 显示购买订单[提交]提现密码
	AdminSettingBasicShowAmountSymbolRight       = "showAmountSymbolRight"       // 显示金额单位在右侧
)

// AdminSettingInviteRegisterReward 管理邀请注册奖励
type AdminSettingInviteRegisterReward struct {
	Register float64 `json:"register"` //	注册奖励
	Invite   float64 `json:"invite"`   //	邀请奖励
}

// AdminSettingDistributionReward 管理分销奖励
type AdminSettingDistributionReward struct {
	Level int8    `json:"level"` //	分销级数
	Type  int8    `json:"type"`  //	账单类型
	Rate  float64 `json:"rate"`  //	收益比例
}

// AdminSettingWalletWithdrawNums 管理钱包提现次数
type AdminSettingWalletWithdrawNums struct {
	Day  int8 `json:"day"`  //	天数
	Nums int8 `json:"nums"` //	次数
}

// PreferredProducts 首选产品
type PreferredProducts struct {
	Forex   string `json:"forex"`   //	期货首选
	Futures string `json:"futures"` //	秒合约首选
	Spot    string `json:"spot"`    //	币币首选
	Staking string `json:"staking"` //	质押首选
}

// FuturesRate 期货收益率
type FuturesRate struct {
	Label     string  `json:"label"`     //	标签
	Second    int     `json:"second"`    //	秒数
	Value     float64 `json:"value"`     //	倍率
	MinAmount float64 `json:"minAmount"` // 最小金额
}
