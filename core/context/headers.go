package context

import (
	"net/url"
	"strings"
)

// GetLang 从请求中检索语言。
// It first checks the Accept-Language header, then falls back to the 'lang' query parameter.
func (c *CustomCtx) GetLang() string {
	// Check Accept-Language header first
	acceptLang := c.Get("Accept-Language")
	if acceptLang != "" {
		// The header might contain multiple languages, we'll take the first one
		langs := strings.Split(acceptLang, ",")
		if len(langs) > 0 {
			return langs[0]
		}
	}

	// If not found in header, check query parameter
	return c.Query("lang")
}

// GetTimeZone 从请求中检索时区。
// It first checks the Time-Zone header, then falls back to the 'timezone' query parameter.
func (c *CustomCtx) GetTimeZone() string {
	// Check Time-Zone header first
	timeZone := c.Get("Time-Zone")
	if timeZone != "" {
		return timeZone
	}

	// If not found in header, check query parameter
	return c.Query("timezone")
}

// GetOriginHost 从请求中检索来源Host。
// It first checks the Origin header, then falls back to the Referer header.
// If neither is present, it returns an empty string.
func (c *CustomCtx) GetOriginHost() string {
	// 首先检查 X-Forwarded-Host 标头 自定义的, 不影响其他操作
	origin := c.Get("X-Forwarded-Host")
	if origin != "" {
		return origin
	}

	// 如果 Origin 不存在，请检查 Referer 标头
	referer := c.Get("Referer")
	if referer != "" {
		// Parse the Referer URL to extract the host
		if parsedURL, err := url.Parse(referer); err == nil {
			return parsedURL.Host
		}
	}

	// 如果 Origin 和 Referer 均不存在，则返回空字符串
	return ""
}
