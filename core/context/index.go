package context

import (
	"fmt"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt/v5"
	"github.com/gomodule/redigo/redis"
)

type Handler[T any] func(*CustomCtx, *T) error
type NoRequestBody struct{}

// TokenClaims represents the claims in the JWT token
type TokenClaims struct {
	AdminID    uint //	管理ID
	MerchantID uint //	商户ID
	UserID     uint //	用户ID
	jwt.RegisteredClaims
}

// CustomCtx 自定义 Fiber 上下文
type CustomCtx struct {
	*fiber.Ctx
	Claims     *TokenClaims //	自定义Claims
	Rds        redis.Conn   //	Redis缓存对象
	OriginHost string       // 	来源域名
	Lang       string       // 	当前语言
	TimeZone   string       // 	客户时区
}

// GetLocalsToken 获取上下文中的Token
func GetLocalsToken(c *fiber.Ctx) (*TokenClaims, error) {
	if c.Locals("token") == nil {
		return nil, nil
	}

	// 从上下文中获取 token
	token := c.Locals("token").(*jwt.Token)

	// 使用 MapClaims 来获取 claims
	mapClaims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}

	// 将 MapClaims 转换为 TokenClaims
	claims := &TokenClaims{
		AdminID:    uint(mapClaims["AdminID"].(float64)),
		MerchantID: uint(mapClaims["MerchantID"].(float64)),
		UserID:     uint(mapClaims["UserID"].(float64)),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Unix(int64(mapClaims["exp"].(float64)), 0)),
			IssuedAt:  jwt.NewNumericDate(time.Unix(int64(mapClaims["iat"].(float64)), 0)),
			ID:        mapClaims["jti"].(string),
		},
	}
	return claims, nil
}
