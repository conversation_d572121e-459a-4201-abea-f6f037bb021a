package context

import (
	"github.com/gofiber/fiber/v2"
)

// Response 表示标准 API 响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"msg"`
	Data    interface{} `json:"data,omitempty"`
}

// SuccessResponse 使用给定的数据创建成功响应
func SuccessResponse(data interface{}) *Response {
	return &Response{
		Code:    fiber.StatusOK,
		Message: "success",
		Data:    data,
	}
}

// ErrorResponse 使用给定的消息和代码创建错误响应
func ErrorResponse(message string, code int) *Response {
	return &Response{
		Code:    code,
		Message: message,
	}
}

// ErrorJson 输出错误响应
func (c *CustomCtx) ErrorJson(message string) error {
	return c.JSON(ErrorResponse(message, -1))
}

// SuccessJson 输出成功响应
func (c *CustomCtx) SuccessJson(data interface{}) error {
	return c.JSON(SuccessResponse(data))
}

// SuccessOk 成功返回OK
func (c *CustomCtx) SuccessOk() error {
	return c.JSON(SuccessResponse("ok"))
}
