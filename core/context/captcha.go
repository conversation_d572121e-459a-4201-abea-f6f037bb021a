package context

import (
	"zfeng/core/cache"

	"github.com/dchest/captcha"
	"github.com/gomodule/redigo/redis"
)

// 初始化函数，设置自定义的验证码存储
func init() {
	captcha.SetCustomStore(&CaptchaStore{})
}

// CaptchaStore 结构体，用于实现自定义的验证码存储
type CaptchaStore struct {
}

// Set 方法用于存储验证码
func (s *CaptchaStore) Set(id string, digits []byte) {
	// 获取 Redis 连接
	rdsConn := cache.Rds.Get()
	defer rdsConn.Close()

	// 将验证码存储到 Redis，设置过期时间
	_, _ = rdsConn.Do("SETEX", "captcha:"+id, int(captcha.Expiration.Seconds()), digits)
}

// Get 方法用于获取验证码
func (s *CaptchaStore) Get(id string, clear bool) (digits []byte) {
	// 获取 Redis 连接
	rdsConn := cache.Rds.Get()
	defer rdsConn.Close()

	// 从 Redis 获取验证码
	data, _ := redis.Bytes(rdsConn.Do("GET", "captcha:"+id))
	// 如果 clear 为 true，则删除验证码
	if clear {
		rdsConn.Do("DEL", "captcha:"+id)
	}
	return data
}
