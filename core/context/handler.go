package context

import (
	"zfeng/core/cache"

	validatorV10 "github.com/go-playground/validator/v10"
	"github.com/gomodule/redigo/redis"

	"github.com/gofiber/fiber/v2"
)

// TranslateFunc 翻译函数
var TranslateFunc func(rdsConn redis.Conn, merchantID uint, lang string, field string, args ...interface{}) string

// NewHandler 包装自定义函数
func NewHandler[T any](h Handler[T]) fiber.Handler {
	return func(ctx *fiber.Ctx) error {
		// 将 *fiber.Ctx 转换为 CustomCtx
		customCtx := &CustomCtx{Ctx: ctx}

		// 使用工厂函数创建一个新的实例来解析请求体
		var body T
		if _, ok := any(&body).(*NoRequestBody); !ok {
			if err := ctx.BodyParser(&body); err != nil {
				return ctx.Status(fiber.StatusBadRequest).JSON(customCtx.ErrorJson(err.Error()))
			}
		}

		// 初始化需要使用的参数
		customCtx.Rds = cache.Rds.Get()
		defer customCtx.Rds.Close()
		customCtx.Lang = customCtx.GetLang()
		customCtx.OriginHost = customCtx.GetOriginHost()
		customCtx.TimeZone = customCtx.GetTimeZone()

		// 获取Token 数据
		customCtx.Claims, _ = GetLocalsToken(ctx)

		// 检测参数结构体
		err := validate.Struct(&body)
		if err != nil {
			for _, errs := range err.(validatorV10.ValidationErrors) {
				//	返回 第一条错误信息
				if TranslateFunc != nil {
					return customCtx.ErrorJson(TranslateFunc(customCtx.Rds, 0, customCtx.Lang, errs.Tag(), errs.Field(), errs.Param()))
				} else {
					return customCtx.ErrorJson("Params errors")
				}
			}
		}

		// 调用自定义处理函数
		return h(customCtx, &body)
	}
}
