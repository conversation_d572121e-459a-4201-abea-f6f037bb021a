package views

import (
	"reflect"
	"strings"
)

// Table 表格数据
type Table struct {
	ID            string                     `json:"id"`            //	唯一值
	URL           string                     `json:"url"`           //	请求地址
	AutoRefresh   bool                       `json:"autoRefresh"`   //	自动刷新
	Searchs       *Form                      `json:"searchs"`       //	搜查栏目
	CommonOptions map[string][]*SelectOption `json:"commonOptions"` //	选择Options Maps
	Selected      map[string]string          `json:"selected"`      //	需要赋值的字段 Key = btnDialogID value = 赋值的字段
	Tools         [][]*ButtonDialog          `json:"tools"`         //	工具栏
	Columns       []*TableColumns            `json:"columns"`       //	数据列头
	ColumnOptions [][]*ButtonDialog          `json:"columnOptions"` //	数据列操作
}

// NewTable 创建表格数据
func NewTable(url string) *Table {
	return &Table{URL: url, Selected: map[string]string{}, CommonOptions: map[string][]*SelectOption{}, Columns: make([]*TableColumns, 0)}
}

// SetSearchs 设置表格的搜索形式
func (t *Table) SetSearchs(form *Form) *Table {
	t.Searchs = form
	return t
}

// AutoRefresh 设置表格自动刷新
func (t *Table) SetAutoRefresh() *Table {
	t.AutoRefresh = true
	return t
}

// SetTools 设置工具栏
func (t *Table) SetTools(index int, buttonDialog *ButtonDialog) *Table {
	// 确保 tools 切片有足够的容量
	for len(t.Tools) <= index {
		t.Tools = append(t.Tools, make([]*ButtonDialog, 0))
	}
	t.Tools[index] = append(t.Tools[index], buttonDialog)
	return t
}

// SetColumnOptions 设置操作栏
func (t *Table) SetColumnOptions(index int, buttonDialog *ButtonDialog) *Table {
	// 确保 ColumnOptions 切片有足够的容量
	for len(t.ColumnOptions) <= index {
		t.ColumnOptions = append(t.ColumnOptions, make([]*ButtonDialog, 0))
	}
	t.ColumnOptions[index] = append(t.ColumnOptions[index], buttonDialog)
	return t
}

// SetToolsAndSelected 设置工具栏并且设置选中数据赋值
func (t *Table) SetToolsAndSelected(index int, buttonDialog *ButtonDialog, formField, formScanField string) *Table {
	buttonDialog.Form.SetFieldInputParam(formField, "scanField", formScanField)
	t.SetTools(index, buttonDialog).SetSelected(buttonDialog.ID, formField)
	return t
}

// SetSelected 设置表格的选中数据
func (t *Table) SetSelected(btnDialogID string, formField string) *Table {
	t.Selected[btnDialogID] = formField
	return t
}

// SetColumnParam sets a parameter for a specific column
func (t *Table) SetFieldColumnParam(fieldName string, key string, value interface{}) *Table {
	t.SetFieldColumnParams(fieldName, map[string]interface{}{key: value})
	return t
}

// GetFieldColumn 返回指定字段名称的 TableColumn
func (t *Table) GetFieldColumn(fieldName string) *TableColumns {
	for _, column := range t.Columns {
		if column.Field == fieldName {
			return column
		}
	}
	return nil
}

// SetCommonOptions 设置表格的选择映射
func (t *Table) SetCommonOptions(field string, options []*SelectOption) *Table {
	if t.CommonOptions == nil {
		t.CommonOptions = make(map[string][]*SelectOption)
	}
	t.CommonOptions[field] = options
	return t
}

// SetFieldColumnParams 设置字段列参数
func (t *Table) SetFieldColumnParams(fieldName string, params map[string]interface{}) *Table {
	for _, column := range t.Columns {
		if column.Field == fieldName {
			for key, value := range params {
				switch key {
				case "format":
					column.Format = value.(string)
				case "align":
					column.Align = value.(string)
				case "sortable":
					column.Sortable = value.(bool)
				case "required":
					column.Required = value.(bool)
				}
			}
			break
		}
	}
	return t
}

// StructColumns 指定结构体转成 TableColumns
func (t *Table) StructColumns(v interface{}) *Table {
	val := reflect.TypeOf(v)
	columns := make([]*TableColumns, 0)

	var processFields func(reflect.Type)
	processFields = func(typ reflect.Type) {
		for i := 0; i < typ.NumField(); i++ {
			field := typ.Field(i)
			if field.Anonymous {
				// 处理嵌入的结构体
				processFields(field.Type)
				continue
			}

			columnTag := field.Tag.Get("column")
			if columnTag != "" && columnTag != "-" {
				currentField := field.Name
				if jsonField := field.Tag.Get("json"); jsonField != "" && jsonField != "-" {
					currentField = strings.Split(jsonField, ",")[0]
				}

				column := NewTableColumns()
				column.Name = currentField
				column.Label = currentField
				column.Field = currentField
				column.Sortable = true
				column.Align = "center"

				var afterField string
				tagParts := strings.Split(columnTag, ";")
				for _, part := range tagParts {
					keyValue := strings.SplitN(part, ":", 2)
					if len(keyValue) != 2 {
						continue
					}
					key := strings.TrimSpace(keyValue[0])
					value := strings.TrimSpace(keyValue[1])
					switch key {
					case "label":
						column.Label = value
					case "type":
						//	给 columns 设置类型, 使用 format格式
						switch value {
						case "icon", "image":
							column.SetAvatarFormat(column.Field)
						case "images":
							column.SetImagesFormat(column.Field)
						case "date":
							column.SetDateTimeFormat("YYYY/MM/DD HH:mm")
						}
					case "scanField":
						column.SetScanField(value)
					case "required":
						column.Required = value == "true"
					case "align":
						column.Align = value
					case "sortable":
						column.Sortable = value == "true"
					case "format":
						column.Format = value
					case "after":
						afterField = value
					}
				}

				if afterField != "" {
					// 找到 afterField 对应的列的索引
					afterIndex := -1
					for j, col := range columns {
						if col.Field == afterField {
							afterIndex = j
							break
						}
					}
					if afterIndex != -1 {
						// 在 afterField 后面插入新列
						columns = append(columns[:afterIndex+1], append([]*TableColumns{column}, columns[afterIndex+1:]...)...)
					} else {
						// 如果没找到 afterField，就追加到末尾
						columns = append(columns, column)
					}
				} else {
					// 没有 after 标签，直接追加到末尾
					columns = append(columns, column)
				}
			}
		}
	}

	processFields(val)
	t.Columns = columns
	return t
}
