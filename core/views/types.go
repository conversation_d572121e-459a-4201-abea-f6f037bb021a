package views

// InputType 定义输入的类型常量
const (
	InputTypeText           = "text"           // 表示文本输入类型
	InputTypeHtml           = "html"           // 表示html输入类型
	InputTypeTextarea       = "textarea"       // 表示多行文本输入类型
	InputTypeNumber         = "number"         // 表示数字输入类型
	InputTypeEditor         = "editor"         // 表示富文本输入类型
	InputTypeSelect         = "select"         // 表示下拉选择输入类型
	InputTypeSelectMultiple = "selectMultiple" // 表示多选下拉选择输入类型
	// mask: user#username>id#type=10 user[表名] #[分隔符] username[字段名] >[分隔符] type=10 模型的条件 id[字段值] options: {"merchant": true} 判断是否使用商户管理ID
	InputTypeSelectSearch    = "selectSearch" // 表示搜索下拉选择输入类型
	InputTypeRadio           = "radio"        // 表示单选按钮输入类型
	InputTypeCheckbox        = "checkbox"     // 表示复选框输入类型
	InputTypeDatePicker      = "date"         // 表示日期输入类型
	InputTypeDateRangePicker = "dateRange"    // 表示范围日期输入类型
	InputTypeTimePicker      = "time"         // 表示时间输入类型
	InputTypeDateTimePicker  = "dateTime"     // 表示日期时间输入类型
	InputTypeFile            = "file"         // 表示文件上传
	InputTypeImage           = "image"        // 表示单图片上传
	InputTypeIcon            = "icon"         // 表示图标上传
	InputTypeMultipleImage   = "images"       // 表示多张图片上传
	InputTypeToggle          = "toggle"       // 表示开关输入类型
	InputTypeColor           = "color"        // 表示颜色选择输入类型
	InputTypeTranslate       = "translate"    // 翻译类型
	InputTypeStruct          = "struct"       // 结构体类型
	InputTypeSlice           = "slice"        // 切片类型
	InputTypeDynamic         = "dynamic"      // 动态输入类型
	InputTypeLine            = "line"         // 线性输入类型

	// Size 定义样式大小常量
	SizeXS = "xs" // 特小尺寸
	SizeSM = "sm" // 小尺寸
	SizeMD = "md" // 中等尺寸
	SizeLG = "lg" // 大尺寸
	SizeXL = "xl" // 特大尺寸

	TimeTypeYear   = "year"
	TimeTypeMonth  = "month"
	TimeTypeDay    = "day"
	TimeTypeHour   = "hour"
	TimeTypeMinute = "minute"
	TimeTypeSecond = "second"

	DialogSizeSmall            = "small"            //	弹窗 small 大小	width 300px
	DialogSizeMedium           = "medium"           //	弹窗 medium 大小	width 700px max-width 80vw
	DialogSizeFullWidth        = "fullWidth"        //	弹窗 fullWidth 大小
	DialogSizeSmallFullHeigth  = "smallFullHeigth"  //	宽度 300px 最大高度
	DialogSizeMediumFullHeigth = "mediumFullHeigth" //	宽度 700px max-width 80vw 最大高度
	DialogSizeFullHeigth       = "fullHeigth"       //	弹窗 fullHeight 大小

	DialogTypeForm   = "form"   //	表单类型
	DialogTypeRoute  = "route"  //	路由类型
	DialogTypeCopy   = "copy"   //	复制类型
	DialogTypeQRCode = "qrcode" //	二维码类型

	ColorPrimary   = "primary"   // 主要颜色
	ColorSecondary = "secondary" // 次要颜色
	ColorAccent    = "accent"    // 强调色
	ColorDark      = "dark"      // 深色
	ColorLight     = "light"     // 浅色
	ColorPositive  = "positive"  // 积极色（通常用于成功状态）
	ColorNegative  = "negative"  // 消极色（通常用于错误状态）
	ColorInfo      = "info"      // 信息色
	ColorWarning   = "warning"   // 警告色
	ColorGrey      = "grey"      //	淡灰色
)

// SelectOption 定义下拉选择框的选项结构
type SelectOption struct {
	Label string      `json:"label"` // 选项的显示标签
	Value interface{} `json:"value"` // 选项的值，可以是任意类型
}

// CheckboxOption 定义复选框的选项结构
type CheckboxOption struct {
	Label   string      `json:"label"`   // 选项的显示标签
	Value   interface{} `json:"value"`   // 选项的值，可以是任意类型
	Checked bool        `json:"checked"` // 是否选中
}

// FormatTimePicker 定义时间格式化输入框的选项结构
type FormatTimePicker struct {
	Type  string `json:"type"`
	Value int    `json:"value"`
}

var FormatTimePickerOptions = []*SelectOption{
	{Label: "年", Value: TimeTypeYear},
	{Label: "月", Value: TimeTypeMonth},
	{Label: "日", Value: TimeTypeDay},
	{Label: "时", Value: TimeTypeHour},
	{Label: "分", Value: TimeTypeMinute},
	{Label: "秒", Value: TimeTypeSecond},
}

// GetSecond 获取秒数
func (f *FormatTimePicker) GetSecond() int {
	switch f.Type {
	case TimeTypeYear:
		return f.Value * 365 * 24 * 60 * 60
	case TimeTypeMonth:
		return f.Value * 30 * 24 * 60 * 60
	case TimeTypeDay:
		return f.Value * 24 * 60 * 60
	case TimeTypeHour:
		return f.Value * 60 * 60
	case TimeTypeMinute:
		return f.Value * 60
	case TimeTypeSecond:
		return f.Value
	}
	return 0
}
