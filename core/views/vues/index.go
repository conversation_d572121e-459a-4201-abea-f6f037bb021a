package vues

import "zfeng/core/views"

type Input struct {
	Icon     string                `json:"icon"`     //	图标
	Label    string                `json:"label"`    //	显示标题
	Small    string                `json:"small"`    //	副标题
	Field    string                `json:"field"`    //	字段名称
	Type     string                `json:"type"`     //	类型
	Style    string                `json:"style"`    //	样式
	Default  interface{}           `json:"default"`  //	默认数据
	Readonly bool                  `json:"readonly"` //	只读状态
	Display  bool                  `json:"display"`  //	是否显示
	Rules    string                `json:"rules"`    //	验证规则
	Mask     string                `json:"mask"`     //	特定格式
	Options  []*views.SelectOption `json:"options"`  //	选项
}
