package views

// Button 按钮
type Button struct {
	Label string `json:"label"` //	按钮标题
	Color string `json:"color"` //	按钮颜色
	Size  string `json:"size"`  //	按钮大小
	Class string `json:"class"` //	按钮Class
	Style string `json:"style"` //	按钮样式
}

// NewCreateButton 新增按钮
func NewCreateButton(label string) *But<PERSON> {
	return &Button{Label: label, Color: ColorPrimary, Size: SizeMD}
}

// NewDeleteButton 删除按钮
func NewDeleteButton(label string) *But<PERSON> {
	return &Button{Label: label, Color: ColorNegative, Size: SizeMD}
}

// NewOptionsButton 操作按钮
func NewOptionsButton(label string) *<PERSON><PERSON> {
	return &Button{Label: label, Color: ColorSecondary, Size: SizeXS}
}

// NewRouteButton 路由按钮
func NewRouteButton(label string) *But<PERSON> {
	return &Button{Label: label, Color: ColorAccent, Size: SizeXS}
}
