package views

import (
	"reflect"
	"strconv"
	"strings"
	"zfeng/core/model"

	"github.com/goccy/go-json"
)

// Form 表单
type Form struct {
	Label        string                 `json:"label"`        //	Form显示标题
	Field        string                 `json:"field"`        //	Form字段名称
	ScanField    string                 `json:"scanField"`    //	Form数据字段
	Inputs       [][]*Input             `json:"inputs"`       //	Form输入框列表
	Params       map[string]interface{} `json:"params"`       //	Form参数
	Options      bool                   `json:"options"`      //	显示操作
	ChildrenForm map[string]*Form       `json:"childrenForm"` //	子集Form key = input字段 value = form
}

// NewForm 新的Form
func NewForm() *Form {
	return &Form{Inputs: make([][]*Input, 0), ChildrenForm: map[string]*Form{}, Params: map[string]interface{}{}}
}

// AddChildForm 在当前表单中添加子表单
func (f *Form) AddChildForm(inputField string, childForm *Form) *Form {
	if f.ChildrenForm == nil {
		f.ChildrenForm = make(map[string]*Form)
	}
	f.Params[inputField] = struct{}{}
	f.ChildrenForm[inputField] = childForm
	return f
}

// AddInputs 向当前表单添加多个输入框
func (f *Form) AddInputs(index int, inputs ...*Input) *Form {
	// 确保 f.Inputs 切片有足够的容量
	for len(f.Inputs) <= index {
		f.Inputs = append(f.Inputs, []*Input{})
	}
	f.Inputs[index] = append(f.Inputs[index], inputs...)
	return f
}

// SetLabel 设置表单的标签
func (f *Form) SetLabel(label string) *Form {
	f.Label = label
	return f
}

// SetField 设置表单的字段名称
func (f *Form) SetField(field string) *Form {
	f.Field = field
	return f
}

// SetPagination 设置表单的显示行数
func (f *Form) SetPagination(pagination *model.Pagination) *Form {
	if pagination == nil {
		pagination = model.NewPagination("id")
	}

	if pagination.SortBy == "" {
		pagination.SortBy = "id"
	}

	if pagination.RowsPerPage <= 0 {
		pagination.RowsPerPage = 20
	}

	if pagination.Page <= 0 {
		pagination.Page = 1
	}

	if !pagination.Descending {
		pagination.Descending = true
	}

	f.SetDefaultParams(map[string]interface{}{"pagination": pagination})
	return f
}

// SetDefaultParams 设置表单的默认参数并与原始参数合并
func (f *Form) SetDefaultParams(defaultParams map[string]interface{}) *Form {
	if f.Params == nil {
		f.Params = make(map[string]interface{})
	}

	// 合并默认参数和原始参数
	for key, value := range defaultParams {
		if _, exists := f.Params[key]; !exists {
			f.Params[key] = value
		}
	}

	return f
}

// SetDefaultParam 设置表单的默认参数
func (f *Form) SetDefaultParam(key string, value interface{}) *Form {
	f.SetDefaultParams(map[string]interface{}{key: value})
	return f
}

// SetOptionsParam 设置表单的 options 参数
func (f *Form) SetOptionsParam(fieldName string, options interface{}) *Form {
	f.SetFieldInputParam(fieldName, "options", options)
	return f
}

// SetFieldInputParam 设置 input 单个参数
func (f *Form) SetFieldInputParam(fieldName string, key string, value interface{}) *Form {
	f.SetFieldInputParams(fieldName, map[string]interface{}{key: value})
	return f
}

// SetFieldInputParams 设置 input 中的参数值
func (f *Form) SetFieldInputParams(fieldName string, params map[string]interface{}) *Form {
	for _, inputGroup := range f.Inputs {
		for _, input := range inputGroup {
			if input.Field == fieldName {
				for key, value := range params {
					switch key {
					case "label":
						input.Label = value.(string)
					case "scanField":
						input.ScanField = value.(string)
					case "type":
						input.Type = value.(string)
					case "default":
						input.Default = value
					case "rules":
						input.Rules = value.(string)
					case "readonly":
						input.Readonly = value.(bool)
					case "display":
						input.Display = value.(bool)
					case "mask":
						input.Mask = value.(string)
					case "options":
						input.Options = value
					}
				}
			}
		}
	}
	return f
}

// SetChildFormFlattenInputs 设置 ChildrenForm 中某个 form 的 FlattenInputs
func (f *Form) SetChildFormFlattenInputs(fieldName string) *Form {
	if _, ok := f.ChildrenForm[fieldName]; !ok {
		return f
	}
	f.ChildrenForm[fieldName].FlattenInputs()
	return f
}

// FlattenInputs 将二维的 Inputs 转换为一维数组
func (f *Form) FlattenInputs() *Form {
	var flatInputs [][]*Input
	for _, inputGroup := range f.Inputs {
		for _, input := range inputGroup {
			flatInputs = append(flatInputs, []*Input{input})
		}

	}
	f.Inputs = flatInputs
	return f
}

// ResetInputs 重置表单的输入框
func (f *Form) ResetInputs(fields [][]string) *Form {
	mapInputs := map[string]*Input{}
	for _, inputGroup := range f.Inputs {
		for _, input := range inputGroup {
			mapInputs[input.Field] = input
		}
	}

	newInputs := make([][]*Input, 0)
	for _, field := range fields {
		if len(field) == 1 {
			newInputs = append(newInputs, []*Input{mapInputs[field[0]]})
		} else {
			inputGroup := make([]*Input, 0)
			for _, v := range field {
				inputGroup = append(inputGroup, mapInputs[v])
			}
			newInputs = append(newInputs, inputGroup)
		}
	}
	f.Inputs = newInputs
	return f
}

// ResetChildrenFormInputs 重置 ChildrenForm 中的某个表单的输入框
func (f *Form) ResetChildrenFormInputs(fieldName string, fields [][]string) *Form {
	if _, ok := f.ChildrenForm[fieldName]; !ok {
		return f
	}
	f.ChildrenForm[fieldName].ResetInputs(fields)
	return f
}

// Struct 指定结构体转成 inputs
func (f *Form) Struct(v interface{}) *Form {
	t := reflect.TypeOf(v)
	f.processStruct(t, 0, "")
	return f
}

func (f *Form) processStruct(t reflect.Type, baseIndex int, parentField string) {
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)

		if field.Anonymous {
			// 处理嵌入的结构体
			f.processStruct(field.Type, baseIndex, parentField)
			continue
		}

		tag := field.Tag.Get("views")
		if tag == "" || tag == "-" {
			continue
		}

		input := createInputFromField(field)
		index := processTagAndUpdateInput(tag, input)

		// 调整索引，考虑基础索引
		actualIndex := baseIndex + index

		// 如果字段是结构体类型或指针类型
		if (input.Type == InputTypeSlice || input.Type == InputTypeStruct) && (field.Type.Kind() == reflect.Struct || field.Type.Kind() == reflect.Ptr) {
			childForm := NewForm()
			var fieldType reflect.Type
			if field.Type.Kind() == reflect.Ptr {
				fieldType = field.Type.Elem()
			} else {
				fieldType = field.Type
			}
			childForm.Struct(reflect.New(fieldType).Elem().Interface())
			// 添加主体 input，并设置类型为 struct
			input.Type = InputTypeStruct
			if fieldType.Kind() == reflect.Slice {
				// 如果字段是数组类型，设置 type 为 slice
				input.Type = InputTypeSlice
				childForm.Options = true
			}
			f.AddChildForm(input.Field, childForm)
			f.Params[input.Field] = struct{}{}
		}
		// 确保 inputs 切片有足够的容量
		for len(f.Inputs) <= actualIndex {
			f.Inputs = append(f.Inputs, make([]*Input, 0))
		}
		// 将每个 input 放入单独的数组中
		f.Inputs[actualIndex] = append(f.Inputs[actualIndex], []*Input{input}...)
	}
}

func createInputFromField(field reflect.StructField) *Input {
	currentField := field.Name
	if jsonField := field.Tag.Get("json"); jsonField != "" && jsonField != "-" {
		currentField = strings.Split(jsonField, ",")[0]
	}

	return &Input{
		Label:     currentField,
		Field:     currentField,
		ScanField: currentField,
		Type:      InputTypeText,
	}
}

func processTagAndUpdateInput(tag string, input *Input) int {
	index := 0
	tagParts := strings.Split(tag, ";")
	for _, part := range tagParts {
		keyValue := strings.SplitN(part, ":", 2)
		if len(keyValue) != 2 {
			continue
		}

		key := strings.TrimSpace(keyValue[0])
		value := strings.TrimSpace(keyValue[1])

		switch key {
		case "index":
			index, _ = strconv.Atoi(value)
		case "label":
			input.Label = value
		case "scanField":
			input.ScanField = value
		case "type":
			input.Type = value
		case "default":
			input.Default = value
		case "rules":
			input.Rules = value
		case "readonly":
			input.Readonly = value == "true"
		case "display":
			input.Display = value == "true"
		case "mask":
			input.Mask = value
		case "options":
			input.Options = map[string]interface{}{}
			_ = json.Unmarshal([]byte(value), &input.Options)

		}
	}
	return index
}

// SetDefaultSettingInput 设置默认的 setting 输入框
func (f *Form) SetDefaultSettingInput(settingField string, settingInput Input, childrenForm map[string]*Form) *Form {
	f.SetOptionsParam("settingValue", map[string]interface{}{"input": settingInput, "childrenForm": childrenForm})
	f.SetDefaultParam("settingField", settingField)
	f.SetDefaultParam("settingType", settingInput.Type)
	return f
}
