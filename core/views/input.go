package views

import (
	"fmt"
	"reflect"
	"strconv"
	"time"

	"github.com/goccy/go-json"
)

// Input 输入框
type Input struct {
	Label     string      `json:"label"`     //	显示标题
	Small     string      `json:"small"`     //	副标题
	Field     string      `json:"field"`     //	字段名称
	ScanField string      `json:"scanField"` //	数据字段
	Type      string      `json:"type"`      //	类型
	Default   interface{} `json:"default"`   //	默认数据
	Readonly  bool        `json:"readonly"`  //	只读
	Display   bool        `json:"display"`   //	是否显示
	Mask      string      `json:"mask"`      //	特定格式
	Rules     string      `json:"rules"`     //	验证规则
	Options   interface{} `json:"options"`   //	选项（用于下拉菜单等）
}

func NewInput(label string, field string, inputType string) *Input {
	return &Input{Label: label, Field: field, ScanField: field, Type: inputType}
}

// SetOptions sets the options for the input
func (i *Input) SetOptions(options interface{}) *Input {
	i.Options = options
	return i
}

// SetReadonly sets the readonly status of the input
func (i *Input) SetReadonly(readonly bool) *Input {
	i.Readonly = readonly
	return i
}

// SetDisplay sets the display status of the input
func (i *Input) SetDisplay(display bool) *Input {
	i.Display = display
	return i
}

// SetMask sets the mask for the input
func (i *Input) SetMask(mask string) *Input {
	i.Mask = mask
	return i
}

// SetRules sets the rules for the input
func (i *Input) SetRules(rules string) *Input {
	i.Rules = rules
	return i
}

// SetDefault 设置输入的默认值
func (i *Input) SetDefault(value string) {
	switch i.Type {
	case InputTypeNumber:
		if intVal, err := strconv.Atoi(value); err == nil {
			i.Default = intVal
		} else if floatVal, err := strconv.ParseFloat(value, 64); err == nil {
			i.Default = floatVal
		} else {
			i.Default = value
		}
	case InputTypeSelect, InputTypeCheckbox, InputTypeMultipleImage, InputTypeStruct, InputTypeSlice, InputTypeSelectMultiple:
		_ = json.Unmarshal([]byte(value), &i.Default)
	default:
		i.Default = value
	}
}

// InputValueToInterface 根据输入类型将字符串值转换为对应的接口类型
func InputValueToInterface(inputType, value string) interface{} {
	switch inputType {
	case InputTypeNumber:
		if intVal, err := strconv.Atoi(value); err == nil {
			return intVal
		}
		if floatVal, err := strconv.ParseFloat(value, 64); err == nil {
			return floatVal
		}
		return value
	case InputTypeMultipleImage, InputTypeStruct, InputTypeSlice, InputTypeSelectMultiple, InputTypeCheckbox:
		var result interface{}
		if err := json.Unmarshal([]byte(value), &result); err == nil {
			return result
		}
		return value
	default:
		return value
	}
}

// InputValueToString 将任何类型的输入值转换为字符串表示形式
func InputValueToString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case bool:
		return strconv.FormatBool(v)
	case time.Time:
		return v.Format(time.RFC3339)
	case []byte:
		return string(v)
	default:
		// For complex types, use reflection
		rv := reflect.ValueOf(value)
		switch rv.Kind() {
		case reflect.Struct, reflect.Ptr, reflect.Map, reflect.Slice, reflect.Array:
			// If value is an object (struct or pointer to struct), convert to JSON
			jsonBytes, err := json.Marshal(v)
			if err != nil {
				return fmt.Sprintf("%+v", v)
			}
			return string(jsonBytes)
		default:
			return fmt.Sprintf("%v", v)
		}
	}
}
