package views

import "github.com/google/uuid"

// ButtonDialog 按钮弹窗
type ButtonDialog struct {
	ID         string               `json:"id"`         //	唯一值
	Type       string               `json:"type"`       //	类型
	URL        string               `json:"url"`        //	请求地址
	Title      string               `json:"title"`      //	标题
	Small      string               `json:"small"`      //	副标题
	Content    string               `json:"content"`    //	内容
	Size       string               `json:"size"`       //	大小
	FullWidth  bool                 `json:"fullWidth"`  //	满屏宽度
	FullHeight bool                 `json:"fullHeight"` //	满屏高度
	Form       *Form                `json:"form"`       //	表单数据
	Display    string               `json:"display"`    //	是否显示 eval 条件
	Button     *Button              `json:"button"`     //	按钮信息
	Options    *ButtonDialogOptions `json:"options"`    //	操作栏
}

// 按钮弹窗操作栏
type ButtonDialogOptions struct {
	Cancel *Button `json:"cancel"` //	取消按钮
	Submit *Button `json:"submit"` //	提交按钮
}

// NewFormCreateButtonDialog 添加创建按钮弹窗
func NewFormCreateButtonDialog(title string, url string, form *Form) *ButtonDialog {
	return NewFormButtonDialog(title, url, NewCreateButton(title), form)
}

// NewFormDeleteButtonDialog 新的删除按钮弹窗
func NewFormDeleteButtonDialog(title string, url string, form *Form) *ButtonDialog {
	return NewFormButtonDialog(title, url, NewDeleteButton(title), form)
}

// NewFormOptionsButtonDialog 新的操作按钮弹窗
func NewFormOptionsButtonDialog(title string, url string, form *Form) *ButtonDialog {
	return NewFormButtonDialog(title, url, NewOptionsButton(title), form)
}

// NewFormButtonDialog 按钮弹窗表单
func NewFormButtonDialog(title string, url string, button *Button, form *Form) *ButtonDialog {
	return &ButtonDialog{
		ID:     uuid.NewString(),
		Type:   DialogTypeForm,
		URL:    url,
		Title:  title,
		Small:  "点击弹窗之外内容 【可关闭弹窗】",
		Size:   DialogSizeSmall,
		Form:   form,
		Button: button,
		Options: &ButtonDialogOptions{
			Cancel: &Button{Label: "取消", Color: ColorGrey, Size: SizeMD},
			Submit: &Button{Label: "提交", Color: ColorPrimary, Size: SizeMD},
		},
	}
}

// NewTipsButtonDialog 新的提示按钮弹窗
func NewTipsButtonDialog(content string, button *Button) *ButtonDialog {
	return &ButtonDialog{
		ID:      uuid.NewString(),
		Content: content,
		Button:  button,
		Form:    &Form{},
		Options: &ButtonDialogOptions{
			Cancel: &Button{Label: "取消", Color: ColorGrey, Size: SizeMD},
			Submit: &Button{Label: "提交", Color: ColorPrimary, Size: SizeMD},
		},
	}
}

// NewRouteButtonDialog 新的路由按钮弹窗
func NewRouteButtonDialog(title string, url string) *ButtonDialog {
	return &ButtonDialog{
		ID:      uuid.NewString(),
		Type:    DialogTypeRoute,
		URL:     url,
		Form:    &Form{},
		Button:  NewRouteButton(title),
		Options: &ButtonDialogOptions{},
	}
}

// NewCopyButtonDialog 新的复制按钮弹窗
func NewCopyButtonDialog(title string, content string) *ButtonDialog {
	return &ButtonDialog{
		ID:      uuid.NewString(),
		Type:    DialogTypeCopy,
		Content: content,
		Form:    &Form{},
		Button:  NewRouteButton(title),
		Options: &ButtonDialogOptions{},
	}
}

// NewQRCodeButtonDialog 新的二维码按钮弹窗
func NewQRCodeButtonDialog(title string, label string, content string) *ButtonDialog {
	return &ButtonDialog{
		ID:      uuid.NewString(),
		Type:    DialogTypeQRCode,
		Title:   label,
		Content: content,
		Form:    &Form{},
		Button:  NewRouteButton(title),
		Options: &ButtonDialogOptions{},
	}
}

// SetSmall 设置副标题
func (d *ButtonDialog) SetSmall(small string) *ButtonDialog {
	d.Small = small
	return d
}

// SetDisplay 设置是否显示弹窗
func (d *ButtonDialog) SetDisplay(display string) *ButtonDialog {
	d.Display = display
	return d
}

// SetButtonColor 设置按钮颜色
func (d *ButtonDialog) SetButtonColor(color string) *ButtonDialog {
	if d.Button != nil {
		d.Button.Color = color
	}
	return d
}

// SetSize 设置弹窗大小
func (d *ButtonDialog) SetSize(size string) *ButtonDialog {
	d.Size = size
	return d
}

// SetOptions 设置操作栏
func (d *ButtonDialog) SetOptions(options *ButtonDialogOptions) *ButtonDialog {
	d.Options = options
	return d
}
