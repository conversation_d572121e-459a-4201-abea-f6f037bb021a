package views

import (
	"fmt"

	"github.com/goccy/go-json"
)

// TableColumns 表格列数据
type TableColumns struct {
	Name     string `json:"name"`     // 字段名称
	Label    string `json:"label"`    // 显示标题
	Field    string `json:"field"`    // 字段名称
	Required bool   `json:"required"` // 始终可见
	Align    string `json:"align"`    // 字符对齐
	Sortable bool   `json:"sortable"` // 是否排序
	Format   string `json:"format"`   // 格式化数据 val: any, row: any 可以使用的变量
}

// NewTableColumns 新的数据表格列
func NewTableColumns() *TableColumns {
	return &TableColumns{
		Format: "(val) => { return val === '' ? '- -' : val }",
	}
}

// SetAvatarFormat 设置头像的格式化函数
func (tc *TableColumns) SetAvatarFormat(avatarField string) *TableColumns {
	s := fmt.Sprintf(`(val, row) => {
		const hostUrl = typeof process !== 'undefined' ? process.env.baseURL.split('/').slice(0, 3).join('/') : new URL(window.location.origin).origin;
		const avatar = row.%s === '' ? hostUrl + '/icon.png' : hostUrl + row.%s;
		return '<div class="images cursor-pointer"><img src="' + avatar + '" alt="" style="width: 50px; height: 50px; border-radius: 50%%; object-fit: cover;"></div>';
	}`, avatarField, avatarField)
	tc.SetFormat(s)
	return tc
}

// SetImagesFormat 多图显示
func (tc *TableColumns) SetImagesFormat(imagesField string) *TableColumns {
	s := fmt.Sprintf(`(val, row) => {
		const hostUrl = typeof process !== 'undefined' ? process.env.baseURL.split('/').slice(0, 3).join('/') : new URL(window.location.origin).origin;
		const images = row.%[1]s && row.%[1]s.length > 0 ? row.%[1]s : ['/icon.png'];
		const width = images.length * 25;
		
		return '<div style="height: 40px; width: ' + width + 'px; position: relative;" class="images cursor-pointer">' +
			images.map((img, index) => 
				'<img src="' + hostUrl + img + '" style="width: 40px; height: 40px; border-radius: 50%%; position: absolute; left: ' + (index * 20) + 'px; z-index: ' + (index + 1) + ';">'
			).join('') +
		'</div>';
	}`, imagesField)
	tc.SetFormat(s)
	return tc
}

// SetDateTimeFormat 设置日期时间格式化函数
func (tc *TableColumns) SetDateTimeFormat(format string) *TableColumns {
	formatFunc := fmt.Sprintf(`(val) => {
		if (val == '') return '- -';
		const date = new Date(val);
		const pad = (num) => num.toString().padStart(2, '0');
		const year = date.getFullYear();
		const month = pad(date.getMonth() + 1);
		const day = pad(date.getDate());
		const hours = pad(date.getHours());
		const minutes = pad(date.getMinutes());
		const seconds = pad(date.getSeconds());
		return '%s'
			.replace('YYYY', year)
			.replace('MM', month)
			.replace('DD', day)
			.replace('HH', hours)
			.replace('mm', minutes)
			.replace('ss', seconds);
	}`, format)

	tc.Format = formatFunc
	return tc
}

// SetFormat 设置格式化函数
func (tc *TableColumns) SetFormat(formatFunc string) *TableColumns {
	tc.Format = formatFunc
	return tc
}

// SetHTMLFormat 设置HTML格式化函数
func (tc *TableColumns) SetHTMLFormat(htmlTemplate string) *TableColumns {
	tc.Format = fmt.Sprintf(`(val, row) => {
		return '%s'
	}`, htmlTemplate)
	return tc
}

// SetScanField 设置其他赋值
func (tc *TableColumns) SetScanField(field string) *TableColumns {
	tc.Format = fmt.Sprintf(`(val, row) => {
		return row.%s
	}`, field)
	return tc
}

// SetSelectFormat 设置下拉框格式化函数
func (tc *TableColumns) SetSelectFormat(options []*SelectOption) *TableColumns {
	optionsMap := make(map[string]string)
	for _, option := range options {
		optionsMap[fmt.Sprintf("%v", option.Value)] = option.Label
	}

	optionsJSON, _ := json.Marshal(optionsMap)
	formatFunc := fmt.Sprintf(`(val) => {
		const options = %s;
		return options[val] || val;
	}`, string(optionsJSON))

	tc.Format = formatFunc
	return tc
}

// SetConditionalFormat 设置条件格式化函数
func (tc *TableColumns) SetConditionalFormat(condition string, trueFormat string, falseFormat string) *TableColumns {
	tc.Format = fmt.Sprintf(`(val, row) => {
		if (%s) {
			return '%s'
		} else {
			return '%s'
		}
	}`, condition, trueFormat, falseFormat)
	return tc
}
