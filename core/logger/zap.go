package logger

import (
	"os"
	"zfeng/core/config"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

const (
	LogDir     = "./logs"
	LogFile    = "app.json"
	LogFileDir = LogDir + "/" + LogFile
)

// init 初始化日志
func init() {
	var (
		logger *zap.Logger
		enc    zapcore.Encoder
		writer zapcore.WriteSyncer
		level  zapcore.Level
	)

	// 通用配置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "ts",
		LevelKey:       "level",
		Name<PERSON><PERSON>:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.FullCallerEncoder,
	}

	if config.Debug {
		// 开发环境配置
		encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
		enc = zapcore.NewConsoleEncoder(encoderConfig)
		writer = zapcore.AddSync(os.Stdout)
		level = zapcore.DebugLevel
	} else {
		// 生产环境配置
		enc = zapcore.NewJSONEncoder(encoderConfig)

		// 使用 lumberjack 进行日志文件大小分割
		logRotate := &lumberjack.Logger{
			Filename:   LogFileDir,
			MaxSize:    100, // 单个文件最大 100M
			MaxBackups: 30,  // 最多保留 30 个备份
			MaxAge:     7,   // 最多保留 7 天
			Compress:   true,
		}

		writer = zapcore.AddSync(logRotate)
		level = zapcore.WarnLevel
	}

	core := zapcore.NewCore(enc, writer, level)

	// 创建 Logger 对象，移除 zap.AddStacktrace 选项
	logger = zap.New(core, zap.AddCaller())

	// 设置全局可用
	zap.ReplaceGlobals(logger)

	// 确保在程序退出时同步日志
	defer logger.Sync()
}
