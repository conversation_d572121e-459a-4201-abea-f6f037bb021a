package cache

import (
	"fmt"
	"sync"
	"time"

	"github.com/goccy/go-json"
	"github.com/gomodule/redigo/redis"
)

// RdsSubscribe Redis 订阅方法
var RdsSubscribe *redisSubscribe

func init() {
	RdsSubscribe = &redisSubscribe{clientMaps: map[string]func(data []byte){}, subConn: redis.PubSubConn{Conn: Rds.Get()}}
	go RdsSubscribe.Consume()
}

type redisSubscribe struct {
	sync.RWMutex
	subConn    redis.PubSubConn             //	定义对象
	clientMaps map[string]func(data []byte) //	订阅者
}

// Subscribe 订阅 Redis 频道并注册回调函数
func (r *redisSubscribe) Subscribe(channel string, callback func(data []byte)) error {
	r.Lock()
	defer r.Unlock()

	if r.subConn.Conn == nil {
		conn := Rds.Get()
		r.subConn = redis.PubSubConn{Conn: conn}
	}

	err := r.subConn.Subscribe(channel)
	if err != nil {
		return err
	}

	r.clientMaps[channel] = callback
	return nil
}

// UnSubscribe 取消订阅 Redis 频道
func (r *redisSubscribe) UnSubscribe(channel string) error {
	r.Lock()
	defer r.Unlock()

	if r.subConn.Conn == nil {
		return nil
	}

	err := r.subConn.Unsubscribe(channel)
	if err != nil {
		return err
	}

	delete(r.clientMaps, channel)
	return nil
}

// Consume 消费订阅消息
func (r *redisSubscribe) Consume() {
	for {
		switch msg := r.subConn.Receive().(type) {
		case redis.Message:
			r.RLock()
			callback, ok := r.clientMaps[msg.Channel]
			r.RUnlock()
			if ok {
				go func(cb func(data []byte), data []byte) {
					cb(data)
				}(callback, msg.Data)
			}
		case redis.Subscription:
			// 订阅/取消订阅事件，可以根据需要处理
		case error:
			// 处理错误，重新连接
			r.Lock()

			// 关闭旧连接
			if r.subConn.Conn != nil {
				r.subConn.Close()
			}

			// 重新建立连接
			conn := Rds.Get()
			r.subConn = redis.PubSubConn{Conn: conn}

			// 重新订阅所有频道
			for channel := range r.clientMaps {
				err := r.subConn.Subscribe(channel)
				if err != nil {
					// 如果重新订阅失败，可以记录日志或采取其他措施
					// 这里简单地打印错误信息
					fmt.Printf("Error resubscribing to channel %s: %v\n", channel, err)
				}
			}

			r.Unlock()

			// 避免在错误情况下过于频繁地重试
			time.Sleep(time.Second)

			// 重新连接后继续循环，保持进程在循环中
			continue
		}
	}
}

// Publish 发送订阅消息
func (r *redisSubscribe) Publish(channel string, message interface{}) error {
	conn := Rds.Get()
	defer conn.Close()

	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %v", err)
	}

	_, err = conn.Do("PUBLISH", channel, data)
	if err != nil {
		return fmt.Errorf("failed to publish message: %v", err)
	}

	return nil
}
