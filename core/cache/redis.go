package cache

import (
	"fmt"
	"time"
	"zfeng/core/config"

	"github.com/gomodule/redigo/redis"
)

// Rds 缓存Redis 池
var Rds *redis.Pool

func init() {
	Rds = &redis.Pool{
		MaxIdle:     0,
		MaxActive:   0,
		IdleTimeout: 30 * time.Second,
		Wait:        false,
		Dial: func() (redis.Conn, error) {
			host := fmt.Sprintf("%s:%d", config.Redis.Server, config.Redis.Port)
			conn, err := redis.Dial(
				config.Redis.Network,
				host,
				redis.DialPassword(config.Redis.Pass),
				redis.DialDatabase(config.Redis.DbName),
				redis.DialConnectTimeout(30*time.Second),
				redis.DialReadTimeout(30*time.Second),
				redis.DialWriteTimeout(30*time.Second),
			)
			if err != nil {
				return nil, err
			}
			return conn, nil
		},
	}
}
