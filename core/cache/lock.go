package cache

import (
	"fmt"
	"time"

	"github.com/gomodule/redigo/redis"
)

// RedisLock 结构体封装锁的功能
type RedisLock struct {
	conn      redis.Conn // Redis 连接
	lockKey   string     // 锁的键
	lockValue string     // 锁的唯一值
	ttl       int        // 锁的过期时间（秒）
}

// NewRedisLock 创建一个新的 RedisLock 实例
func NewRedisLock(conn redis.Conn, lockKey string, ttl int) *RedisLock {
	return &RedisLock{
		conn:      conn,
		lockKey:   lockKey,
		lockValue: fmt.Sprintf("%d", time.Now().UnixNano()), // 使用当前时间戳生成唯一值
		ttl:       ttl,
	}
}

// Lock 加锁
func (r *RedisLock) Lock() (bool, error) {
	// 尝试获取锁
	reply, err := redis.String(r.conn.Do("SET", r.lockKey, r.lockValue, "NX", "EX", r.ttl))
	if err != nil {
		return false, err
	}
	return reply == "OK", nil
}

// Unlock 解锁
func (r *RedisLock) Unlock() error {
	// Lua 脚本，确保解锁是原子操作
	luaScript := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("DEL", KEYS[1])
		else
			return 0
		end
	`
	_, err := redis.Int(r.conn.Do("EVAL", luaScript, 1, r.lockKey, r.lockValue))
	return err
}
