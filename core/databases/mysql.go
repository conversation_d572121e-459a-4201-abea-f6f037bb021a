package databases

import (
	"context"
	"fmt"
	"time"
	"zfeng/core/config"
	_ "zfeng/core/logger"

	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
	"gorm.io/gorm/utils"
)

// Db gorm 驱动
var Db *gorm.DB

// InitGorm 初始化Gorm
func init() {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&loc=Local&parseTime=true", config.Db.User, config.Db.Pass, config.Db.Server, config.Db.Port, config.Db.DbName)

	db, _ := gorm.Open(mysql.Open(dsn), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
		},
		Logger: new(GormZapLogger).LogMode(logger.Info),
	})

	sqlDB, _ := db.DB()
	sqlDB.SetMaxIdleConns(config.Db.MaxIdleConns)       // 设置最大空闲连接数
	sqlDB.SetMaxOpenConns(config.Db.MaxOpenConns)       // 设置最大打开连接数
	sqlDB.SetConnMaxLifetime(config.Db.ConnMaxLifetime) // 设置连接的最大生存时间

	Db = db
}

// GormZapLogger 是使用 zap 的 GORM 自定义记录器
type GormZapLogger struct{}

// LogMode 设置记录器的日志模式
func (l *GormZapLogger) LogMode(level logger.LogLevel) logger.Interface {
	return l
}

// Info 记录信息消息
func (l *GormZapLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	zap.L().Info(fmt.Sprintf(msg, data...))
}

// Warn 记录警告消息
func (l *GormZapLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	zap.L().Warn(fmt.Sprintf(msg, data...))
}

// Error 记录错误消息
func (l *GormZapLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	zap.L().Error(fmt.Sprintf(msg, data...))
}

// Trace 记录跟踪消息
func (l *GormZapLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	elapsed := time.Since(begin)
	sql, rows := fc()
	fields := []zap.Field{
		zap.String("sql", sql),
		zap.Int64("rows", rows),
		zap.Duration("elapsed", elapsed),
	}

	if err != nil {
		zap.L().WithOptions(zap.WithCaller(false)).Error(utils.FileWithLineNum()+"   gorm ", append(fields, zap.Error(err))...)
		return
	}
	zap.L().WithOptions(zap.WithCaller(false)).Debug(utils.FileWithLineNum()+"   gorm ", fields...)
}
