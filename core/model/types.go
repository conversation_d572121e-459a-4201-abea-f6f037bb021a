package model

import (
	"database/sql/driver"
	"errors"
	"strings"
	"time"
	"zfeng/utils"

	"github.com/goccy/go-json"
	"gorm.io/gorm"
)

const (
	BoolTrue  int8 = 1 //	真
	BoolFalse int8 = 2 //	假
)

// BaseModel 包含所有数据库表的公共字段
type BaseModel struct {
	ID        uint           `gorm:"primaryKey;autoIncrement" json:"id"` //	主键ID
	CreatedAt time.Time      `gorm:"autoCreateTime" json:"createdAt"`    //	创建时间
	UpdatedAt time.Time      `gorm:"autoUpdateTime" json:"updatedAt"`    //	更新时间
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deletedAt,omitempty"`   //	删除对象
}

// IndexData 列表数据
type IndexData struct {
	Items interface{} `json:"items"` //	数据列表
	Count int64       `json:"count"` //	总数
}

// DeleteParams 删除的参数
type DeleteParams struct {
	Ids []uint `json:"ids" validate:"required" views:"display:true"`
}

// RangeDatePicker 时间范围
type RangeDatePicker struct {
	From string `json:"from"` //	开始时间
	To   string `json:"to"`   //	结束时间
}

// Pagination 分页
type Pagination struct {
	SortBy      string `json:"sortBy"`      //	排序字段
	Descending  bool   `json:"descending"`  //	排序 真DESC 假ASC
	Page        int    `json:"page"`        //	当前页数
	RowsPerPage int    `json:"rowsPerPage"` //	每页显示条数
}

// NewPagination 默认分页数据
func NewPagination(sortBy string) *Pagination {
	return &Pagination{SortBy: sortBy, Descending: true, Page: 1, RowsPerPage: 10}
}

// SetRowsPerPage 设置每页显示的行数
func (p *Pagination) SetRowsPerPage(rows int) *Pagination {
	if rows > 0 {
		p.RowsPerPage = rows
	}
	return p
}

// Scopes 分页处理
func (p *Pagination) Scopes() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if p == nil || p.RowsPerPage <= 0 {
			return db
		}

		if p.Page <= 0 {
			p.Page = 1
		}

		if p.SortBy != "" {
			tableName := db.Statement.Table
			if tableName != "" {
				tableName += "."
			}
			direction := "ASC"
			if p.Descending {
				direction = "DESC"
			}
			db = db.Order(tableName + p.SortBy + " " + direction)
		}

		offset := (p.Page - 1) * p.RowsPerPage
		return db.Offset(offset).Limit(p.RowsPerPage)
	}
}

// GormDateTimeParams 时间格式
type GormDateTimeParams string

func (gtp GormDateTimeParams) Value() (driver.Value, error) {
	if gtp == "" {
		return time.Now(), nil
	}
	return gtp.ToTime(), nil
}

func (gtp GormDateTimeParams) ToTime() time.Time {
	t, err := time.ParseInLocation(time.RFC3339, string(gtp), time.Local)
	if err != nil {
		return time.Now()
	}
	return t
}

// ToTimeZone 将时间转换为指定时区
func (gtp GormDateTimeParams) ToTimeZone(timeZoneOffset string) time.Time {
	gtp = FormatDateTimeToRFC3339(gtp, timeZoneOffset)
	t, err := time.ParseInLocation(time.RFC3339, string(gtp), time.Local)
	if err != nil {
		return time.Now()
	}
	return t
}

// ToLocalTime 转换为本地时区时间
func (gtp GormDateTimeParams) ToLocalTime(timeZoneOffset string) time.Time {
	inputTimeStr := string(gtp) + " " + timeZoneOffset
	layout := "2006/01/02 15:04:05 -07:00"
	// 解析时间
	parsedTime, err := time.ParseInLocation(layout, inputTimeStr, time.Local)
	if err != nil {
		return time.Now()
	}
	return parsedTime
}

// FormatDateTimeToRFC3339 将时间字符串格式化为 RFC3339 格式
func FormatDateTimeToRFC3339(dateTimeStr GormDateTimeParams, timeZoneOffset string) GormDateTimeParams {
	if dateTimeStr == "" || timeZoneOffset == "" {
		return ""
	}

	dateTime := string(dateTimeStr)
	dateTime = strings.ReplaceAll(dateTime, "/", "-")

	if len(dateTimeStr) == 19 {
		return GormDateTimeParams(utils.TimeZoneConversion(dateTime, "2006-01-02 15:04:05", timeZoneOffset).Format(time.RFC3339))
	}

	if len(dateTime) == 10 {
		return GormDateTimeParams(utils.TimeZoneConversion(dateTime, "2006-01-02", timeZoneOffset).Format(time.RFC3339))
	}

	return GormDateTimeParams(time.Now().Format(time.RFC3339))
}

// GormPasswordParams 加密密码类型
type GormPasswordParams string

func (gpp GormPasswordParams) Value() (driver.Value, error) {
	if string(gpp) == "" {
		return "", nil
	}

	return utils.EncryptPassword(string(gpp)), nil
}

// GormStringSlice 用于处理 GORM 中的字符串切片类型
type GormStringSlice []string

// Scan 实现 sql.Scanner 接口，用于从数据库读取数据到 Go 类型
func (gss *GormStringSlice) Scan(value interface{}) error {
	if value == nil {
		*gss = nil
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, gss)
	case string:
		return json.Unmarshal([]byte(v), gss)
	default:
		return errors.New("unsupported Scan, storing driver.Value type into type GormStringSlice")
	}
}

// Value 实现 driver.Valuer 接口，用于将 Go 类型写入数据库
func (gss GormStringSlice) Value() (driver.Value, error) {
	if len(gss) == 0 {
		return "[]", nil
	}
	return json.Marshal(gss)
}
