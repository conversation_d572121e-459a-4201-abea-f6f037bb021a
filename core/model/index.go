package model

import (
	"fmt"
	"time"
	"zfeng/core/databases"

	"gorm.io/gorm"
)

type Model struct {
	*gorm.DB
}

// DB 返回全局数据库实例
func NewModel() *Model {
	return &Model{databases.Db}
}

// SubQueryIn 执行子查询并添加 IN 条件
func (m *Model) SubQueryIn(field string, value interface{}, subQuery *gorm.DB) *Model {
	if IsEmpty(value) {
		return m
	}

	var values []interface{}
	subQuery.Pluck("id", &values)
	if len(values) > 0 {
		m.In(field, values)
	} else {
		m.DB = m.DB.Where("1 = 0") // 如果子查询结果为空，确保不返回任何结果
	}
	return m
}

// BetweenTime 添加模型时间范围查询条件
func (m *Model) BetweenTime(field string, date *RangeDatePicker, contextTimeZoneOffset string) *Model {
	if date == nil || (date.From == "" && date.To == "") {
		return m
	}

	offset := parseTimeZoneOffset(contextTimeZoneOffset)
	parseTime := func(t string) (time.Time, error) {
		parsed, err := time.Parse("2006/01/02", t)
		if err != nil {
			return time.Time{}, err
		}
		return parsed.Add(offset), nil
	}

	staTime, _ := parseTime(date.From)
	endTime, _ := parseTime(date.To)
	m.Between(field, staTime, endTime.Add(86399*time.Second))
	return m
}

// Equal 添加模型相等的条件
func (m *Model) Equal(field string, val interface{}) *Model {
	if IsEmpty(val) {
		return m
	}
	m.DB = m.DB.Where(field+" = ?", val)
	return m
}

// In 添加模型 IN 条件
func (m *Model) In(field string, vals interface{}) *Model {
	if IsEmpty(vals) {
		return m
	}
	m.DB = m.DB.Where(field+" IN ?", vals)
	return m
}

// Gt 添加模型 Gt 大于条件
func (m *Model) Gt(field string, val interface{}) *Model {
	if IsEmpty(val) {
		return m
	}
	m.DB = m.DB.Where(field+" > ?", val)
	return m
}

// Gte 添加模型 Gte 大于等于条件
func (m *Model) Gte(field string, val interface{}) *Model {
	if IsEmpty(val) {
		return m
	}
	m.DB = m.DB.Where(field+" >= ?", val)
	return m
}

// Lt 添加模型 Lt 小于条件
func (m *Model) Lt(field string, val interface{}) *Model {
	if IsEmpty(val) {
		return m
	}
	m.DB = m.DB.Where(field+" < ?", val)
	return m
}

// Lte 添加模型 Lte 小于等于条件
func (m *Model) Lte(field string, val interface{}) *Model {
	if IsEmpty(val) {
		return m
	}
	m.DB = m.DB.Where(field+" <= ?", val)
	return m
}

// Like 添加模型模糊查询条件
func (m *Model) Like(field string, val interface{}) *Model {
	if IsEmpty(val) {
		return m
	}
	m.DB = m.DB.Where(field+" LIKE ?", "%"+val.(string)+"%")
	return m
}

// Between 添加模型范围查询条件
func (m *Model) Between(field string, start, end interface{}) *Model {
	if IsEmpty(start) {
		return m
	}
	m.DB = m.DB.Where(field+" BETWEEN ? AND ?", start, end)
	return m
}

func parseTimeZoneOffset(offset string) time.Duration {
	if offset == "" {
		offset = "+08:00"
	}

	duration, err := time.ParseDuration(offset)
	if err == nil {
		return duration
	}

	hours, minutes := 0, 0
	_, err = fmt.Sscanf(offset, "%d:%d", &hours, &minutes)
	if err != nil {
		return 8 * time.Hour // 默认使用 UTC+8
	}

	duration = time.Duration(hours)*time.Hour + time.Duration(minutes)*time.Minute
	if offset[0] == '-' {
		duration = -duration
	}

	return duration
}

// IsEmpty 判断是否为空
func IsEmpty(v any) bool {
	switch p := v.(type) {
	case []string:
		return len(p) == 0
	case []int:
		return len(p) == 0
	case []int32:
		return len(p) == 0
	case []int64:
		return len(p) == 0
	case []uint:
		return len(p) == 0
	case []uint32:
		return len(p) == 0
	case []uint64:
		return len(p) == 0
	case []float32:
		return len(p) == 0
	case []float64:
		return len(p) == 0
	case string:
		return p == ""
	case int:
		return p == 0
	case int8:
		return p == 0
	case int32:
		return p == 0
	case int64:
		return p == 0
	case uint:
		return p == 0
	case uint32:
		return p == 0
	case uint64:
		return p == 0
	case float32:
		return p == 0
	case float64:
		return p == 0
	case nil:
		return true
	default:
		return false
	}
}
