package config

import (
	"github.com/spf13/viper"
)

var (
	Name            string      = "BaJie"    //	项目名称
	Port            string      = "4000"     //	启动端口
	StaticFilesPath string      = "./static" // 定义静态资源文件路径
	Debug           bool        = true       //	Debug模式
	PreFork         bool        = true       //	开启子进程
	Redis           *cacheRedis              //	缓存Redis配置
	Db              *database                //	数据配置
)

func init() {
	viper.SetConfigName("app")  // 配置文件名（不包括扩展名）
	viper.SetConfigType("yaml") // 配置文件类型
	viper.AddConfigPath(".")    // 配置文件路径

	err := viper.ReadInConfig()
	if err != nil {
		panic(err)
	}

	// 基础配置
	Name = viper.GetString("Name")
	Port = viper.GetString("Port")
	StaticFilesPath = viper.GetString("StaticPath")
	Debug = viper.GetBool("Debug")
	PreFork = viper.GetBool("PreFork")

	// 数据库配置
	Db = &database{
		DbName:          viper.GetString("Database.DbName"),
		Network:         viper.GetString("Database.Network"),
		Server:          viper.GetString("Database.Server"),
		Port:            viper.GetInt("Database.Port"),
		User:            viper.GetString("Database.User"),
		Pass:            viper.GetString("Database.Pass"),
		MaxIdleConns:    viper.GetInt("Database.MaxIdleConns"),
		MaxOpenConns:    viper.GetInt("Database.MaxOpenConns"),
		ConnMaxLifetime: viper.GetDuration("Database.ConnMaxLifetime"),
	}

	// 缓存Redis配置
	Redis = &cacheRedis{
		DbName:  viper.GetInt("Redis.DbName"),
		Network: viper.GetString("Redis.Network"),
		Server:  viper.GetString("Redis.Server"),
		Port:    viper.GetInt("Redis.Port"),
		Pass:    viper.GetString("Redis.Pass"),
	}
}
