package config

import "time"

// database 数据库配置
type database struct {
	DbName          string        // 数据库
	Network         string        // 协议
	Server          string        // 网络
	Port            int           // 端口
	User            string        // 用户名
	Pass            string        // 密码
	MaxIdleConns    int           // 最大空闲连接数
	MaxOpenConns    int           // 最大打开连接数
	ConnMaxLifetime time.Duration // 连接的最大生存时间
}
