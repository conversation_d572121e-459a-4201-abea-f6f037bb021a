import{d as n,_ as r,l,m as a,n as o,j as c,Q as p}from"./index-H1mtFlU6.js";const _=n({name:"ErrorNotFound",__name:"404",setup(s,{expose:e}){e();const t={};return Object.defineProperty(t,"__isScriptSetup",{enumerable:!1,value:!0}),t}}),i={class:"fullscreen bg-blue text-white text-center q-pa-md flex flex-center"};function d(s,e,t,u,f,m){return l(),a("div",i,[o("div",null,[e[0]||(e[0]=o("div",{style:{"font-size":"30vh"}}," 404 ",-1)),e[1]||(e[1]=o("div",{class:"text-h2",style:{opacity:".4"}}," Oops. Nothing here... ",-1)),c(p,{class:"q-mt-xl",color:"white","text-color":"blue",unelevated:"",to:"/",label:"Go Home","no-caps":""})])])}const v=r(_,[["render",d],["__file","404.vue"]]);export{v as default};
