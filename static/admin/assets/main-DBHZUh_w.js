import{a as Yt,b as Wt,c as Te,f as Kt,d as Xt,Q as Jt,e as Zt}from"./QChatMessage-o4w2c2DK.js";import{p as le,i as et,q as de,r as m,c as h,w as q,V as pe,N as yt,h as P,u as tt,g as we,aD as _t,aE as Ue,d as oe,J as ea,I as Oe,aF as Xe,o as xe,a as ta,_ as re,l as k,m as R,j as o,z as u,Q as Z,B as ee,F as me,D as Se,y as L,M as Y,L as st,E as X,A as J,H as Me,C as at,n as z,am as K,x as qe,a1 as ut,t as Re,ak as aa,aG as na,aH as St,aI as la,a4 as oa,aA as ra,aJ as pt,a3 as ia,aK as wt,aL as xt,W as sa,U as It,S as kt,Y as ua,aM as Ae,aN as je,aO as Ie,X as da,aP as dt,P as Ct,Z as ca,aQ as Ge,aR as fa,G as va,av as ma,aS as ga}from"./index-H1mtFlU6.js";import{y as Mt,U as Et,x as Pt,u as ha,q as ba,z as ya,Q as Ee,e as se,c as V,a as _a,b as ke,C as he,B as Dt,D as zt,E as At,F as Sa,T as Ce,l as ue,G as pa,f as Le,d as Be,g as wa,A as xa}from"./QSkeleton-G0ihNNrd.js";import{b as _e,a as te,Q as $e}from"./QCardActions-CYjdimmU.js";import{Q as Je}from"./QResizeObserver-DJfGZaa6.js";import{Q as ne,j as Ia,m as ka}from"./QInput-F165baog.js";import{i as ce,a as De}from"./axios-JqZ6Qsjg.js";import{g as Ca,h as Ma,i as Ea,j as Pa,k as Da}from"./index-CuHAsf8O.js";import{a as za,Q as Pe}from"./use-checkbox-ZuTr6CRM.js";import{u as nt,a as lt}from"./use-dark-BZZjkWkU.js";const Aa=le({name:"QHeader",props:{modelValue:{type:Boolean,default:!0},reveal:Boolean,revealOffset:{type:Number,default:250},bordered:Boolean,elevated:Boolean,heightHint:{type:[String,Number],default:50}},emits:["reveal","focusin"],setup(e,{slots:a,emit:i}){const{proxy:{$q:t}}=we(),r=et(tt,de);if(r===de)return console.error("QHeader needs to be child of QLayout"),de;const c=m(parseInt(e.heightHint,10)),n=m(!0),l=h(()=>e.reveal===!0||r.view.value.indexOf("H")!==-1||t.platform.is.ios&&r.isContainer.value===!0),d=h(()=>{if(e.modelValue!==!0)return 0;if(l.value===!0)return n.value===!0?c.value:0;const x=c.value-r.scroll.value.position;return x>0?x:0}),_=h(()=>e.modelValue!==!0||l.value===!0&&n.value!==!0),s=h(()=>e.modelValue===!0&&_.value===!0&&e.reveal===!0),S=h(()=>"q-header q-layout__section--marginal "+(l.value===!0?"fixed":"absolute")+"-top"+(e.bordered===!0?" q-header--bordered":"")+(_.value===!0?" q-header--hidden":"")+(e.modelValue!==!0?" q-layout--prevent-focus":"")),v=h(()=>{const x=r.rows.value.top,O={};return x[0]==="l"&&r.left.space===!0&&(O[t.lang.rtl===!0?"right":"left"]=`${r.left.size}px`),x[2]==="r"&&r.right.space===!0&&(O[t.lang.rtl===!0?"left":"right"]=`${r.right.size}px`),O});function y(x,O){r.update("header",x,O)}function I(x,O){x.value!==O&&(x.value=O)}function b({height:x}){I(c,x),y("size",x)}function E(x){s.value===!0&&I(n,!0),i("focusin",x)}q(()=>e.modelValue,x=>{y("space",x),I(n,!0),r.animate()}),q(d,x=>{y("offset",x)}),q(()=>e.reveal,x=>{x===!1&&I(n,e.modelValue)}),q(n,x=>{r.animate(),i("reveal",x)}),q(r.scroll,x=>{e.reveal===!0&&I(n,x.direction==="up"||x.position<=e.revealOffset||x.position-x.inflectionPoint<100)});const w={};return r.instances.header=w,e.modelValue===!0&&y("size",c.value),y("space",e.modelValue),y("offset",d.value),pe(()=>{r.instances.header===w&&(r.instances.header=void 0,y("size",0),y("offset",0),y("space",!1))}),()=>{const x=yt(a.default,[]);return e.elevated===!0&&x.push(P("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),x.push(P(Je,{debounce:0,onResize:b})),P("header",{class:S.value,style:v.value,onFocusin:E},x)}}}),Ye="_settingAudio",Tt=_t("setting",{state:()=>({isAudio:Ue.hasItem(Ye)?Ue.getItem(Ye):!0}),actions:{setIsAudio(e){this.isAudio=e,Ue.set(Ye,e)}}}),Ta=oe({name:"HeaderLayout",__name:"header",emits:["toggle-drawer","toggle-right-drawer"],setup(e,{expose:a}){a();const i=ea(),t=Oe(),r=Xe(),c=Tt(),n=Xe(),l=Mt(),d=m(null),_=m(null),s=m(!1),S=m([]),v=m(0),y=m({id:0}),I=m(!1),b=m(!1),E=m({type:1,oldPassword:"",newPassword:"",cmfPassword:""});xe(()=>{Ca().then(g=>{S.value=g.notices,v.value=g.notices.filter(D=>(D.status===10&&y.value.id==0&&(y.value=D),D.status===10)).length,y.value.id>0&&l.notify({message:y.value.title,icon:"info",color:"red",position:"center",actions:[{label:"忽略",color:"white",handler:()=>{}},{label:"查看",color:"white",handler:()=>{H(y.value)}}]})}),_.value==null&&(_.value=setInterval(()=>{F()},2e4))}),ta(()=>{_.value!=null&&(clearInterval(_.value),_.value=null)});const w=()=>{r.initTabs(),t.clearAllUserData(),i.push("/login")},x=()=>{Pa(E.value).then(()=>{b.value=!1,E.value.type===1&&w(),l.notify({message:"更新成功",type:"positive"})})},O=()=>{Da(t.userInfo).then(()=>{I.value=!1,t.setUserInfo(t.userInfo),l.notify({message:"更新成功",type:"positive"})})},H=g=>{Ma({id:g.id}).then(()=>{y.value=g,g.status===10&&(y.value.status=20,v.value--),s.value=!0})},F=()=>{c.isAudio&&Ea().then(g=>{d.value!=null&&g.source!=""&&(d.value.src=ce(g.source),d.value.play(),l.notify({message:g.label,type:"warning"}))})},Q={router:i,userStore:t,$tabsStore:r,$settingStore:c,$tabs:n,$q:l,audioRef:d,audioInterval:_,noticeDetailDialog:s,notices:S,noticesNums:v,noticeDetail:y,updateUserInfoDialog:I,updatePasswordDialog:b,updateParams:E,logoutFunc:w,updatePasswordFunc:x,updateUserInfoFunc:O,noticeDetailFunc:H,audioFunc:F,uploaderComponents:Et,get date(){return Pt},get imageSrc(){return ce}};return Object.defineProperty(Q,"__isScriptSetup",{enumerable:!1,value:!0}),Q}}),qa={key:0,class:"col"},La={class:"text-caption"},Ba={class:"text-h6"},Oa={class:"q-gutter-sm"},Ra={class:"q-gutter-sm"},Va={class:"text-h6"},Fa=["innerHTML"],Na={ref:"audioRef"};function Qa(e,a,i,t,r,c){return k(),R(me,null,[o(Aa,{bordered:"",class:"bg-primary text-white"},{default:u(()=>[o(Yt,null,{default:u(()=>[o(Z,{flat:"",dense:"",round:"",icon:"menu",onClick:a[0]||(a[0]=n=>e.$emit("toggle-drawer"))}),o(Wt,{style:{"min-width":"120px"}},{default:u(()=>a[18]||(a[18]=[ee(" 管理系统 ")])),_:1}),t.$q.screen.gt.xs?(k(),R("div",qa,[o(ha,{align:"left",breakpoint:0,"inline-label":"","narrow-indicator":"","model-value":e.$route.fullPath},{default:u(()=>[(k(!0),R(me,null,Se(t.$tabs.tabs.values(),(n,l)=>(k(),L(ba,{key:l,"no-caps":"",name:n.route,label:n.label,onClick:st(d=>t.router.push(n.route),["stop"])},{default:u(()=>[l>0?(k(),L(Te,{key:0,color:"primary",floating:""},{default:u(()=>[o(Y,{name:"close",onClick:st(d=>t.$tabs.removeTab(t.router,n.route),["stop"])},null,8,["onClick"])]),_:2},1024)):X("",!0)]),_:2},1032,["name","label","onClick"]))),128))]),_:1},8,["model-value"])])):X("",!0),o(Z,{flat:"",round:"",dense:"",icon:t.$q.fullscreen.isActive?"fullscreen_exit":"fullscreen",onClick:a[1]||(a[1]=n=>t.$q.fullscreen.toggle())},null,8,["icon"]),o(Z,{flat:"",round:"",dense:"",icon:"notifications"},{default:u(()=>[t.noticesNums>0?(k(),L(Te,{key:0,color:"red",floating:""},{default:u(()=>[ee(J(t.noticesNums),1)]),_:1})):X("",!0),o(ya,null,{default:u(()=>[t.notices.length>0?(k(),L(_e,{key:0,flat:""},{default:u(()=>[o(Ee,{class:"q-ma-sm"},{default:u(()=>[(k(!0),R(me,null,Se(t.notices,n=>(k(),L(se,{key:n.id,class:Me(n.status===10?"":"text-grey"),onClick:l=>t.noticeDetailFunc(n),clickable:""},{default:u(()=>[o(V,{avatar:""},{default:u(()=>[o(at,{color:n.status===10?"primary":"grey-6","text-color":"white",icon:"notifications"},null,8,["color"])]),_:2},1024),o(V,{class:"ellipsis"},{default:u(()=>[ee(J(n.title)+" ",1),z("div",La,J(t.date.formatDate(n.createdAt,"YYYY-MM-DD HH:mm:ss")),1)]),_:2},1024)]),_:2},1032,["class","onClick"]))),128))]),_:1})]),_:1})):(k(),L(_e,{key:1,flat:""},{default:u(()=>[o(te,null,{default:u(()=>a[19]||(a[19]=[z("div",{class:"text-center text-caption"},"暂无消息通知",-1)])),_:1})]),_:1}))]),_:1})]),_:1}),o(Z,{flat:"",dense:"",icon:"img:"+t.imageSrc(t.userStore.userInfo.avatar),label:t.userStore.userInfo.username,"no-caps":""},{default:u(()=>[o(_a,null,{default:u(()=>[o(Ee,{style:{"min-width":"148px"}},{default:u(()=>[K((k(),L(se,{clickable:"",onClick:a[2]||(a[2]=n=>t.updateUserInfoDialog=!0)},{default:u(()=>[o(V,{avatar:"",style:{"min-width":"0","padding-right":"8px"}},{default:u(()=>[o(Y,{name:"person"})]),_:1}),o(V,null,{default:u(()=>a[20]||(a[20]=[ee("更新管理信息")])),_:1})]),_:1})),[[he]]),K((k(),L(se,{clickable:"",onClick:a[3]||(a[3]=n=>{t.updateParams.type=1,t.updatePasswordDialog=!0})},{default:u(()=>[o(V,{avatar:"",style:{"min-width":"0","padding-right":"8px"}},{default:u(()=>[o(Y,{name:"key"})]),_:1}),o(V,null,{default:u(()=>a[21]||(a[21]=[ee("更新登录密码")])),_:1})]),_:1})),[[he]]),K((k(),L(se,{clickable:"",onClick:a[4]||(a[4]=n=>{t.updateParams.type=2,t.updatePasswordDialog=!0})},{default:u(()=>[o(V,{avatar:"",style:{"min-width":"0","padding-right":"8px"}},{default:u(()=>[o(Y,{name:"password"})]),_:1}),o(V,null,{default:u(()=>a[22]||(a[22]=[ee("更新支付密码")])),_:1})]),_:1})),[[he]]),K((k(),L(se,{clickable:"",onClick:t.logoutFunc},{default:u(()=>[o(V,{avatar:"",style:{"min-width":"0","padding-right":"8px"}},{default:u(()=>[o(Y,{name:"logout"})]),_:1}),o(V,null,{default:u(()=>a[23]||(a[23]=[ee("退出登录")])),_:1})]),_:1})),[[he]])]),_:1})]),_:1})]),_:1},8,["icon","label"]),o(Z,{flat:"",dense:"",round:"",icon:"settings",onClick:a[5]||(a[5]=n=>e.$emit("toggle-right-drawer"))})]),_:1})]),_:1}),o(ke,{modelValue:t.updatePasswordDialog,"onUpdate:modelValue":a[9]||(a[9]=n=>t.updatePasswordDialog=n)},{default:u(()=>[o(_e,{style:{"min-width":"280px"}},{default:u(()=>[o(te,null,{default:u(()=>[z("div",Ba,J(t.updateParams.type===1?"更新登录密码":"更新支付密码"),1)]),_:1}),o(te,null,{default:u(()=>[z("div",Oa,[o(ne,{dense:"",filled:"",modelValue:t.updateParams.oldPassword,"onUpdate:modelValue":a[6]||(a[6]=n=>t.updateParams.oldPassword=n),label:"旧密码"},null,8,["modelValue"]),o(ne,{dense:"",filled:"",modelValue:t.updateParams.newPassword,"onUpdate:modelValue":a[7]||(a[7]=n=>t.updateParams.newPassword=n),label:"新密码"},null,8,["modelValue"]),o(ne,{dense:"",filled:"",modelValue:t.updateParams.cmfPassword,"onUpdate:modelValue":a[8]||(a[8]=n=>t.updateParams.cmfPassword=n),label:"确认密码"},null,8,["modelValue"])])]),_:1}),o($e,{align:"right"},{default:u(()=>[K(o(Z,{flat:"",label:"取消"},null,512),[[he]]),o(Z,{flat:"",label:"确定",onClick:t.updatePasswordFunc})]),_:1})]),_:1})]),_:1},8,["modelValue"]),o(ke,{modelValue:t.updateUserInfoDialog,"onUpdate:modelValue":a[16]||(a[16]=n=>t.updateUserInfoDialog=n)},{default:u(()=>[o(_e,{style:{"min-width":"280px"}},{default:u(()=>[o(te,null,{default:u(()=>a[24]||(a[24]=[z("div",{class:"text-h6"},"更新用户信息",-1)])),_:1}),o(te,null,{default:u(()=>[z("div",Ra,[o(t.uploaderComponents,{path:t.userStore.userInfo.avatar,onUploaded:a[10]||(a[10]=n=>{t.userStore.userInfo.avatar=n})},null,8,["path"]),o(ne,{dense:"",readonly:"",filled:"","model-value":t.date.formatDate(t.userStore.userInfo.expiredAt,"YYYY-MM-DD HH:mm:ss"),label:"过期时间"},null,8,["model-value"]),o(ne,{dense:"",filled:"",modelValue:t.userStore.userInfo.nickname,"onUpdate:modelValue":a[11]||(a[11]=n=>t.userStore.userInfo.nickname=n),label:"昵称"},null,8,["modelValue"]),o(ne,{dense:"",filled:"",modelValue:t.userStore.userInfo.email,"onUpdate:modelValue":a[12]||(a[12]=n=>t.userStore.userInfo.email=n),label:"邮箱"},null,8,["modelValue"]),o(ne,{dense:"",filled:"",modelValue:t.userStore.userInfo.telephone,"onUpdate:modelValue":a[13]||(a[13]=n=>t.userStore.userInfo.telephone=n),label:"号码"},null,8,["modelValue"]),o(ne,{dense:"",filled:"",modelValue:t.userStore.userInfo.chatUrl,"onUpdate:modelValue":a[14]||(a[14]=n=>t.userStore.userInfo.chatUrl=n),label:"客服链接(http|https)"},null,8,["modelValue"]),o(ne,{dense:"",filled:"",type:"textarea",modelValue:t.userStore.userInfo.domains,"onUpdate:modelValue":a[15]||(a[15]=n=>t.userStore.userInfo.domains=n),label:"域名组"},null,8,["modelValue"])])]),_:1}),o($e,{align:"right"},{default:u(()=>[K(o(Z,{flat:"",label:"取消"},null,512),[[he]]),o(Z,{flat:"",label:"确定",onClick:t.updateUserInfoFunc})]),_:1})]),_:1})]),_:1},8,["modelValue"]),o(ke,{modelValue:t.noticeDetailDialog,"onUpdate:modelValue":a[17]||(a[17]=n=>t.noticeDetailDialog=n)},{default:u(()=>[o(_e,{style:{"min-width":"280px"},class:"bg-red text-white"},{default:u(()=>[o(te,null,{default:u(()=>[z("div",Va,J(t.noticeDetail.title),1)]),_:1}),o(te,null,{default:u(()=>[z("div",{innerHTML:t.noticeDetail.content},null,8,Fa)]),_:1}),o($e,{align:"right"},{default:u(()=>[K(o(Z,{flat:"",label:"确定"},null,512),[[he]])]),_:1})]),_:1})]),_:1},8,["modelValue"]),z("audio",Na,null,512)],64)}const Ha=re(Ta,[["render",Qa],["__file","header.vue"]]),ct=150,qt=le({name:"QDrawer",inheritAttrs:!1,props:{...Dt,...nt,side:{type:String,default:"left",validator:e=>["left","right"].includes(e)},width:{type:Number,default:300},mini:Boolean,miniToOverlay:Boolean,miniWidth:{type:Number,default:57},noMiniAnimation:Boolean,breakpoint:{type:Number,default:1023},showIfAbove:Boolean,behavior:{type:String,validator:e=>["default","desktop","mobile"].includes(e),default:"default"},bordered:Boolean,elevated:Boolean,overlay:Boolean,persistent:Boolean,noSwipeOpen:Boolean,noSwipeClose:Boolean,noSwipeBackdrop:Boolean},emits:[...zt,"onLayout","miniState"],setup(e,{slots:a,emit:i,attrs:t}){const r=we(),{proxy:{$q:c}}=r,n=lt(e,c),{preventBodyScroll:l}=pa(),{registerTimeout:d,removeTimeout:_}=za(),s=et(tt,de);if(s===de)return console.error("QDrawer needs to be child of QLayout"),de;let S,v=null,y;const I=m(e.behavior==="mobile"||e.behavior!=="desktop"&&s.totalWidth.value<=e.breakpoint),b=h(()=>e.mini===!0&&I.value!==!0),E=h(()=>b.value===!0?e.miniWidth:e.width),w=m(e.showIfAbove===!0&&I.value===!1?!0:e.modelValue===!0),x=h(()=>e.persistent!==!0&&(I.value===!0||ge.value===!0));function O(f,A){if(g(),f!==!1&&s.animate(),W(0),I.value===!0){const G=s.instances[T.value];G!==void 0&&G.belowBreakpoint===!0&&G.hide(!1),fe(1),s.isContainer.value!==!0&&l(!0)}else fe(0),f!==!1&&Ne(!1);d(()=>{f!==!1&&Ne(!0),A!==!0&&i("show",f)},ct)}function H(f,A){D(),f!==!1&&s.animate(),fe(0),W($.value*E.value),Qe(),A!==!0?d(()=>{i("hide",f)},ct):_()}const{show:F,hide:Q}=At({showing:w,hideOnRouteChange:x,handleShow:O,handleHide:H}),{addToHistory:g,removeFromHistory:D}=Sa(w,Q,x),B={belowBreakpoint:I,hide:Q},U=h(()=>e.side==="right"),$=h(()=>(c.lang.rtl===!0?-1:1)*(U.value===!0?1:-1)),ie=m(0),ae=m(!1),p=m(!1),C=m(E.value*$.value),T=h(()=>U.value===!0?"left":"right"),M=h(()=>w.value===!0&&I.value===!1&&e.overlay===!1?e.miniToOverlay===!0?e.miniWidth:E.value:0),N=h(()=>e.overlay===!0||e.miniToOverlay===!0||s.view.value.indexOf(U.value?"R":"L")!==-1||c.platform.is.ios===!0&&s.isContainer.value===!0),j=h(()=>e.overlay===!1&&w.value===!0&&I.value===!1),ge=h(()=>e.overlay===!0&&w.value===!0&&I.value===!1),Ve=h(()=>"fullscreen q-drawer__backdrop"+(w.value===!1&&ae.value===!1?" hidden":"")),Bt=h(()=>({backgroundColor:`rgba(0,0,0,${ie.value*.4})`})),rt=h(()=>U.value===!0?s.rows.value.top[2]==="r":s.rows.value.top[0]==="l"),Ot=h(()=>U.value===!0?s.rows.value.bottom[2]==="r":s.rows.value.bottom[0]==="l"),Rt=h(()=>{const f={};return s.header.space===!0&&rt.value===!1&&(N.value===!0?f.top=`${s.header.offset}px`:s.header.space===!0&&(f.top=`${s.header.size}px`)),s.footer.space===!0&&Ot.value===!1&&(N.value===!0?f.bottom=`${s.footer.offset}px`:s.footer.space===!0&&(f.bottom=`${s.footer.size}px`)),f}),Vt=h(()=>{const f={width:`${E.value}px`,transform:`translateX(${C.value}px)`};return I.value===!0?f:Object.assign(f,Rt.value)}),Ft=h(()=>"q-drawer__content fit "+(s.isContainer.value!==!0?"scroll":"overflow-auto")),Nt=h(()=>`q-drawer q-drawer--${e.side}`+(p.value===!0?" q-drawer--mini-animate":"")+(e.bordered===!0?" q-drawer--bordered":"")+(n.value===!0?" q-drawer--dark q-dark":"")+(ae.value===!0?" no-transition":w.value===!0?"":" q-layout--prevent-focus")+(I.value===!0?" fixed q-drawer--on-top q-drawer--mobile q-drawer--top-padding":` q-drawer--${b.value===!0?"mini":"standard"}`+(N.value===!0||j.value!==!0?" fixed":"")+(e.overlay===!0||e.miniToOverlay===!0?" q-drawer--on-top":"")+(rt.value===!0?" q-drawer--top-padding":""))),Qt=h(()=>{const f=c.lang.rtl===!0?e.side:T.value;return[[Ce,Gt,void 0,{[f]:!0,mouse:!0}]]}),Ht=h(()=>{const f=c.lang.rtl===!0?T.value:e.side;return[[Ce,it,void 0,{[f]:!0,mouse:!0}]]}),Ut=h(()=>{const f=c.lang.rtl===!0?T.value:e.side;return[[Ce,it,void 0,{[f]:!0,mouse:!0,mouseAllDir:!0}]]});function Fe(){$t(I,e.behavior==="mobile"||e.behavior!=="desktop"&&s.totalWidth.value<=e.breakpoint)}q(I,f=>{f===!0?(S=w.value,w.value===!0&&Q(!1)):e.overlay===!1&&e.behavior!=="mobile"&&S!==!1&&(w.value===!0?(W(0),fe(0),Qe()):F(!1))}),q(()=>e.side,(f,A)=>{s.instances[A]===B&&(s.instances[A]=void 0,s[A].space=!1,s[A].offset=0),s.instances[f]=B,s[f].size=E.value,s[f].space=j.value,s[f].offset=M.value}),q(s.totalWidth,()=>{(s.isContainer.value===!0||document.qScrollPrevented!==!0)&&Fe()}),q(()=>e.behavior+e.breakpoint,Fe),q(s.isContainer,f=>{w.value===!0&&l(f!==!0),f===!0&&Fe()}),q(s.scrollbarWidth,()=>{W(w.value===!0?0:void 0)}),q(M,f=>{ve("offset",f)}),q(j,f=>{i("onLayout",f),ve("space",f)}),q(U,()=>{W()}),q(E,f=>{W(),He(e.miniToOverlay,f)}),q(()=>e.miniToOverlay,f=>{He(f,E.value)}),q(()=>c.lang.rtl,()=>{W()}),q(()=>e.mini,()=>{e.noMiniAnimation||e.modelValue===!0&&(jt(),s.animate())}),q(b,f=>{i("miniState",f)});function W(f){f===void 0?qe(()=>{f=w.value===!0?0:E.value,W($.value*f)}):(s.isContainer.value===!0&&U.value===!0&&(I.value===!0||Math.abs(f)===E.value)&&(f+=$.value*s.scrollbarWidth.value),C.value=f)}function fe(f){ie.value=f}function Ne(f){const A=f===!0?"remove":s.isContainer.value!==!0?"add":"";A!==""&&document.body.classList[A]("q-body--drawer-toggle")}function jt(){v!==null&&clearTimeout(v),r.proxy&&r.proxy.$el&&r.proxy.$el.classList.add("q-drawer--mini-animate"),p.value=!0,v=setTimeout(()=>{v=null,p.value=!1,r&&r.proxy&&r.proxy.$el&&r.proxy.$el.classList.remove("q-drawer--mini-animate")},150)}function Gt(f){if(w.value!==!1)return;const A=E.value,G=ue(f.distance.x,0,A);if(f.isFinal===!0){G>=Math.min(75,A)===!0?F():(s.animate(),fe(0),W($.value*A)),ae.value=!1;return}W((c.lang.rtl===!0?U.value!==!0:U.value)?Math.max(A-G,0):Math.min(0,G-A)),fe(ue(G/A,0,1)),f.isFirst===!0&&(ae.value=!0)}function it(f){if(w.value!==!0)return;const A=E.value,G=f.direction===e.side,ze=(c.lang.rtl===!0?G!==!0:G)?ue(f.distance.x,0,A):0;if(f.isFinal===!0){Math.abs(ze)<Math.min(75,A)===!0?(s.animate(),fe(1),W(0)):Q(),ae.value=!1;return}W($.value*ze),fe(ue(1-ze/A,0,1)),f.isFirst===!0&&(ae.value=!0)}function Qe(){l(!1),Ne(!0)}function ve(f,A){s.update(e.side,f,A)}function $t(f,A){f.value!==A&&(f.value=A)}function He(f,A){ve("size",f===!0?e.miniWidth:A)}return s.instances[e.side]=B,He(e.miniToOverlay,E.value),ve("space",j.value),ve("offset",M.value),e.showIfAbove===!0&&e.modelValue!==!0&&w.value===!0&&e["onUpdate:modelValue"]!==void 0&&i("update:modelValue",!0),xe(()=>{i("onLayout",j.value),i("miniState",b.value),S=e.showIfAbove===!0;const f=()=>{(w.value===!0?O:H)(!1,!0)};if(s.totalWidth.value!==0){qe(f);return}y=q(s.totalWidth,()=>{y(),y=void 0,w.value===!1&&e.showIfAbove===!0&&I.value===!1?F(!1):f()})}),pe(()=>{y!==void 0&&y(),v!==null&&(clearTimeout(v),v=null),w.value===!0&&Qe(),s.instances[e.side]===B&&(s.instances[e.side]=void 0,ve("size",0),ve("offset",0),ve("space",!1))}),()=>{const f=[];I.value===!0&&(e.noSwipeOpen===!1&&f.push(K(P("div",{key:"open",class:`q-drawer__opener fixed-${e.side}`,"aria-hidden":"true"}),Qt.value)),f.push(ut("div",{ref:"backdrop",class:Ve.value,style:Bt.value,"aria-hidden":"true",onClick:Q},void 0,"backdrop",e.noSwipeBackdrop!==!0&&w.value===!0,()=>Ut.value)));const A=b.value===!0&&a.mini!==void 0,G=[P("div",{...t,key:""+A,class:[Ft.value,t.class]},A===!0?a.mini():Re(a.default))];return e.elevated===!0&&w.value===!0&&G.push(P("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),f.push(ut("aside",{ref:"content",class:Nt.value,style:Vt.value},G,"contentclose",e.noSwipeClose!==!0&&I.value===!0,()=>Ht.value)),P("div",{class:"q-drawer-container"},f)}}}),Ua=le({name:"QSlideTransition",props:{appear:Boolean,duration:{type:Number,default:300}},emits:["show","hide"],setup(e,{slots:a,emit:i}){let t=!1,r,c,n=null,l=null,d,_;function s(){r&&r(),r=null,t=!1,n!==null&&(clearTimeout(n),n=null),l!==null&&(clearTimeout(l),l=null),c!==void 0&&c.removeEventListener("transitionend",d),d=null}function S(b,E,w){E!==void 0&&(b.style.height=`${E}px`),b.style.transition=`height ${e.duration}ms cubic-bezier(.25, .8, .50, 1)`,t=!0,r=w}function v(b,E){b.style.overflowY=null,b.style.height=null,b.style.transition=null,s(),E!==_&&i(E)}function y(b,E){let w=0;c=b,t===!0?(s(),w=b.offsetHeight===b.scrollHeight?0:void 0):(_="hide",b.style.overflowY="hidden"),S(b,w,E),n=setTimeout(()=>{n=null,b.style.height=`${b.scrollHeight}px`,d=x=>{l=null,(Object(x)!==x||x.target===b)&&v(b,"show")},b.addEventListener("transitionend",d),l=setTimeout(d,e.duration*1.1)},100)}function I(b,E){let w;c=b,t===!0?s():(_="show",b.style.overflowY="hidden",w=b.scrollHeight),S(b,w,E),n=setTimeout(()=>{n=null,b.style.height=0,d=x=>{l=null,(Object(x)!==x||x.target===b)&&v(b,"hide")},b.addEventListener("transitionend",d),l=setTimeout(d,e.duration*1.1)},100)}return pe(()=>{t===!0&&s()}),()=>P(aa,{css:!1,appear:e.appear,onEnter:y,onLeave:I},a.default)}}),be=na({}),ja=Object.keys(St),Ga=le({name:"QExpansionItem",props:{...St,...Dt,...nt,icon:String,label:String,labelLines:[Number,String],caption:String,captionLines:[Number,String],dense:Boolean,toggleAriaLabel:String,expandIcon:String,expandedIcon:String,expandIconClass:[Array,String,Object],duration:{},headerInsetLevel:Number,contentInsetLevel:Number,expandSeparator:Boolean,defaultOpened:Boolean,hideExpandIcon:Boolean,expandIconToggle:Boolean,switchToggleSide:Boolean,denseToggle:Boolean,group:String,popup:Boolean,headerStyle:[Array,String,Object],headerClass:[Array,String,Object]},emits:[...zt,"click","afterShow","afterHide"],setup(e,{slots:a,emit:i}){const{proxy:{$q:t}}=we(),r=lt(e,t),c=m(e.modelValue!==null?e.modelValue:e.defaultOpened),n=m(null),l=Ia(),{show:d,hide:_,toggle:s}=At({showing:c});let S,v;const y=h(()=>`q-expansion-item q-item-type q-expansion-item--${c.value===!0?"expanded":"collapsed"} q-expansion-item--${e.popup===!0?"popup":"standard"}`),I=h(()=>e.contentInsetLevel===void 0?null:{["padding"+(t.lang.rtl===!0?"Right":"Left")]:e.contentInsetLevel*56+"px"}),b=h(()=>e.disable!==!0&&(e.href!==void 0||e.to!==void 0&&e.to!==null&&e.to!=="")),E=h(()=>{const M={};return ja.forEach(N=>{M[N]=e[N]}),M}),w=h(()=>b.value===!0||e.expandIconToggle!==!0),x=h(()=>e.expandedIcon!==void 0&&c.value===!0?e.expandedIcon:e.expandIcon||t.iconSet.expansionItem[e.denseToggle===!0?"denseIcon":"icon"]),O=h(()=>e.disable!==!0&&(b.value===!0||e.expandIconToggle===!0)),H=h(()=>({expanded:c.value===!0,detailsId:l.value,toggle:s,show:d,hide:_})),F=h(()=>{const M=e.toggleAriaLabel!==void 0?e.toggleAriaLabel:t.lang.label[c.value===!0?"collapse":"expand"](e.label);return{role:"button","aria-expanded":c.value===!0?"true":"false","aria-controls":l.value,"aria-label":M}});q(()=>e.group,M=>{v!==void 0&&v(),M!==void 0&&$()});function Q(M){b.value!==!0&&s(M),i("click",M)}function g(M){M.keyCode===13&&D(M,!0)}function D(M,N){N!==!0&&n.value!==null&&n.value.focus(),s(M),oa(M)}function B(){i("afterShow")}function U(){i("afterHide")}function $(){S===void 0&&(S=ka()),c.value===!0&&(be[e.group]=S);const M=q(c,j=>{j===!0?be[e.group]=S:be[e.group]===S&&delete be[e.group]}),N=q(()=>be[e.group],(j,ge)=>{ge===S&&j!==void 0&&j!==S&&_()});v=()=>{M(),N(),be[e.group]===S&&delete be[e.group],v=void 0}}function ie(){const M={class:[`q-focusable relative-position cursor-pointer${e.denseToggle===!0&&e.switchToggleSide===!0?" items-end":""}`,e.expandIconClass],side:e.switchToggleSide!==!0,avatar:e.switchToggleSide},N=[P(Y,{class:"q-expansion-item__toggle-icon"+(e.expandedIcon===void 0&&c.value===!0?" q-expansion-item__toggle-icon--rotated":""),name:x.value})];return O.value===!0&&(Object.assign(M,{tabindex:0,...F.value,onClick:D,onKeyup:g}),N.unshift(P("div",{ref:n,class:"q-expansion-item__toggle-focus q-icon q-focus-helper q-focus-helper--rounded",tabindex:-1}))),P(V,M,()=>N)}function ae(){let M;return a.header!==void 0?M=[].concat(a.header(H.value)):(M=[P(V,()=>[P(Be,{lines:e.labelLines},()=>e.label||""),e.caption?P(Be,{lines:e.captionLines,caption:!0},()=>e.caption):null])],e.icon&&M[e.switchToggleSide===!0?"push":"unshift"](P(V,{side:e.switchToggleSide===!0,avatar:e.switchToggleSide!==!0},()=>P(Y,{name:e.icon})))),e.disable!==!0&&e.hideExpandIcon!==!0&&M[e.switchToggleSide===!0?"unshift":"push"](ie()),M}function p(){const M={ref:"item",style:e.headerStyle,class:e.headerClass,dark:r.value,disable:e.disable,dense:e.dense,insetLevel:e.headerInsetLevel};return w.value===!0&&(M.clickable=!0,M.onClick=Q,Object.assign(M,b.value===!0?E.value:F.value)),P(se,M,ae)}function C(){return K(P("div",{key:"e-content",class:"q-expansion-item__content relative-position",style:I.value,id:l.value},Re(a.default)),[[la,c.value]])}function T(){const M=[p(),P(Ua,{duration:e.duration,onShow:B,onHide:U},C)];return e.expandSeparator===!0&&M.push(P(Le,{class:"q-expansion-item__border q-expansion-item__border--top absolute-top",dark:r.value}),P(Le,{class:"q-expansion-item__border q-expansion-item__border--bottom absolute-bottom",dark:r.value})),M}return e.group!==void 0&&$(),pe(()=>{v!==void 0&&v()}),()=>P("div",{class:y.value},[P("div",{class:"q-expansion-item__container relative-position"},T())])}}),$a=oe({name:"RecursiveMenu",__name:"menu",props:{defaultOpened:{type:Boolean,default:!1},menu:{type:Object,required:!0}},setup(e,{expose:a}){a();const i=e,t=ra(),r=h(()=>t.path===i.menu.route),c=h(()=>r.value?!0:i.menu.children?i.menu.children.some(d=>n(d,t.path)):!1),n=(d,_)=>d.route===_?!0:d.children?d.children.some(s=>n(s,_)):!1,l={props:i,route:t,isCurrentRoute:r,isExpanded:c,isChildRouteActive:n};return Object.defineProperty(l,"__isScriptSetup",{enumerable:!1,value:!0}),l}});function Ya(e,a,i,t,r,c){const n=pt("recursive-menu");return!i.menu.children||i.menu.children.length===0?K((k(),L(se,{key:0,to:i.menu.route,clickable:"",class:Me(["menu-item",{"active-route":t.isCurrentRoute}])},{default:u(()=>[o(V,{avatar:"",class:"custom-avatar-section"},{default:u(()=>[o(Y,{name:i.menu.data.icon,color:"primary",size:"sm"},null,8,["name"])]),_:1}),o(V,null,{default:u(()=>[o(Be,null,{default:u(()=>[ee(J(i.menu.name),1)]),_:1})]),_:1})]),_:1},8,["to","class"])),[[ia]]):(k(),L(Ga,{key:1,label:i.menu.name,"default-opened":t.isExpanded||i.defaultOpened,group:"menu","expand-separator":"",class:"menu-expansion-item"},{header:u(()=>[o(V,{avatar:"",class:"custom-avatar-section"},{default:u(()=>[o(Y,{name:i.menu.data.icon,color:"primary",size:"sm"},null,8,["name"])]),_:1}),o(V,null,{default:u(()=>[o(Be,null,{default:u(()=>[ee(J(i.menu.name),1)]),_:1})]),_:1})]),default:u(()=>[o(Ee,{class:"q-pl-lg q-pb-sm"},{default:u(()=>[(k(!0),R(me,null,Se(i.menu.children,l=>(k(),L(n,{key:l.id,menu:l},null,8,["menu"]))),128))]),_:1})]),_:1},8,["label","default-opened"]))}const Wa=re($a,[["render",Ya],["__scopeId","data-v-fcb328b6"],["__file","menu.vue"]]),Ka=oe({name:"LeftDrawerLayout",__name:"leftDrawer",setup(e,{expose:a}){const i=Oe(),t=m(!1),r=m([]),c=m(0),n={id:"default",name:"控制台",route:"/",data:{icon:"dashboard"}};xe(()=>{r.value=i.menus}),a({toggleLeftDrawer:()=>{t.value=!t.value}});const l={userStore:i,leftDrawerOpen:t,menus:r,defaultMenuId:c,defaultMenu:n,RecursiveMenu:Wa};return Object.defineProperty(l,"__isScriptSetup",{enumerable:!1,value:!0}),l}});function Xa(e,a,i,t,r,c){return k(),L(qt,{modelValue:t.leftDrawerOpen,"onUpdate:modelValue":a[0]||(a[0]=n=>t.leftDrawerOpen=n),"show-if-above":"",bordered:"",side:"left",class:"bg-grey-1 q-pt-md",width:260},{default:u(()=>[o(Ee,null,{default:u(()=>[o(t.RecursiveMenu,{menu:t.defaultMenu}),(k(!0),R(me,null,Se(t.menus,n=>(k(),R(me,{key:n.id},[ee(J(void(t.defaultMenuId=t.defaultMenuId==0&&n.children.length>0?n.id:t.defaultMenuId))+" ",1),o(t.RecursiveMenu,{menu:n,"default-opened":t.defaultMenuId==n.id},null,8,["menu","default-opened"])],64))),128))]),_:1})]),_:1},8,["modelValue"])}const Ja=re(Ka,[["render",Xa],["__scopeId","data-v-861a4a22"],["__file","leftDrawer.vue"]]),Za=oe({name:"RightDrawerLayout",__name:"rightDrawer",setup(e,{expose:a}){const i=Tt(),t=m(!1);a({toggleRightDrawer:()=>{t.value=!t.value}});const r={$settingStore:i,rightDrawerOpen:t};return Object.defineProperty(r,"__isScriptSetup",{enumerable:!1,value:!0}),r}}),en={class:"column q-ma-md"},tn={class:"col-auto"};function an(e,a,i,t,r,c){return k(),L(qt,{modelValue:t.rightDrawerOpen,"onUpdate:modelValue":a[2]||(a[2]=n=>t.rightDrawerOpen=n),"show-if-above":"",bordered:"",behavior:"mobile",side:"right"},{default:u(()=>[z("div",en,[a[3]||(a[3]=z("div",{class:"col-auto"},[z("div",{class:"text-h6"},"偏好设置")],-1)),z("div",tn,[o(wa,{modelValue:t.$settingStore.isAudio,"onUpdate:modelValue":[a[0]||(a[0]=n=>t.$settingStore.isAudio=n),a[1]||(a[1]=n=>t.$settingStore.setIsAudio(n))],label:"开启提示音"},null,8,["modelValue"])])])]),_:1},8,["modelValue"])}const nn=re(Za,[["render",an],["__scopeId","data-v-de7e8a37"],["__file","rightDrawer.vue"]]),ln='<g fill="none" fill-rule="evenodd" transform="translate(1 1)" stroke-width="2"><circle cx="22" cy="22" r="6"><animate attributeName="r" begin="1.5s" dur="3s" values="6;22" calcMode="linear" repeatCount="indefinite"></animate><animate attributeName="stroke-opacity" begin="1.5s" dur="3s" values="1;0" calcMode="linear" repeatCount="indefinite"></animate><animate attributeName="stroke-width" begin="1.5s" dur="3s" values="2;0" calcMode="linear" repeatCount="indefinite"></animate></circle><circle cx="22" cy="22" r="6"><animate attributeName="r" begin="3s" dur="3s" values="6;22" calcMode="linear" repeatCount="indefinite"></animate><animate attributeName="stroke-opacity" begin="3s" dur="3s" values="1;0" calcMode="linear" repeatCount="indefinite"></animate><animate attributeName="stroke-width" begin="3s" dur="3s" values="2;0" calcMode="linear" repeatCount="indefinite"></animate></circle><circle cx="22" cy="22" r="8"><animate attributeName="r" begin="0s" dur="1.5s" values="6;1;2;3;4;5;6" calcMode="linear" repeatCount="indefinite"></animate></circle></g>',on=le({name:"QSpinnerRings",props:wt,setup(e){const{cSize:a,classes:i}=xt(e);return()=>P("svg",{class:i.value,stroke:"currentColor",width:a.value,height:a.value,viewBox:"0 0 45 45",xmlns:"http://www.w3.org/2000/svg",innerHTML:ln})}}),rn={position:{type:String,default:"bottom-right",validator:e=>["top-right","top-left","bottom-right","bottom-left","top","right","bottom","left"].includes(e)},offset:{type:Array,validator:e=>e.length===2},expand:Boolean};function sn(){const{props:e,proxy:{$q:a}}=we(),i=et(tt,de);if(i===de)return console.error("QPageSticky needs to be child of QLayout"),de;const t=h(()=>{const S=e.position;return{top:S.indexOf("top")!==-1,right:S.indexOf("right")!==-1,bottom:S.indexOf("bottom")!==-1,left:S.indexOf("left")!==-1,vertical:S==="top"||S==="bottom",horizontal:S==="left"||S==="right"}}),r=h(()=>i.header.offset),c=h(()=>i.right.offset),n=h(()=>i.footer.offset),l=h(()=>i.left.offset),d=h(()=>{let S=0,v=0;const y=t.value,I=a.lang.rtl===!0?-1:1;y.top===!0&&r.value!==0?v=`${r.value}px`:y.bottom===!0&&n.value!==0&&(v=`${-n.value}px`),y.left===!0&&l.value!==0?S=`${I*l.value}px`:y.right===!0&&c.value!==0&&(S=`${-I*c.value}px`);const b={transform:`translate(${S}, ${v})`};return e.offset&&(b.margin=`${e.offset[1]}px ${e.offset[0]}px`),y.vertical===!0?(l.value!==0&&(b[a.lang.rtl===!0?"right":"left"]=`${l.value}px`),c.value!==0&&(b[a.lang.rtl===!0?"left":"right"]=`${c.value}px`)):y.horizontal===!0&&(r.value!==0&&(b.top=`${r.value}px`),n.value!==0&&(b.bottom=`${n.value}px`)),b}),_=h(()=>`q-page-sticky row flex-center fixed-${e.position} q-page-sticky--${e.expand===!0?"expand":"shrink"}`);function s(S){const v=Re(S.default);return P("div",{class:_.value,style:d.value},e.expand===!0?v:[P("div",v)])}return{$layout:i,getStickyContent:s}}const un=le({name:"QPageSticky",props:rn,setup(e,{slots:a}){const{getStickyContent:i}=sn();return()=>i(a)}});class dn{ws=null;options;reconnectAttempts=0;maxReconnectAttempts=5;reconnectInterval=3e3;reconnectTimeoutId=null;constructor(a){this.options=a,this.connect()}connect(){try{this.ws=new WebSocket(this.options.url),this.ws.onopen=()=>{this.reconnectAttempts=0,this.options.onOpen&&this.options.onOpen()},this.ws.onmessage=a=>{if(this.options.onMessage){let i;try{i=JSON.parse(a.data)}catch{i=a.data}this.options.onMessage(i)}},this.ws.onclose=()=>{this.options.onClose&&this.options.onClose(),this.attemptReconnect()},this.ws.onerror=a=>{this.options.onError&&this.options.onError(a),this.ws?.close()}}catch(a){console.error("WebSocket connection error:",a),this.attemptReconnect()}}attemptReconnect(){this.reconnectAttempts<this.maxReconnectAttempts&&(this.reconnectAttempts++,this.reconnectTimeoutId&&window.clearTimeout(this.reconnectTimeoutId),this.reconnectTimeoutId=window.setTimeout(()=>{this.connect()},this.reconnectInterval))}send(a){if(this.ws?.readyState===WebSocket.OPEN){const i=typeof a=="string"?a:JSON.stringify(a);this.ws.send(i)}}close(){this.reconnectTimeoutId&&window.clearTimeout(this.reconnectTimeoutId),this.ws?.close()}getState(){return this.ws?.readyState}}const ft=1,Ze=2,We=2,vt=2,cn="bindUser",fn="online",vn="offline",ot="message",mn="read",gn=_t("chats",{state:()=>({userToken:"",clientSocket:null,onMessage:null}),getters:{socket:e=>(e.clientSocket||(e.clientSocket=new dn({url:"/admin/chats/ws",onOpen:()=>{e.clientSocket?.send({op:cn,data:e.userToken})},onMessage:a=>{e.onMessage&&e.onMessage(a)}})),e.clientSocket)},actions:{init(e){this.userToken=e,this.socket},onMessage(e){this.onMessage=e}}}),hn='<circle cx="15" cy="15" r="15"><animate attributeName="r" from="15" to="15" begin="0s" dur="0.8s" values="15;9;15" calcMode="linear" repeatCount="indefinite"></animate><animate attributeName="fill-opacity" from="1" to="1" begin="0s" dur="0.8s" values="1;.5;1" calcMode="linear" repeatCount="indefinite"></animate></circle><circle cx="60" cy="15" r="9" fill-opacity=".3"><animate attributeName="r" from="9" to="9" begin="0s" dur="0.8s" values="9;15;9" calcMode="linear" repeatCount="indefinite"></animate><animate attributeName="fill-opacity" from=".5" to=".5" begin="0s" dur="0.8s" values=".5;1;.5" calcMode="linear" repeatCount="indefinite"></animate></circle><circle cx="105" cy="15" r="15"><animate attributeName="r" from="15" to="15" begin="0s" dur="0.8s" values="15;9;15" calcMode="linear" repeatCount="indefinite"></animate><animate attributeName="fill-opacity" from="1" to="1" begin="0s" dur="0.8s" values="1;.5;1" calcMode="linear" repeatCount="indefinite"></animate></circle>',bn=le({name:"QSpinnerDots",props:wt,setup(e){const{cSize:a,classes:i}=xt(e);return()=>P("svg",{class:i.value,fill:"currentColor",width:a.value,height:a.value,viewBox:"0 0 120 30",xmlns:"http://www.w3.org/2000/svg",innerHTML:hn})}}),{passive:ye}=ua,yn=le({name:"QInfiniteScroll",props:{offset:{type:Number,default:500},debounce:{type:[String,Number],default:100},scrollTarget:sa,initialIndex:{type:Number,default:0},disable:Boolean,reverse:Boolean},emits:["load"],setup(e,{slots:a,emit:i}){const t=m(!1),r=m(!0),c=m(null),n=m(null);let l=e.initialIndex,d,_;const s=h(()=>"q-infinite-scroll__loading"+(t.value===!0?"":" invisible"));function S(){if(e.disable===!0||t.value===!0||r.value===!1)return;const g=Ae(d),D=je(d),B=dt(d);e.reverse===!1?Math.round(D+B+e.offset)>=Math.round(g)&&v():Math.round(D)<=e.offset&&v()}function v(){if(e.disable===!0||t.value===!0||r.value===!1)return;l++,t.value=!0;const g=Ae(d);i("load",l,D=>{r.value===!0&&(t.value=!1,qe(()=>{if(e.reverse===!0){const B=Ae(d),U=je(d),$=B-g;Ie(d,U+$)}D===!0?b():c.value&&c.value.closest("body")&&_()}))})}function y(){l=0}function I(){r.value===!1&&(r.value=!0,d.addEventListener("scroll",_,ye)),S()}function b(){r.value===!0&&(r.value=!1,t.value=!1,d.removeEventListener("scroll",_,ye),_!==void 0&&_.cancel!==void 0&&_.cancel())}function E(){if(d&&r.value===!0&&d.removeEventListener("scroll",_,ye),d=da(c.value,e.scrollTarget),r.value===!0){if(d.addEventListener("scroll",_,ye),e.reverse===!0){const g=Ae(d),D=dt(d);Ie(d,g-D)}S()}}function w(g){l=g}function x(g){g=parseInt(g,10);const D=_;_=g<=0?S:Ct(S,isNaN(g)===!0?100:g),d&&r.value===!0&&(D!==void 0&&d.removeEventListener("scroll",D,ye),d.addEventListener("scroll",_,ye))}function O(g){if(H.value===!0){if(n.value===null){g!==!0&&qe(()=>{O(!0)});return}const D=`${t.value===!0?"un":""}pauseAnimations`;Array.from(n.value.getElementsByTagName("svg")).forEach(B=>{B[D]()})}}const H=h(()=>e.disable!==!0&&r.value===!0);q([t,H],()=>{O()}),q(()=>e.disable,g=>{g===!0?b():I()}),q(()=>e.reverse,()=>{t.value===!1&&r.value===!0&&S()}),q(()=>e.scrollTarget,E),q(()=>e.debounce,x);let F=!1;It(()=>{F!==!1&&d&&Ie(d,F)}),kt(()=>{F=d?je(d):!1}),pe(()=>{r.value===!0&&d.removeEventListener("scroll",_,ye)}),xe(()=>{x(e.debounce),E(),t.value===!1&&O()});const Q=we();return Object.assign(Q.proxy,{poll:()=>{_!==void 0&&_()},trigger:v,stop:b,reset:y,resume:I,setIndex:w,updateScrollTarget:E}),()=>{const g=yt(a.default,[]);return H.value===!0&&g[e.reverse===!1?"push":"unshift"](P("div",{ref:n,class:s.value},Re(a.loading))),P("div",{class:"q-infinite-scroll",ref:c},g)}}}),_n=le({props:["store","barStyle","verticalBarStyle","horizontalBarStyle"],setup(e){return()=>[P("div",{class:e.store.scroll.vertical.barClass.value,style:[e.barStyle,e.verticalBarStyle],"aria-hidden":"true",onMousedown:e.store.onVerticalMousedown}),P("div",{class:e.store.scroll.horizontal.barClass.value,style:[e.barStyle,e.horizontalBarStyle],"aria-hidden":"true",onMousedown:e.store.onHorizontalMousedown}),K(P("div",{ref:e.store.scroll.vertical.ref,class:e.store.scroll.vertical.thumbClass.value,style:e.store.scroll.vertical.style.value,"aria-hidden":"true"}),e.store.thumbVertDir),K(P("div",{ref:e.store.scroll.horizontal.ref,class:e.store.scroll.horizontal.thumbClass.value,style:e.store.scroll.horizontal.style.value,"aria-hidden":"true"}),e.store.thumbHorizDir)]}}),mt=["vertical","horizontal"],Ke={vertical:{offset:"offsetY",scroll:"scrollTop",dir:"down",dist:"y"},horizontal:{offset:"offsetX",scroll:"scrollLeft",dir:"right",dist:"x"}},gt={prevent:!0,mouse:!0,mouseAllDir:!0},ht=e=>e>=250?50:Math.ceil(e/5),Sn=le({name:"QScrollArea",props:{...nt,thumbStyle:Object,verticalThumbStyle:Object,horizontalThumbStyle:Object,barStyle:[Array,String,Object],verticalBarStyle:[Array,String,Object],horizontalBarStyle:[Array,String,Object],verticalOffset:{type:Array,default:[0,0]},horizontalOffset:{type:Array,default:[0,0]},contentStyle:[Array,String,Object],contentActiveStyle:[Array,String,Object],delay:{type:[String,Number],default:1e3},visible:{type:Boolean,default:null},tabindex:[String,Number],onScroll:Function},setup(e,{slots:a,emit:i}){const t=m(!1),r=m(!1),c=m(!1),n={vertical:m(0),horizontal:m(0)},l={vertical:{ref:m(null),position:m(0),size:m(0)},horizontal:{ref:m(null),position:m(0),size:m(0)}},{proxy:d}=we(),_=lt(e,d.$q);let s=null,S;const v=m(null),y=h(()=>"q-scrollarea"+(_.value===!0?" q-scrollarea--dark":""));Object.assign(n,{verticalInner:h(()=>n.vertical.value-e.verticalOffset[0]-e.verticalOffset[1]),horizontalInner:h(()=>n.horizontal.value-e.horizontalOffset[0]-e.horizontalOffset[1])}),l.vertical.percentage=h(()=>{const p=l.vertical.size.value-n.vertical.value;if(p<=0)return 0;const C=ue(l.vertical.position.value/p,0,1);return Math.round(C*1e4)/1e4}),l.vertical.thumbHidden=h(()=>(e.visible===null?c.value:e.visible)!==!0&&t.value===!1&&r.value===!1||l.vertical.size.value<=n.vertical.value+1),l.vertical.thumbStart=h(()=>e.verticalOffset[0]+l.vertical.percentage.value*(n.verticalInner.value-l.vertical.thumbSize.value)),l.vertical.thumbSize=h(()=>Math.round(ue(n.verticalInner.value*n.verticalInner.value/l.vertical.size.value,ht(n.verticalInner.value),n.verticalInner.value))),l.vertical.style=h(()=>({...e.thumbStyle,...e.verticalThumbStyle,top:`${l.vertical.thumbStart.value}px`,height:`${l.vertical.thumbSize.value}px`,right:`${e.horizontalOffset[1]}px`})),l.vertical.thumbClass=h(()=>"q-scrollarea__thumb q-scrollarea__thumb--v absolute-right"+(l.vertical.thumbHidden.value===!0?" q-scrollarea__thumb--invisible":"")),l.vertical.barClass=h(()=>"q-scrollarea__bar q-scrollarea__bar--v absolute-right"+(l.vertical.thumbHidden.value===!0?" q-scrollarea__bar--invisible":"")),l.horizontal.percentage=h(()=>{const p=l.horizontal.size.value-n.horizontal.value;if(p<=0)return 0;const C=ue(Math.abs(l.horizontal.position.value)/p,0,1);return Math.round(C*1e4)/1e4}),l.horizontal.thumbHidden=h(()=>(e.visible===null?c.value:e.visible)!==!0&&t.value===!1&&r.value===!1||l.horizontal.size.value<=n.horizontal.value+1),l.horizontal.thumbStart=h(()=>e.horizontalOffset[0]+l.horizontal.percentage.value*(n.horizontalInner.value-l.horizontal.thumbSize.value)),l.horizontal.thumbSize=h(()=>Math.round(ue(n.horizontalInner.value*n.horizontalInner.value/l.horizontal.size.value,ht(n.horizontalInner.value),n.horizontalInner.value))),l.horizontal.style=h(()=>({...e.thumbStyle,...e.horizontalThumbStyle,[d.$q.lang.rtl===!0?"right":"left"]:`${l.horizontal.thumbStart.value}px`,width:`${l.horizontal.thumbSize.value}px`,bottom:`${e.verticalOffset[1]}px`})),l.horizontal.thumbClass=h(()=>"q-scrollarea__thumb q-scrollarea__thumb--h absolute-bottom"+(l.horizontal.thumbHidden.value===!0?" q-scrollarea__thumb--invisible":"")),l.horizontal.barClass=h(()=>"q-scrollarea__bar q-scrollarea__bar--h absolute-bottom"+(l.horizontal.thumbHidden.value===!0?" q-scrollarea__bar--invisible":""));const I=h(()=>l.vertical.thumbHidden.value===!0&&l.horizontal.thumbHidden.value===!0?e.contentStyle:e.contentActiveStyle);function b(){const p={};return mt.forEach(C=>{const T=l[C];Object.assign(p,{[C+"Position"]:T.position.value,[C+"Percentage"]:T.percentage.value,[C+"Size"]:T.size.value,[C+"ContainerSize"]:n[C].value,[C+"ContainerInnerSize"]:n[C+"Inner"].value})}),p}const E=Ct(()=>{const p=b();p.ref=d,i("scroll",p)},0);function w(p,C,T){if(mt.includes(p)===!1){console.error("[QScrollArea]: wrong first param of setScrollPosition (vertical/horizontal)");return}(p==="vertical"?Ie:Ge)(v.value,C,T)}function x({height:p,width:C}){let T=!1;n.vertical.value!==p&&(n.vertical.value=p,T=!0),n.horizontal.value!==C&&(n.horizontal.value=C,T=!0),T===!0&&g()}function O({position:p}){let C=!1;l.vertical.position.value!==p.top&&(l.vertical.position.value=p.top,C=!0),l.horizontal.position.value!==p.left&&(l.horizontal.position.value=p.left,C=!0),C===!0&&g()}function H({height:p,width:C}){l.horizontal.size.value!==C&&(l.horizontal.size.value=C,g()),l.vertical.size.value!==p&&(l.vertical.size.value=p,g())}function F(p,C){const T=l[C];if(p.isFirst===!0){if(T.thumbHidden.value===!0)return;S=T.position.value,r.value=!0}else if(r.value!==!0)return;p.isFinal===!0&&(r.value=!1);const M=Ke[C],N=(T.size.value-n[C].value)/(n[C+"Inner"].value-T.thumbSize.value),j=p.distance[M.dist],ge=S+(p.direction===M.dir?1:-1)*j*N;D(ge,C)}function Q(p,C){const T=l[C];if(T.thumbHidden.value!==!0){const M=C==="vertical"?e.verticalOffset[0]:e.horizontalOffset[0],N=p[Ke[C].offset]-M,j=T.thumbStart.value-M;if(N<j||N>j+T.thumbSize.value){const ge=N-T.thumbSize.value/2,Ve=ue(ge/(n[C+"Inner"].value-T.thumbSize.value),0,1);D(Ve*Math.max(0,T.size.value-n[C].value),C)}T.ref.value!==null&&T.ref.value.dispatchEvent(new MouseEvent(p.type,p))}}function g(){t.value=!0,s!==null&&clearTimeout(s),s=setTimeout(()=>{s=null,t.value=!1},e.delay),e.onScroll!==void 0&&E()}function D(p,C){v.value[Ke[C].scroll]=p}let B=null;function U(){B!==null&&clearTimeout(B),B=setTimeout(()=>{B=null,c.value=!0},d.$q.platform.is.ios?50:0)}function $(){B!==null&&(clearTimeout(B),B=null),c.value=!1}let ie=null;q(()=>d.$q.lang.rtl,p=>{v.value!==null&&Ge(v.value,Math.abs(l.horizontal.position.value)*(p===!0?-1:1))}),kt(()=>{ie={top:l.vertical.position.value,left:l.horizontal.position.value}}),It(()=>{if(ie===null)return;const p=v.value;p!==null&&(Ge(p,ie.left),Ie(p,ie.top))}),pe(E.cancel),Object.assign(d,{getScrollTarget:()=>v.value,getScroll:b,getScrollPosition:()=>({top:l.vertical.position.value,left:l.horizontal.position.value}),getScrollPercentage:()=>({top:l.vertical.percentage.value,left:l.horizontal.percentage.value}),setScrollPosition:w,setScrollPercentage(p,C,T){w(p,C*(l[p].size.value-n[p].value)*(p==="horizontal"&&d.$q.lang.rtl===!0?-1:1),T)}});const ae={scroll:l,thumbVertDir:[[Ce,p=>{F(p,"vertical")},void 0,{vertical:!0,...gt}]],thumbHorizDir:[[Ce,p=>{F(p,"horizontal")},void 0,{horizontal:!0,...gt}]],onVerticalMousedown(p){Q(p,"vertical")},onHorizontalMousedown(p){Q(p,"horizontal")}};return()=>P("div",{class:y.value,onMouseenter:U,onMouseleave:$},[P("div",{ref:v,class:"q-scrollarea__container scroll relative-position fit hide-scrollbar",tabindex:e.tabindex!==void 0?e.tabindex:void 0},[P("div",{class:"q-scrollarea__content absolute",style:I.value},ca(a.default,[P(Je,{debounce:0,onResize:H})])),P(Kt,{axis:"both",onScroll:O})]),P(Je,{debounce:0,onResize:x}),P(_n,{store:ae,barStyle:e.barStyle,verticalBarStyle:e.verticalBarStyle,horizontalBarStyle:e.horizontalBarStyle})])}}),pn=oe({name:"NoneComponents",__name:"none",setup(e,{expose:a}){a();const i={get imageSrc(){return ce}};return Object.defineProperty(i,"__isScriptSetup",{enumerable:!1,value:!0}),i}}),wn={class:"column items-center q-my-md"};function xn(e,a,i,t,r,c){return k(),R("div",null,[o(se,null,{default:u(()=>[o(V,null,{default:u(()=>[z("div",wn,[o(Pe,{"no-spinner":"",src:t.imageSrc("/images/noRecord.png"),style:{width:"100px"}},null,8,["src"]),a[0]||(a[0]=z("div",{class:"text-body1 text-grey text-center"},"暂无数据",-1))])]),_:1})]),_:1})])}const In=re(pn,[["render",xn],["__file","none.vue"]]),kn=oe({name:"PaginationComponents",__name:"pagination",props:{url:{type:String,default:"",required:!0},trigger:{type:Boolean,default:!1},reverse:{type:Boolean,default:!1},params:{type:Object,default:()=>({pagination:{sortBy:"id",descending:!0,page:1,rowsPerPage:10}})}},emits:["changeRows"],setup(e,{expose:a,emit:i}){const t=i,r=e,c=m(null),n=m(null),l=m(null),d=m(!0),_=m(0),s=m(r.params),S=m([]),v=(E,w)=>{s.value.pagination.page=E,De.post(r.url,s.value).then(x=>{r.reverse?S.value.unshift(...x.items.reverse()):S.value.push(...x.items),d.value=x.items.length>=s.value.pagination.rowsPerPage,_.value=Math.ceil(x.count/s.value.pagination.rowsPerPage),_.value===0&&(_.value=-1),t("changeRows",S.value),w()})};xe(()=>{r.trigger&&l.value.trigger()});const y=()=>{c.value?.setScrollPosition("vertical",n.value.clientHeight,500)},I=()=>{l.value.reset(),S.value=[],d.value=!0,_.value=0};a({resetInfiniteScroll:I,animateScroll:y});const b={emits:t,props:r,scrollAreaRef:c,scrollContainerRef:n,scrollTargetRef:l,isLoad:d,totalPages:_,bodyParams:s,rows:S,onLoad:v,animateScroll:y,resetInfiniteScroll:I,NoneComponents:In};return Object.defineProperty(b,"__isScriptSetup",{enumerable:!1,value:!0}),b}}),Cn={ref:"scrollContainerRef"},Mn={key:0,class:"row justify-center q-my-md"};function En(e,a,i,t,r,c){return k(),L(Sn,{style:{height:"100%",width:"100%"},"thumb-style":{width:"4px"},ref:"scrollAreaRef"},{default:u(()=>[z("div",Cn,[o(yn,{ref:"scrollTargetRef",onLoad:t.onLoad,offset:0,reverse:i.reverse,disable:!t.isLoad||t.totalPages==-1},{loading:u(()=>[t.isLoad?(k(),R("div",Mn,[o(bn,{color:"primary",size:"40px"})])):X("",!0)]),default:u(()=>[fa(e.$slots,"item-body",{rows:t.rows}),t.rows.length===0?(k(),L(t.NoneComponents,{key:0,style:{"margin-top":"100px"}})):X("",!0)]),_:3},8,["reverse","disable"])],512)]),_:3},512)}const Lt=re(kn,[["render",En],["__file","pagination.vue"]]),Pn=e=>De.post("/chats/session",e,{headers:{"X-Skip-Loading":"true"}}),Dn=e=>De.post("/chats/send",e),bt=e=>De.post("/chats/read",e,{headers:{"X-Skip-Loading":"true"}}),zn=e=>De.post("/chats/revoke",e),An=oe({name:"ChatsSessionsComponents",__name:"chatsSessions",emits:["switchSession"],setup(e,{expose:a,emit:i}){const t=i,r=m([]),c=m({id:0}),n=m({pagination:{sortBy:"updated_at",descending:!0,page:1,rowsPerPage:10}}),l=v=>{c.value=v,t("switchSession",c.value)},d=v=>{switch(v.op){case ot:{const y=r.value.find(I=>I.sessionId===v.data.sessionId);y?(r.value=r.value.filter(I=>I.sessionId!==v.data.sessionId),y.data=v.data,y.id!==c.value.id&&y.number++,r.value.unshift(y)):Pn({sessionId:v.data.sessionId}).then(I=>{r.value.unshift(I)});break}case fn:{const y=r.value.find(I=>I.userId===v.data);y&&(y.online=!0);break}case vn:{const y=r.value.find(I=>I.userId===v.data);y&&(y.online=!1);break}}},_=v=>{v.length>0&&c.value.id===0&&(c.value=v[0],t("switchSession",c.value)),r.value=v},s=v=>{c.value=v};a({onMessage:d,changeSession:s});const S={emits:t,sessionList:r,currentSessionInfo:c,bodyParams:n,switchSessionFunc:l,onMessage:d,updateSessionList:_,changeSession:s,get imageSrc(){return ce},get MESSAGE_TYPE_IMAGE(){return Ze},PaginationComponents:Lt};return Object.defineProperty(S,"__isScriptSetup",{enumerable:!1,value:!0}),S}}),Tn={key:1,class:"text-grey-8 text-caption"},qn={style:{"max-width":"132px"}},Ln={class:"text-body2 ellipsis"},Bn={class:"text-caption text-grey"},On={key:0,class:"ellipsis"},Rn={key:1,class:"ellipsis"};function Vn(e,a,i,t,r,c){return k(),R("div",null,[o(t.PaginationComponents,{url:"/chats/sessions",params:t.bodyParams,onChangeRows:t.updateSessionList,trigger:""},{"item-body":u(({rows:n})=>[n.length>0?(k(),L(Ee,{key:0},{default:u(()=>[(k(!0),R(me,null,Se(n,l=>(k(),L(se,{key:l.id,clickable:"",style:{padding:"8px 8px"},onClick:d=>t.switchSessionFunc(l),class:Me({"bg-grey-3":l.id===t.currentSessionInfo.id})},{default:u(()=>[o(V,{avatar:""},{default:u(()=>[o(at,{class:"bg-grey-3"},{default:u(()=>[l.online?(k(),L(Pe,{key:0,src:t.imageSrc(l.userInfo.avatar),"no-spinner":""},null,8,["src"])):(k(),R("div",Tn,"离线"))]),_:2},1024)]),_:2},1024),o(V,null,{default:u(()=>[z("div",qn,[z("div",Ln,J(l.userInfo.username),1),z("div",Bn,[l.data.type===t.MESSAGE_TYPE_IMAGE?(k(),R("div",On,"[图片]")):(k(),R("div",Rn,J(l.data.message),1))])])]),_:2},1024),o(V,{side:""},{default:u(()=>[l.number>0?(k(),L(Te,{key:0,rounded:"",color:"red",label:l.number},null,8,["label"])):X("",!0)]),_:2},1024)]),_:2},1032,["onClick","class"]))),128))]),_:2},1024)):X("",!0)]),_:1},8,["params"])])}const Fn=re(An,[["render",Vn],["__file","chatsSessions.vue"]]),Nn=oe({name:"ChatsMessagesComponents",__name:"chatsMessages",emits:["changeSession","playAudio"],setup(e,{expose:a,emit:i}){const t=i,r=Mt(),c=m(!1),n=m(""),l=m(!1),d=Oe(),_=m({id:0,userInfo:{avatar:""}}),s=m({sessionId:"",pagination:{sortBy:"id",descending:!0,page:1,rowsPerPage:10}}),S=m(null),v=m([]),y=m(""),I=g=>{v.value=g;const D=v.value.filter(B=>B.status!==We&&B.senderType!=vt).map(B=>B.id);D.length>0&&(_.value.number-=D.length,t("changeSession",_.value),bt({ids:D}))},b=g=>{if(!y.value.trim())return g.preventDefault(),!1;if(g&&g.key=="Enter"&&g.shiftKey)return!1;w(ft)},E=g=>{if(_.value.id==0){r.notify({message:"请先选择会话",type:"warning"});return}y.value=g,w(Ze)},w=g=>{Dn({sessionId:s.value.sessionId,message:y.value,type:g}).then(D=>{t("playAudio",ce("/mp3/send.mp3")),v.value.push(D),y.value="",_.value.data=D,t("changeSession",_.value),S.value?.animateScroll()})},x=g=>{switch(g.op){case ot:_.value.sessionId==g.data.sessionId&&(v.value.push(g.data),S.value?.animateScroll(),bt({ids:[g.data.id]})),t("playAudio",ce("/mp3/msg2.mp3"));break;case mn:v.value.forEach(D=>{D.id==g.data&&(D.status=We)});break}},O=g=>{S.value?.resetInfiniteScroll(),_.value=g,s.value.sessionId=g.sessionId},H=async g=>{await zn({id:g}).catch(()=>!1),F(g)},F=g=>{if(v.value.length<=0)return!1;const D=v.value.findIndex(B=>B.id==g);v.value.splice(D,1),_.value.data=v.value[v.value.length-1],t("changeSession",_.value)};a({onMessage:x,switchSession:O});const Q={emits:t,$q:r,showImagePreview:c,imagePreviewPath:n,isLoadingImage:l,$userStore:d,currentSessionInfo:_,bodyParams:s,paginationRef:S,messageList:v,message:y,updateMessageList:I,sendTextMessage:b,sendImageMessage:E,sendMessage:w,onMessage:x,switchSession:O,revokeMessage:H,deleteRevokeMessage:F,get imageSrc(){return ce},get date(){return Pt},get MESSAGE_SENDER_ADMIN(){return vt},get MESSAGE_TYPE_TEXT(){return ft},get MESSAGE_TYPE_IMAGE(){return Ze},get MESSAGE_READ(){return We},PaginationComponents:Lt,UploaderComponents:Et};return Object.defineProperty(Q,"__isScriptSetup",{enumerable:!1,value:!0}),Q}}),Qn={class:"full-height"},Hn={style:{height:"calc(100% - 152px)"}},Un=["innerHTML"],jn={key:1},Gn=["onClick"],$n={class:"q-ml-sm"},Yn={key:1,class:"q-ml-xs"},Wn={class:"row items-center"},Kn={class:"q-ma-xs"};function Xn(e,a,i,t,r,c){return k(),R("div",Qn,[z("div",Hn,[t.bodyParams.sessionId!=""?(k(),L(t.PaginationComponents,{key:0,style:{height:"100%"},url:"/chats/messages",params:t.bodyParams,reverse:"",onChangeRows:t.updateMessageList,ref:"paginationRef"},{"item-body":u(({rows:n})=>[(k(!0),R(me,null,Se(n,l=>(k(),R("div",{key:l.id,class:Me(["q-my-md",l.senderType==t.MESSAGE_SENDER_ADMIN?"q-pl-xl q-pr-md":"q-pl-md q-pr-xl"])},[o(Xt,{sent:l.senderType==t.MESSAGE_SENDER_ADMIN,avatar:t.imageSrc(l.senderType==t.MESSAGE_SENDER_ADMIN?t.$userStore.userInfo.avatar:t.currentSessionInfo.userInfo.avatar),"bg-color":l.senderType==t.MESSAGE_SENDER_ADMIN?"green-2":"grey-2",style:{"margin-bottom":"2px"}},{default:u(()=>[l.type==t.MESSAGE_TYPE_TEXT?(k(),R("div",{key:0,innerHTML:l.message,style:{"white-space":"pre-line"}},null,8,Un)):l.type==t.MESSAGE_TYPE_IMAGE?(k(),R("div",jn,[o(Pe,{onClick:d=>{t.imagePreviewPath=l.message,t.showImagePreview=!0},src:t.imageSrc(l.message),style:{width:"200px"},alt:"",class:"cursor-pointer",onLoad:a[0]||(a[0]=d=>t.isLoadingImage=!1)},null,8,["onClick","src"]),t.isLoadingImage?(k(),L(xa,{key:0,type:"QAvatar",style:{height:"200px"}})):X("",!0)])):X("",!0)]),_:2},1032,["sent","avatar","bg-color"]),z("div",{class:Me(["row text-grey",l.senderType==t.MESSAGE_SENDER_ADMIN?"q-pr-xl justify-end":"q-pl-xl"]),style:{"font-size":"10px"}},[l.senderType==t.MESSAGE_SENDER_ADMIN?(k(),R("div",{key:0,onClick:d=>t.revokeMessage(l.id),class:"text-grey text-weight-medium cursor-pointer",style:{"font-size":"10px"}},[o(Y,{name:"undo",color:"grey"}),a[5]||(a[5]=ee(" 撤回 "))],8,Gn)):X("",!0),z("div",$n,J(t.date.formatDate(l.createdAt,"MM/DD HH:mm")),1),l.senderType==t.MESSAGE_SENDER_ADMIN?(k(),R("div",Yn,[l.status==t.MESSAGE_READ?(k(),L(Y,{key:0,name:"done_all",color:"green",size:"14px"})):(k(),L(Y,{key:1,name:"done_all",color:"grey",size:"14px"}))])):X("",!0)],2)],2))),128))]),_:1},8,["params"])):X("",!0)]),z("div",null,[o(Le),z("div",Wn,[z("div",Kn,[o(t.UploaderComponents,{onUploaded:t.sendImageMessage,style:{width:"30px",height:"30px"}},{default:u(()=>[o(Y,{name:"o_image",size:"30px",class:"text-grey-8 cursor-pointer"})]),_:1})])]),o(ne,{modelValue:t.message,"onUpdate:modelValue":a[1]||(a[1]=n=>t.message=n),borderless:"",autofocus:"",maxlength:"2040",dense:"",disable:t.currentSessionInfo.id==0,placeholder:"请输入消息",type:"textarea",onKeydown:a[2]||(a[2]=va(n=>t.sendTextMessage(n),["enter"])),class:"no-padding no-margin","input-style":"padding: 0 8px; resize: none;"},null,8,["modelValue","disable"])]),o(ke,{modelValue:t.showImagePreview,"onUpdate:modelValue":a[4]||(a[4]=n=>t.showImagePreview=n),"full-width":""},{default:u(()=>[z("div",{onClick:a[3]||(a[3]=n=>t.showImagePreview=!1),class:"row justify-center items-center cursor-pointer"},[o(Pe,{src:t.imageSrc(t.imagePreviewPath),style:{width:"60vw"},class:"bg-primary-2"},null,8,["src"])])]),_:1},8,["modelValue"])])}const Jn=re(Nn,[["render",Xn],["__scopeId","data-v-8652313e"],["__file","chatsMessages.vue"]]),Zn=oe({name:"ChatsLayout",__name:"chats",setup(e,{expose:a}){a();const i=Oe(),t=gn(),r=m(null),c=m(""),n=m(null),l=m(null),d=m({}),_=m([-80,18]),s=m(!1),S=m(0),v=w=>{if(n.value?.onMessage(w),s.value)l.value?.onMessage(w);else switch(w.op){case ot:b(ce("/mp3/msg.mp3")),S.value++;break}},y=w=>{d.value=w,l.value.switchSession(w)},I=w=>{d.value=w,n.value.changeSession(w)},b=w=>{c.value=w,r.value.play()};xe(()=>{t.init(i.userToken),t.onMessage(v)});const E={$userStore:i,$chatsStore:t,audioPlayer:r,audioSrc:c,chatsSessionsRef:n,chatsMessagesRef:l,currentSessionInfo:d,chatsOffset:_,showChat:s,unReadCount:S,onMessage:v,switchSession:y,changeSession:I,playAudio:b,get imageSrc(){return ce},ChatsSessionsComponents:Fn,ChatsMessagesComponents:Jn};return Object.defineProperty(E,"__isScriptSetup",{enumerable:!1,value:!0}),E}}),el={key:0,class:"row items-center"},tl={key:1,class:"row items-center"},al={class:"row items-center"},nl={class:"text-h6 q-ml-sm"},ll={class:"full-height"},ol={class:"full-height"},rl=["src"];function il(e,a,i,t,r,c){return k(),R("div",null,[o(un,{position:"bottom-right",offset:t.chatsOffset,onMouseover:a[1]||(a[1]=n=>t.chatsOffset=[0,18]),onMouseleave:a[2]||(a[2]=n=>t.chatsOffset=[-80,18]),style:{transition:"all 0.3s ease-in-out"}},{default:u(()=>[o(Z,{fab:"",color:"primary",onClick:a[0]||(a[0]=n=>{t.unReadCount=0,t.showChat=!0}),class:"no-padding",style:{width:"120px","border-top-right-radius":"0","border-bottom-right-radius":"0","border-top-left-radius":"28px","border-bottom-left-radius":"28px"}},{default:u(()=>[z("div",null,[t.unReadCount===0?(k(),R("div",el,[o(on,{color:"green",size:"2em"}),a[4]||(a[4]=z("div",{class:"col q-ml-sm"},"等待接收",-1))])):(k(),R("div",tl,[o(Te,{color:"red",label:t.unReadCount},null,8,["label"]),a[5]||(a[5]=z("div",{class:"col q-ml-sm"},"新到消息",-1))]))])]),_:1})]),_:1},8,["offset"]),o(ke,{modelValue:t.showChat,"onUpdate:modelValue":a[3]||(a[3]=n=>t.showChat=n)},{default:u(()=>[o(_e,{style:{width:"860px","max-width":"80vw",height:"80vh"}},{default:u(()=>[o(te,null,{default:u(()=>[z("div",al,[o(at,null,{default:u(()=>[o(Pe,{src:t.imageSrc(t.$userStore.userInfo.avatar),"no-spinner":""},null,8,["src"])]),_:1}),z("div",nl,J(t.$userStore.userInfo.nickname),1)])]),_:1}),o(Le),o(te,{horizontal:"",style:{height:"calc(100% - 81px)"}},{default:u(()=>[o(te,{style:{width:"246px","border-right":"1px solid #e0e0e0",height:"100%"},class:"no-padding"},{default:u(()=>[z("div",ll,[o(t.ChatsSessionsComponents,{class:"full-height",ref:"chatsSessionsRef",onSwitchSession:t.switchSession},null,512)])]),_:1}),o(te,{class:"col no-padding",style:{height:"100%"}},{default:u(()=>[z("div",ol,[o(t.ChatsMessagesComponents,{class:"full-height",ref:"chatsMessagesRef",onChangeSession:t.changeSession,onPlayAudio:t.playAudio},null,512)])]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"]),z("audio",{ref:"audioPlayer",style:{display:"none"},src:t.audioSrc},null,8,rl)])}const sl=re(Zn,[["render",il],["__file","chats.vue"]]),ul=oe({name:"MainLayout",__name:"main",setup(e,{expose:a}){a();const i=Xe(),t=m(null),r=m(null),l={$tabsStore:i,leftDrawerRef:t,rightDrawerRef:r,toggleLeftDrawer:()=>{t.value?.toggleLeftDrawer()},toggleRightDrawer:()=>{r.value?.toggleRightDrawer()},HeaderLayout:Ha,LeftDrawerLayout:Ja,RightDrawerLayout:nn,ChatsLayout:sl};return Object.defineProperty(l,"__isScriptSetup",{enumerable:!1,value:!0}),l}});function dl(e,a,i,t,r,c){const n=pt("router-view");return k(),L(Jt,{view:"hHh Lpr lFf"},{default:u(()=>[o(t.HeaderLayout,{onToggleDrawer:t.toggleLeftDrawer,onToggleRightDrawer:t.toggleRightDrawer}),o(t.LeftDrawerLayout,{ref:"leftDrawerRef"},null,512),o(t.RightDrawerLayout,{ref:"rightDrawerRef"},null,512),o(Zt,null,{default:u(()=>[o(n,null,{default:u(({Component:l})=>[(k(),L(ma,{max:30},[(k(),L(ga(l),{key:t.$tabsStore.tabs.get(e.$route.fullPath)?.key??e.$route.fullPath}))],1024))]),_:1})]),_:1}),o(t.ChatsLayout)]),_:1})}const wl=re(ul,[["render",dl],["__scopeId","data-v-d21fc19f"],["__file","main.vue"]]);export{wl as default};
