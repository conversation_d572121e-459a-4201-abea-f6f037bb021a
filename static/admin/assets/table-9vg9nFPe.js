import{a as QCheckbox,Q as QForm}from"./QForm-F1RTM61F.js";import{p as createComponent,c as computed,h,t as hSlot,g as getCurrentInstance,N as hUniqueSlot,M as QIcon,O as noop,r as ref,w as watch,P as debounce,R as onBeforeMount,S as onDeactivated,U as onActivated,V as onBeforeUnmount,x as nextTick,W as scrollTargetProp,o as onMounted,X as getScrollTarget,Y as listenOpts,Z as hMergeSlot,$ as useSizeProps,a0 as useSize,a1 as hDir,a2 as hMergeSlotSafely,a3 as Ripple,a4 as stopAndPrevent,a5 as onBeforeUpdate,a6 as onUpdated,a7 as prevent,a8 as isDeepEqual,a9 as isKeyCode,aa as stop,ab as shouldIgnoreKey,ac as vmHasRouter,ad as History,ae as isNumber,af as isDate,ag as isObject,ah as injectMultipleProps,ai as injectProp,Q as QBtn,aj as toRaw,ak as Transition,al as position,am as withDirectives,an as vmIsDestroyed,ao as createDirective,ap as client,aq as leftClick,ar as addEvt,as as preventDraggable,at as cleanEvt,au as getNormalizedVNodes,av as KeepAlive,aw as throttle,ax as nonRoundBtnProps,ay as getBtnDesignAttr,d as defineComponent,_ as _export_sfc,l as openBlock,m as createElementBlock,j as createVNode,z as withCtx,n as createBaseVNode,F as Fragment,D as renderList,H as normalizeClass,y as createBlock,A as toDisplayString,E as createCommentVNode,G as withKeys,az as normalizeStyle,L as withModifiers,B as createTextVNode,I as useUserStore,aA as useRoute}from"./index-H1mtFlU6.js";import{r as rtlHasScrollBug,Q as QList,n as normalizeToInterval,a as QMenu,b as QDialog,c as QItemSection,d as QItemLabel,e as QItem,f as QSeparator,g as QToggle,t as toJalaali,p as pad,h as toGregorian,i as formatDate,j as getDateDiff,k as jalaaliMonthLength,_ as __splitDate,T as TouchPan,l as between,m as getModifierDirections,s as shouldStart,o as clearSelection,q as QTab,u as QTabs,v as useTransitionProps,w as QTooltip,U as UploaderComponents,x as date,y as useQuasar,z as QPopupProxy,C as ClosePopup,A as QSkeleton}from"./QSkeleton-G0ihNNrd.js";import{u as useDarkProps,a as useDark}from"./use-dark-BZZjkWkU.js";import{u as useFieldProps,a as useFieldEmits,b as useField,c as useFieldState,d as useFormProps,e as useFormInputNameAttr,f as fieldValueIsFilled,g as useKeyComposition,h as useFormInject,i as useFormAttrs,t as testPattern,j as useId,k as useSplitAttrs,l as addFocusFn,Q as QInput}from"./QInput-F165baog.js";import{b as QCard,a as QCardSection,Q as QCardActions}from"./QCardActions-CYjdimmU.js";import{c as requestOptions,d as requestLangTags,e as requestUpdateTranslate,f as requestConfigure}from"./index-CuHAsf8O.js";import{i as imageSrc,c as copyText,a as api}from"./axios-JqZ6Qsjg.js";import{o as optionSizes,u as useRefocusTarget,a as useTimeout}from"./use-checkbox-ZuTr6CRM.js";import{i as init}from"./index-DFpWVYxE.js";import"./QResizeObserver-DJfGZaa6.js";const QTd=createComponent({name:"QTd",props:{props:Object,autoWidth:Boolean,noHover:Boolean},setup(e,{slots:n}){const l=getCurrentInstance(),t=computed(()=>"q-td"+(e.autoWidth===!0?" q-table--col-auto-width":"")+(e.noHover===!0?" q-td--no-hover":"")+" ");return()=>{if(e.props===void 0)return h("td",{class:t.value},hSlot(n.default));const o=l.vnode.key,r=(e.props.colsMap!==void 0?e.props.colsMap[o]:null)||e.props.col;if(r===void 0)return;const{row:i}=e.props;return h("td",{class:t.value+r.__tdClass(i),style:r.__tdStyle(i)},hSlot(n.default))}}}),QTr=createComponent({name:"QTr",props:{props:Object,noHover:Boolean},setup(e,{slots:n}){const l=computed(()=>"q-tr"+(e.props===void 0||e.props.header===!0?"":" "+e.props.__trClass)+(e.noHover===!0?" q-tr--no-hover":""));return()=>h("tr",{class:l.value},hSlot(n.default))}}),QTh=createComponent({name:"QTh",props:{props:Object,autoWidth:Boolean},emits:["click"],setup(e,{slots:n,emit:l}){const t=getCurrentInstance(),{proxy:{$q:o}}=t,r=i=>{l("click",i)};return()=>{if(e.props===void 0)return h("th",{class:e.autoWidth===!0?"q-table--col-auto-width":"",onClick:r},hSlot(n.default));let i,a;const u=t.vnode.key;if(u){if(i=e.props.colsMap[u],i===void 0)return}else i=e.props.col;if(i.sortable===!0){const s=i.align==="right"?"unshift":"push";a=hUniqueSlot(n.default,[]),a[s](h(QIcon,{class:i.__iconClass,name:o.iconSet.table.arrowUp}))}else a=hSlot(n.default);const f={class:i.__thClass+(e.autoWidth===!0?" q-table--col-auto-width":""),style:i.headerStyle,onClick:s=>{i.sortable===!0&&e.props.sort(i),r(s)}};return h("th",f,a)}}}),separatorValues=["horizontal","vertical","cell","none"],QMarkupTable=createComponent({name:"QMarkupTable",props:{...useDarkProps,dense:Boolean,flat:Boolean,bordered:Boolean,square:Boolean,wrapCells:Boolean,separator:{type:String,default:"horizontal",validator:e=>separatorValues.includes(e)}},setup(e,{slots:n}){const l=getCurrentInstance(),t=useDark(e,l.proxy.$q),o=computed(()=>`q-markup-table q-table__container q-table__card q-table--${e.separator}-separator`+(t.value===!0?" q-table--dark q-table__card--dark q-dark":"")+(e.dense===!0?" q-table--dense":"")+(e.flat===!0?" q-table--flat":"")+(e.bordered===!0?" q-table--bordered":"")+(e.square===!0?" q-table--square":"")+(e.wrapCells===!1?" q-table--no-wrap":""));return()=>h("div",{class:o.value},[h("table",{class:"q-table"},hSlot(n.default))])}});function getTableMiddle(e,n){return h("div",e,[h("table",{class:"q-table"},n)])}const aggBucketSize=1e3,scrollToEdges=["start","center","end","start-force","center-force","end-force"],filterProto=Array.prototype.filter,setOverflowAnchor=window.getComputedStyle(document.body).overflowAnchor===void 0?noop:function(e,n){e!==null&&(e._qOverflowAnimationFrame!==void 0&&cancelAnimationFrame(e._qOverflowAnimationFrame),e._qOverflowAnimationFrame=requestAnimationFrame(()=>{if(e===null)return;e._qOverflowAnimationFrame=void 0;const l=e.children||[];filterProto.call(l,o=>o.dataset&&o.dataset.qVsAnchor!==void 0).forEach(o=>{delete o.dataset.qVsAnchor});const t=l[n];t&&t.dataset&&(t.dataset.qVsAnchor="")}))};function sumFn(e,n){return e+n}function getScrollDetails(e,n,l,t,o,r,i,a){const u=e===window?document.scrollingElement||document.documentElement:e,f=o===!0?"offsetWidth":"offsetHeight",s={scrollStart:0,scrollViewSize:-i-a,scrollMaxSize:0,offsetStart:-i,offsetEnd:-a};if(o===!0?(e===window?(s.scrollStart=window.pageXOffset||window.scrollX||document.body.scrollLeft||0,s.scrollViewSize+=document.documentElement.clientWidth):(s.scrollStart=u.scrollLeft,s.scrollViewSize+=u.clientWidth),s.scrollMaxSize=u.scrollWidth,r===!0&&(s.scrollStart=(rtlHasScrollBug===!0?s.scrollMaxSize-s.scrollViewSize:0)-s.scrollStart)):(e===window?(s.scrollStart=window.pageYOffset||window.scrollY||document.body.scrollTop||0,s.scrollViewSize+=document.documentElement.clientHeight):(s.scrollStart=u.scrollTop,s.scrollViewSize+=u.clientHeight),s.scrollMaxSize=u.scrollHeight),l!==null)for(let b=l.previousElementSibling;b!==null;b=b.previousElementSibling)b.classList.contains("q-virtual-scroll--skip")===!1&&(s.offsetStart+=b[f]);if(t!==null)for(let b=t.nextElementSibling;b!==null;b=b.nextElementSibling)b.classList.contains("q-virtual-scroll--skip")===!1&&(s.offsetEnd+=b[f]);if(n!==e){const b=u.getBoundingClientRect(),m=n.getBoundingClientRect();o===!0?(s.offsetStart+=m.left-b.left,s.offsetEnd-=m.width):(s.offsetStart+=m.top-b.top,s.offsetEnd-=m.height),e!==window&&(s.offsetStart+=s.scrollStart),s.offsetEnd+=s.scrollMaxSize-s.offsetStart}return s}function setScroll(e,n,l,t){n==="end"&&(n=(e===window?document.body:e)[l===!0?"scrollWidth":"scrollHeight"]),e===window?l===!0?(t===!0&&(n=(rtlHasScrollBug===!0?document.body.scrollWidth-document.documentElement.clientWidth:0)-n),window.scrollTo(n,window.pageYOffset||window.scrollY||document.body.scrollTop||0)):window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,n):l===!0?(t===!0&&(n=(rtlHasScrollBug===!0?e.scrollWidth-e.offsetWidth:0)-n),e.scrollLeft=n):e.scrollTop=n}function sumSize(e,n,l,t){if(l>=t)return 0;const o=n.length,r=Math.floor(l/aggBucketSize),i=Math.floor((t-1)/aggBucketSize)+1;let a=e.slice(r,i).reduce(sumFn,0);return l%aggBucketSize!==0&&(a-=n.slice(r*aggBucketSize,l).reduce(sumFn,0)),t%aggBucketSize!==0&&t!==o&&(a-=n.slice(t,i*aggBucketSize).reduce(sumFn,0)),a}const commonVirtScrollProps={virtualScrollSliceSize:{type:[Number,String],default:10},virtualScrollSliceRatioBefore:{type:[Number,String],default:1},virtualScrollSliceRatioAfter:{type:[Number,String],default:1},virtualScrollItemSize:{type:[Number,String],default:24},virtualScrollStickySizeStart:{type:[Number,String],default:0},virtualScrollStickySizeEnd:{type:[Number,String],default:0},tableColspan:[Number,String]},commonVirtScrollPropsList=Object.keys(commonVirtScrollProps),useVirtualScrollProps={virtualScrollHorizontal:Boolean,onVirtualScroll:Function,...commonVirtScrollProps};function useVirtualScroll({virtualScrollLength:e,getVirtualScrollTarget:n,getVirtualScrollEl:l,virtualScrollItemSizeComputed:t}){const o=getCurrentInstance(),{props:r,emit:i,proxy:a}=o,{$q:u}=a;let f,s,b,m=[],p;const y=ref(0),B=ref(0),S=ref({}),M=ref(null),D=ref(null),C=ref(null),d=ref({from:0,to:0}),I=computed(()=>r.tableColspan!==void 0?r.tableColspan:100);t===void 0&&(t=computed(()=>r.virtualScrollItemSize));const H=computed(()=>t.value+";"+r.virtualScrollHorizontal),E=computed(()=>H.value+";"+r.virtualScrollSliceRatioBefore+";"+r.virtualScrollSliceRatioAfter);watch(E,()=>{ee()}),watch(H,F);function F(){oe(s,!0)}function L(x){oe(x===void 0?s:x)}function K(x,N){const te=n();if(te==null||te.nodeType===8)return;const ve=getScrollDetails(te,l(),M.value,D.value,r.virtualScrollHorizontal,u.lang.rtl,r.virtualScrollStickySizeStart,r.virtualScrollStickySizeEnd);b!==ve.scrollViewSize&&ee(ve.scrollViewSize),de(te,ve,Math.min(e.value-1,Math.max(0,parseInt(x,10)||0)),0,scrollToEdges.indexOf(N)!==-1?N:s!==-1&&x>s?"end":"start")}function le(){const x=n();if(x==null||x.nodeType===8)return;const N=getScrollDetails(x,l(),M.value,D.value,r.virtualScrollHorizontal,u.lang.rtl,r.virtualScrollStickySizeStart,r.virtualScrollStickySizeEnd),te=e.value-1,ve=N.scrollMaxSize-N.offsetStart-N.offsetEnd-B.value;if(f===N.scrollStart)return;if(N.scrollMaxSize<=0){de(x,N,0,0);return}b!==N.scrollViewSize&&ee(N.scrollViewSize),ce(d.value.from);const ke=Math.floor(N.scrollMaxSize-Math.max(N.scrollViewSize,N.offsetEnd)-Math.min(p[te],N.scrollViewSize/2));if(ke>0&&Math.ceil(N.scrollStart)>=ke){de(x,N,te,N.scrollMaxSize-N.offsetEnd-m.reduce(sumFn,0));return}let w=0,P=N.scrollStart-N.offsetStart,G=P;if(P<=ve&&P+N.scrollViewSize>=y.value)P-=y.value,w=d.value.from,G=P;else for(let Q=0;P>=m[Q]&&w<te;Q++)P-=m[Q],w+=aggBucketSize;for(;P>0&&w<te;)P-=p[w],P>-N.scrollViewSize?(w++,G=P):G=p[w]+P;de(x,N,w,G)}function de(x,N,te,ve,ke){const w=typeof ke=="string"&&ke.indexOf("-force")!==-1,P=w===!0?ke.replace("-force",""):ke,G=P!==void 0?P:"start";let Q=Math.max(0,te-S.value[G]),be=Q+S.value.total;be>e.value&&(be=e.value,Q=Math.max(0,be-S.value.total)),f=N.scrollStart;const fe=Q!==d.value.from||be!==d.value.to;if(fe===!1&&P===void 0){xe(te);return}const{activeElement:T}=document,Y=C.value;fe===!0&&Y!==null&&Y!==T&&Y.contains(T)===!0&&(Y.addEventListener("focusout",J),setTimeout(()=>{Y!==null&&Y.removeEventListener("focusout",J)})),setOverflowAnchor(Y,te-Q);const ie=P!==void 0?p.slice(Q,te).reduce(sumFn,0):0;if(fe===!0){const re=be>=d.value.from&&Q<=d.value.to?d.value.to:be;d.value={from:Q,to:re},y.value=sumSize(m,p,0,Q),B.value=sumSize(m,p,be,e.value),requestAnimationFrame(()=>{d.value.to!==be&&f===N.scrollStart&&(d.value={from:d.value.from,to:be},B.value=sumSize(m,p,be,e.value))})}requestAnimationFrame(()=>{if(f!==N.scrollStart)return;fe===!0&&ce(Q);const re=p.slice(Q,te).reduce(sumFn,0),ye=re+N.offsetStart+y.value,Z=ye+p[te];let qe=ye+ve;if(P!==void 0){const _e=re-ie,De=N.scrollStart+_e;qe=w!==!0&&De<ye&&Z<De+N.scrollViewSize?De:P==="end"?Z-N.scrollViewSize:ye-(P==="start"?0:Math.round((N.scrollViewSize-p[te])/2))}f=qe,setScroll(x,qe,r.virtualScrollHorizontal,u.lang.rtl),xe(te)})}function ce(x){const N=C.value;if(N){const te=filterProto.call(N.children,Q=>Q.classList&&Q.classList.contains("q-virtual-scroll--skip")===!1),ve=te.length,ke=r.virtualScrollHorizontal===!0?Q=>Q.getBoundingClientRect().width:Q=>Q.offsetHeight;let w=x,P,G;for(let Q=0;Q<ve;){for(P=ke(te[Q]),Q++;Q<ve&&te[Q].classList.contains("q-virtual-scroll--with-prev")===!0;)P+=ke(te[Q]),Q++;G=P-p[w],G!==0&&(p[w]+=G,m[Math.floor(w/aggBucketSize)]+=G),w++}}}function J(){C.value!==null&&C.value!==void 0&&C.value.focus()}function oe(x,N){const te=1*t.value;(N===!0||Array.isArray(p)===!1)&&(p=[]);const ve=p.length;p.length=e.value;for(let w=e.value-1;w>=ve;w--)p[w]=te;const ke=Math.floor((e.value-1)/aggBucketSize);m=[];for(let w=0;w<=ke;w++){let P=0;const G=Math.min((w+1)*aggBucketSize,e.value);for(let Q=w*aggBucketSize;Q<G;Q++)P+=p[Q];m.push(P)}s=-1,f=void 0,y.value=sumSize(m,p,0,d.value.from),B.value=sumSize(m,p,d.value.to,e.value),x>=0?(ce(d.value.from),nextTick(()=>{K(x)})):Ve()}function ee(x){if(x===void 0&&typeof window<"u"){const P=n();P!=null&&P.nodeType!==8&&(x=getScrollDetails(P,l(),M.value,D.value,r.virtualScrollHorizontal,u.lang.rtl,r.virtualScrollStickySizeStart,r.virtualScrollStickySizeEnd).scrollViewSize)}b=x;const N=parseFloat(r.virtualScrollSliceRatioBefore)||0,te=parseFloat(r.virtualScrollSliceRatioAfter)||0,ve=1+N+te,ke=x===void 0||x<=0?1:Math.ceil(x/t.value),w=Math.max(1,ke,Math.ceil((r.virtualScrollSliceSize>0?r.virtualScrollSliceSize:10)/ve));S.value={total:Math.ceil(w*ve),start:Math.ceil(w*N),center:Math.ceil(w*(.5+N)),end:Math.ceil(w*(1+N)),view:ke}}function he(x,N){const te=r.virtualScrollHorizontal===!0?"width":"height",ve={["--q-virtual-scroll-item-"+te]:t.value+"px"};return[x==="tbody"?h(x,{class:"q-virtual-scroll__padding",key:"before",ref:M},[h("tr",[h("td",{style:{[te]:`${y.value}px`,...ve},colspan:I.value})])]):h(x,{class:"q-virtual-scroll__padding",key:"before",ref:M,style:{[te]:`${y.value}px`,...ve}}),h(x,{class:"q-virtual-scroll__content",key:"content",ref:C,tabindex:-1},N.flat()),x==="tbody"?h(x,{class:"q-virtual-scroll__padding",key:"after",ref:D},[h("tr",[h("td",{style:{[te]:`${B.value}px`,...ve},colspan:I.value})])]):h(x,{class:"q-virtual-scroll__padding",key:"after",ref:D,style:{[te]:`${B.value}px`,...ve}})]}function xe(x){s!==x&&(r.onVirtualScroll!==void 0&&i("virtualScroll",{index:x,from:d.value.from,to:d.value.to-1,direction:x<s?"decrease":"increase",ref:a}),s=x)}ee();const Ve=debounce(le,u.platform.is.ios===!0?120:35);onBeforeMount(()=>{ee()});let Fe=!1;return onDeactivated(()=>{Fe=!0}),onActivated(()=>{if(Fe!==!0)return;const x=n();f!==void 0&&x!==void 0&&x!==null&&x.nodeType!==8?setScroll(x,f,r.virtualScrollHorizontal,u.lang.rtl):K(s)}),onBeforeUnmount(()=>{Ve.cancel()}),Object.assign(a,{scrollTo:K,reset:F,refresh:L}),{virtualScrollSliceRange:d,virtualScrollSliceSizeComputed:S,setVirtualScrollSize:ee,onVirtualScrollEvt:Ve,localResetVirtualScroll:oe,padVirtualScroll:he,scrollTo:K,reset:F,refresh:L}}const comps={list:QList,table:QMarkupTable},typeOptions=["list","table","__qtable"],QVirtualScroll=createComponent({name:"QVirtualScroll",props:{...useVirtualScrollProps,type:{type:String,default:"list",validator:e=>typeOptions.includes(e)},items:{type:Array,default:()=>[]},itemsFn:Function,itemsSize:Number,scrollTarget:scrollTargetProp},setup(e,{slots:n,attrs:l}){let t;const o=ref(null),r=computed(()=>e.itemsSize>=0&&e.itemsFn!==void 0?parseInt(e.itemsSize,10):Array.isArray(e.items)?e.items.length:0),{virtualScrollSliceRange:i,localResetVirtualScroll:a,padVirtualScroll:u,onVirtualScrollEvt:f}=useVirtualScroll({virtualScrollLength:r,getVirtualScrollTarget:y,getVirtualScrollEl:p}),s=computed(()=>{if(r.value===0)return[];const D=(C,d)=>({index:i.value.from+d,item:C});return e.itemsFn===void 0?e.items.slice(i.value.from,i.value.to).map(D):e.itemsFn(i.value.from,i.value.to-i.value.from).map(D)}),b=computed(()=>"q-virtual-scroll q-virtual-scroll"+(e.virtualScrollHorizontal===!0?"--horizontal":"--vertical")+(e.scrollTarget!==void 0?"":" scroll")),m=computed(()=>e.scrollTarget!==void 0?{}:{tabindex:0});watch(r,()=>{a()}),watch(()=>e.scrollTarget,()=>{S(),B()});function p(){return o.value.$el||o.value}function y(){return t}function B(){t=getScrollTarget(p(),e.scrollTarget),t.addEventListener("scroll",f,listenOpts.passive)}function S(){t!==void 0&&(t.removeEventListener("scroll",f,listenOpts.passive),t=void 0)}function M(){let D=u(e.type==="list"?"div":"tbody",s.value.map(n.default));return n.before!==void 0&&(D=n.before().concat(D)),hMergeSlot(n.after,D)}return onBeforeMount(()=>{a()}),onMounted(()=>{B()}),onActivated(()=>{B()}),onDeactivated(()=>{S()}),onBeforeUnmount(()=>{S()}),()=>{if(n.default===void 0){console.error("QVirtualScroll: default scoped slot is required for rendering");return}return e.type==="__qtable"?getTableMiddle({ref:o,class:"q-table__middle "+b.value},M()):h(comps[e.type],{...l,ref:o,class:[l.class,b.value],...m.value},M)}}}),QField=createComponent({name:"QField",inheritAttrs:!1,props:{...useFieldProps,tag:{type:String,default:"label"}},emits:useFieldEmits,setup(){return useField(useFieldState({tagProp:!0}))}}),defaultSizes$1={xs:8,sm:10,md:14,lg:20,xl:24},QChip=createComponent({name:"QChip",props:{...useDarkProps,...useSizeProps,dense:Boolean,icon:String,iconRight:String,iconRemove:String,iconSelected:String,label:[String,Number],color:String,textColor:String,modelValue:{type:Boolean,default:!0},selected:{type:Boolean,default:null},square:Boolean,outline:Boolean,clickable:Boolean,removable:Boolean,removeAriaLabel:String,tabindex:[String,Number],disable:Boolean,ripple:{type:[Boolean,Object],default:!0}},emits:["update:modelValue","update:selected","remove","click"],setup(e,{slots:n,emit:l}){const{proxy:{$q:t}}=getCurrentInstance(),o=useDark(e,t),r=useSize(e,defaultSizes$1),i=computed(()=>e.selected===!0||e.icon!==void 0),a=computed(()=>e.selected===!0?e.iconSelected||t.iconSet.chip.selected:e.icon),u=computed(()=>e.iconRemove||t.iconSet.chip.remove),f=computed(()=>e.disable===!1&&(e.clickable===!0||e.selected!==null)),s=computed(()=>{const S=e.outline===!0&&e.color||e.textColor;return"q-chip row inline no-wrap items-center"+(e.outline===!1&&e.color!==void 0?` bg-${e.color}`:"")+(S?` text-${S} q-chip--colored`:"")+(e.disable===!0?" disabled":"")+(e.dense===!0?" q-chip--dense":"")+(e.outline===!0?" q-chip--outline":"")+(e.selected===!0?" q-chip--selected":"")+(f.value===!0?" q-chip--clickable cursor-pointer non-selectable q-hoverable":"")+(e.square===!0?" q-chip--square":"")+(o.value===!0?" q-chip--dark q-dark":"")}),b=computed(()=>{const S=e.disable===!0?{tabindex:-1,"aria-disabled":"true"}:{tabindex:e.tabindex||0},M={...S,role:"button","aria-hidden":"false","aria-label":e.removeAriaLabel||t.lang.label.remove};return{chip:S,remove:M}});function m(S){S.keyCode===13&&p(S)}function p(S){e.disable||(l("update:selected",!e.selected),l("click",S))}function y(S){(S.keyCode===void 0||S.keyCode===13)&&(stopAndPrevent(S),e.disable===!1&&(l("update:modelValue",!1),l("remove")))}function B(){const S=[];f.value===!0&&S.push(h("div",{class:"q-focus-helper"})),i.value===!0&&S.push(h(QIcon,{class:"q-chip__icon q-chip__icon--left",name:a.value}));const M=e.label!==void 0?[h("div",{class:"ellipsis"},[e.label])]:void 0;return S.push(h("div",{class:"q-chip__content col row no-wrap items-center q-anchor--skip"},hMergeSlotSafely(n.default,M))),e.iconRight&&S.push(h(QIcon,{class:"q-chip__icon q-chip__icon--right",name:e.iconRight})),e.removable===!0&&S.push(h(QIcon,{class:"q-chip__icon q-chip__icon--remove cursor-pointer",name:u.value,...b.value.remove,onClick:y,onKeyup:y})),S}return()=>{if(e.modelValue===!1)return;const S={class:s.value,style:r.value};return f.value===!0&&Object.assign(S,b.value.chip,{onClick:p,onKeyup:m}),hDir("div",S,B(),"ripple",e.ripple!==!1&&e.disable!==!0,()=>[[Ripple,e.ripple]])}}}),validateNewValueMode=e=>["add","add-unique","toggle"].includes(e),reEscapeList=".*+?^${}()|[]\\",fieldPropsList=Object.keys(useFieldProps);function getPropValueFn$1(e,n){if(typeof e=="function")return e;const l=e!==void 0?e:n;return t=>t!==null&&typeof t=="object"&&l in t?t[l]:t}const QSelect=createComponent({name:"QSelect",inheritAttrs:!1,props:{...useVirtualScrollProps,...useFormProps,...useFieldProps,modelValue:{required:!0},multiple:Boolean,displayValue:[String,Number],displayValueHtml:Boolean,dropdownIcon:String,options:{type:Array,default:()=>[]},optionValue:[Function,String],optionLabel:[Function,String],optionDisable:[Function,String],hideSelected:Boolean,hideDropdownIcon:Boolean,fillInput:Boolean,maxValues:[Number,String],optionsDense:Boolean,optionsDark:{type:Boolean,default:null},optionsSelectedClass:String,optionsHtml:Boolean,optionsCover:Boolean,menuShrink:Boolean,menuAnchor:String,menuSelf:String,menuOffset:Array,popupContentClass:String,popupContentStyle:[String,Array,Object],popupNoRouteDismiss:Boolean,useInput:Boolean,useChips:Boolean,newValueMode:{type:String,validator:validateNewValueMode},mapOptions:Boolean,emitValue:Boolean,disableTabSelection:Boolean,inputDebounce:{type:[Number,String],default:500},inputClass:[Array,String,Object],inputStyle:[Array,String,Object],tabindex:{type:[String,Number],default:0},autocomplete:String,transitionShow:{},transitionHide:{},transitionDuration:{},behavior:{type:String,validator:e=>["default","menu","dialog"].includes(e),default:"default"},virtualScrollItemSize:useVirtualScrollProps.virtualScrollItemSize.type,onNewValue:Function,onFilter:Function},emits:[...useFieldEmits,"add","remove","inputValue","keyup","keypress","keydown","popupShow","popupHide","filterAbort"],setup(e,{slots:n,emit:l}){const{proxy:t}=getCurrentInstance(),{$q:o}=t,r=ref(!1),i=ref(!1),a=ref(-1),u=ref(""),f=ref(!1),s=ref(!1);let b=null,m=null,p,y,B,S=null,M,D,C,d;const I=ref(null),H=ref(null),E=ref(null),F=ref(null),L=ref(null),K=useFormInputNameAttr(e),le=useKeyComposition(V),de=computed(()=>Array.isArray(e.options)?e.options.length:0),ce=computed(()=>e.virtualScrollItemSize===void 0?e.optionsDense===!0?24:48:e.virtualScrollItemSize),{virtualScrollSliceRange:J,virtualScrollSliceSizeComputed:oe,localResetVirtualScroll:ee,padVirtualScroll:he,onVirtualScrollEvt:xe,scrollTo:Ve,setVirtualScrollSize:Fe}=useVirtualScroll({virtualScrollLength:de,getVirtualScrollTarget:We,getVirtualScrollEl:ze,virtualScrollItemSizeComputed:ce}),x=useFieldState(),N=computed(()=>{const v=e.mapOptions===!0&&e.multiple!==!0,j=e.modelValue!==void 0&&(e.modelValue!==null||v===!0)?e.multiple===!0&&Array.isArray(e.modelValue)?e.modelValue:[e.modelValue]:[];if(e.mapOptions===!0&&Array.isArray(e.options)===!0){const $=e.mapOptions===!0&&p!==void 0?p:[],pe=j.map(Pe=>k(Pe,$));return e.modelValue===null&&v===!0?pe.filter(Pe=>Pe!==null):pe}return j}),te=computed(()=>{const v={};return fieldPropsList.forEach(j=>{const $=e[j];$!==void 0&&(v[j]=$)}),v}),ve=computed(()=>e.optionsDark===null?x.isDark.value:e.optionsDark),ke=computed(()=>fieldValueIsFilled(N.value)),w=computed(()=>{let v="q-field__input q-placeholder col";return e.hideSelected===!0||N.value.length===0?[v,e.inputClass]:(v+=" q-field__input--padding",e.inputClass===void 0?v:[v,e.inputClass])}),P=computed(()=>(e.virtualScrollHorizontal===!0?"q-virtual-scroll--horizontal":"")+(e.popupContentClass?" "+e.popupContentClass:"")),G=computed(()=>de.value===0),Q=computed(()=>N.value.map(v=>Be.value(v)).join(", ")),be=computed(()=>e.displayValue!==void 0?e.displayValue:Q.value),fe=computed(()=>e.optionsHtml===!0?()=>!0:v=>v!=null&&v.html===!0),T=computed(()=>e.displayValueHtml===!0||e.displayValue===void 0&&(e.optionsHtml===!0||N.value.some(fe.value))),Y=computed(()=>x.focused.value===!0?e.tabindex:-1),ie=computed(()=>{const v={tabindex:e.tabindex,role:"combobox","aria-label":e.label,"aria-readonly":e.readonly===!0?"true":"false","aria-autocomplete":e.useInput===!0?"list":"none","aria-expanded":r.value===!0?"true":"false","aria-controls":`${x.targetUid.value}_lb`};return a.value>=0&&(v["aria-activedescendant"]=`${x.targetUid.value}_${a.value}`),v}),re=computed(()=>({id:`${x.targetUid.value}_lb`,role:"listbox","aria-multiselectable":e.multiple===!0?"true":"false"})),ye=computed(()=>N.value.map((v,j)=>({index:j,opt:v,html:fe.value(v),selected:!0,removeAtIndex:$e,toggleOption:Le,tabindex:Y.value}))),Z=computed(()=>{if(de.value===0)return[];const{from:v,to:j}=J.value;return e.options.slice(v,j).map(($,pe)=>{const Pe=Re.value($)===!0,Te=R($)===!0,Oe=v+pe,Ae={clickable:!0,active:Te,activeClass:De.value,manualFocus:!0,focused:!1,disable:Pe,tabindex:-1,dense:e.optionsDense,dark:ve.value,role:"option","aria-selected":Te===!0?"true":"false",id:`${x.targetUid.value}_${Oe}`,onClick:()=>{Le($)}};return Pe!==!0&&(a.value===Oe&&(Ae.focused=!0),o.platform.is.desktop===!0&&(Ae.onMousemove=()=>{r.value===!0&&q(Oe)})),{index:Oe,opt:$,html:fe.value($),label:Be.value($),selected:Ae.active,focused:Ae.focused,toggleOption:Le,setOptionIndex:q,itemProps:Ae}})}),qe=computed(()=>e.dropdownIcon!==void 0?e.dropdownIcon:o.iconSet.arrow.dropdown),_e=computed(()=>e.optionsCover===!1&&e.outlined!==!0&&e.standout!==!0&&e.borderless!==!0&&e.rounded!==!0),De=computed(()=>e.optionsSelectedClass!==void 0?e.optionsSelectedClass:e.color!==void 0?`text-${e.color}`:""),Ie=computed(()=>getPropValueFn$1(e.optionValue,"value")),Be=computed(()=>getPropValueFn$1(e.optionLabel,"label")),Re=computed(()=>getPropValueFn$1(e.optionDisable,"disable")),Qe=computed(()=>N.value.map(Ie.value)),je=computed(()=>{const v={onInput:V,onChange:le,onKeydown:Me,onKeyup:W,onKeypress:Se,onFocus:ne,onClick(j){y===!0&&stop(j)}};return v.onCompositionstart=v.onCompositionupdate=v.onCompositionend=le,v});watch(N,v=>{p=v,e.useInput===!0&&e.fillInput===!0&&e.multiple!==!0&&x.innerLoading.value!==!0&&(i.value!==!0&&r.value!==!0||ke.value!==!0)&&(B!==!0&&Xe(),(i.value===!0||r.value===!0)&&ue(""))},{immediate:!0}),watch(()=>e.fillInput,Xe),watch(r,Ze),watch(de,at);function He(v){return e.emitValue===!0?Ie.value(v):v}function Ne(v){if(v!==-1&&v<N.value.length)if(e.multiple===!0){const j=e.modelValue.slice();l("remove",{index:v,value:j.splice(v,1)[0]}),l("update:modelValue",j)}else l("update:modelValue",null)}function $e(v){Ne(v),x.focus()}function Ke(v,j){const $=He(v);if(e.multiple!==!0){e.fillInput===!0&&ae(Be.value(v),!0,!0),l("update:modelValue",$);return}if(N.value.length===0){l("add",{index:0,value:$}),l("update:modelValue",e.multiple===!0?[$]:$);return}if(j===!0&&R(v)===!0||e.maxValues!==void 0&&e.modelValue.length>=e.maxValues)return;const pe=e.modelValue.slice();l("add",{index:pe.length,value:$}),pe.push($),l("update:modelValue",pe)}function Le(v,j){if(x.editable.value!==!0||v===void 0||Re.value(v)===!0)return;const $=Ie.value(v);if(e.multiple!==!0){j!==!0&&(ae(e.fillInput===!0?Be.value(v):"",!0,!0),Ue()),H.value!==null&&H.value.focus(),(N.value.length===0||isDeepEqual(Ie.value(N.value[0]),$)!==!0)&&l("update:modelValue",e.emitValue===!0?$:v);return}if((y!==!0||f.value===!0)&&x.focus(),ne(),N.value.length===0){const Te=e.emitValue===!0?$:v;l("add",{index:0,value:Te}),l("update:modelValue",e.multiple===!0?[Te]:Te);return}const pe=e.modelValue.slice(),Pe=Qe.value.findIndex(Te=>isDeepEqual(Te,$));if(Pe!==-1)l("remove",{index:Pe,value:pe.splice(Pe,1)[0]});else{if(e.maxValues!==void 0&&pe.length>=e.maxValues)return;const Te=e.emitValue===!0?$:v;l("add",{index:pe.length,value:Te}),pe.push(Te)}l("update:modelValue",pe)}function q(v){if(o.platform.is.desktop!==!0)return;const j=v!==-1&&v<de.value?v:-1;a.value!==j&&(a.value=j)}function A(v=1,j){if(r.value===!0){let $=a.value;do $=normalizeToInterval($+v,-1,de.value-1);while($!==-1&&$!==a.value&&Re.value(e.options[$])===!0);a.value!==$&&(q($),Ve($),j!==!0&&e.useInput===!0&&e.fillInput===!0&&U($>=0?Be.value(e.options[$]):M,!0))}}function k(v,j){const $=pe=>isDeepEqual(Ie.value(pe),v);return e.options.find($)||j.find($)||v}function R(v){const j=Ie.value(v);return Qe.value.find($=>isDeepEqual($,j))!==void 0}function ne(v){e.useInput===!0&&H.value!==null&&(v===void 0||H.value===v.target&&v.target.value===Q.value)&&H.value.select()}function me(v){isKeyCode(v,27)===!0&&r.value===!0&&(stop(v),Ue(),Xe()),l("keyup",v)}function W(v){const{value:j}=v.target;if(v.keyCode!==void 0){me(v);return}if(v.target.value="",b!==null&&(clearTimeout(b),b=null),m!==null&&(clearTimeout(m),m=null),Xe(),typeof j=="string"&&j.length!==0){const $=j.toLocaleLowerCase(),pe=Te=>{const Oe=e.options.find(Ae=>String(Te.value(Ae)).toLocaleLowerCase()===$);return Oe===void 0?!1:(N.value.indexOf(Oe)===-1?Le(Oe):Ue(),!0)},Pe=Te=>{pe(Ie)!==!0&&Te!==!0&&pe(Be)!==!0&&ue(j,!0,()=>Pe(!0))};Pe()}else x.clearValue(v)}function Se(v){l("keypress",v)}function Me(v){if(l("keydown",v),shouldIgnoreKey(v)===!0)return;const j=u.value.length!==0&&(e.newValueMode!==void 0||e.onNewValue!==void 0),$=v.shiftKey!==!0&&e.disableTabSelection!==!0&&e.multiple!==!0&&(a.value!==-1||j===!0);if(v.keyCode===27){prevent(v);return}if(v.keyCode===9&&$===!1){se();return}if(v.target===void 0||v.target.id!==x.targetUid.value||x.editable.value!==!0)return;if(v.keyCode===40&&x.innerLoading.value!==!0&&r.value===!1){stopAndPrevent(v),we();return}if(v.keyCode===8&&(e.useChips===!0||e.clearable===!0)&&e.hideSelected!==!0&&u.value.length===0){e.multiple===!0&&Array.isArray(e.modelValue)===!0?Ne(e.modelValue.length-1):e.multiple!==!0&&e.modelValue!==null&&l("update:modelValue",null);return}(v.keyCode===35||v.keyCode===36)&&(typeof u.value!="string"||u.value.length===0)&&(stopAndPrevent(v),a.value=-1,A(v.keyCode===36?1:-1,e.multiple)),(v.keyCode===33||v.keyCode===34)&&oe.value!==void 0&&(stopAndPrevent(v),a.value=Math.max(-1,Math.min(de.value,a.value+(v.keyCode===33?-1:1)*oe.value.view)),A(v.keyCode===33?1:-1,e.multiple)),(v.keyCode===38||v.keyCode===40)&&(stopAndPrevent(v),A(v.keyCode===38?-1:1,e.multiple));const pe=de.value;if((C===void 0||d<Date.now())&&(C=""),pe>0&&e.useInput!==!0&&v.key!==void 0&&v.key.length===1&&v.altKey===!1&&v.ctrlKey===!1&&v.metaKey===!1&&(v.keyCode!==32||C.length!==0)){r.value!==!0&&we(v);const Pe=v.key.toLocaleLowerCase(),Te=C.length===1&&C[0]===Pe;d=Date.now()+1500,Te===!1&&(stopAndPrevent(v),C+=Pe);const Oe=new RegExp("^"+C.split("").map(et=>reEscapeList.indexOf(et)!==-1?"\\"+et:et).join(".*"),"i");let Ae=a.value;if(Te===!0||Ae<0||Oe.test(Be.value(e.options[Ae]))!==!0)do Ae=normalizeToInterval(Ae+1,-1,pe-1);while(Ae!==a.value&&(Re.value(e.options[Ae])===!0||Oe.test(Be.value(e.options[Ae]))!==!0));a.value!==Ae&&nextTick(()=>{q(Ae),Ve(Ae),Ae>=0&&e.useInput===!0&&e.fillInput===!0&&U(Be.value(e.options[Ae]),!0)});return}if(!(v.keyCode!==13&&(v.keyCode!==32||e.useInput===!0||C!=="")&&(v.keyCode!==9||$===!1))){if(v.keyCode!==9&&stopAndPrevent(v),a.value!==-1&&a.value<pe){Le(e.options[a.value]);return}if(j===!0){const Pe=(Te,Oe)=>{if(Oe){if(validateNewValueMode(Oe)!==!0)return}else Oe=e.newValueMode;if(ae("",e.multiple!==!0,!0),Te==null)return;(Oe==="toggle"?Le:Ke)(Te,Oe==="add-unique"),e.multiple!==!0&&(H.value!==null&&H.value.focus(),Ue())};if(e.onNewValue!==void 0?l("newValue",u.value,Pe):Pe(u.value),e.multiple!==!0)return}r.value===!0?se():x.innerLoading.value!==!0&&we()}}function ze(){return y===!0?L.value:E.value!==null&&E.value.contentEl!==null?E.value.contentEl:void 0}function We(){return ze()}function Ye(){return e.hideSelected===!0?[]:n["selected-item"]!==void 0?ye.value.map(v=>n["selected-item"](v)).slice():n.selected!==void 0?[].concat(n.selected()):e.useChips===!0?ye.value.map((v,j)=>h(QChip,{key:"option-"+j,removable:x.editable.value===!0&&Re.value(v.opt)!==!0,dense:!0,textColor:e.color,tabindex:Y.value,onRemove(){v.removeAtIndex(j)}},()=>h("span",{class:"ellipsis",[v.html===!0?"innerHTML":"textContent"]:Be.value(v.opt)}))):[h("span",{[T.value===!0?"innerHTML":"textContent"]:be.value})]}function Ge(){if(G.value===!0)return n["no-option"]!==void 0?n["no-option"]({inputValue:u.value}):void 0;const v=n.option!==void 0?n.option:$=>h(QItem,{key:$.index,...$.itemProps},()=>h(QItemSection,()=>h(QItemLabel,()=>h("span",{[$.html===!0?"innerHTML":"textContent"]:$.label}))));let j=he("div",Z.value.map(v));return n["before-options"]!==void 0&&(j=n["before-options"]().concat(j)),hMergeSlot(n["after-options"],j)}function Je(v,j){const $=j===!0?{...ie.value,...x.splitAttrs.attributes.value}:void 0,pe={ref:j===!0?H:void 0,key:"i_t",class:w.value,style:e.inputStyle,value:u.value!==void 0?u.value:"",type:"search",...$,id:j===!0?x.targetUid.value:void 0,maxlength:e.maxlength,autocomplete:e.autocomplete,"data-autofocus":v===!0||e.autofocus===!0||void 0,disabled:e.disable===!0,readonly:e.readonly===!0,...je.value};return v!==!0&&y===!0&&(Array.isArray(pe.class)===!0?pe.class=[...pe.class,"no-pointer-events"]:pe.class+=" no-pointer-events"),h("input",pe)}function V(v){b!==null&&(clearTimeout(b),b=null),m!==null&&(clearTimeout(m),m=null),!(v&&v.target&&v.target.qComposing===!0)&&(U(v.target.value||""),B=!0,M=u.value,x.focused.value!==!0&&(y!==!0||f.value===!0)&&x.focus(),e.onFilter!==void 0&&(b=setTimeout(()=>{b=null,ue(u.value)},e.inputDebounce)))}function U(v,j){u.value!==v&&(u.value=v,j===!0||e.inputDebounce===0||e.inputDebounce==="0"?l("inputValue",v):m=setTimeout(()=>{m=null,l("inputValue",v)},e.inputDebounce))}function ae(v,j,$){B=$!==!0,e.useInput===!0&&(U(v,!0),(j===!0||$!==!0)&&(M=v),j!==!0&&ue(v))}function ue(v,j,$){if(e.onFilter===void 0||j!==!0&&x.focused.value!==!0)return;x.innerLoading.value===!0?l("filterAbort"):(x.innerLoading.value=!0,s.value=!0),v!==""&&e.multiple!==!0&&N.value.length!==0&&B!==!0&&v===Be.value(N.value[0])&&(v="");const pe=setTimeout(()=>{r.value===!0&&(r.value=!1)},10);S!==null&&clearTimeout(S),S=pe,l("filter",v,(Pe,Te)=>{(j===!0||x.focused.value===!0)&&S===pe&&(clearTimeout(S),typeof Pe=="function"&&Pe(),s.value=!1,nextTick(()=>{x.innerLoading.value=!1,x.editable.value===!0&&(j===!0?r.value===!0&&Ue():r.value===!0?Ze(!0):r.value=!0),typeof Te=="function"&&nextTick(()=>{Te(t)}),typeof $=="function"&&nextTick(()=>{$(t)})}))},()=>{x.focused.value===!0&&S===pe&&(clearTimeout(S),x.innerLoading.value=!1,s.value=!1),r.value===!0&&(r.value=!1)})}function ge(){return h(QMenu,{ref:E,class:P.value,style:e.popupContentStyle,modelValue:r.value,fit:e.menuShrink!==!0,cover:e.optionsCover===!0&&G.value!==!0&&e.useInput!==!0,anchor:e.menuAnchor,self:e.menuSelf,offset:e.menuOffset,dark:ve.value,noParentEvent:!0,noRefocus:!0,noFocus:!0,noRouteDismiss:e.popupNoRouteDismiss,square:_e.value,transitionShow:e.transitionShow,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,separateClosePopup:!0,...re.value,onScrollPassive:xe,onBeforeShow:nt,onBeforeHide:Ee,onShow:c},Ge)}function Ee(v){lt(v),se()}function c(){Fe()}function g(v){stop(v),H.value!==null&&H.value.focus(),f.value=!0,window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,0)}function _(v){stop(v),nextTick(()=>{f.value=!1})}function O(){const v=[h(QField,{class:`col-auto ${x.fieldClass.value}`,...te.value,for:x.targetUid.value,dark:ve.value,square:!0,loading:s.value,itemAligned:!1,filled:!0,stackLabel:u.value.length!==0,...x.splitAttrs.listeners.value,onFocus:g,onBlur:_},{...n,rawControl:()=>x.getControl(!0),before:void 0,after:void 0})];return r.value===!0&&v.push(h("div",{ref:L,class:P.value+" scroll",style:e.popupContentStyle,...re.value,onClick:prevent,onScrollPassive:xe},Ge())),h(QDialog,{ref:F,modelValue:i.value,position:e.useInput===!0?"top":void 0,transitionShow:D,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,noRouteDismiss:e.popupNoRouteDismiss,onBeforeShow:nt,onBeforeHide:z,onHide:Ce,onShow:X},()=>h("div",{class:"q-select__dialog"+(ve.value===!0?" q-select__dialog--dark q-dark":"")+(f.value===!0?" q-select__dialog--focused":"")},v))}function z(v){lt(v),F.value!==null&&F.value.__updateRefocusTarget(x.rootRef.value.querySelector(".q-field__native > [tabindex]:last-child")),x.focused.value=!1}function Ce(v){Ue(),x.focused.value===!1&&l("blur",v),Xe()}function X(){const v=document.activeElement;(v===null||v.id!==x.targetUid.value)&&H.value!==null&&H.value!==v&&H.value.focus(),Fe()}function se(){i.value!==!0&&(a.value=-1,r.value===!0&&(r.value=!1),x.focused.value===!1&&(S!==null&&(clearTimeout(S),S=null),x.innerLoading.value===!0&&(l("filterAbort"),x.innerLoading.value=!1,s.value=!1)))}function we(v){x.editable.value===!0&&(y===!0?(x.onControlFocusin(v),i.value=!0,nextTick(()=>{x.focus()})):x.focus(),e.onFilter!==void 0?ue(u.value):(G.value!==!0||n["no-option"]!==void 0)&&(r.value=!0))}function Ue(){i.value=!1,se()}function Xe(){e.useInput===!0&&ae(e.multiple!==!0&&e.fillInput===!0&&N.value.length!==0&&Be.value(N.value[0])||"",!0,!0)}function Ze(v){let j=-1;if(v===!0){if(N.value.length!==0){const $=Ie.value(N.value[0]);j=e.options.findIndex(pe=>isDeepEqual(Ie.value(pe),$))}ee(j)}q(j)}function at(v,j){r.value===!0&&x.innerLoading.value===!1&&(ee(-1,!0),nextTick(()=>{r.value===!0&&x.innerLoading.value===!1&&(v>j?ee():Ze(!0))}))}function tt(){i.value===!1&&E.value!==null&&E.value.updatePosition()}function nt(v){v!==void 0&&stop(v),l("popupShow",v),x.hasPopupOpen=!0,x.onControlFocusin(v)}function lt(v){v!==void 0&&stop(v),l("popupHide",v),x.hasPopupOpen=!1,x.onControlFocusout(v)}function ot(){y=o.platform.is.mobile!==!0&&e.behavior!=="dialog"?!1:e.behavior!=="menu"&&(e.useInput===!0?n["no-option"]!==void 0||e.onFilter!==void 0||G.value===!1:!0),D=o.platform.is.ios===!0&&y===!0&&e.useInput===!0?"fade":e.transitionShow}return onBeforeUpdate(ot),onUpdated(tt),ot(),onBeforeUnmount(()=>{b!==null&&clearTimeout(b),m!==null&&clearTimeout(m)}),Object.assign(t,{showPopup:we,hidePopup:Ue,removeAtIndex:Ne,add:Ke,toggleOption:Le,getOptionIndex:()=>a.value,setOptionIndex:q,moveOptionSelection:A,filter:ue,updateMenuPosition:tt,updateInputValue:ae,isOptionSelected:R,getEmittingOptionValue:He,isOptionDisabled:(...v)=>Re.value.apply(null,v)===!0,getOptionValue:(...v)=>Ie.value.apply(null,v),getOptionLabel:(...v)=>Be.value.apply(null,v)}),Object.assign(x,{innerValue:N,fieldClass:computed(()=>`q-select q-field--auto-height q-select--with${e.useInput!==!0?"out":""}-input q-select--with${e.useChips!==!0?"out":""}-chips q-select--${e.multiple===!0?"multiple":"single"}`),inputRef:I,targetRef:H,hasValue:ke,showPopup:we,floatingLabel:computed(()=>e.hideSelected!==!0&&ke.value===!0||typeof u.value=="number"||u.value.length!==0||fieldValueIsFilled(e.displayValue)),getControlChild:()=>{if(x.editable.value!==!1&&(i.value===!0||G.value!==!0||n["no-option"]!==void 0))return y===!0?O():ge();x.hasPopupOpen===!0&&(x.hasPopupOpen=!1)},controlEvents:{onFocusin(v){x.onControlFocusin(v)},onFocusout(v){x.onControlFocusout(v,()=>{Xe(),se()})},onClick(v){if(prevent(v),y!==!0&&r.value===!0){se(),H.value!==null&&H.value.focus();return}we(v)}},getControl:v=>{const j=Ye(),$=v===!0||i.value!==!0||y!==!0;if(e.useInput===!0)j.push(Je(v,$));else if(x.editable.value===!0){const Pe=$===!0?ie.value:void 0;j.push(h("input",{ref:$===!0?H:void 0,key:"d_t",class:"q-select__focus-target",id:$===!0?x.targetUid.value:void 0,value:be.value,readonly:!0,"data-autofocus":v===!0||e.autofocus===!0||void 0,...Pe,onKeydown:Me,onKeyup:me,onKeypress:Se})),$===!0&&typeof e.autocomplete=="string"&&e.autocomplete.length!==0&&j.push(h("input",{class:"q-select__autocomplete-input",autocomplete:e.autocomplete,tabindex:-1,onKeyup:W}))}if(K.value!==void 0&&e.disable!==!0&&Qe.value.length!==0){const Pe=Qe.value.map(Te=>h("option",{value:Te,selected:!0}));j.push(h("select",{class:"hidden",name:K.value,multiple:e.multiple},Pe))}const pe=e.useInput===!0||$!==!0?void 0:x.splitAttrs.attributes.value;return h("div",{class:"q-field__native row items-center",...pe,...x.splitAttrs.listeners.value},j)},getInnerAppend:()=>e.loading!==!0&&s.value!==!0&&e.hideDropdownIcon!==!0?[h(QIcon,{class:"q-select__dropdown-icon"+(r.value===!0?" rotate-180":""),name:qe.value})]:null}),useField(x)}}),defaultSizes={xs:2,sm:4,md:6,lg:10,xl:14};function width(e,n,l){return{transform:n===!0?`translateX(${l.lang.rtl===!0?"-":""}100%) scale3d(${-e},1,1)`:`scale3d(${e},1,1)`}}const QLinearProgress=createComponent({name:"QLinearProgress",props:{...useDarkProps,...useSizeProps,value:{type:Number,default:0},buffer:Number,color:String,trackColor:String,reverse:Boolean,stripe:Boolean,indeterminate:Boolean,query:Boolean,rounded:Boolean,animationSpeed:{type:[String,Number],default:2100},instantFeedback:Boolean},setup(e,{slots:n}){const{proxy:l}=getCurrentInstance(),t=useDark(e,l.$q),o=useSize(e,defaultSizes),r=computed(()=>e.indeterminate===!0||e.query===!0),i=computed(()=>e.reverse!==e.query),a=computed(()=>({...o.value!==null?o.value:{},"--q-linear-progress-speed":`${e.animationSpeed}ms`})),u=computed(()=>"q-linear-progress"+(e.color!==void 0?` text-${e.color}`:"")+(e.reverse===!0||e.query===!0?" q-linear-progress--reverse":"")+(e.rounded===!0?" rounded-borders":"")),f=computed(()=>width(e.buffer!==void 0?e.buffer:1,i.value,l.$q)),s=computed(()=>`with${e.instantFeedback===!0?"out":""}-transition`),b=computed(()=>`q-linear-progress__track absolute-full q-linear-progress__track--${s.value} q-linear-progress__track--${t.value===!0?"dark":"light"}`+(e.trackColor!==void 0?` bg-${e.trackColor}`:"")),m=computed(()=>width(r.value===!0?1:e.value,i.value,l.$q)),p=computed(()=>`q-linear-progress__model absolute-full q-linear-progress__model--${s.value} q-linear-progress__model--${r.value===!0?"in":""}determinate`),y=computed(()=>({width:`${e.value*100}%`})),B=computed(()=>`q-linear-progress__stripe absolute-${e.reverse===!0?"right":"left"} q-linear-progress__stripe--${s.value}`);return()=>{const S=[h("div",{class:b.value,style:f.value}),h("div",{class:p.value,style:m.value})];return e.stripe===!0&&r.value===!1&&S.push(h("div",{class:B.value,style:y.value})),h("div",{class:u.value,style:a.value,role:"progressbar","aria-valuemin":0,"aria-valuemax":1,"aria-valuenow":e.indeterminate===!0?void 0:e.value},hMergeSlot(n.default,S))}}});let counter=0;const useFullscreenProps={fullscreen:Boolean,noRouteFullscreenExit:Boolean},useFullscreenEmits=["update:fullscreen","fullscreen"];function useFullscreen(){const e=getCurrentInstance(),{props:n,emit:l,proxy:t}=e;let o,r,i;const a=ref(!1);vmHasRouter(e)===!0&&watch(()=>t.$route.fullPath,()=>{n.noRouteFullscreenExit!==!0&&s()}),watch(()=>n.fullscreen,b=>{a.value!==b&&u()}),watch(a,b=>{l("update:fullscreen",b),l("fullscreen",b)});function u(){a.value===!0?s():f()}function f(){a.value!==!0&&(a.value=!0,i=t.$el.parentNode,i.replaceChild(r,t.$el),document.body.appendChild(t.$el),counter++,counter===1&&document.body.classList.add("q-body--fullscreen-mixin"),o={handler:s},History.add(o))}function s(){a.value===!0&&(o!==void 0&&(History.remove(o),o=void 0),i.replaceChild(t.$el,r),a.value=!1,counter=Math.max(0,counter-1),counter===0&&(document.body.classList.remove("q-body--fullscreen-mixin"),t.$el.scrollIntoView!==void 0&&setTimeout(()=>{t.$el.scrollIntoView()})))}return onBeforeMount(()=>{r=document.createElement("span")}),onMounted(()=>{n.fullscreen===!0&&f()}),onBeforeUnmount(s),Object.assign(t,{toggleFullscreen:u,setFullscreen:f,exitFullscreen:s}),{inFullscreen:a,toggleFullscreen:u}}function sortDate(e,n){return new Date(e)-new Date(n)}const useTableSortProps={sortMethod:Function,binaryStateSort:Boolean,columnSortOrder:{type:String,validator:e=>e==="ad"||e==="da",default:"ad"}};function useTableSort(e,n,l,t){const o=computed(()=>{const{sortBy:a}=n.value;return a&&l.value.find(u=>u.name===a)||null}),r=computed(()=>e.sortMethod!==void 0?e.sortMethod:(a,u,f)=>{const s=l.value.find(p=>p.name===u);if(s===void 0||s.field===void 0)return a;const b=f===!0?-1:1,m=typeof s.field=="function"?p=>s.field(p):p=>p[s.field];return a.sort((p,y)=>{let B=m(p),S=m(y);return s.rawSort!==void 0?s.rawSort(B,S,p,y)*b:B==null?-1*b:S==null?1*b:s.sort!==void 0?s.sort(B,S,p,y)*b:isNumber(B)===!0&&isNumber(S)===!0?(B-S)*b:isDate(B)===!0&&isDate(S)===!0?sortDate(B,S)*b:typeof B=="boolean"&&typeof S=="boolean"?(B-S)*b:([B,S]=[B,S].map(M=>(M+"").toLocaleString().toLowerCase()),B<S?-1*b:B===S?0:b)})});function i(a){let u=e.columnSortOrder;if(isObject(a)===!0)a.sortOrder&&(u=a.sortOrder),a=a.name;else{const b=l.value.find(m=>m.name===a);b!==void 0&&b.sortOrder&&(u=b.sortOrder)}let{sortBy:f,descending:s}=n.value;f!==a?(f=a,s=u==="da"):e.binaryStateSort===!0?s=!s:s===!0?u==="ad"?f=null:s=!1:u==="ad"?s=!0:f=null,t({sortBy:f,descending:s,page:1})}return{columnToSort:o,computedSortMethod:r,sort:i}}const useTableFilterProps={filter:[String,Object],filterMethod:Function};function useTableFilter(e,n){const l=computed(()=>e.filterMethod!==void 0?e.filterMethod:(t,o,r,i)=>{const a=o?o.toLowerCase():"";return t.filter(u=>r.some(f=>{const s=i(f,u)+"";return(s==="undefined"||s==="null"?"":s.toLowerCase()).indexOf(a)!==-1}))});return watch(()=>e.filter,()=>{nextTick(()=>{n({page:1},!0)})},{deep:!0}),{computedFilterMethod:l}}function samePagination(e,n){for(const l in n)if(n[l]!==e[l])return!1;return!0}function fixPagination(e){return e.page<1&&(e.page=1),e.rowsPerPage!==void 0&&e.rowsPerPage<1&&(e.rowsPerPage=0),e}const useTablePaginationProps={pagination:Object,rowsPerPageOptions:{type:Array,default:()=>[5,7,10,15,20,25,50,0]},"onUpdate:pagination":[Function,Array]};function useTablePaginationState(e,n){const{props:l,emit:t}=e,o=ref(Object.assign({sortBy:null,descending:!1,page:1,rowsPerPage:l.rowsPerPageOptions.length!==0?l.rowsPerPageOptions[0]:5},l.pagination)),r=computed(()=>{const s=l["onUpdate:pagination"]!==void 0?{...o.value,...l.pagination}:o.value;return fixPagination(s)}),i=computed(()=>r.value.rowsNumber!==void 0);function a(s){u({pagination:s,filter:l.filter})}function u(s={}){nextTick(()=>{t("request",{pagination:s.pagination||r.value,filter:s.filter||l.filter,getCellValue:n})})}function f(s,b){const m=fixPagination({...r.value,...s});if(samePagination(r.value,m)===!0){i.value===!0&&b===!0&&a(m);return}if(i.value===!0){a(m);return}l.pagination!==void 0&&l["onUpdate:pagination"]!==void 0?t("update:pagination",m):o.value=m}return{innerPagination:o,computedPagination:r,isServerSide:i,requestServerInteraction:u,setPagination:f}}function useTablePagination(e,n,l,t,o,r){const{props:i,emit:a,proxy:{$q:u}}=e,f=computed(()=>t.value===!0?l.value.rowsNumber||0:r.value),s=computed(()=>{const{page:d,rowsPerPage:I}=l.value;return(d-1)*I}),b=computed(()=>{const{page:d,rowsPerPage:I}=l.value;return d*I}),m=computed(()=>l.value.page===1),p=computed(()=>l.value.rowsPerPage===0?1:Math.max(1,Math.ceil(f.value/l.value.rowsPerPage))),y=computed(()=>b.value===0?!0:l.value.page>=p.value),B=computed(()=>(i.rowsPerPageOptions.includes(n.value.rowsPerPage)?i.rowsPerPageOptions:[n.value.rowsPerPage].concat(i.rowsPerPageOptions)).map(I=>({label:I===0?u.lang.table.allRows:""+I,value:I})));watch(p,(d,I)=>{if(d===I)return;const H=l.value.page;d&&!H?o({page:1}):d<H&&o({page:d})});function S(){o({page:1})}function M(){const{page:d}=l.value;d>1&&o({page:d-1})}function D(){const{page:d,rowsPerPage:I}=l.value;b.value>0&&d*I<f.value&&o({page:d+1})}function C(){o({page:p.value})}return i["onUpdate:pagination"]!==void 0&&a("update:pagination",{...l.value}),{firstRowIndex:s,lastRowIndex:b,isFirstPage:m,isLastPage:y,pagesNumber:p,computedRowsPerPageOptions:B,computedRowsNumber:f,firstPage:S,prevPage:M,nextPage:D,lastPage:C}}const useTableRowSelectionProps={selection:{type:String,default:"none",validator:e=>["single","multiple","none"].includes(e)},selected:{type:Array,default:()=>[]}},useTableRowSelectionEmits=["update:selected","selection"];function useTableRowSelection(e,n,l,t){const o=computed(()=>{const y={};return e.selected.map(t.value).forEach(B=>{y[B]=!0}),y}),r=computed(()=>e.selection!=="none"),i=computed(()=>e.selection==="single"),a=computed(()=>e.selection==="multiple"),u=computed(()=>l.value.length!==0&&l.value.every(y=>o.value[t.value(y)]===!0)),f=computed(()=>u.value!==!0&&l.value.some(y=>o.value[t.value(y)]===!0)),s=computed(()=>e.selected.length);function b(y){return o.value[y]===!0}function m(){n("update:selected",[])}function p(y,B,S,M){n("selection",{rows:B,added:S,keys:y,evt:M});const D=i.value===!0?S===!0?B:[]:S===!0?e.selected.concat(B):e.selected.filter(C=>y.includes(t.value(C))===!1);n("update:selected",D)}return{hasSelectionMode:r,singleSelection:i,multipleSelection:a,allRowsSelected:u,someRowsSelected:f,rowsSelectedNumber:s,isRowSelected:b,clearSelection:m,updateSelection:p}}function getVal(e){return Array.isArray(e)?e.slice():[]}const useTableRowExpandProps={expanded:Array},useTableRowExpandEmits=["update:expanded"];function useTableRowExpand(e,n){const l=ref(getVal(e.expanded));watch(()=>e.expanded,i=>{l.value=getVal(i)});function t(i){return l.value.includes(i)}function o(i){e.expanded!==void 0?n("update:expanded",i):l.value=i}function r(i,a){const u=l.value.slice(),f=u.indexOf(i);a===!0?f===-1&&(u.push(i),o(u)):f!==-1&&(u.splice(f,1),o(u))}return{isRowExpanded:t,setExpanded:o,updateExpanded:r}}const useTableColumnSelectionProps={visibleColumns:Array};function useTableColumnSelection(e,n,l){const t=computed(()=>{if(e.columns!==void 0)return e.columns;const a=e.rows[0];return a!==void 0?Object.keys(a).map(u=>({name:u,label:u.toUpperCase(),field:u,align:isNumber(a[u])?"right":"left",sortable:!0})):[]}),o=computed(()=>{const{sortBy:a,descending:u}=n.value;return(e.visibleColumns!==void 0?t.value.filter(s=>s.required===!0||e.visibleColumns.includes(s.name)===!0):t.value).map(s=>{const b=s.align||"right",m=`text-${b}`;return{...s,align:b,__iconClass:`q-table__sort-icon q-table__sort-icon--${b}`,__thClass:m+(s.headerClasses!==void 0?" "+s.headerClasses:"")+(s.sortable===!0?" sortable":"")+(s.name===a?` sorted ${u===!0?"sort-desc":""}`:""),__tdStyle:s.style!==void 0?typeof s.style!="function"?()=>s.style:s.style:()=>null,__tdClass:s.classes!==void 0?typeof s.classes!="function"?()=>m+" "+s.classes:p=>m+" "+s.classes(p):()=>m}})}),r=computed(()=>{const a={};return o.value.forEach(u=>{a[u.name]=u}),a}),i=computed(()=>e.tableColspan!==void 0?e.tableColspan:o.value.length+(l.value===!0?1:0));return{colList:t,computedCols:o,computedColsMap:r,computedColspan:i}}const bottomClass="q-table__bottom row items-center",virtScrollPassthroughProps={};commonVirtScrollPropsList.forEach(e=>{virtScrollPassthroughProps[e]={}});const QTable=createComponent({name:"QTable",props:{rows:{type:Array,required:!0},rowKey:{type:[String,Function],default:"id"},columns:Array,loading:Boolean,iconFirstPage:String,iconPrevPage:String,iconNextPage:String,iconLastPage:String,title:String,hideHeader:Boolean,grid:Boolean,gridHeader:Boolean,dense:Boolean,flat:Boolean,bordered:Boolean,square:Boolean,separator:{type:String,default:"horizontal",validator:e=>["horizontal","vertical","cell","none"].includes(e)},wrapCells:Boolean,virtualScroll:Boolean,virtualScrollTarget:{},...virtScrollPassthroughProps,noDataLabel:String,noResultsLabel:String,loadingLabel:String,selectedRowsLabel:Function,rowsPerPageLabel:String,paginationLabel:Function,color:{type:String,default:"grey-8"},titleClass:[String,Array,Object],tableStyle:[String,Array,Object],tableClass:[String,Array,Object],tableHeaderStyle:[String,Array,Object],tableHeaderClass:[String,Array,Object],cardContainerClass:[String,Array,Object],cardContainerStyle:[String,Array,Object],cardStyle:[String,Array,Object],cardClass:[String,Array,Object],hideBottom:Boolean,hideSelectedBanner:Boolean,hideNoData:Boolean,hidePagination:Boolean,onRowClick:Function,onRowDblclick:Function,onRowContextmenu:Function,...useDarkProps,...useFullscreenProps,...useTableColumnSelectionProps,...useTableFilterProps,...useTablePaginationProps,...useTableRowExpandProps,...useTableRowSelectionProps,...useTableSortProps},emits:["request","virtualScroll",...useFullscreenEmits,...useTableRowExpandEmits,...useTableRowSelectionEmits],setup(e,{slots:n,emit:l}){const t=getCurrentInstance(),{proxy:{$q:o}}=t,r=useDark(e,o),{inFullscreen:i,toggleFullscreen:a}=useFullscreen(),u=computed(()=>typeof e.rowKey=="function"?e.rowKey:V=>V[e.rowKey]),f=ref(null),s=ref(null),b=computed(()=>e.grid!==!0&&e.virtualScroll===!0),m=computed(()=>" q-table__card"+(r.value===!0?" q-table__card--dark q-dark":"")+(e.square===!0?" q-table--square":"")+(e.flat===!0?" q-table--flat":"")+(e.bordered===!0?" q-table--bordered":"")),p=computed(()=>`q-table__container q-table--${e.separator}-separator column no-wrap`+(e.grid===!0?" q-table--grid":m.value)+(r.value===!0?" q-table--dark":"")+(e.dense===!0?" q-table--dense":"")+(e.wrapCells===!1?" q-table--no-wrap":"")+(i.value===!0?" fullscreen scroll":"")),y=computed(()=>p.value+(e.loading===!0?" q-table--loading":""));watch(()=>e.tableStyle+e.tableClass+e.tableHeaderStyle+e.tableHeaderClass+p.value,()=>{b.value===!0&&s.value!==null&&s.value.reset()});const{innerPagination:B,computedPagination:S,isServerSide:M,requestServerInteraction:D,setPagination:C}=useTablePaginationState(t,q),{computedFilterMethod:d}=useTableFilter(e,C),{isRowExpanded:I,setExpanded:H,updateExpanded:E}=useTableRowExpand(e,l),F=computed(()=>{let V=e.rows;if(M.value===!0||V.length===0)return V;const{sortBy:U,descending:ae}=S.value;return e.filter&&(V=d.value(V,e.filter,x.value,q)),ve.value!==null&&(V=ke.value(e.rows===V?V.slice():V,U,ae)),V}),L=computed(()=>F.value.length),K=computed(()=>{let V=F.value;if(M.value===!0)return V;const{rowsPerPage:U}=S.value;return U!==0&&(P.value===0&&e.rows!==V?V.length>G.value&&(V=V.slice(0,G.value)):V=V.slice(P.value,G.value)),V}),{hasSelectionMode:le,singleSelection:de,multipleSelection:ce,allRowsSelected:J,someRowsSelected:oe,rowsSelectedNumber:ee,isRowSelected:he,clearSelection:xe,updateSelection:Ve}=useTableRowSelection(e,l,K,u),{colList:Fe,computedCols:x,computedColsMap:N,computedColspan:te}=useTableColumnSelection(e,S,le),{columnToSort:ve,computedSortMethod:ke,sort:w}=useTableSort(e,S,Fe,C),{firstRowIndex:P,lastRowIndex:G,isFirstPage:Q,isLastPage:be,pagesNumber:fe,computedRowsPerPageOptions:T,computedRowsNumber:Y,firstPage:ie,prevPage:re,nextPage:ye,lastPage:Z}=useTablePagination(t,B,S,M,C,L),qe=computed(()=>K.value.length===0),_e=computed(()=>{const V={};return commonVirtScrollPropsList.forEach(U=>{V[U]=e[U]}),V.virtualScrollItemSize===void 0&&(V.virtualScrollItemSize=e.dense===!0?28:48),V});function De(){b.value===!0&&s.value.reset()}function Ie(){if(e.grid===!0)return Je();const V=e.hideHeader!==!0?ne:null;if(b.value===!0){const ae=n["top-row"],ue=n["bottom-row"],ge={default:Ee=>je(Ee.item,n.body,Ee.index)};if(ae!==void 0){const Ee=h("tbody",ae({cols:x.value}));ge.before=V===null?()=>Ee:()=>[V()].concat(Ee)}else V!==null&&(ge.before=V);return ue!==void 0&&(ge.after=()=>h("tbody",ue({cols:x.value}))),h(QVirtualScroll,{ref:s,class:e.tableClass,style:e.tableStyle,..._e.value,scrollTarget:e.virtualScrollTarget,items:K.value,type:"__qtable",tableColspan:te.value,onVirtualScroll:Re},ge)}const U=[He()];return V!==null&&U.unshift(V()),getTableMiddle({class:["q-table__middle scroll",e.tableClass],style:e.tableStyle},U)}function Be(V,U){if(s.value!==null){s.value.scrollTo(V,U);return}V=parseInt(V,10);const ae=f.value.querySelector(`tbody tr:nth-of-type(${V+1})`);if(ae!==null){const ue=f.value.querySelector(".q-table__middle.scroll"),ge=ae.offsetTop-e.virtualScrollStickySizeStart,Ee=ge<ue.scrollTop?"decrease":"increase";ue.scrollTop=ge,l("virtualScroll",{index:V,from:0,to:B.value.rowsPerPage-1,direction:Ee})}}function Re(V){l("virtualScroll",V)}function Qe(){return[h(QLinearProgress,{class:"q-table__linear-progress",color:e.color,dark:r.value,indeterminate:!0,trackColor:"transparent"})]}function je(V,U,ae){const ue=u.value(V),ge=he(ue);if(U!==void 0)return U(Ne({key:ue,row:V,pageIndex:ae,__trClass:ge?"selected":""}));const Ee=n["body-cell"],c=x.value.map(_=>{const O=n[`body-cell-${_.name}`],z=O!==void 0?O:Ee;return z!==void 0?z($e({key:ue,row:V,pageIndex:ae,col:_})):h("td",{class:_.__tdClass(V),style:_.__tdStyle(V)},q(_,V))});if(le.value===!0){const _=n["body-selection"],O=_!==void 0?_(Ke({key:ue,row:V,pageIndex:ae})):[h(QCheckbox,{modelValue:ge,color:e.color,dark:r.value,dense:e.dense,"onUpdate:modelValue":(z,Ce)=>{Ve([ue],[V],z,Ce)}})];c.unshift(h("td",{class:"q-table--col-auto-width"},O))}const g={key:ue,class:{selected:ge}};return e.onRowClick!==void 0&&(g.class["cursor-pointer"]=!0,g.onClick=_=>{l("rowClick",_,V,ae)}),e.onRowDblclick!==void 0&&(g.class["cursor-pointer"]=!0,g.onDblclick=_=>{l("rowDblclick",_,V,ae)}),e.onRowContextmenu!==void 0&&(g.class["cursor-pointer"]=!0,g.onContextmenu=_=>{l("rowContextmenu",_,V,ae)}),h("tr",g,c)}function He(){const V=n.body,U=n["top-row"],ae=n["bottom-row"];let ue=K.value.map((ge,Ee)=>je(ge,V,Ee));return U!==void 0&&(ue=U({cols:x.value}).concat(ue)),ae!==void 0&&(ue=ue.concat(ae({cols:x.value}))),h("tbody",ue)}function Ne(V){return Le(V),V.cols=V.cols.map(U=>injectProp({...U},"value",()=>q(U,V.row))),V}function $e(V){return Le(V),injectProp(V,"value",()=>q(V.col,V.row)),V}function Ke(V){return Le(V),V}function Le(V){Object.assign(V,{cols:x.value,colsMap:N.value,sort:w,rowIndex:P.value+V.pageIndex,color:e.color,dark:r.value,dense:e.dense}),le.value===!0&&injectProp(V,"selected",()=>he(V.key),(U,ae)=>{Ve([V.key],[V.row],U,ae)}),injectProp(V,"expand",()=>I(V.key),U=>{E(V.key,U)})}function q(V,U){const ae=typeof V.field=="function"?V.field(U):U[V.field];return V.format!==void 0?V.format(ae,U):ae}const A=computed(()=>({pagination:S.value,pagesNumber:fe.value,isFirstPage:Q.value,isLastPage:be.value,firstPage:ie,prevPage:re,nextPage:ye,lastPage:Z,inFullscreen:i.value,toggleFullscreen:a}));function k(){const V=n.top,U=n["top-left"],ae=n["top-right"],ue=n["top-selection"],ge=le.value===!0&&ue!==void 0&&ee.value>0,Ee="q-table__top relative-position row items-center";if(V!==void 0)return h("div",{class:Ee},[V(A.value)]);let c;if(ge===!0?c=ue(A.value).slice():(c=[],U!==void 0?c.push(h("div",{class:"q-table__control"},[U(A.value)])):e.title&&c.push(h("div",{class:"q-table__control"},[h("div",{class:["q-table__title",e.titleClass]},e.title)]))),ae!==void 0&&(c.push(h("div",{class:"q-table__separator col"})),c.push(h("div",{class:"q-table__control"},[ae(A.value)]))),c.length!==0)return h("div",{class:Ee},c)}const R=computed(()=>oe.value===!0?null:J.value);function ne(){const V=me();return e.loading===!0&&n.loading===void 0&&V.push(h("tr",{class:"q-table__progress"},[h("th",{class:"relative-position",colspan:te.value},Qe())])),h("thead",V)}function me(){const V=n.header,U=n["header-cell"];if(V!==void 0)return V(W({header:!0})).slice();const ae=x.value.map(ue=>{const ge=n[`header-cell-${ue.name}`],Ee=ge!==void 0?ge:U,c=W({col:ue});return Ee!==void 0?Ee(c):h(QTh,{key:ue.name,props:c},()=>ue.label)});if(de.value===!0&&e.grid!==!0)ae.unshift(h("th",{class:"q-table--col-auto-width"}," "));else if(ce.value===!0){const ue=n["header-selection"],ge=ue!==void 0?ue(W({})):[h(QCheckbox,{color:e.color,modelValue:R.value,dark:r.value,dense:e.dense,"onUpdate:modelValue":Se})];ae.unshift(h("th",{class:"q-table--col-auto-width"},ge))}return[h("tr",{class:e.tableHeaderClass,style:e.tableHeaderStyle},ae)]}function W(V){return Object.assign(V,{cols:x.value,sort:w,colsMap:N.value,color:e.color,dark:r.value,dense:e.dense}),ce.value===!0&&injectProp(V,"selected",()=>R.value,Se),V}function Se(V){oe.value===!0&&(V=!1),Ve(K.value.map(u.value),K.value,V)}const Me=computed(()=>{const V=[e.iconFirstPage||o.iconSet.table.firstPage,e.iconPrevPage||o.iconSet.table.prevPage,e.iconNextPage||o.iconSet.table.nextPage,e.iconLastPage||o.iconSet.table.lastPage];return o.lang.rtl===!0?V.reverse():V});function ze(){if(e.hideBottom===!0)return;if(qe.value===!0){if(e.hideNoData===!0)return;const ae=e.loading===!0?e.loadingLabel||o.lang.table.loading:e.filter?e.noResultsLabel||o.lang.table.noResults:e.noDataLabel||o.lang.table.noData,ue=n["no-data"],ge=ue!==void 0?[ue({message:ae,icon:o.iconSet.table.warning,filter:e.filter})]:[h(QIcon,{class:"q-table__bottom-nodata-icon",name:o.iconSet.table.warning}),ae];return h("div",{class:bottomClass+" q-table__bottom--nodata"},ge)}const V=n.bottom;if(V!==void 0)return h("div",{class:bottomClass},[V(A.value)]);const U=e.hideSelectedBanner!==!0&&le.value===!0&&ee.value>0?[h("div",{class:"q-table__control"},[h("div",[(e.selectedRowsLabel||o.lang.table.selectedRecords)(ee.value)])])]:[];if(e.hidePagination!==!0)return h("div",{class:bottomClass+" justify-end"},Ye(U));if(U.length!==0)return h("div",{class:bottomClass},U)}function We(V){C({page:1,rowsPerPage:V.value})}function Ye(V){let U;const{rowsPerPage:ae}=S.value,ue=e.paginationLabel||o.lang.table.pagination,ge=n.pagination,Ee=e.rowsPerPageOptions.length>1;if(V.push(h("div",{class:"q-table__separator col"})),Ee===!0&&V.push(h("div",{class:"q-table__control"},[h("span",{class:"q-table__bottom-item"},[e.rowsPerPageLabel||o.lang.table.recordsPerPage]),h(QSelect,{class:"q-table__select inline q-table__bottom-item",color:e.color,modelValue:ae,options:T.value,displayValue:ae===0?o.lang.table.allRows:ae,dark:r.value,borderless:!0,dense:!0,optionsDense:!0,optionsCover:!0,"onUpdate:modelValue":We})])),ge!==void 0)U=ge(A.value);else if(U=[h("span",ae!==0?{class:"q-table__bottom-item"}:{},[ae?ue(P.value+1,Math.min(G.value,Y.value),Y.value):ue(1,L.value,Y.value)])],ae!==0&&fe.value>1){const c={color:e.color,round:!0,dense:!0,flat:!0};e.dense===!0&&(c.size="sm"),fe.value>2&&U.push(h(QBtn,{key:"pgFirst",...c,icon:Me.value[0],disable:Q.value,onClick:ie})),U.push(h(QBtn,{key:"pgPrev",...c,icon:Me.value[1],disable:Q.value,onClick:re}),h(QBtn,{key:"pgNext",...c,icon:Me.value[2],disable:be.value,onClick:ye})),fe.value>2&&U.push(h(QBtn,{key:"pgLast",...c,icon:Me.value[3],disable:be.value,onClick:Z}))}return V.push(h("div",{class:"q-table__control"},U)),V}function Ge(){const V=e.gridHeader===!0?[h("table",{class:"q-table"},[ne()])]:e.loading===!0&&n.loading===void 0?Qe():void 0;return h("div",{class:"q-table__middle"},V)}function Je(){const V=n.item!==void 0?n.item:U=>{const ae=U.cols.map(ge=>h("div",{class:"q-table__grid-item-row"},[h("div",{class:"q-table__grid-item-title"},[ge.label]),h("div",{class:"q-table__grid-item-value"},[ge.value])]));if(le.value===!0){const ge=n["body-selection"],Ee=ge!==void 0?ge(U):[h(QCheckbox,{modelValue:U.selected,color:e.color,dark:r.value,dense:e.dense,"onUpdate:modelValue":(c,g)=>{Ve([U.key],[U.row],c,g)}})];ae.unshift(h("div",{class:"q-table__grid-item-row"},Ee),h(QSeparator,{dark:r.value}))}const ue={class:["q-table__grid-item-card"+m.value,e.cardClass],style:e.cardStyle};return(e.onRowClick!==void 0||e.onRowDblclick!==void 0)&&(ue.class[0]+=" cursor-pointer",e.onRowClick!==void 0&&(ue.onClick=ge=>{l("RowClick",ge,U.row,U.pageIndex)}),e.onRowDblclick!==void 0&&(ue.onDblclick=ge=>{l("RowDblclick",ge,U.row,U.pageIndex)})),h("div",{class:"q-table__grid-item col-xs-12 col-sm-6 col-md-4 col-lg-3"+(U.selected===!0?" q-table__grid-item--selected":"")},[h("div",ue,ae)])};return h("div",{class:["q-table__grid-content row",e.cardContainerClass],style:e.cardContainerStyle},K.value.map((U,ae)=>V(Ne({key:u.value(U),row:U,pageIndex:ae}))))}return Object.assign(t.proxy,{requestServerInteraction:D,setPagination:C,firstPage:ie,prevPage:re,nextPage:ye,lastPage:Z,isRowSelected:he,clearSelection:xe,isRowExpanded:I,setExpanded:H,sort:w,resetVirtualScroll:De,scrollTo:Be,getCellValue:q}),injectMultipleProps(t.proxy,{filteredSortedRows:()=>F.value,computedRows:()=>K.value,computedRowsNumber:()=>Y.value}),()=>{const V=[k()],U={ref:f,class:y.value};return e.grid===!0?V.push(Ge()):Object.assign(U,{class:[U.class,e.cardClass],style:e.cardStyle}),V.push(Ie(),ze()),e.loading===!0&&n.loading!==void 0&&V.push(n.loading()),h("div",U,V)}}}),createSvg=()=>h("svg",{key:"svg",class:"q-radio__bg absolute non-selectable",viewBox:"0 0 24 24"},[h("path",{d:"M12,22a10,10 0 0 1 -10,-10a10,10 0 0 1 10,-10a10,10 0 0 1 10,10a10,10 0 0 1 -10,10m0,-22a12,12 0 0 0 -12,12a12,12 0 0 0 12,12a12,12 0 0 0 12,-12a12,12 0 0 0 -12,-12"}),h("path",{class:"q-radio__check",d:"M12,6a6,6 0 0 0 -6,6a6,6 0 0 0 6,6a6,6 0 0 0 6,-6a6,6 0 0 0 -6,-6"})]),QRadio=createComponent({name:"QRadio",props:{...useDarkProps,...useSizeProps,...useFormProps,modelValue:{required:!0},val:{required:!0},label:String,leftLabel:Boolean,checkedIcon:String,uncheckedIcon:String,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},emits:["update:modelValue"],setup(e,{slots:n,emit:l}){const{proxy:t}=getCurrentInstance(),o=useDark(e,t.$q),r=useSize(e,optionSizes),i=ref(null),{refocusTargetEl:a,refocusTarget:u}=useRefocusTarget(e,i),f=computed(()=>toRaw(e.modelValue)===toRaw(e.val)),s=computed(()=>"q-radio cursor-pointer no-outline row inline no-wrap items-center"+(e.disable===!0?" disabled":"")+(o.value===!0?" q-radio--dark":"")+(e.dense===!0?" q-radio--dense":"")+(e.leftLabel===!0?" reverse":"")),b=computed(()=>{const d=e.color!==void 0&&(e.keepColor===!0||f.value===!0)?` text-${e.color}`:"";return`q-radio__inner relative-position q-radio__inner--${f.value===!0?"truthy":"falsy"}${d}`}),m=computed(()=>(f.value===!0?e.checkedIcon:e.uncheckedIcon)||null),p=computed(()=>e.disable===!0?-1:e.tabindex||0),y=computed(()=>{const d={type:"radio"};return e.name!==void 0&&Object.assign(d,{".checked":f.value===!0,"^checked":f.value===!0?"checked":void 0,name:e.name,value:e.val}),d}),B=useFormInject(y);function S(d){d!==void 0&&(stopAndPrevent(d),u(d)),e.disable!==!0&&f.value!==!0&&l("update:modelValue",e.val,d)}function M(d){(d.keyCode===13||d.keyCode===32)&&stopAndPrevent(d)}function D(d){(d.keyCode===13||d.keyCode===32)&&S(d)}Object.assign(t,{set:S});const C=createSvg();return()=>{const d=m.value!==null?[h("div",{key:"icon",class:"q-radio__icon-container absolute-full flex flex-center no-wrap"},[h(QIcon,{class:"q-radio__icon",name:m.value})])]:[C];e.disable!==!0&&B(d,"unshift"," q-radio__native q-ma-none q-pa-none");const I=[h("div",{class:b.value,style:r.value,"aria-hidden":"true"},d)];a.value!==null&&I.push(a.value);const H=e.label!==void 0?hMergeSlot(n.default,[e.label]):hSlot(n.default);return H!==void 0&&I.push(h("div",{class:"q-radio__label q-anchor--skip"},H)),h("div",{ref:i,class:s.value,tabindex:p.value,role:"radio","aria-label":e.label,"aria-checked":f.value===!0?"true":"false","aria-disabled":e.disable===!0?"true":void 0,onClick:S,onKeydown:M,onKeyup:D},I)}}}),components={radio:QRadio,checkbox:QCheckbox,toggle:QToggle},typeValues=Object.keys(components);function getPropValueFn(e,n){if(typeof e=="function")return e;const l=e!==void 0?e:n;return t=>t[l]}const QOptionGroup=createComponent({name:"QOptionGroup",props:{...useDarkProps,modelValue:{required:!0},options:{type:Array,validator:e=>e.every(isObject),default:()=>[]},optionValue:[Function,String],optionLabel:[Function,String],optionDisable:[Function,String],name:String,type:{type:String,default:"radio",validator:e=>typeValues.includes(e)},color:String,keepColor:Boolean,dense:Boolean,size:String,leftLabel:Boolean,inline:Boolean,disable:Boolean},emits:["update:modelValue"],setup(e,{emit:n,slots:l}){const{proxy:{$q:t}}=getCurrentInstance(),o=Array.isArray(e.modelValue);e.type==="radio"?o===!0&&console.error("q-option-group: model should not be array"):o===!1&&console.error("q-option-group: model should be array in your case");const r=useDark(e,t),i=computed(()=>components[e.type]),a=computed(()=>getPropValueFn(e.optionValue,"value")),u=computed(()=>getPropValueFn(e.optionLabel,"label")),f=computed(()=>getPropValueFn(e.optionDisable,"disable")),s=computed(()=>e.options.map(y=>({val:a.value(y),name:y.name===void 0?e.name:y.name,disable:e.disable||f.value(y),leftLabel:y.leftLabel===void 0?e.leftLabel:y.leftLabel,color:y.color===void 0?e.color:y.color,checkedIcon:y.checkedIcon,uncheckedIcon:y.uncheckedIcon,dark:y.dark===void 0?r.value:y.dark,size:y.size===void 0?e.size:y.size,dense:e.dense,keepColor:y.keepColor===void 0?e.keepColor:y.keepColor}))),b=computed(()=>"q-option-group q-gutter-x-sm"+(e.inline===!0?" q-option-group--inline":"")),m=computed(()=>{const y={role:"group"};return e.type==="radio"&&(y.role="radiogroup",e.disable===!0&&(y["aria-disabled"]="true")),y});function p(y){n("update:modelValue",y)}return()=>h("div",{class:b.value,...m.value},e.options.map((y,B)=>{const S=l["label-"+B]!==void 0?()=>l["label-"+B](y):l.label!==void 0?()=>l.label(y):void 0;return h("div",[h(i.value,{label:S===void 0?u.value(y):null,modelValue:e.modelValue,"onUpdate:modelValue":p,...s.value[B]},S)])}))}});function useRenderCache(){let e=Object.create(null);return{getCache:(n,l)=>e[n]===void 0?e[n]=typeof l=="function"?l():l:e[n],setCache(n,l){e[n]=l},hasCache(n){return Object.hasOwnProperty.call(e,n)},clearCache(n){n!==void 0?delete e[n]:e=Object.create(null)}}}const calendars=["gregorian","persian"],useDatetimeProps={mask:{type:String},locale:Object,calendar:{type:String,validator:e=>calendars.includes(e),default:"gregorian"},landscape:Boolean,color:String,textColor:String,square:Boolean,flat:Boolean,bordered:Boolean,readonly:Boolean,disable:Boolean},useDatetimeEmits=["update:modelValue"];function getDayHash(e){return e.year+"/"+pad(e.month)+"/"+pad(e.day)}function useDatetime(e,n){const l=computed(()=>e.disable!==!0&&e.readonly!==!0),t=computed(()=>l.value===!0?0:-1),o=computed(()=>{const a=[];return e.color!==void 0&&a.push(`bg-${e.color}`),e.textColor!==void 0&&a.push(`text-${e.textColor}`),a.join(" ")});function r(){return e.locale!==void 0?{...n.lang.date,...e.locale}:n.lang.date}function i(a){const u=new Date,f=a===!0?null:0;if(e.calendar==="persian"){const s=toJalaali(u);return{year:s.jy,month:s.jm,day:s.jd}}return{year:u.getFullYear(),month:u.getMonth()+1,day:u.getDate(),hour:f,minute:f,second:f,millisecond:f}}return{editable:l,tabindex:t,headerClass:o,getLocale:r,getCurrentDate:i}}const yearsInterval=20,views=["Calendar","Years","Months"],viewIsValid=e=>views.includes(e),yearMonthValidator=e=>/^-?[\d]+\/[0-1]\d$/.test(e),lineStr=" — ";function getMonthHash(e){return e.year+"/"+pad(e.month)}const QDate=createComponent({name:"QDate",props:{...useDatetimeProps,...useFormProps,...useDarkProps,modelValue:{required:!0,validator:e=>typeof e=="string"||Array.isArray(e)===!0||Object(e)===e||e===null},multiple:Boolean,range:Boolean,title:String,subtitle:String,mask:{...useDatetimeProps.mask,default:"YYYY/MM/DD"},defaultYearMonth:{type:String,validator:yearMonthValidator},yearsInMonthView:Boolean,events:[Array,Function],eventColor:[String,Function],emitImmediately:Boolean,options:[Array,Function],navigationMinYearMonth:{type:String,validator:yearMonthValidator},navigationMaxYearMonth:{type:String,validator:yearMonthValidator},noUnset:Boolean,firstDayOfWeek:[String,Number],todayBtn:Boolean,minimal:Boolean,defaultView:{type:String,default:"Calendar",validator:viewIsValid}},emits:[...useDatetimeEmits,"rangeStart","rangeEnd","navigation"],setup(e,{slots:n,emit:l}){const{proxy:t}=getCurrentInstance(),{$q:o}=t,r=useDark(e,o),{getCache:i}=useRenderCache(),{tabindex:a,headerClass:u,getLocale:f,getCurrentDate:s}=useDatetime(e,o);let b;const m=useFormAttrs(e),p=useFormInject(m),y=ref(null),B=ref($e()),S=ref(f()),M=computed(()=>$e()),D=computed(()=>f()),C=computed(()=>s()),d=ref(Le(B.value,S.value)),I=ref(e.defaultView),H=computed(()=>o.lang.rtl===!0?"right":"left"),E=ref(H.value),F=ref(H.value),L=d.value.year,K=ref(L-L%yearsInterval-(L<0?yearsInterval:0)),le=ref(null),de=computed(()=>{const c=e.landscape===!0?"landscape":"portrait";return`q-date q-date--${c} q-date--${c}-${e.minimal===!0?"minimal":"standard"}`+(r.value===!0?" q-date--dark q-dark":"")+(e.bordered===!0?" q-date--bordered":"")+(e.square===!0?" q-date--square no-border-radius":"")+(e.flat===!0?" q-date--flat no-shadow":"")+(e.disable===!0?" disabled":e.readonly===!0?" q-date--readonly":"")}),ce=computed(()=>e.color||"primary"),J=computed(()=>e.textColor||"white"),oe=computed(()=>e.emitImmediately===!0&&e.multiple!==!0&&e.range!==!0),ee=computed(()=>Array.isArray(e.modelValue)===!0?e.modelValue:e.modelValue!==null&&e.modelValue!==void 0?[e.modelValue]:[]),he=computed(()=>ee.value.filter(c=>typeof c=="string").map(c=>Ke(c,B.value,S.value)).filter(c=>c.dateHash!==null&&c.day!==null&&c.month!==null&&c.year!==null)),xe=computed(()=>{const c=g=>Ke(g,B.value,S.value);return ee.value.filter(g=>isObject(g)===!0&&g.from!==void 0&&g.to!==void 0).map(g=>({from:c(g.from),to:c(g.to)})).filter(g=>g.from.dateHash!==null&&g.to.dateHash!==null&&g.from.dateHash<g.to.dateHash)}),Ve=computed(()=>e.calendar!=="persian"?c=>new Date(c.year,c.month-1,c.day):c=>{const g=toGregorian(c.year,c.month,c.day);return new Date(g.gy,g.gm-1,g.gd)}),Fe=computed(()=>e.calendar==="persian"?getDayHash:(c,g,_)=>formatDate(new Date(c.year,c.month-1,c.day,c.hour,c.minute,c.second,c.millisecond),g===void 0?B.value:g,_===void 0?S.value:_,c.year,c.timezoneOffset)),x=computed(()=>he.value.length+xe.value.reduce((c,g)=>c+1+getDateDiff(Ve.value(g.to),Ve.value(g.from)),0)),N=computed(()=>{if(e.title!==void 0&&e.title!==null&&e.title.length!==0)return e.title;if(le.value!==null){const _=le.value.init,O=Ve.value(_);return S.value.daysShort[O.getDay()]+", "+S.value.monthsShort[_.month-1]+" "+_.day+lineStr+"?"}if(x.value===0)return lineStr;if(x.value>1)return`${x.value} ${S.value.pluralDay}`;const c=he.value[0],g=Ve.value(c);return isNaN(g.valueOf())===!0?lineStr:S.value.headerTitle!==void 0?S.value.headerTitle(g,c):S.value.daysShort[g.getDay()]+", "+S.value.monthsShort[c.month-1]+" "+c.day}),te=computed(()=>he.value.concat(xe.value.map(g=>g.from)).sort((g,_)=>g.year-_.year||g.month-_.month)[0]),ve=computed(()=>he.value.concat(xe.value.map(g=>g.to)).sort((g,_)=>_.year-g.year||_.month-g.month)[0]),ke=computed(()=>{if(e.subtitle!==void 0&&e.subtitle!==null&&e.subtitle.length!==0)return e.subtitle;if(x.value===0)return lineStr;if(x.value>1){const c=te.value,g=ve.value,_=S.value.monthsShort;return _[c.month-1]+(c.year!==g.year?" "+c.year+lineStr+_[g.month-1]+" ":c.month!==g.month?lineStr+_[g.month-1]:"")+" "+g.year}return he.value[0].year}),w=computed(()=>{const c=[o.iconSet.datetime.arrowLeft,o.iconSet.datetime.arrowRight];return o.lang.rtl===!0?c.reverse():c}),P=computed(()=>e.firstDayOfWeek!==void 0?Number(e.firstDayOfWeek):S.value.firstDayOfWeek),G=computed(()=>{const c=S.value.daysShort,g=P.value;return g>0?c.slice(g,7).concat(c.slice(0,g)):c}),Q=computed(()=>{const c=d.value;return e.calendar!=="persian"?new Date(c.year,c.month,0).getDate():jalaaliMonthLength(c.year,c.month)}),be=computed(()=>typeof e.eventColor=="function"?e.eventColor:()=>e.eventColor),fe=computed(()=>{if(e.navigationMinYearMonth===void 0)return null;const c=e.navigationMinYearMonth.split("/");return{year:parseInt(c[0],10),month:parseInt(c[1],10)}}),T=computed(()=>{if(e.navigationMaxYearMonth===void 0)return null;const c=e.navigationMaxYearMonth.split("/");return{year:parseInt(c[0],10),month:parseInt(c[1],10)}}),Y=computed(()=>{const c={month:{prev:!0,next:!0},year:{prev:!0,next:!0}};return fe.value!==null&&fe.value.year>=d.value.year&&(c.year.prev=!1,fe.value.year===d.value.year&&fe.value.month>=d.value.month&&(c.month.prev=!1)),T.value!==null&&T.value.year<=d.value.year&&(c.year.next=!1,T.value.year===d.value.year&&T.value.month<=d.value.month&&(c.month.next=!1)),c}),ie=computed(()=>{const c={};return he.value.forEach(g=>{const _=getMonthHash(g);c[_]===void 0&&(c[_]=[]),c[_].push(g.day)}),c}),re=computed(()=>{const c={};return xe.value.forEach(g=>{const _=getMonthHash(g.from),O=getMonthHash(g.to);if(c[_]===void 0&&(c[_]=[]),c[_].push({from:g.from.day,to:_===O?g.to.day:void 0,range:g}),_<O){let z;const{year:Ce,month:X}=g.from,se=X<12?{year:Ce,month:X+1}:{year:Ce+1,month:1};for(;(z=getMonthHash(se))<=O;)c[z]===void 0&&(c[z]=[]),c[z].push({from:void 0,to:z===O?g.to.day:void 0,range:g}),se.month++,se.month>12&&(se.year++,se.month=1)}}),c}),ye=computed(()=>{if(le.value===null)return;const{init:c,initHash:g,final:_,finalHash:O}=le.value,[z,Ce]=g<=O?[c,_]:[_,c],X=getMonthHash(z),se=getMonthHash(Ce);if(X!==Z.value&&se!==Z.value)return;const we={};return X===Z.value?(we.from=z.day,we.includeFrom=!0):we.from=1,se===Z.value?(we.to=Ce.day,we.includeTo=!0):we.to=Q.value,we}),Z=computed(()=>getMonthHash(d.value)),qe=computed(()=>{const c={};if(e.options===void 0){for(let _=1;_<=Q.value;_++)c[_]=!0;return c}const g=typeof e.options=="function"?e.options:_=>e.options.includes(_);for(let _=1;_<=Q.value;_++){const O=Z.value+"/"+pad(_);c[_]=g(O)}return c}),_e=computed(()=>{const c={};if(e.events===void 0)for(let g=1;g<=Q.value;g++)c[g]=!1;else{const g=typeof e.events=="function"?e.events:_=>e.events.includes(_);for(let _=1;_<=Q.value;_++){const O=Z.value+"/"+pad(_);c[_]=g(O)===!0&&be.value(O)}}return c}),De=computed(()=>{let c,g;const{year:_,month:O}=d.value;if(e.calendar!=="persian")c=new Date(_,O-1,1),g=new Date(_,O-1,0).getDate();else{const z=toGregorian(_,O,1);c=new Date(z.gy,z.gm-1,z.gd);let Ce=O-1,X=_;Ce===0&&(Ce=12,X--),g=jalaaliMonthLength(X,Ce)}return{days:c.getDay()-P.value-1,endDay:g}}),Ie=computed(()=>{const c=[],{days:g,endDay:_}=De.value,O=g<0?g+7:g;if(O<6)for(let X=_-O;X<=_;X++)c.push({i:X,fill:!0});const z=c.length;for(let X=1;X<=Q.value;X++){const se={i:X,event:_e.value[X],classes:[]};qe.value[X]===!0&&(se.in=!0,se.flat=!0),c.push(se)}if(ie.value[Z.value]!==void 0&&ie.value[Z.value].forEach(X=>{const se=z+X-1;Object.assign(c[se],{selected:!0,unelevated:!0,flat:!1,color:ce.value,textColor:J.value})}),re.value[Z.value]!==void 0&&re.value[Z.value].forEach(X=>{if(X.from!==void 0){const se=z+X.from-1,we=z+(X.to||Q.value)-1;for(let Ue=se;Ue<=we;Ue++)Object.assign(c[Ue],{range:X.range,unelevated:!0,color:ce.value,textColor:J.value});Object.assign(c[se],{rangeFrom:!0,flat:!1}),X.to!==void 0&&Object.assign(c[we],{rangeTo:!0,flat:!1})}else if(X.to!==void 0){const se=z+X.to-1;for(let we=z;we<=se;we++)Object.assign(c[we],{range:X.range,unelevated:!0,color:ce.value,textColor:J.value});Object.assign(c[se],{flat:!1,rangeTo:!0})}else{const se=z+Q.value-1;for(let we=z;we<=se;we++)Object.assign(c[we],{range:X.range,unelevated:!0,color:ce.value,textColor:J.value})}}),ye.value!==void 0){const X=z+ye.value.from-1,se=z+ye.value.to-1;for(let we=X;we<=se;we++)c[we].color=ce.value,c[we].editRange=!0;ye.value.includeFrom===!0&&(c[X].editRangeFrom=!0),ye.value.includeTo===!0&&(c[se].editRangeTo=!0)}d.value.year===C.value.year&&d.value.month===C.value.month&&(c[z+C.value.day-1].today=!0);const Ce=c.length%7;if(Ce>0){const X=7-Ce;for(let se=1;se<=X;se++)c.push({i:se,fill:!0})}return c.forEach(X=>{let se="q-date__calendar-item ";X.fill===!0?se+="q-date__calendar-item--fill":(se+=`q-date__calendar-item--${X.in===!0?"in":"out"}`,X.range!==void 0&&(se+=` q-date__range${X.rangeTo===!0?"-to":X.rangeFrom===!0?"-from":""}`),X.editRange===!0&&(se+=` q-date__edit-range${X.editRangeFrom===!0?"-from":""}${X.editRangeTo===!0?"-to":""}`),(X.range!==void 0||X.editRange===!0)&&(se+=` text-${X.color}`)),X.classes=se}),c}),Be=computed(()=>e.disable===!0?{"aria-disabled":"true"}:{});watch(()=>e.modelValue,c=>{if(b===c)b=0;else{const g=Le(B.value,S.value);Se(g.year,g.month,g)}}),watch(I,()=>{y.value!==null&&t.$el.contains(document.activeElement)===!0&&y.value.focus()}),watch(()=>d.value.year+"|"+d.value.month,()=>{l("navigation",{year:d.value.year,month:d.value.month})}),watch(M,c=>{V(c,S.value,"mask"),B.value=c}),watch(D,c=>{V(B.value,c,"locale"),S.value=c});function Re(){const{year:c,month:g,day:_}=C.value,O={...d.value,year:c,month:g,day:_},z=ie.value[getMonthHash(O)];(z===void 0||z.includes(O.day)===!1)&&Ge(O),He(O.year,O.month)}function Qe(c){viewIsValid(c)===!0&&(I.value=c)}function je(c,g){["month","year"].includes(c)&&(c==="month"?A:k)(g===!0?-1:1)}function He(c,g){I.value="Calendar",Se(c,g)}function Ne(c,g){if(e.range===!1||!c){le.value=null;return}const _=Object.assign({...d.value},c),O=g!==void 0?Object.assign({...d.value},g):_;le.value={init:_,initHash:getDayHash(_),final:O,finalHash:getDayHash(O)},He(_.year,_.month)}function $e(){return e.calendar==="persian"?"YYYY/MM/DD":e.mask}function Ke(c,g,_){return __splitDate(c,g,_,e.calendar,{hour:0,minute:0,second:0,millisecond:0})}function Le(c,g){const _=Array.isArray(e.modelValue)===!0?e.modelValue:e.modelValue?[e.modelValue]:[];if(_.length===0)return q();const O=_[_.length-1],z=Ke(O.from!==void 0?O.from:O,c,g);return z.dateHash===null?q():z}function q(){let c,g;if(e.defaultYearMonth!==void 0){const _=e.defaultYearMonth.split("/");c=parseInt(_[0],10),g=parseInt(_[1],10)}else{const _=C.value!==void 0?C.value:s();c=_.year,g=_.month}return{year:c,month:g,day:1,hour:0,minute:0,second:0,millisecond:0,dateHash:c+"/"+pad(g)+"/01"}}function A(c){let g=d.value.year,_=Number(d.value.month)+c;_===13?(_=1,g++):_===0&&(_=12,g--),Se(g,_),oe.value===!0&&ze("month")}function k(c){const g=Number(d.value.year)+c;Se(g,d.value.month),oe.value===!0&&ze("year")}function R(c){Se(c,d.value.month),I.value=e.defaultView==="Years"?"Months":"Calendar",oe.value===!0&&ze("year")}function ne(c){Se(d.value.year,c),I.value="Calendar",oe.value===!0&&ze("month")}function me(c,g){const _=ie.value[g];(_!==void 0&&_.includes(c.day)===!0?Je:Ge)(c)}function W(c){return{year:c.year,month:c.month,day:c.day}}function Se(c,g,_){if(fe.value!==null&&c<=fe.value.year&&((g<fe.value.month||c<fe.value.year)&&(g=fe.value.month),c=fe.value.year),T.value!==null&&c>=T.value.year&&((g>T.value.month||c>T.value.year)&&(g=T.value.month),c=T.value.year),_!==void 0){const{hour:z,minute:Ce,second:X,millisecond:se,timezoneOffset:we,timeHash:Ue}=_;Object.assign(d.value,{hour:z,minute:Ce,second:X,millisecond:se,timezoneOffset:we,timeHash:Ue})}const O=c+"/"+pad(g)+"/01";O!==d.value.dateHash&&(E.value=d.value.dateHash<O==(o.lang.rtl!==!0)?"left":"right",c!==d.value.year&&(F.value=E.value),nextTick(()=>{K.value=c-c%yearsInterval-(c<0?yearsInterval:0),Object.assign(d.value,{year:c,month:g,day:1,dateHash:O})}))}function Me(c,g,_){const O=c!==null&&c.length===1&&e.multiple===!1?c[0]:c;b=O;const{reason:z,details:Ce}=We(g,_);l("update:modelValue",O,z,Ce)}function ze(c){const g=he.value[0]!==void 0&&he.value[0].dateHash!==null?{...he.value[0]}:{...d.value};nextTick(()=>{g.year=d.value.year,g.month=d.value.month;const _=e.calendar!=="persian"?new Date(g.year,g.month,0).getDate():jalaaliMonthLength(g.year,g.month);g.day=Math.min(Math.max(1,g.day),_);const O=Ye(g);b=O;const{details:z}=We("",g);l("update:modelValue",O,c,z)})}function We(c,g){return g.from!==void 0?{reason:`${c}-range`,details:{...W(g.target),from:W(g.from),to:W(g.to)}}:{reason:`${c}-day`,details:W(g)}}function Ye(c,g,_){return c.from!==void 0?{from:Fe.value(c.from,g,_),to:Fe.value(c.to,g,_)}:Fe.value(c,g,_)}function Ge(c){let g;if(e.multiple===!0)if(c.from!==void 0){const _=getDayHash(c.from),O=getDayHash(c.to),z=he.value.filter(X=>X.dateHash<_||X.dateHash>O),Ce=xe.value.filter(({from:X,to:se})=>se.dateHash<_||X.dateHash>O);g=z.concat(Ce).concat(c).map(X=>Ye(X))}else{const _=ee.value.slice();_.push(Ye(c)),g=_}else g=Ye(c);Me(g,"add",c)}function Je(c){if(e.noUnset===!0)return;let g=null;if(e.multiple===!0&&Array.isArray(e.modelValue)===!0){const _=Ye(c);c.from!==void 0?g=e.modelValue.filter(O=>O.from!==void 0?O.from!==_.from&&O.to!==_.to:!0):g=e.modelValue.filter(O=>O!==_),g.length===0&&(g=null)}Me(g,"remove",c)}function V(c,g,_){const O=he.value.concat(xe.value).map(z=>Ye(z,c,g)).filter(z=>z.from!==void 0?z.from.dateHash!==null&&z.to.dateHash!==null:z.dateHash!==null);l("update:modelValue",(e.multiple===!0?O:O[0])||null,_)}function U(){if(e.minimal!==!0)return h("div",{class:"q-date__header "+u.value},[h("div",{class:"relative-position"},[h(Transition,{name:"q-transition--fade"},()=>h("div",{key:"h-yr-"+ke.value,class:"q-date__header-subtitle q-date__header-link "+(I.value==="Years"?"q-date__header-link--active":"cursor-pointer"),tabindex:a.value,...i("vY",{onClick(){I.value="Years"},onKeyup(c){c.keyCode===13&&(I.value="Years")}})},[ke.value]))]),h("div",{class:"q-date__header-title relative-position flex no-wrap"},[h("div",{class:"relative-position col"},[h(Transition,{name:"q-transition--fade"},()=>h("div",{key:"h-sub"+N.value,class:"q-date__header-title-label q-date__header-link "+(I.value==="Calendar"?"q-date__header-link--active":"cursor-pointer"),tabindex:a.value,...i("vC",{onClick(){I.value="Calendar"},onKeyup(c){c.keyCode===13&&(I.value="Calendar")}})},[N.value]))]),e.todayBtn===!0?h(QBtn,{class:"q-date__header-today self-start",icon:o.iconSet.datetime.today,flat:!0,size:"sm",round:!0,tabindex:a.value,onClick:Re}):null])])}function ae({label:c,type:g,key:_,dir:O,goTo:z,boundaries:Ce,cls:X}){return[h("div",{class:"row items-center q-date__arrow"},[h(QBtn,{round:!0,dense:!0,size:"sm",flat:!0,icon:w.value[0],tabindex:a.value,disable:Ce.prev===!1,...i("go-#"+g,{onClick(){z(-1)}})})]),h("div",{class:"relative-position overflow-hidden flex flex-center"+X},[h(Transition,{name:"q-transition--jump-"+O},()=>h("div",{key:_},[h(QBtn,{flat:!0,dense:!0,noCaps:!0,label:c,tabindex:a.value,...i("view#"+g,{onClick:()=>{I.value=g}})})]))]),h("div",{class:"row items-center q-date__arrow"},[h(QBtn,{round:!0,dense:!0,size:"sm",flat:!0,icon:w.value[1],tabindex:a.value,disable:Ce.next===!1,...i("go+#"+g,{onClick(){z(1)}})})])]}const ue={Calendar:()=>[h("div",{key:"calendar-view",class:"q-date__view q-date__calendar"},[h("div",{class:"q-date__navigation row items-center no-wrap"},ae({label:S.value.months[d.value.month-1],type:"Months",key:d.value.month,dir:E.value,goTo:A,boundaries:Y.value.month,cls:" col"}).concat(ae({label:d.value.year,type:"Years",key:d.value.year,dir:F.value,goTo:k,boundaries:Y.value.year,cls:""}))),h("div",{class:"q-date__calendar-weekdays row items-center no-wrap"},G.value.map(c=>h("div",{class:"q-date__calendar-item"},[h("div",c)]))),h("div",{class:"q-date__calendar-days-container relative-position overflow-hidden"},[h(Transition,{name:"q-transition--slide-"+E.value},()=>h("div",{key:Z.value,class:"q-date__calendar-days fit"},Ie.value.map(c=>h("div",{class:c.classes},[c.in===!0?h(QBtn,{class:c.today===!0?"q-date__today":"",dense:!0,flat:c.flat,unelevated:c.unelevated,color:c.color,textColor:c.textColor,label:c.i,tabindex:a.value,...i("day#"+c.i,{onClick:()=>{ge(c.i)},onMouseover:()=>{Ee(c.i)}})},c.event!==!1?()=>h("div",{class:"q-date__event bg-"+c.event}):null):h("div",""+c.i)]))))])])],Months(){const c=d.value.year===C.value.year,g=O=>fe.value!==null&&d.value.year===fe.value.year&&fe.value.month>O||T.value!==null&&d.value.year===T.value.year&&T.value.month<O,_=S.value.monthsShort.map((O,z)=>{const Ce=d.value.month===z+1;return h("div",{class:"q-date__months-item flex flex-center"},[h(QBtn,{class:c===!0&&C.value.month===z+1?"q-date__today":null,flat:Ce!==!0,label:O,unelevated:Ce,color:Ce===!0?ce.value:null,textColor:Ce===!0?J.value:null,tabindex:a.value,disable:g(z+1),...i("month#"+z,{onClick:()=>{ne(z+1)}})})])});return e.yearsInMonthView===!0&&_.unshift(h("div",{class:"row no-wrap full-width"},[ae({label:d.value.year,type:"Years",key:d.value.year,dir:F.value,goTo:k,boundaries:Y.value.year,cls:" col"})])),h("div",{key:"months-view",class:"q-date__view q-date__months flex flex-center"},_)},Years(){const c=K.value,g=c+yearsInterval,_=[],O=z=>fe.value!==null&&fe.value.year>z||T.value!==null&&T.value.year<z;for(let z=c;z<=g;z++){const Ce=d.value.year===z;_.push(h("div",{class:"q-date__years-item flex flex-center"},[h(QBtn,{key:"yr"+z,class:C.value.year===z?"q-date__today":null,flat:!Ce,label:z,dense:!0,unelevated:Ce,color:Ce===!0?ce.value:null,textColor:Ce===!0?J.value:null,tabindex:a.value,disable:O(z),...i("yr#"+z,{onClick:()=>{R(z)}})})]))}return h("div",{class:"q-date__view q-date__years flex flex-center"},[h("div",{class:"col-auto"},[h(QBtn,{round:!0,dense:!0,flat:!0,icon:w.value[0],tabindex:a.value,disable:O(c),...i("y-",{onClick:()=>{K.value-=yearsInterval}})})]),h("div",{class:"q-date__years-content col self-stretch row items-center"},_),h("div",{class:"col-auto"},[h(QBtn,{round:!0,dense:!0,flat:!0,icon:w.value[1],tabindex:a.value,disable:O(g),...i("y+",{onClick:()=>{K.value+=yearsInterval}})})])])}};function ge(c){const g={...d.value,day:c};if(e.range===!1){me(g,Z.value);return}if(le.value===null){const _=Ie.value.find(z=>z.fill!==!0&&z.i===c);if(e.noUnset!==!0&&_.range!==void 0){Je({target:g,from:_.range.from,to:_.range.to});return}if(_.selected===!0){Je(g);return}const O=getDayHash(g);le.value={init:g,initHash:O,final:g,finalHash:O},l("rangeStart",W(g))}else{const _=le.value.initHash,O=getDayHash(g),z=_<=O?{from:le.value.init,to:g}:{from:g,to:le.value.init};le.value=null,Ge(_===O?g:{target:g,...z}),l("rangeEnd",{from:W(z.from),to:W(z.to)})}}function Ee(c){if(le.value!==null){const g={...d.value,day:c};Object.assign(le.value,{final:g,finalHash:getDayHash(g)})}}return Object.assign(t,{setToday:Re,setView:Qe,offsetCalendar:je,setCalendarTo:He,setEditingRange:Ne}),()=>{const c=[h("div",{class:"q-date__content col relative-position"},[h(Transition,{name:"q-transition--fade"},ue[I.value])])],g=hSlot(n.default);return g!==void 0&&c.push(h("div",{class:"q-date__actions"},g)),e.name!==void 0&&e.disable!==!0&&p(c,"push"),h("div",{class:de.value,...Be.value},[U(),h("div",{ref:y,class:"q-date__main col column",tabindex:-1},c)])}}});function getViewByModel(e,n){return e.hour!==null&&e.minute===null?"minute":"hour"}function getCurrentTime(){const e=new Date;return{hour:e.getHours(),minute:e.getMinutes(),second:e.getSeconds(),millisecond:e.getMilliseconds()}}const QTime=createComponent({name:"QTime",props:{...useDarkProps,...useFormProps,...useDatetimeProps,modelValue:{required:!0,validator:e=>typeof e=="string"||e===null},mask:{...useDatetimeProps.mask,default:null},format24h:{type:Boolean,default:null},defaultDate:{type:String,validator:e=>/^-?[\d]+\/[0-1]\d\/[0-3]\d$/.test(e)},options:Function,hourOptions:Array,minuteOptions:Array,secondOptions:Array,withSeconds:Boolean,nowBtn:Boolean},emits:useDatetimeEmits,setup(e,{slots:n,emit:l}){const t=getCurrentInstance(),{$q:o}=t.proxy,r=useDark(e,o),{tabindex:i,headerClass:a,getLocale:u,getCurrentDate:f}=useDatetime(e,o),s=useFormAttrs(e),b=useFormInject(s);let m,p;const y=ref(null),B=computed(()=>ke()),S=computed(()=>u()),M=computed(()=>w()),D=__splitDate(e.modelValue,B.value,S.value,e.calendar,M.value),C=ref(getViewByModel(D)),d=ref(D),I=ref(D.hour===null||D.hour<12),H=computed(()=>`q-time q-time--${e.landscape===!0?"landscape":"portrait"}`+(r.value===!0?" q-time--dark q-dark":"")+(e.disable===!0?" disabled":e.readonly===!0?" q-time--readonly":"")+(e.bordered===!0?" q-time--bordered":"")+(e.square===!0?" q-time--square no-border-radius":"")+(e.flat===!0?" q-time--flat no-shadow":"")),E=computed(()=>{const q=d.value;return{hour:q.hour===null?"--":F.value===!0?pad(q.hour):String(I.value===!0?q.hour===0?12:q.hour:q.hour>12?q.hour-12:q.hour),minute:q.minute===null?"--":pad(q.minute),second:q.second===null?"--":pad(q.second)}}),F=computed(()=>e.format24h!==null?e.format24h:o.lang.date.format24h),L=computed(()=>{const q=C.value==="hour",A=q===!0?12:60,k=d.value[C.value];let ne=`rotate(${Math.round(k*(360/A))-180}deg) translateX(-50%)`;return q===!0&&F.value===!0&&d.value.hour>=12&&(ne+=" scale(.7)"),{transform:ne}}),K=computed(()=>d.value.hour!==null),le=computed(()=>K.value===!0&&d.value.minute!==null),de=computed(()=>e.hourOptions!==void 0?q=>e.hourOptions.includes(q):e.options!==void 0?q=>e.options(q,null,null):null),ce=computed(()=>e.minuteOptions!==void 0?q=>e.minuteOptions.includes(q):e.options!==void 0?q=>e.options(d.value.hour,q,null):null),J=computed(()=>e.secondOptions!==void 0?q=>e.secondOptions.includes(q):e.options!==void 0?q=>e.options(d.value.hour,d.value.minute,q):null),oe=computed(()=>{if(de.value===null)return null;const q=N(0,11,de.value),A=N(12,11,de.value);return{am:q,pm:A,values:q.values.concat(A.values)}}),ee=computed(()=>ce.value!==null?N(0,59,ce.value):null),he=computed(()=>J.value!==null?N(0,59,J.value):null),xe=computed(()=>{switch(C.value){case"hour":return oe.value;case"minute":return ee.value;case"second":return he.value}}),Ve=computed(()=>{let q,A,k=0,R=1;const ne=xe.value!==null?xe.value.values:void 0;C.value==="hour"?F.value===!0?(q=0,A=23):(q=0,A=11,I.value===!1&&(k=12)):(q=0,A=55,R=5);const me=[];for(let W=q,Se=q;W<=A;W+=R,Se++){const Me=W+k,ze=ne!==void 0&&ne.includes(Me)===!1,We=C.value==="hour"&&W===0?F.value===!0?"00":"12":W;me.push({val:Me,index:Se,disable:ze,label:We})}return me}),Fe=computed(()=>[[TouchPan,Q,void 0,{stop:!0,prevent:!0,mouse:!0}]]);watch(()=>e.modelValue,q=>{const A=__splitDate(q,B.value,S.value,e.calendar,M.value);(A.dateHash!==d.value.dateHash||A.timeHash!==d.value.timeHash)&&(d.value=A,A.hour===null?C.value="hour":I.value=A.hour<12)}),watch([B,S],()=>{nextTick(()=>{$e()})});function x(){const q={...f(),...getCurrentTime()};$e(q),Object.assign(d.value,q),C.value="hour"}function N(q,A,k){const R=Array.apply(null,{length:A+1}).map((ne,me)=>{const W=me+q;return{index:W,val:k(W)===!0}}).filter(ne=>ne.val===!0).map(ne=>ne.index);return{min:R[0],max:R[R.length-1],values:R,threshold:A+1}}function te(q,A,k){const R=Math.abs(q-A);return Math.min(R,k-R)}function ve(q,{min:A,max:k,values:R,threshold:ne}){if(q===A)return A;if(q<A||q>k)return te(q,A,ne)<=te(q,k,ne)?A:k;const me=R.findIndex(Me=>q<=Me),W=R[me-1],Se=R[me];return q-W<=Se-q?W:Se}function ke(){return e.calendar!=="persian"&&e.mask!==null?e.mask:`HH:mm${e.withSeconds===!0?":ss":""}`}function w(){if(typeof e.defaultDate!="string"){const q=f(!0);return q.dateHash=getDayHash(q),q}return __splitDate(e.defaultDate,"YYYY/MM/DD",void 0,e.calendar)}function P(){return vmIsDestroyed(t)===!0||xe.value!==null&&(xe.value.values.length===0||C.value==="hour"&&F.value!==!0&&oe.value[I.value===!0?"am":"pm"].values.length===0)}function G(){const q=y.value,{top:A,left:k,width:R}=q.getBoundingClientRect(),ne=R/2;return{top:A+ne,left:k+ne,dist:ne*.7}}function Q(q){if(P()!==!0){if(q.isFirst===!0){m=G(),p=fe(q.evt,m);return}p=fe(q.evt,m,p),q.isFinal===!0&&(m=!1,p=null,be())}}function be(){C.value==="hour"?C.value="minute":e.withSeconds&&C.value==="minute"&&(C.value="second")}function fe(q,A,k){const R=position(q),ne=Math.abs(R.top-A.top),me=Math.sqrt(Math.pow(Math.abs(R.top-A.top),2)+Math.pow(Math.abs(R.left-A.left),2));let W,Se=Math.asin(ne/me)*(180/Math.PI);if(R.top<A.top?Se=A.left<R.left?90-Se:270+Se:Se=A.left<R.left?Se+90:270-Se,C.value==="hour"){if(W=Se/30,oe.value!==null){const Me=F.value!==!0?I.value===!0:oe.value.am.values.length!==0&&oe.value.pm.values.length!==0?me>=A.dist:oe.value.am.values.length!==0;W=ve(W+(Me===!0?0:12),oe.value[Me===!0?"am":"pm"])}else W=Math.round(W),F.value===!0?me<A.dist?W<12&&(W+=12):W===12&&(W=0):I.value===!0&&W===12?W=0:I.value===!1&&W!==12&&(W+=12);F.value===!0&&(I.value=W<12)}else W=Math.round(Se/6)%60,C.value==="minute"&&ee.value!==null?W=ve(W,ee.value):C.value==="second"&&he.value!==null&&(W=ve(W,he.value));return k!==W&&Re[C.value](W),W}const T={hour(){C.value="hour"},minute(){C.value="minute"},second(){C.value="second"}};function Y(q){q.keyCode===13&&Qe()}function ie(q){q.keyCode===13&&je()}function re(q){P()!==!0&&(o.platform.is.desktop!==!0&&fe(q,G()),be())}function ye(q){P()!==!0&&fe(q,G())}function Z(q){if(q.keyCode===13)C.value="hour";else if([37,39].includes(q.keyCode)){const A=q.keyCode===37?-1:1;if(oe.value!==null){const k=F.value===!0?oe.value.values:oe.value[I.value===!0?"am":"pm"].values;if(k.length===0)return;if(d.value.hour===null)De(k[0]);else{const R=(k.length+k.indexOf(d.value.hour)+A)%k.length;De(k[R])}}else{const k=F.value===!0?24:12,R=F.value!==!0&&I.value===!1?12:0,ne=d.value.hour===null?-A:d.value.hour;De(R+(24+ne+A)%k)}}}function qe(q){if(q.keyCode===13)C.value="minute";else if([37,39].includes(q.keyCode)){const A=q.keyCode===37?-1:1;if(ee.value!==null){const k=ee.value.values;if(k.length===0)return;if(d.value.minute===null)Ie(k[0]);else{const R=(k.length+k.indexOf(d.value.minute)+A)%k.length;Ie(k[R])}}else{const k=d.value.minute===null?-A:d.value.minute;Ie((60+k+A)%60)}}}function _e(q){if(q.keyCode===13)C.value="second";else if([37,39].includes(q.keyCode)){const A=q.keyCode===37?-1:1;if(he.value!==null){const k=he.value.values;if(k.length===0)return;if(d.value.seconds===null)Be(k[0]);else{const R=(k.length+k.indexOf(d.value.second)+A)%k.length;Be(k[R])}}else{const k=d.value.second===null?-A:d.value.second;Be((60+k+A)%60)}}}function De(q){d.value.hour!==q&&(d.value.hour=q,Ne())}function Ie(q){d.value.minute!==q&&(d.value.minute=q,Ne())}function Be(q){d.value.second!==q&&(d.value.second=q,Ne())}const Re={hour:De,minute:Ie,second:Be};function Qe(){I.value===!1&&(I.value=!0,d.value.hour!==null&&(d.value.hour-=12,Ne()))}function je(){I.value===!0&&(I.value=!1,d.value.hour!==null&&(d.value.hour+=12,Ne()))}function He(q){const A=e.modelValue;C.value!==q&&A!==void 0&&A!==null&&A!==""&&typeof A!="string"&&(C.value=q)}function Ne(){if(de.value!==null&&de.value(d.value.hour)!==!0){d.value=__splitDate(),He("hour");return}if(ce.value!==null&&ce.value(d.value.minute)!==!0){d.value.minute=null,d.value.second=null,He("minute");return}if(e.withSeconds===!0&&J.value!==null&&J.value(d.value.second)!==!0){d.value.second=null,He("second");return}d.value.hour===null||d.value.minute===null||e.withSeconds===!0&&d.value.second===null||$e()}function $e(q){const A=Object.assign({...d.value},q),k=e.calendar==="persian"?pad(A.hour)+":"+pad(A.minute)+(e.withSeconds===!0?":"+pad(A.second):""):formatDate(new Date(A.year,A.month===null?null:A.month-1,A.day,A.hour,A.minute,A.second,A.millisecond),B.value,S.value,A.year,A.timezoneOffset);A.changed=k!==e.modelValue,l("update:modelValue",k,A)}function Ke(){const q=[h("div",{class:"q-time__link "+(C.value==="hour"?"q-time__link--active":"cursor-pointer"),tabindex:i.value,onClick:T.hour,onKeyup:Z},E.value.hour),h("div",":"),h("div",K.value===!0?{class:"q-time__link "+(C.value==="minute"?"q-time__link--active":"cursor-pointer"),tabindex:i.value,onKeyup:qe,onClick:T.minute}:{class:"q-time__link"},E.value.minute)];e.withSeconds===!0&&q.push(h("div",":"),h("div",le.value===!0?{class:"q-time__link "+(C.value==="second"?"q-time__link--active":"cursor-pointer"),tabindex:i.value,onKeyup:_e,onClick:T.second}:{class:"q-time__link"},E.value.second));const A=[h("div",{class:"q-time__header-label row items-center no-wrap",dir:"ltr"},q)];return F.value===!1&&A.push(h("div",{class:"q-time__header-ampm column items-between no-wrap"},[h("div",{class:"q-time__link "+(I.value===!0?"q-time__link--active":"cursor-pointer"),tabindex:i.value,onClick:Qe,onKeyup:Y},"AM"),h("div",{class:"q-time__link "+(I.value!==!0?"q-time__link--active":"cursor-pointer"),tabindex:i.value,onClick:je,onKeyup:ie},"PM")])),h("div",{class:"q-time__header flex flex-center no-wrap "+a.value},A)}function Le(){const q=d.value[C.value];return h("div",{class:"q-time__content col relative-position"},[h(Transition,{name:"q-transition--scale"},()=>h("div",{key:"clock"+C.value,class:"q-time__container-parent absolute-full"},[h("div",{ref:y,class:"q-time__container-child fit overflow-hidden"},[withDirectives(h("div",{class:"q-time__clock cursor-pointer non-selectable",onClick:re,onMousedown:ye},[h("div",{class:"q-time__clock-circle fit"},[h("div",{class:"q-time__clock-pointer"+(d.value[C.value]===null?" hidden":e.color!==void 0?` text-${e.color}`:""),style:L.value}),Ve.value.map(A=>h("div",{class:`q-time__clock-position row flex-center q-time__clock-pos-${A.index}`+(A.val===q?" q-time__clock-position--active "+a.value:A.disable===!0?" q-time__clock-position--disable":"")},[h("span",A.label)]))])]),Fe.value)])])),e.nowBtn===!0?h(QBtn,{class:"q-time__now-button absolute",icon:o.iconSet.datetime.now,unelevated:!0,size:"sm",round:!0,color:e.color,textColor:e.textColor,tabindex:i.value,onClick:x}):null])}return t.proxy.setNow=x,()=>{const q=[Le()],A=hSlot(n.default);return A!==void 0&&q.push(h("div",{class:"q-time__actions"},A)),e.name!==void 0&&e.disable!==!0&&b(q,"push"),h("div",{class:H.value,tabindex:-1},[Ke(),h("div",{class:"q-time__main col overflow-auto"},q)])}}}),markerPrefixClass="q-slider__marker-labels",defaultMarkerConvertFn=e=>({value:e}),defaultMarkerLabelRenderFn=({marker:e})=>h("div",{key:e.value,style:e.style,class:e.classes},e.label),keyCodes=[34,37,40,33,39,38],useSliderProps={...useDarkProps,...useFormProps,min:{type:Number,default:0},max:{type:Number,default:100},innerMin:Number,innerMax:Number,step:{type:Number,default:1,validator:e=>e>=0},snap:Boolean,vertical:Boolean,reverse:Boolean,color:String,markerLabelsClass:String,label:Boolean,labelColor:String,labelTextColor:String,labelAlways:Boolean,switchLabelSide:Boolean,markers:[Boolean,Number],markerLabels:[Boolean,Array,Object,Function],switchMarkerLabelsSide:Boolean,trackImg:String,trackColor:String,innerTrackImg:String,innerTrackColor:String,selectionColor:String,selectionImg:String,thumbSize:{type:String,default:"20px"},trackSize:{type:String,default:"4px"},disable:Boolean,readonly:Boolean,dense:Boolean,tabindex:[String,Number],thumbColor:String,thumbPath:{type:String,default:"M 4, 10 a 6,6 0 1,0 12,0 a 6,6 0 1,0 -12,0"}},useSliderEmits=["pan","update:modelValue","change"];function useSlider({updateValue:e,updatePosition:n,getDragging:l,formAttrs:t}){const{props:o,emit:r,slots:i,proxy:{$q:a}}=getCurrentInstance(),u=useDark(o,a),f=useFormInject(t),s=ref(!1),b=ref(!1),m=ref(!1),p=ref(!1),y=computed(()=>o.vertical===!0?"--v":"--h"),B=computed(()=>"-"+(o.switchLabelSide===!0?"switched":"standard")),S=computed(()=>o.vertical===!0?o.reverse===!0:o.reverse!==(a.lang.rtl===!0)),M=computed(()=>isNaN(o.innerMin)===!0||o.innerMin<o.min?o.min:o.innerMin),D=computed(()=>isNaN(o.innerMax)===!0||o.innerMax>o.max?o.max:o.innerMax),C=computed(()=>o.disable!==!0&&o.readonly!==!0&&M.value<D.value),d=computed(()=>{if(o.step===0)return R=>R;const k=(String(o.step).trim().split(".")[1]||"").length;return R=>parseFloat(R.toFixed(k))}),I=computed(()=>o.step===0?1:o.step),H=computed(()=>C.value===!0?o.tabindex||0:-1),E=computed(()=>o.max-o.min),F=computed(()=>D.value-M.value),L=computed(()=>fe(M.value)),K=computed(()=>fe(D.value)),le=computed(()=>o.vertical===!0?S.value===!0?"bottom":"top":S.value===!0?"right":"left"),de=computed(()=>o.vertical===!0?"height":"width"),ce=computed(()=>o.vertical===!0?"width":"height"),J=computed(()=>o.vertical===!0?"vertical":"horizontal"),oe=computed(()=>{const k={role:"slider","aria-valuemin":M.value,"aria-valuemax":D.value,"aria-orientation":J.value,"data-step":o.step};return o.disable===!0?k["aria-disabled"]="true":o.readonly===!0&&(k["aria-readonly"]="true"),k}),ee=computed(()=>`q-slider q-slider${y.value} q-slider--${s.value===!0?"":"in"}active inline no-wrap `+(o.vertical===!0?"row":"column")+(o.disable===!0?" disabled":" q-slider--enabled"+(C.value===!0?" q-slider--editable":""))+(m.value==="both"?" q-slider--focus":"")+(o.label||o.labelAlways===!0?" q-slider--label":"")+(o.labelAlways===!0?" q-slider--label-always":"")+(u.value===!0?" q-slider--dark":"")+(o.dense===!0?" q-slider--dense q-slider--dense"+y.value:""));function he(k){const R="q-slider__"+k;return`${R} ${R}${y.value} ${R}${y.value}${B.value}`}function xe(k){const R="q-slider__"+k;return`${R} ${R}${y.value}`}const Ve=computed(()=>{const k=o.selectionColor||o.color;return"q-slider__selection absolute"+(k!==void 0?` text-${k}`:"")}),Fe=computed(()=>xe("markers")+" absolute overflow-hidden"),x=computed(()=>xe("track-container")),N=computed(()=>he("pin")),te=computed(()=>he("label")),ve=computed(()=>he("text-container")),ke=computed(()=>he("marker-labels-container")+(o.markerLabelsClass!==void 0?` ${o.markerLabelsClass}`:"")),w=computed(()=>"q-slider__track relative-position no-outline"+(o.trackColor!==void 0?` bg-${o.trackColor}`:"")),P=computed(()=>{const k={[ce.value]:o.trackSize};return o.trackImg!==void 0&&(k.backgroundImage=`url(${o.trackImg}) !important`),k}),G=computed(()=>"q-slider__inner absolute"+(o.innerTrackColor!==void 0?` bg-${o.innerTrackColor}`:"")),Q=computed(()=>{const k=K.value-L.value,R={[le.value]:`${100*L.value}%`,[de.value]:k===0?"2px":`${100*k}%`};return o.innerTrackImg!==void 0&&(R.backgroundImage=`url(${o.innerTrackImg}) !important`),R});function be(k){const{min:R,max:ne,step:me}=o;let W=R+k*(ne-R);if(me>0){const Se=(W-M.value)%me;W+=(Math.abs(Se)>=me/2?(Se<0?-1:1)*me:0)-Se}return W=d.value(W),between(W,M.value,D.value)}function fe(k){return E.value===0?0:(k-o.min)/E.value}function T(k,R){const ne=position(k),me=o.vertical===!0?between((ne.top-R.top)/R.height,0,1):between((ne.left-R.left)/R.width,0,1);return between(S.value===!0?1-me:me,L.value,K.value)}const Y=computed(()=>isNumber(o.markers)===!0?o.markers:I.value),ie=computed(()=>{const k=[],R=Y.value,ne=o.max;let me=o.min;do k.push(me),me+=R;while(me<ne);return k.push(ne),k}),re=computed(()=>{const k=` ${markerPrefixClass}${y.value}-`;return markerPrefixClass+`${k}${o.switchMarkerLabelsSide===!0?"switched":"standard"}${k}${S.value===!0?"rtl":"ltr"}`}),ye=computed(()=>o.markerLabels===!1?null:_e(o.markerLabels).map((k,R)=>({index:R,value:k.value,label:k.label||k.value,classes:re.value+(k.classes!==void 0?" "+k.classes:""),style:{...De(k.value),...k.style||{}}}))),Z=computed(()=>({markerList:ye.value,markerMap:Ie.value,classes:re.value,getStyle:De})),qe=computed(()=>{const k=F.value===0?"2px":100*Y.value/F.value;return{...Q.value,backgroundSize:o.vertical===!0?`2px ${k}%`:`${k}% 2px`}});function _e(k){if(k===!1)return null;if(k===!0)return ie.value.map(defaultMarkerConvertFn);if(typeof k=="function")return ie.value.map(ne=>{const me=k(ne);return isObject(me)===!0?{...me,value:ne}:{value:ne,label:me}});const R=({value:ne})=>ne>=o.min&&ne<=o.max;return Array.isArray(k)===!0?k.map(ne=>isObject(ne)===!0?ne:{value:ne}).filter(R):Object.keys(k).map(ne=>{const me=k[ne],W=Number(ne);return isObject(me)===!0?{...me,value:W}:{value:W,label:me}}).filter(R)}function De(k){return{[le.value]:`${100*(k-o.min)/E.value}%`}}const Ie=computed(()=>{if(o.markerLabels===!1)return null;const k={};return ye.value.forEach(R=>{k[R.value]=R}),k});function Be(){if(i["marker-label-group"]!==void 0)return i["marker-label-group"](Z.value);const k=i["marker-label"]||defaultMarkerLabelRenderFn;return ye.value.map(R=>k({marker:R,...Z.value}))}const Re=computed(()=>[[TouchPan,Qe,void 0,{[J.value]:!0,prevent:!0,stop:!0,mouse:!0,mouseAllDir:!0}]]);function Qe(k){k.isFinal===!0?(p.value!==void 0&&(n(k.evt),k.touch===!0&&e(!0),p.value=void 0,r("pan","end")),s.value=!1,m.value=!1):k.isFirst===!0?(p.value=l(k.evt),n(k.evt),e(),s.value=!0,r("pan","start")):(n(k.evt),e())}function je(){m.value=!1}function He(k){n(k,l(k)),e(),b.value=!0,s.value=!0,document.addEventListener("mouseup",Ne,!0)}function Ne(){b.value=!1,s.value=!1,e(!0),je(),document.removeEventListener("mouseup",Ne,!0)}function $e(k){n(k,l(k)),e(!0)}function Ke(k){keyCodes.includes(k.keyCode)&&e(!0)}function Le(k){if(o.vertical===!0)return null;const R=a.lang.rtl!==o.reverse?1-k:k;return{transform:`translateX(calc(${2*R-1} * ${o.thumbSize} / 2 + ${50-100*R}%))`}}function q(k){const R=computed(()=>b.value===!1&&(m.value===k.focusValue||m.value==="both")?" q-slider--focus":""),ne=computed(()=>`q-slider__thumb q-slider__thumb${y.value} q-slider__thumb${y.value}-${S.value===!0?"rtl":"ltr"} absolute non-selectable`+R.value+(k.thumbColor.value!==void 0?` text-${k.thumbColor.value}`:"")),me=computed(()=>({width:o.thumbSize,height:o.thumbSize,[le.value]:`${100*k.ratio.value}%`,zIndex:m.value===k.focusValue?2:void 0})),W=computed(()=>k.labelColor.value!==void 0?` text-${k.labelColor.value}`:""),Se=computed(()=>Le(k.ratio.value)),Me=computed(()=>"q-slider__text"+(k.labelTextColor.value!==void 0?` text-${k.labelTextColor.value}`:""));return()=>{const ze=[h("svg",{class:"q-slider__thumb-shape absolute-full",viewBox:"0 0 20 20","aria-hidden":"true"},[h("path",{d:o.thumbPath})]),h("div",{class:"q-slider__focus-ring fit"})];return(o.label===!0||o.labelAlways===!0)&&(ze.push(h("div",{class:N.value+" absolute fit no-pointer-events"+W.value},[h("div",{class:te.value,style:{minWidth:o.thumbSize}},[h("div",{class:ve.value,style:Se.value},[h("span",{class:Me.value},k.label.value)])])])),o.name!==void 0&&o.disable!==!0&&f(ze,"push")),h("div",{class:ne.value,style:me.value,...k.getNodeData()},ze)}}function A(k,R,ne,me){const W=[];o.innerTrackColor!=="transparent"&&W.push(h("div",{key:"inner",class:G.value,style:Q.value})),o.selectionColor!=="transparent"&&W.push(h("div",{key:"selection",class:Ve.value,style:k.value})),o.markers!==!1&&W.push(h("div",{key:"marker",class:Fe.value,style:qe.value})),me(W);const Se=[hDir("div",{key:"trackC",class:x.value,tabindex:R.value,...ne.value},[h("div",{class:w.value,style:P.value},W)],"slide",C.value,()=>Re.value)];if(o.markerLabels!==!1){const Me=o.switchMarkerLabelsSide===!0?"unshift":"push";Se[Me](h("div",{key:"markerL",class:ke.value},Be()))}return Se}return onBeforeUnmount(()=>{document.removeEventListener("mouseup",Ne,!0)}),{state:{active:s,focus:m,preventFocus:b,dragging:p,editable:C,classes:ee,tabindex:H,attributes:oe,roundValueFn:d,keyStep:I,trackLen:E,innerMin:M,innerMinRatio:L,innerMax:D,innerMaxRatio:K,positionProp:le,sizeProp:de,isReversed:S},methods:{onActivate:He,onMobileClick:$e,onBlur:je,onKeyup:Ke,getContent:A,getThumbRenderFn:q,convertRatioToModel:be,convertModelToRatio:fe,getDraggingRatio:T}}}const getNodeData=()=>({}),QSlider=createComponent({name:"QSlider",props:{...useSliderProps,modelValue:{required:!0,default:null,validator:e=>typeof e=="number"||e===null},labelValue:[String,Number]},emits:useSliderEmits,setup(e,{emit:n}){const{proxy:{$q:l}}=getCurrentInstance(),{state:t,methods:o}=useSlider({updateValue:y,updatePosition:S,getDragging:B,formAttrs:useFormAttrs(e)}),r=ref(null),i=ref(0),a=ref(0);function u(){a.value=e.modelValue===null?t.innerMin.value:between(e.modelValue,t.innerMin.value,t.innerMax.value)}watch(()=>`${e.modelValue}|${t.innerMin.value}|${t.innerMax.value}`,u),u();const f=computed(()=>o.convertModelToRatio(a.value)),s=computed(()=>t.active.value===!0?i.value:f.value),b=computed(()=>{const C={[t.positionProp.value]:`${100*t.innerMinRatio.value}%`,[t.sizeProp.value]:`${100*(s.value-t.innerMinRatio.value)}%`};return e.selectionImg!==void 0&&(C.backgroundImage=`url(${e.selectionImg}) !important`),C}),m=o.getThumbRenderFn({focusValue:!0,getNodeData,ratio:s,label:computed(()=>e.labelValue!==void 0?e.labelValue:a.value),thumbColor:computed(()=>e.thumbColor||e.color),labelColor:computed(()=>e.labelColor),labelTextColor:computed(()=>e.labelTextColor)}),p=computed(()=>t.editable.value!==!0?{}:l.platform.is.mobile===!0?{onClick:o.onMobileClick}:{onMousedown:o.onActivate,onFocus:M,onBlur:o.onBlur,onKeydown:D,onKeyup:o.onKeyup});function y(C){a.value!==e.modelValue&&n("update:modelValue",a.value),C===!0&&n("change",a.value)}function B(){return r.value.getBoundingClientRect()}function S(C,d=t.dragging.value){const I=o.getDraggingRatio(C,d);a.value=o.convertRatioToModel(I),i.value=e.snap!==!0||e.step===0?I:o.convertModelToRatio(a.value)}function M(){t.focus.value=!0}function D(C){if(!keyCodes.includes(C.keyCode))return;stopAndPrevent(C);const d=([34,33].includes(C.keyCode)?10:1)*t.keyStep.value,I=([34,37,40].includes(C.keyCode)?-1:1)*(t.isReversed.value===!0?-1:1)*(e.vertical===!0?-1:1)*d;a.value=between(t.roundValueFn.value(a.value+I),t.innerMin.value,t.innerMax.value),y()}return()=>{const C=o.getContent(b,t.tabindex,p,d=>{d.push(m())});return h("div",{ref:r,class:t.classes.value+(e.modelValue===null?" q-slider--no-value":""),...t.attributes.value,"aria-valuenow":e.modelValue},C)}}});function parseArg(e){const n=[.06,6,50];return typeof e=="string"&&e.length&&e.split(":").forEach((l,t)=>{const o=parseFloat(l);o&&(n[t]=o)}),n}const TouchSwipe=createDirective({name:"touch-swipe",beforeMount(e,{value:n,arg:l,modifiers:t}){if(t.mouse!==!0&&client.has.touch!==!0)return;const o=t.mouseCapture===!0?"Capture":"",r={handler:n,sensitivity:parseArg(l),direction:getModifierDirections(t),noop,mouseStart(i){shouldStart(i,r)&&leftClick(i)&&(addEvt(r,"temp",[[document,"mousemove","move",`notPassive${o}`],[document,"mouseup","end","notPassiveCapture"]]),r.start(i,!0))},touchStart(i){if(shouldStart(i,r)){const a=i.target;addEvt(r,"temp",[[a,"touchmove","move","notPassiveCapture"],[a,"touchcancel","end","notPassiveCapture"],[a,"touchend","end","notPassiveCapture"]]),r.start(i)}},start(i,a){client.is.firefox===!0&&preventDraggable(e,!0);const u=position(i);r.event={x:u.left,y:u.top,time:Date.now(),mouse:a===!0,dir:!1}},move(i){if(r.event===void 0)return;if(r.event.dir!==!1){stopAndPrevent(i);return}const a=Date.now()-r.event.time;if(a===0)return;const u=position(i),f=u.left-r.event.x,s=Math.abs(f),b=u.top-r.event.y,m=Math.abs(b);if(r.event.mouse!==!0){if(s<r.sensitivity[1]&&m<r.sensitivity[1]){r.end(i);return}}else if(window.getSelection().toString()!==""){r.end(i);return}else if(s<r.sensitivity[2]&&m<r.sensitivity[2])return;const p=s/a,y=m/a;r.direction.vertical===!0&&s<m&&s<100&&y>r.sensitivity[0]&&(r.event.dir=b<0?"up":"down"),r.direction.horizontal===!0&&s>m&&m<100&&p>r.sensitivity[0]&&(r.event.dir=f<0?"left":"right"),r.direction.up===!0&&s<m&&b<0&&s<100&&y>r.sensitivity[0]&&(r.event.dir="up"),r.direction.down===!0&&s<m&&b>0&&s<100&&y>r.sensitivity[0]&&(r.event.dir="down"),r.direction.left===!0&&s>m&&f<0&&m<100&&p>r.sensitivity[0]&&(r.event.dir="left"),r.direction.right===!0&&s>m&&f>0&&m<100&&p>r.sensitivity[0]&&(r.event.dir="right"),r.event.dir!==!1?(stopAndPrevent(i),r.event.mouse===!0&&(document.body.classList.add("no-pointer-events--children"),document.body.classList.add("non-selectable"),clearSelection(),r.styleCleanup=B=>{r.styleCleanup=void 0,document.body.classList.remove("non-selectable");const S=()=>{document.body.classList.remove("no-pointer-events--children")};B===!0?setTimeout(S,50):S()}),r.handler({evt:i,touch:r.event.mouse!==!0,mouse:r.event.mouse,direction:r.event.dir,duration:a,distance:{x:s,y:m}})):r.end(i)},end(i){r.event!==void 0&&(cleanEvt(r,"temp"),client.is.firefox===!0&&preventDraggable(e,!1),r.styleCleanup!==void 0&&r.styleCleanup(!0),i!==void 0&&r.event.dir!==!1&&stopAndPrevent(i),r.event=void 0)}};if(e.__qtouchswipe=r,t.mouse===!0){const i=t.mouseCapture===!0||t.mousecapture===!0?"Capture":"";addEvt(r,"main",[[e,"mousedown","mouseStart",`passive${i}`]])}client.has.touch===!0&&addEvt(r,"main",[[e,"touchstart","touchStart",`passive${t.capture===!0?"Capture":""}`],[e,"touchmove","noop","notPassiveCapture"]])},updated(e,n){const l=e.__qtouchswipe;l!==void 0&&(n.oldValue!==n.value&&(typeof n.value!="function"&&l.end(),l.handler=n.value),l.direction=getModifierDirections(n.modifiers))},beforeUnmount(e){const n=e.__qtouchswipe;n!==void 0&&(cleanEvt(n,"main"),cleanEvt(n,"temp"),client.is.firefox===!0&&preventDraggable(e,!1),n.styleCleanup!==void 0&&n.styleCleanup(),delete e.__qtouchswipe)}}),usePanelChildProps={name:{required:!0},disable:Boolean},PanelWrapper={setup(e,{slots:n}){return()=>h("div",{class:"q-panel scroll",role:"tabpanel"},hSlot(n.default))}},usePanelProps={modelValue:{required:!0},animated:Boolean,infinite:Boolean,swipeable:Boolean,vertical:Boolean,transitionPrev:String,transitionNext:String,transitionDuration:{type:[String,Number],default:300},keepAlive:Boolean,keepAliveInclude:[String,Array,RegExp],keepAliveExclude:[String,Array,RegExp],keepAliveMax:Number},usePanelEmits=["update:modelValue","beforeTransition","transition"];function usePanel(){const{props:e,emit:n,proxy:l}=getCurrentInstance(),{getCache:t}=useRenderCache(),{registerTimeout:o}=useTimeout();let r,i;const a=ref(null),u=ref(null);function f(J){const oe=e.vertical===!0?"up":"left";F((l.$q.lang.rtl===!0?-1:1)*(J.direction===oe?1:-1))}const s=computed(()=>[[TouchSwipe,f,void 0,{horizontal:e.vertical!==!0,vertical:e.vertical,mouse:!0}]]),b=computed(()=>e.transitionPrev||`slide-${e.vertical===!0?"down":"right"}`),m=computed(()=>e.transitionNext||`slide-${e.vertical===!0?"up":"left"}`),p=computed(()=>`--q-transition-duration: ${e.transitionDuration}ms`),y=computed(()=>typeof e.modelValue=="string"||typeof e.modelValue=="number"?e.modelValue:String(e.modelValue)),B=computed(()=>({include:e.keepAliveInclude,exclude:e.keepAliveExclude,max:e.keepAliveMax})),S=computed(()=>e.keepAliveInclude!==void 0||e.keepAliveExclude!==void 0);watch(()=>e.modelValue,(J,oe)=>{const ee=d(J)===!0?I(J):-1;i!==!0&&E(ee===-1?0:ee<I(oe)?-1:1),a.value!==ee&&(a.value=ee,n("beforeTransition",J,oe),o(()=>{n("transition",J,oe)},e.transitionDuration))});function M(){F(1)}function D(){F(-1)}function C(J){n("update:modelValue",J)}function d(J){return J!=null&&J!==""}function I(J){return r.findIndex(oe=>oe.props.name===J&&oe.props.disable!==""&&oe.props.disable!==!0)}function H(){return r.filter(J=>J.props.disable!==""&&J.props.disable!==!0)}function E(J){const oe=J!==0&&e.animated===!0&&a.value!==-1?"q-transition--"+(J===-1?b.value:m.value):null;u.value!==oe&&(u.value=oe)}function F(J,oe=a.value){let ee=oe+J;for(;ee!==-1&&ee<r.length;){const he=r[ee];if(he!==void 0&&he.props.disable!==""&&he.props.disable!==!0){E(J),i=!0,n("update:modelValue",he.props.name),setTimeout(()=>{i=!1});return}ee+=J}e.infinite===!0&&r.length!==0&&oe!==-1&&oe!==r.length&&F(J,J===-1?r.length:-1)}function L(){const J=I(e.modelValue);return a.value!==J&&(a.value=J),!0}function K(){const J=d(e.modelValue)===!0&&L()&&r[a.value];return e.keepAlive===!0?[h(KeepAlive,B.value,[h(S.value===!0?t(y.value,()=>({...PanelWrapper,name:y.value})):PanelWrapper,{key:y.value,style:p.value},()=>J)])]:[h("div",{class:"q-panel scroll",style:p.value,key:y.value,role:"tabpanel"},[J])]}function le(){if(r.length!==0)return e.animated===!0?[h(Transition,{name:u.value},K)]:K()}function de(J){return r=getNormalizedVNodes(hSlot(J.default,[])).filter(oe=>oe.props!==null&&oe.props.slot===void 0&&d(oe.props.name)===!0),r.length}function ce(){return r}return Object.assign(l,{next:M,previous:D,goTo:C}),{panelIndex:a,panelDirectives:s,updatePanelsList:de,updatePanelIndex:L,getPanelContent:le,getEnabledPanels:H,getPanels:ce,isValidPanelName:d,keepAliveProps:B,needsUniqueKeepAliveWrapper:S,goToPanelByOffset:F,goToPanel:C,nextPanel:M,previousPanel:D}}const QTabPanels=createComponent({name:"QTabPanels",props:{...usePanelProps,...useDarkProps},emits:usePanelEmits,setup(e,{slots:n}){const l=getCurrentInstance(),t=useDark(e,l.proxy.$q),{updatePanelsList:o,getPanelContent:r,panelDirectives:i}=usePanel(),a=computed(()=>"q-tab-panels q-panel-parent"+(t.value===!0?" q-tab-panels--dark q-dark":""));return()=>(o(n),hDir("div",{class:a.value},r(),"pan",e.swipeable,()=>i.value))}}),QTabPanel=createComponent({name:"QTabPanel",props:usePanelChildProps,setup(e,{slots:n}){return()=>h("div",{class:"q-tab-panel",role:"tabpanel"},hSlot(n.default))}}),reRGBA=/^rgb(a)?\((\d{1,3}),(\d{1,3}),(\d{1,3}),?([01]?\.?\d*?)?\)$/;function rgbToHex({r:e,g:n,b:l,a:t}){const o=t!==void 0;if(e=Math.round(e),n=Math.round(n),l=Math.round(l),e>255||n>255||l>255||o&&t>100)throw new TypeError("Expected 3 numbers below 256 (and optionally one below 100)");return t=o?(Math.round(255*t/100)|256).toString(16).slice(1):"","#"+(l|n<<8|e<<16|1<<24).toString(16).slice(1)+t}function rgbToString({r:e,g:n,b:l,a:t}){return`rgb${t!==void 0?"a":""}(${e},${n},${l}${t!==void 0?","+t/100:""})`}function hexToRgb(e){if(typeof e!="string")throw new TypeError("Expected a string");e=e.replace(/^#/,""),e.length===3?e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]:e.length===4&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]+e[3]+e[3]);const n=parseInt(e,16);return e.length>6?{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:Math.round((n&255)/2.55)}:{r:n>>16,g:n>>8&255,b:n&255}}function hsvToRgb({h:e,s:n,v:l,a:t}){let o,r,i;n=n/100,l=l/100,e=e/360;const a=Math.floor(e*6),u=e*6-a,f=l*(1-n),s=l*(1-u*n),b=l*(1-(1-u)*n);switch(a%6){case 0:o=l,r=b,i=f;break;case 1:o=s,r=l,i=f;break;case 2:o=f,r=l,i=b;break;case 3:o=f,r=s,i=l;break;case 4:o=b,r=f,i=l;break;case 5:o=l,r=f,i=s;break}return{r:Math.round(o*255),g:Math.round(r*255),b:Math.round(i*255),a:t}}function rgbToHsv({r:e,g:n,b:l,a:t}){const o=Math.max(e,n,l),r=Math.min(e,n,l),i=o-r,a=o===0?0:i/o,u=o/255;let f;switch(o){case r:f=0;break;case e:f=n-l+i*(n<l?6:0),f/=6*i;break;case n:f=l-e+i*2,f/=6*i;break;case l:f=e-n+i*4,f/=6*i;break}return{h:Math.round(f*360),s:Math.round(a*100),v:Math.round(u*100),a:t}}function textToRgb(e){if(typeof e!="string")throw new TypeError("Expected a string");const n=e.replace(/ /g,""),l=reRGBA.exec(n);if(l===null)return hexToRgb(n);const t={r:Math.min(255,parseInt(l[2],10)),g:Math.min(255,parseInt(l[3],10)),b:Math.min(255,parseInt(l[4],10))};if(l[1]){const o=parseFloat(l[5]);t.a=Math.min(1,isNaN(o)===!0?1:o)*100}return t}function luminosity(e){if(typeof e!="string"&&(!e||e.r===void 0))throw new TypeError("Expected a string or a {r, g, b} object as color");const n=typeof e=="string"?textToRgb(e):e,l=n.r/255,t=n.g/255,o=n.b/255,r=l<=.03928?l/12.92:Math.pow((l+.055)/1.055,2.4),i=t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4),a=o<=.03928?o/12.92:Math.pow((o+.055)/1.055,2.4);return .2126*r+.7152*i+.0722*a}const palette=["rgb(255,204,204)","rgb(255,230,204)","rgb(255,255,204)","rgb(204,255,204)","rgb(204,255,230)","rgb(204,255,255)","rgb(204,230,255)","rgb(204,204,255)","rgb(230,204,255)","rgb(255,204,255)","rgb(255,153,153)","rgb(255,204,153)","rgb(255,255,153)","rgb(153,255,153)","rgb(153,255,204)","rgb(153,255,255)","rgb(153,204,255)","rgb(153,153,255)","rgb(204,153,255)","rgb(255,153,255)","rgb(255,102,102)","rgb(255,179,102)","rgb(255,255,102)","rgb(102,255,102)","rgb(102,255,179)","rgb(102,255,255)","rgb(102,179,255)","rgb(102,102,255)","rgb(179,102,255)","rgb(255,102,255)","rgb(255,51,51)","rgb(255,153,51)","rgb(255,255,51)","rgb(51,255,51)","rgb(51,255,153)","rgb(51,255,255)","rgb(51,153,255)","rgb(51,51,255)","rgb(153,51,255)","rgb(255,51,255)","rgb(255,0,0)","rgb(255,128,0)","rgb(255,255,0)","rgb(0,255,0)","rgb(0,255,128)","rgb(0,255,255)","rgb(0,128,255)","rgb(0,0,255)","rgb(128,0,255)","rgb(255,0,255)","rgb(245,0,0)","rgb(245,123,0)","rgb(245,245,0)","rgb(0,245,0)","rgb(0,245,123)","rgb(0,245,245)","rgb(0,123,245)","rgb(0,0,245)","rgb(123,0,245)","rgb(245,0,245)","rgb(214,0,0)","rgb(214,108,0)","rgb(214,214,0)","rgb(0,214,0)","rgb(0,214,108)","rgb(0,214,214)","rgb(0,108,214)","rgb(0,0,214)","rgb(108,0,214)","rgb(214,0,214)","rgb(163,0,0)","rgb(163,82,0)","rgb(163,163,0)","rgb(0,163,0)","rgb(0,163,82)","rgb(0,163,163)","rgb(0,82,163)","rgb(0,0,163)","rgb(82,0,163)","rgb(163,0,163)","rgb(92,0,0)","rgb(92,46,0)","rgb(92,92,0)","rgb(0,92,0)","rgb(0,92,46)","rgb(0,92,92)","rgb(0,46,92)","rgb(0,0,92)","rgb(46,0,92)","rgb(92,0,92)","rgb(255,255,255)","rgb(205,205,205)","rgb(178,178,178)","rgb(153,153,153)","rgb(127,127,127)","rgb(102,102,102)","rgb(76,76,76)","rgb(51,51,51)","rgb(25,25,25)","rgb(0,0,0)"],thumbPath="M5 5 h10 v10 h-10 v-10 z",alphaTrackImg="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAH0lEQVQoU2NkYGAwZkAFZ5G5jPRRgOYEVDeB3EBjBQBOZwTVugIGyAAAAABJRU5ErkJggg==",QColor=createComponent({name:"QColor",props:{...useDarkProps,...useFormProps,modelValue:String,defaultValue:String,defaultView:{type:String,default:"spectrum",validator:e=>["spectrum","tune","palette"].includes(e)},formatModel:{type:String,default:"auto",validator:e=>["auto","hex","rgb","hexa","rgba"].includes(e)},palette:Array,noHeader:Boolean,noHeaderTabs:Boolean,noFooter:Boolean,square:Boolean,flat:Boolean,bordered:Boolean,disable:Boolean,readonly:Boolean},emits:["update:modelValue","change"],setup(e,{emit:n}){const{proxy:l}=getCurrentInstance(),{$q:t}=l,o=useDark(e,t),{getCache:r}=useRenderCache(),i=ref(null),a=ref(null),u=computed(()=>e.formatModel==="auto"?null:e.formatModel.indexOf("hex")!==-1),f=computed(()=>e.formatModel==="auto"?null:e.formatModel.indexOf("a")!==-1),s=ref(e.formatModel==="auto"?e.modelValue===void 0||e.modelValue===null||e.modelValue===""||e.modelValue.startsWith("#")?"hex":"rgb":e.formatModel.startsWith("hex")?"hex":"rgb"),b=ref(e.defaultView),m=ref(de(e.modelValue||e.defaultValue)),p=computed(()=>e.disable!==!0&&e.readonly!==!0),y=computed(()=>e.modelValue===void 0||e.modelValue===null||e.modelValue===""||e.modelValue.startsWith("#")),B=computed(()=>u.value!==null?u.value:y.value),S=computed(()=>({type:"hidden",name:e.name,value:m.value[B.value===!0?"hex":"rgb"]})),M=useFormInject(S),D=computed(()=>f.value!==null?f.value:m.value.a!==void 0),C=computed(()=>({backgroundColor:m.value.rgb||"#000"})),d=computed(()=>`q-color-picker__header-content q-color-picker__header-content--${(m.value.a!==void 0&&m.value.a<65?!0:luminosity(m.value)>.4)?"light":"dark"}`),I=computed(()=>({background:`hsl(${m.value.h},100%,50%)`})),H=computed(()=>({top:`${100-m.value.v}%`,[t.lang.rtl===!0?"right":"left"]:`${m.value.s}%`})),E=computed(()=>e.palette!==void 0&&e.palette.length!==0?e.palette:palette),F=computed(()=>"q-color-picker"+(e.bordered===!0?" q-color-picker--bordered":"")+(e.square===!0?" q-color-picker--square no-border-radius":"")+(e.flat===!0?" q-color-picker--flat no-shadow":"")+(e.disable===!0?" disabled":"")+(o.value===!0?" q-color-picker--dark q-dark":"")),L=computed(()=>e.disable===!0?{"aria-disabled":"true"}:{}),K=computed(()=>[[TouchPan,Ve,void 0,{prevent:!0,stop:!0,mouse:!0}]]);watch(()=>e.modelValue,T=>{const Y=de(T||e.defaultValue);Y.hex!==m.value.hex&&(m.value=Y)}),watch(()=>e.defaultValue,T=>{if(!e.modelValue&&T){const Y=de(T);Y.hex!==m.value.hex&&(m.value=Y)}});function le(T,Y){m.value.hex=rgbToHex(T),m.value.rgb=rgbToString(T),m.value.r=T.r,m.value.g=T.g,m.value.b=T.b,m.value.a=T.a;const ie=m.value[B.value===!0?"hex":"rgb"];n("update:modelValue",ie),Y===!0&&n("change",ie)}function de(T){const Y=f.value!==void 0?f.value:e.formatModel==="auto"?null:e.formatModel.indexOf("a")!==-1;if(typeof T!="string"||T.length===0||testPattern.anyColor(T.replace(/ /g,""))!==!0)return{h:0,s:0,v:0,r:0,g:0,b:0,a:Y===!0?100:void 0,hex:void 0,rgb:void 0};const ie=textToRgb(T);return Y===!0&&ie.a===void 0&&(ie.a=100),ie.hex=rgbToHex(ie),ie.rgb=rgbToString(ie),Object.assign(ie,rgbToHsv(ie))}function ce(T,Y,ie){const re=i.value;if(re===null)return;const ye=re.clientWidth,Z=re.clientHeight,qe=re.getBoundingClientRect();let _e=Math.min(ye,Math.max(0,T-qe.left));t.lang.rtl===!0&&(_e=ye-_e);const De=Math.min(Z,Math.max(0,Y-qe.top)),Ie=Math.round(100*_e/ye),Be=Math.round(100*Math.max(0,Math.min(1,-(De/Z)+1))),Re=hsvToRgb({h:m.value.h,s:Ie,v:Be,a:D.value===!0?m.value.a:void 0});m.value.s=Ie,m.value.v=Be,le(Re,ie)}function J(T,Y){const ie=Math.round(T),re=hsvToRgb({h:ie,s:m.value.s,v:m.value.v,a:D.value===!0?m.value.a:void 0});m.value.h=ie,le(re,Y)}function oe(T){J(T,!0)}function ee(T,Y,ie,re,ye){if(re!==void 0&&stop(re),!/^[0-9]+$/.test(T)){ye===!0&&l.$forceUpdate();return}const Z=Math.floor(Number(T));if(Z<0||Z>ie){ye===!0&&l.$forceUpdate();return}const qe={r:Y==="r"?Z:m.value.r,g:Y==="g"?Z:m.value.g,b:Y==="b"?Z:m.value.b,a:D.value===!0?Y==="a"?Z:m.value.a:void 0};if(Y!=="a"){const _e=rgbToHsv(qe);m.value.h=_e.h,m.value.s=_e.s,m.value.v=_e.v}if(le(qe,ye),re!==void 0&&ye!==!0&&re.target.selectionEnd!==void 0){const _e=re.target.selectionEnd;nextTick(()=>{re.target.setSelectionRange(_e,_e)})}}function he(T,Y){let ie;const re=T.target.value;if(stop(T),s.value==="hex"){if(re.length!==(D.value===!0?9:7)||!/^#[0-9A-Fa-f]+$/.test(re))return!0;ie=hexToRgb(re)}else{let Z;if(re.endsWith(")"))if(D.value!==!0&&re.startsWith("rgb(")){if(Z=re.substring(4,re.length-1).split(",").map(qe=>parseInt(qe,10)),Z.length!==3||!/^rgb\([0-9]{1,3},[0-9]{1,3},[0-9]{1,3}\)$/.test(re))return!0}else if(D.value===!0&&re.startsWith("rgba(")){if(Z=re.substring(5,re.length-1).split(","),Z.length!==4||!/^rgba\([0-9]{1,3},[0-9]{1,3},[0-9]{1,3},(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/.test(re))return!0;for(let _e=0;_e<3;_e++){const De=parseInt(Z[_e],10);if(De<0||De>255)return!0;Z[_e]=De}const qe=parseFloat(Z[3]);if(qe<0||qe>1)return!0;Z[3]=qe}else return!0;else return!0;if(Z[0]<0||Z[0]>255||Z[1]<0||Z[1]>255||Z[2]<0||Z[2]>255||D.value===!0&&(Z[3]<0||Z[3]>1))return!0;ie={r:Z[0],g:Z[1],b:Z[2],a:D.value===!0?Z[3]*100:void 0}}const ye=rgbToHsv(ie);if(m.value.h=ye.h,m.value.s=ye.s,m.value.v=ye.v,le(ie,Y),Y!==!0){const Z=T.target.selectionEnd;nextTick(()=>{T.target.setSelectionRange(Z,Z)})}}function xe(T){const Y=de(T),ie={r:Y.r,g:Y.g,b:Y.b,a:Y.a};ie.a===void 0&&(ie.a=m.value.a),m.value.h=Y.h,m.value.s=Y.s,m.value.v=Y.v,le(ie,!0)}function Ve(T){T.isFinal?ce(T.position.left,T.position.top,!0):Fe(T)}const Fe=throttle(T=>{ce(T.position.left,T.position.top)},20);function x(T){ce(T.pageX-window.pageXOffset,T.pageY-window.pageYOffset,!0)}function N(T){ce(T.pageX-window.pageXOffset,T.pageY-window.pageYOffset)}function te(T){a.value!==null&&(a.value.$el.style.opacity=T?1:0)}function ve(T){s.value=T}function ke(){const T=[];return e.noHeaderTabs!==!0&&T.push(h(QTabs,{class:"q-color-picker__header-tabs",modelValue:s.value,dense:!0,align:"justify","onUpdate:modelValue":ve},()=>[h(QTab,{label:"HEX"+(D.value===!0?"A":""),name:"hex",ripple:!1}),h(QTab,{label:"RGB"+(D.value===!0?"A":""),name:"rgb",ripple:!1})])),T.push(h("div",{class:"q-color-picker__header-banner row flex-center no-wrap"},[h("input",{class:"fit",value:m.value[s.value],...p.value!==!0?{readonly:!0}:{},...r("topIn",{onInput:Y=>{te(he(Y)===!0)},onChange:stop,onBlur:Y=>{he(Y,!0)===!0&&l.$forceUpdate(),te(!1)}})}),h(QIcon,{ref:a,class:"q-color-picker__error-icon absolute no-pointer-events",name:t.iconSet.type.negative})])),h("div",{class:"q-color-picker__header relative-position overflow-hidden"},[h("div",{class:"q-color-picker__header-bg absolute-full"}),h("div",{class:d.value,style:C.value},T)])}function w(){return h(QTabPanels,{modelValue:b.value,animated:!0},()=>[h(QTabPanel,{class:"q-color-picker__spectrum-tab overflow-hidden",name:"spectrum"},Q),h(QTabPanel,{class:"q-pa-md q-color-picker__tune-tab",name:"tune"},be),h(QTabPanel,{class:"q-color-picker__palette-tab",name:"palette"},fe)])}function P(T){b.value=T}function G(){return h("div",{class:"q-color-picker__footer relative-position overflow-hidden"},[h(QTabs,{class:"absolute-full",modelValue:b.value,dense:!0,align:"justify","onUpdate:modelValue":P},()=>[h(QTab,{icon:t.iconSet.colorPicker.spectrum,name:"spectrum",ripple:!1}),h(QTab,{icon:t.iconSet.colorPicker.tune,name:"tune",ripple:!1}),h(QTab,{icon:t.iconSet.colorPicker.palette,name:"palette",ripple:!1})])])}function Q(){const T={ref:i,class:"q-color-picker__spectrum non-selectable relative-position cursor-pointer"+(p.value!==!0?" readonly":""),style:I.value,...p.value===!0?{onClick:x,onMousedown:N}:{}},Y=[h("div",{style:{paddingBottom:"100%"}}),h("div",{class:"q-color-picker__spectrum-white absolute-full"}),h("div",{class:"q-color-picker__spectrum-black absolute-full"}),h("div",{class:"absolute",style:H.value},[m.value.hex!==void 0?h("div",{class:"q-color-picker__spectrum-circle"}):null])],ie=[h(QSlider,{class:"q-color-picker__hue non-selectable",modelValue:m.value.h,min:0,max:360,trackSize:"8px",innerTrackColor:"transparent",selectionColor:"transparent",readonly:p.value!==!0,thumbPath,"onUpdate:modelValue":J,onChange:oe})];return D.value===!0&&ie.push(h(QSlider,{class:"q-color-picker__alpha non-selectable",modelValue:m.value.a,min:0,max:100,trackSize:"8px",trackColor:"white",innerTrackColor:"transparent",selectionColor:"transparent",trackImg:alphaTrackImg,readonly:p.value!==!0,hideSelection:!0,thumbPath,...r("alphaSlide",{"onUpdate:modelValue":re=>ee(re,"a",100),onChange:re=>ee(re,"a",100,void 0,!0)})})),[hDir("div",T,Y,"spec",p.value,()=>K.value),h("div",{class:"q-color-picker__sliders"},ie)]}function be(){return[h("div",{class:"row items-center no-wrap"},[h("div","R"),h(QSlider,{modelValue:m.value.r,min:0,max:255,color:"red",dark:o.value,readonly:p.value!==!0,...r("rSlide",{"onUpdate:modelValue":T=>ee(T,"r",255),onChange:T=>ee(T,"r",255,void 0,!0)})}),h("input",{value:m.value.r,maxlength:3,readonly:p.value!==!0,onChange:stop,...r("rIn",{onInput:T=>ee(T.target.value,"r",255,T),onBlur:T=>ee(T.target.value,"r",255,T,!0)})})]),h("div",{class:"row items-center no-wrap"},[h("div","G"),h(QSlider,{modelValue:m.value.g,min:0,max:255,color:"green",dark:o.value,readonly:p.value!==!0,...r("gSlide",{"onUpdate:modelValue":T=>ee(T,"g",255),onChange:T=>ee(T,"g",255,void 0,!0)})}),h("input",{value:m.value.g,maxlength:3,readonly:p.value!==!0,onChange:stop,...r("gIn",{onInput:T=>ee(T.target.value,"g",255,T),onBlur:T=>ee(T.target.value,"g",255,T,!0)})})]),h("div",{class:"row items-center no-wrap"},[h("div","B"),h(QSlider,{modelValue:m.value.b,min:0,max:255,color:"blue",readonly:p.value!==!0,dark:o.value,...r("bSlide",{"onUpdate:modelValue":T=>ee(T,"b",255),onChange:T=>ee(T,"b",255,void 0,!0)})}),h("input",{value:m.value.b,maxlength:3,readonly:p.value!==!0,onChange:stop,...r("bIn",{onInput:T=>ee(T.target.value,"b",255,T),onBlur:T=>ee(T.target.value,"b",255,T,!0)})})]),D.value===!0?h("div",{class:"row items-center no-wrap"},[h("div","A"),h(QSlider,{modelValue:m.value.a,color:"grey",readonly:p.value!==!0,dark:o.value,...r("aSlide",{"onUpdate:modelValue":T=>ee(T,"a",100),onChange:T=>ee(T,"a",100,void 0,!0)})}),h("input",{value:m.value.a,maxlength:3,readonly:p.value!==!0,onChange:stop,...r("aIn",{onInput:T=>ee(T.target.value,"a",100,T),onBlur:T=>ee(T.target.value,"a",100,T,!0)})})]):null]}function fe(){const T=Y=>h("div",{class:"q-color-picker__cube col-auto",style:{backgroundColor:Y},...p.value===!0?r("palette#"+Y,{onClick:()=>{xe(Y)}}):{}});return[h("div",{class:"row items-center q-color-picker__palette-rows"+(p.value===!0?" q-color-picker__palette-rows--editable":"")},E.value.map(T))]}return()=>{const T=[w()];return e.name!==void 0&&e.disable!==!0&&M(T,"push"),e.noHeader!==!0&&T.unshift(ke()),e.noFooter!==!0&&T.push(G()),h("div",{class:F.value,...L.value},T)}}});function getBlockElement(e,n){if(n&&e===n)return null;const l=e.nodeName.toLowerCase();if(["div","li","ul","ol","blockquote"].includes(l)===!0)return e;const t=window.getComputedStyle?window.getComputedStyle(e):e.currentStyle,o=t.display;return o==="block"||o==="table"?e:getBlockElement(e.parentNode)}function isChildOf(e,n,l){return!e||e===document.body?!1:l===!0&&e===n||(n===document?document.body:n).contains(e.parentNode)}function createRange(e,n,l){if(l||(l=document.createRange(),l.selectNode(e),l.setStart(e,0)),n.count===0)l.setEnd(e,n.count);else if(n.count>0)if(e.nodeType===Node.TEXT_NODE)e.textContent.length<n.count?n.count-=e.textContent.length:(l.setEnd(e,n.count),n.count=0);else for(let t=0;n.count!==0&&t<e.childNodes.length;t++)l=createRange(e.childNodes[t],n,l);return l}const urlRegex=/^https?:\/\//;class Caret{constructor(n,l){this.el=n,this.eVm=l,this._range=null}get selection(){if(this.el){const n=document.getSelection();if(isChildOf(n.anchorNode,this.el,!0)&&isChildOf(n.focusNode,this.el,!0))return n}return null}get hasSelection(){return this.selection!==null?this.selection.toString().length!==0:!1}get range(){const n=this.selection;return n!==null&&n.rangeCount?n.getRangeAt(0):this._range}get parent(){const n=this.range;if(n!==null){const l=n.startContainer;return l.nodeType===document.ELEMENT_NODE?l:l.parentNode}return null}get blockParent(){const n=this.parent;return n!==null?getBlockElement(n,this.el):null}save(n=this.range){n!==null&&(this._range=n)}restore(n=this._range){const l=document.createRange(),t=document.getSelection();n!==null?(l.setStart(n.startContainer,n.startOffset),l.setEnd(n.endContainer,n.endOffset),t.removeAllRanges(),t.addRange(l)):(t.selectAllChildren(this.el),t.collapseToEnd())}savePosition(){let n=-1,l;const t=document.getSelection(),o=this.el.parentNode;if(t.focusNode&&isChildOf(t.focusNode,o))for(l=t.focusNode,n=t.focusOffset;l&&l!==o;)l!==this.el&&l.previousSibling?(l=l.previousSibling,n+=l.textContent.length):l=l.parentNode;this.savedPos=n}restorePosition(n=0){if(this.savedPos>0&&this.savedPos<n){const l=window.getSelection(),t=createRange(this.el,{count:this.savedPos});t&&(t.collapse(!1),l.removeAllRanges(),l.addRange(t))}}hasParent(n,l){const t=l?this.parent:this.blockParent;return t!==null?t.nodeName.toLowerCase()===n.toLowerCase():!1}hasParents(n,l,t=this.parent){return t===null?!1:n.includes(t.nodeName.toLowerCase())===!0?!0:l===!0?this.hasParents(n,l,t.parentNode):!1}is(n,l){if(this.selection===null)return!1;switch(n){case"formatBlock":return l==="DIV"&&this.parent===this.el||this.hasParent(l,l==="PRE");case"link":return this.hasParent("A",!0);case"fontSize":return document.queryCommandValue(n)===l;case"fontName":const t=document.queryCommandValue(n);return t===`"${l}"`||t===l;case"fullscreen":return this.eVm.inFullscreen.value;case"viewsource":return this.eVm.isViewingSource.value;case void 0:return!1;default:const o=document.queryCommandState(n);return l!==void 0?o===l:o}}getParentAttribute(n){return this.parent!==null?this.parent.getAttribute(n):null}can(n){if(n==="outdent")return this.hasParents(["blockquote","li"],!0);if(n==="indent")return this.hasParents(["li"],!0);if(n==="link")return this.selection!==null||this.is("link")}apply(n,l,t=noop){if(n==="formatBlock")["BLOCKQUOTE","H1","H2","H3","H4","H5","H6"].includes(l)&&this.is(n,l)&&(n="outdent",l=null),l==="PRE"&&this.is(n,"PRE")&&(l="P");else if(n==="print"){t();const o=window.open();o.document.write(`
        <!doctype html>
        <html>
          <head>
            <title>Print - ${document.title}</title>
          </head>
          <body>
            <div>${this.el.innerHTML}</div>
          </body>
        </html>
      `),o.print(),o.close();return}else if(n==="link"){const o=this.getParentAttribute("href");if(o===null){const r=this.selectWord(this.selection),i=r?r.toString():"";if(!i.length&&(!this.range||!this.range.cloneContents().querySelector("img")))return;this.eVm.editLinkUrl.value=urlRegex.test(i)?i:"https://",document.execCommand("createLink",!1,this.eVm.editLinkUrl.value),this.save(r.getRangeAt(0))}else this.eVm.editLinkUrl.value=o,this.range.selectNodeContents(this.parent),this.save();return}else if(n==="fullscreen"){this.eVm.toggleFullscreen(),t();return}else if(n==="viewsource"){this.eVm.isViewingSource.value=this.eVm.isViewingSource.value===!1,this.eVm.setContent(this.eVm.props.modelValue),t();return}document.execCommand(n,!1,l),t()}selectWord(n){if(n===null||n.isCollapsed!==!0||n.modify===void 0)return n;const l=document.createRange();l.setStart(n.anchorNode,n.anchorOffset),l.setEnd(n.focusNode,n.focusOffset);const t=l.collapsed?["backward","forward"]:["forward","backward"];l.detach();const o=n.focusNode,r=n.focusOffset;return n.collapse(n.anchorNode,n.anchorOffset),n.modify("move",t[0],"character"),n.modify("move",t[1],"word"),n.extend(o,r),n.modify("extend",t[1],"character"),n.modify("extend",t[0],"word"),n}}const QBtnGroup=createComponent({name:"QBtnGroup",props:{unelevated:Boolean,outline:Boolean,flat:Boolean,rounded:Boolean,square:Boolean,push:Boolean,stretch:Boolean,glossy:Boolean,spread:Boolean},setup(e,{slots:n}){const l=computed(()=>{const t=["unelevated","outline","flat","rounded","square","push","stretch","glossy"].filter(o=>e[o]===!0).map(o=>`q-btn-group--${o}`).join(" ");return`q-btn-group row no-wrap${t.length!==0?" "+t:""}`+(e.spread===!0?" q-btn-group--spread":" inline")});return()=>h("div",{class:l.value},hSlot(n.default))}}),btnPropsList=Object.keys(nonRoundBtnProps);function passBtnProps(e){return btnPropsList.reduce((n,l)=>{const t=e[l];return t!==void 0&&(n[l]=t),n},{})}const QBtnDropdown=createComponent({name:"QBtnDropdown",props:{...nonRoundBtnProps,...useTransitionProps,modelValue:Boolean,split:Boolean,dropdownIcon:String,contentClass:[Array,String,Object],contentStyle:[Array,String,Object],cover:Boolean,persistent:Boolean,noRouteDismiss:Boolean,autoClose:Boolean,menuAnchor:{type:String,default:"bottom end"},menuSelf:{type:String,default:"top end"},menuOffset:Array,disableMainBtn:Boolean,disableDropdown:Boolean,noIconAnimation:Boolean,toggleAriaLabel:String},emits:["update:modelValue","click","beforeShow","show","beforeHide","hide"],setup(e,{slots:n,emit:l}){const{proxy:t}=getCurrentInstance(),o=ref(e.modelValue),r=ref(null),i=useId(),a=computed(()=>{const d={"aria-expanded":o.value===!0?"true":"false","aria-haspopup":"true","aria-controls":i.value,"aria-label":e.toggleAriaLabel||t.$q.lang.label[o.value===!0?"collapse":"expand"](e.label)};return(e.disable===!0||e.split===!1&&e.disableMainBtn===!0||e.disableDropdown===!0)&&(d["aria-disabled"]="true"),d}),u=computed(()=>"q-btn-dropdown__arrow"+(o.value===!0&&e.noIconAnimation===!1?" rotate-180":"")+(e.split===!1?" q-btn-dropdown__arrow-container":"")),f=computed(()=>getBtnDesignAttr(e)),s=computed(()=>passBtnProps(e));watch(()=>e.modelValue,d=>{r.value!==null&&r.value[d?"show":"hide"]()}),watch(()=>e.split,C);function b(d){o.value=!0,l("beforeShow",d)}function m(d){l("show",d),l("update:modelValue",!0)}function p(d){o.value=!1,l("beforeHide",d)}function y(d){l("hide",d),l("update:modelValue",!1)}function B(d){l("click",d)}function S(d){stop(d),C(),l("click",d)}function M(d){r.value!==null&&r.value.toggle(d)}function D(d){r.value!==null&&r.value.show(d)}function C(d){r.value!==null&&r.value.hide(d)}return Object.assign(t,{show:D,hide:C,toggle:M}),onMounted(()=>{e.modelValue===!0&&D()}),()=>{const d=[h(QIcon,{class:u.value,name:e.dropdownIcon||t.$q.iconSet.arrow.dropdown})];return e.disableDropdown!==!0&&d.push(h(QMenu,{ref:r,id:i.value,class:e.contentClass,style:e.contentStyle,cover:e.cover,fit:!0,persistent:e.persistent,noRouteDismiss:e.noRouteDismiss,autoClose:e.autoClose,anchor:e.menuAnchor,self:e.menuSelf,offset:e.menuOffset,separateClosePopup:!0,transitionShow:e.transitionShow,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,onBeforeShow:b,onShow:m,onBeforeHide:p,onHide:y},n.default)),e.split===!1?h(QBtn,{class:"q-btn-dropdown q-btn-dropdown--simple",...s.value,...a.value,disable:e.disable===!0||e.disableMainBtn===!0,noWrap:!0,round:!1,onClick:B},{default:()=>hSlot(n.label,[]).concat(d),loading:n.loading}):h(QBtnGroup,{class:"q-btn-dropdown q-btn-dropdown--split no-wrap q-btn-item",rounded:e.rounded,square:e.square,...f.value,glossy:e.glossy,stretch:e.stretch},()=>[h(QBtn,{class:"q-btn-dropdown--current",...s.value,disable:e.disable===!0||e.disableMainBtn===!0,noWrap:!0,round:!1,onClick:S},{default:n.label,loading:n.loading}),h(QBtn,{class:"q-btn-dropdown__arrow-container q-anchor--skip",...a.value,...f.value,disable:e.disable===!0||e.disableDropdown===!0,rounded:e.rounded,color:e.color,textColor:e.textColor,dense:e.dense,size:e.size,padding:e.padding,ripple:e.ripple},()=>d)])}}});function run(e,n,l){n.handler?n.handler(e,l,l.caret):l.runCmd(n.cmd,n.param)}function getGroup(e){return h("div",{class:"q-editor__toolbar-group"},e)}function getBtn(e,n,l,t=!1){const o=t||(n.type==="toggle"?n.toggled?n.toggled(e):n.cmd&&e.caret.is(n.cmd,n.param):!1),r=[];if(n.tip&&e.$q.platform.is.desktop){const i=n.key?h("div",[h("small",`(CTRL + ${String.fromCharCode(n.key)})`)]):null;r.push(h(QTooltip,{delay:1e3},()=>[h("div",{innerHTML:n.tip}),i]))}return h(QBtn,{...e.buttonProps.value,icon:n.icon!==null?n.icon:void 0,color:o?n.toggleColor||e.props.toolbarToggleColor:n.color||e.props.toolbarColor,textColor:o&&!e.props.toolbarPush?null:n.textColor||e.props.toolbarTextColor,label:n.label,disable:n.disable?typeof n.disable=="function"?n.disable(e):!0:!1,size:"sm",onClick(i){l&&l(),run(i,n,e)}},()=>r)}function getDropdown(e,n){const l=n.list==="only-icons";let t=n.label,o=n.icon!==null?n.icon:void 0,r,i;function a(){f.component.proxy.hide()}if(l)i=n.options.map(s=>{const b=s.type===void 0?e.caret.is(s.cmd,s.param):!1;return b&&(t=s.tip,o=s.icon!==null?s.icon:void 0),getBtn(e,s,a,b)}),r=e.toolbarBackgroundClass.value,i=[getGroup(i)];else{const s=e.props.toolbarToggleColor!==void 0?`text-${e.props.toolbarToggleColor}`:null,b=e.props.toolbarTextColor!==void 0?`text-${e.props.toolbarTextColor}`:null,m=n.list==="no-icons";i=n.options.map(p=>{const y=p.disable?p.disable(e):!1,B=p.type===void 0?e.caret.is(p.cmd,p.param):!1;B&&(t=p.tip,o=p.icon!==null?p.icon:void 0);const S=p.htmlTip;return h(QItem,{active:B,activeClass:s,clickable:!0,disable:y,dense:!0,onClick(M){a(),e.contentRef.value!==null&&e.contentRef.value.focus(),e.caret.restore(),run(M,p,e)}},()=>[m===!0?null:h(QItemSection,{class:B?s:b,side:!0},()=>h(QIcon,{name:p.icon!==null?p.icon:void 0})),h(QItemSection,S?()=>h("div",{class:"text-no-wrap",innerHTML:p.htmlTip}):p.tip?()=>h("div",{class:"text-no-wrap"},p.tip):void 0)])}),r=[e.toolbarBackgroundClass.value,b]}const u=n.highlight&&t!==n.label,f=h(QBtnDropdown,{...e.buttonProps.value,noCaps:!0,noWrap:!0,color:u?e.props.toolbarToggleColor:e.props.toolbarColor,textColor:u&&!e.props.toolbarPush?null:e.props.toolbarTextColor,label:n.fixedLabel?n.label:t,icon:n.fixedIcon?n.icon!==null?n.icon:void 0:o,contentClass:r,onShow:s=>e.emit("dropdownShow",s),onHide:s=>e.emit("dropdownHide",s),onBeforeShow:s=>e.emit("dropdownBeforeShow",s),onBeforeHide:s=>e.emit("dropdownBeforeHide",s)},()=>i);return f}function getToolbar(e){if(e.caret)return e.buttons.value.filter(n=>!e.isViewingSource.value||n.find(l=>l.cmd==="viewsource")).map(n=>getGroup(n.map(l=>e.isViewingSource.value&&l.cmd!=="viewsource"?!1:l.type==="slot"?hSlot(e.slots[l.slot]):l.type==="dropdown"?getDropdown(e,l):getBtn(e,l))))}function getFonts(e,n,l,t={}){const o=Object.keys(t);if(o.length===0)return{};const r={default_font:{cmd:"fontName",param:e,icon:l,tip:n}};return o.forEach(i=>{const a=t[i];r[i]={cmd:"fontName",param:a,icon:l,tip:a,htmlTip:`<font face="${a}">${a}</font>`}}),r}function getLinkEditor(e){if(e.caret){const n=e.props.toolbarColor||e.props.toolbarTextColor;let l=e.editLinkUrl.value;const t=()=>{e.caret.restore(),l!==e.editLinkUrl.value&&document.execCommand("createLink",!1,l===""?" ":l),e.editLinkUrl.value=null};return[h("div",{class:`q-mx-xs text-${n}`},`${e.$q.lang.editor.url}: `),h("input",{key:"qedt_btm_input",class:"col q-editor__link-input",value:l,onInput:o=>{stop(o),l=o.target.value},onKeydown:o=>{if(shouldIgnoreKey(o)!==!0)switch(o.keyCode){case 13:return prevent(o),t();case 27:prevent(o),e.caret.restore(),(!e.editLinkUrl.value||e.editLinkUrl.value==="https://")&&document.execCommand("unlink"),e.editLinkUrl.value=null;break}}}),getGroup([h(QBtn,{key:"qedt_btm_rem",tabindex:-1,...e.buttonProps.value,label:e.$q.lang.label.remove,noCaps:!0,onClick:()=>{e.caret.restore(),document.execCommand("unlink"),e.editLinkUrl.value=null}}),h(QBtn,{key:"qedt_btm_upd",...e.buttonProps.value,label:e.$q.lang.label.update,noCaps:!0,onClick:t})])]}}const toString=Object.prototype.toString,hasOwn=Object.prototype.hasOwnProperty,notPlainObject=new Set(["Boolean","Number","String","Function","Array","Date","RegExp"].map(e=>"[object "+e+"]"));function isPlainObject(e){if(e!==Object(e)||notPlainObject.has(toString.call(e))===!0||e.constructor&&hasOwn.call(e,"constructor")===!1&&hasOwn.call(e.constructor.prototype,"isPrototypeOf")===!1)return!1;let n;for(n in e);return n===void 0||hasOwn.call(e,n)}function extend(){let e,n,l,t,o,r,i=arguments[0]||{},a=1,u=!1;const f=arguments.length;for(typeof i=="boolean"&&(u=i,i=arguments[1]||{},a=2),Object(i)!==i&&typeof i!="function"&&(i={}),f===a&&(i=this,a--);a<f;a++)if((e=arguments[a])!==null)for(n in e)l=i[n],t=e[n],i!==t&&(u===!0&&t&&((o=Array.isArray(t))||isPlainObject(t)===!0)?(o===!0?r=Array.isArray(l)===!0?l:[]:r=isPlainObject(l)===!0?l:{},i[n]=extend(u,r,t)):t!==void 0&&(i[n]=t));return i}const QEditor=createComponent({name:"QEditor",props:{...useDarkProps,...useFullscreenProps,modelValue:{type:String,required:!0},readonly:Boolean,disable:Boolean,minHeight:{type:String,default:"10rem"},maxHeight:String,height:String,definitions:Object,fonts:Object,placeholder:String,toolbar:{type:Array,validator:e=>e.length===0||e.every(n=>n.length),default:()=>[["left","center","right","justify"],["bold","italic","underline","strike"],["undo","redo"]]},toolbarColor:String,toolbarBg:String,toolbarTextColor:String,toolbarToggleColor:{type:String,default:"primary"},toolbarOutline:Boolean,toolbarPush:Boolean,toolbarRounded:Boolean,paragraphTag:{type:String,validator:e=>["div","p"].includes(e),default:"div"},contentStyle:Object,contentClass:[Object,Array,String],square:Boolean,flat:Boolean,dense:Boolean},emits:[...useFullscreenEmits,"update:modelValue","keydown","click","focus","blur","dropdownShow","dropdownHide","dropdownBeforeShow","dropdownBeforeHide","linkShow","linkHide"],setup(e,{slots:n,emit:l}){const{proxy:t}=getCurrentInstance(),{$q:o}=t,r=useDark(e,o),{inFullscreen:i,toggleFullscreen:a}=useFullscreen(),u=useSplitAttrs(),f=ref(null),s=ref(null),b=ref(null),m=ref(!1),p=computed(()=>!e.readonly&&!e.disable);let y,B,S=e.modelValue;document.execCommand("defaultParagraphSeparator",!1,e.paragraphTag),y=window.getComputedStyle(document.body).fontFamily;const M=computed(()=>e.toolbarBg?` bg-${e.toolbarBg}`:""),D=computed(()=>({type:"a",flat:e.toolbarOutline!==!0&&e.toolbarPush!==!0,noWrap:!0,outline:e.toolbarOutline,push:e.toolbarPush,rounded:e.toolbarRounded,dense:!0,color:e.toolbarColor,disable:!p.value,size:"sm"})),C=computed(()=>{const w=o.lang.editor,P=o.iconSet.editor;return{bold:{cmd:"bold",icon:P.bold,tip:w.bold,key:66},italic:{cmd:"italic",icon:P.italic,tip:w.italic,key:73},strike:{cmd:"strikeThrough",icon:P.strikethrough,tip:w.strikethrough,key:83},underline:{cmd:"underline",icon:P.underline,tip:w.underline,key:85},unordered:{cmd:"insertUnorderedList",icon:P.unorderedList,tip:w.unorderedList},ordered:{cmd:"insertOrderedList",icon:P.orderedList,tip:w.orderedList},subscript:{cmd:"subscript",icon:P.subscript,tip:w.subscript,htmlTip:"x<subscript>2</subscript>"},superscript:{cmd:"superscript",icon:P.superscript,tip:w.superscript,htmlTip:"x<superscript>2</superscript>"},link:{cmd:"link",disable:G=>G.caret&&!G.caret.can("link"),icon:P.hyperlink,tip:w.hyperlink,key:76},fullscreen:{cmd:"fullscreen",icon:P.toggleFullscreen,tip:w.toggleFullscreen,key:70},viewsource:{cmd:"viewsource",icon:P.viewSource,tip:w.viewSource},quote:{cmd:"formatBlock",param:"BLOCKQUOTE",icon:P.quote,tip:w.quote,key:81},left:{cmd:"justifyLeft",icon:P.left,tip:w.left},center:{cmd:"justifyCenter",icon:P.center,tip:w.center},right:{cmd:"justifyRight",icon:P.right,tip:w.right},justify:{cmd:"justifyFull",icon:P.justify,tip:w.justify},print:{type:"no-state",cmd:"print",icon:P.print,tip:w.print,key:80},outdent:{type:"no-state",disable:G=>G.caret&&!G.caret.can("outdent"),cmd:"outdent",icon:P.outdent,tip:w.outdent},indent:{type:"no-state",disable:G=>G.caret&&!G.caret.can("indent"),cmd:"indent",icon:P.indent,tip:w.indent},removeFormat:{type:"no-state",cmd:"removeFormat",icon:P.removeFormat,tip:w.removeFormat},hr:{type:"no-state",cmd:"insertHorizontalRule",icon:P.hr,tip:w.hr},undo:{type:"no-state",cmd:"undo",icon:P.undo,tip:w.undo,key:90},redo:{type:"no-state",cmd:"redo",icon:P.redo,tip:w.redo,key:89},h1:{cmd:"formatBlock",param:"H1",icon:P.heading1||P.heading,tip:w.heading1,htmlTip:`<h1 class="q-ma-none">${w.heading1}</h1>`},h2:{cmd:"formatBlock",param:"H2",icon:P.heading2||P.heading,tip:w.heading2,htmlTip:`<h2 class="q-ma-none">${w.heading2}</h2>`},h3:{cmd:"formatBlock",param:"H3",icon:P.heading3||P.heading,tip:w.heading3,htmlTip:`<h3 class="q-ma-none">${w.heading3}</h3>`},h4:{cmd:"formatBlock",param:"H4",icon:P.heading4||P.heading,tip:w.heading4,htmlTip:`<h4 class="q-ma-none">${w.heading4}</h4>`},h5:{cmd:"formatBlock",param:"H5",icon:P.heading5||P.heading,tip:w.heading5,htmlTip:`<h5 class="q-ma-none">${w.heading5}</h5>`},h6:{cmd:"formatBlock",param:"H6",icon:P.heading6||P.heading,tip:w.heading6,htmlTip:`<h6 class="q-ma-none">${w.heading6}</h6>`},p:{cmd:"formatBlock",param:e.paragraphTag,icon:P.heading,tip:w.paragraph},code:{cmd:"formatBlock",param:"PRE",icon:P.code,htmlTip:`<code>${w.code}</code>`},"size-1":{cmd:"fontSize",param:"1",icon:P.size1||P.size,tip:w.size1,htmlTip:`<font size="1">${w.size1}</font>`},"size-2":{cmd:"fontSize",param:"2",icon:P.size2||P.size,tip:w.size2,htmlTip:`<font size="2">${w.size2}</font>`},"size-3":{cmd:"fontSize",param:"3",icon:P.size3||P.size,tip:w.size3,htmlTip:`<font size="3">${w.size3}</font>`},"size-4":{cmd:"fontSize",param:"4",icon:P.size4||P.size,tip:w.size4,htmlTip:`<font size="4">${w.size4}</font>`},"size-5":{cmd:"fontSize",param:"5",icon:P.size5||P.size,tip:w.size5,htmlTip:`<font size="5">${w.size5}</font>`},"size-6":{cmd:"fontSize",param:"6",icon:P.size6||P.size,tip:w.size6,htmlTip:`<font size="6">${w.size6}</font>`},"size-7":{cmd:"fontSize",param:"7",icon:P.size7||P.size,tip:w.size7,htmlTip:`<font size="7">${w.size7}</font>`}}}),d=computed(()=>{const w=e.definitions||{},P=e.definitions||e.fonts?extend(!0,{},C.value,w,getFonts(y,o.lang.editor.defaultFont,o.iconSet.editor.font,e.fonts)):C.value;return e.toolbar.map(G=>G.map(Q=>{if(Q.options)return{type:"dropdown",icon:Q.icon,label:Q.label,size:"sm",dense:!0,fixedLabel:Q.fixedLabel,fixedIcon:Q.fixedIcon,highlight:Q.highlight,list:Q.list,options:Q.options.map(fe=>P[fe])};const be=P[Q];return be?be.type==="no-state"||w[Q]&&(be.cmd===void 0||C.value[be.cmd]&&C.value[be.cmd].type==="no-state")?be:Object.assign({type:"toggle"},be):{type:"slot",slot:Q}}))}),I={$q:o,props:e,slots:n,emit:l,inFullscreen:i,toggleFullscreen:a,runCmd:N,isViewingSource:m,editLinkUrl:b,toolbarBackgroundClass:M,buttonProps:D,contentRef:s,buttons:d,setContent:x};watch(()=>e.modelValue,w=>{S!==w&&(S=w,x(w,!0))}),watch(b,w=>{l(`link${w?"Show":"Hide"}`)});const H=computed(()=>e.toolbar&&e.toolbar.length!==0),E=computed(()=>{const w={},P=G=>{G.key&&(w[G.key]={cmd:G.cmd,param:G.param})};return d.value.forEach(G=>{G.forEach(Q=>{Q.options?Q.options.forEach(P):P(Q)})}),w}),F=computed(()=>i.value?e.contentStyle:[{minHeight:e.minHeight,height:e.height,maxHeight:e.maxHeight},e.contentStyle]),L=computed(()=>`q-editor q-editor--${m.value===!0?"source":"default"}`+(e.disable===!0?" disabled":"")+(i.value===!0?" fullscreen column":"")+(e.square===!0?" q-editor--square no-border-radius":"")+(e.flat===!0?" q-editor--flat":"")+(e.dense===!0?" q-editor--dense":"")+(r.value===!0?" q-editor--dark q-dark":"")),K=computed(()=>[e.contentClass,"q-editor__content",{col:i.value,"overflow-auto":i.value||e.maxHeight}]),le=computed(()=>e.disable===!0?{"aria-disabled":"true"}:{});function de(){if(s.value!==null){const w=`inner${m.value===!0?"Text":"HTML"}`,P=s.value[w];P!==e.modelValue&&(S=P,l("update:modelValue",P))}}function ce(w){if(l("keydown",w),w.ctrlKey!==!0||shouldIgnoreKey(w)===!0){te();return}const P=w.keyCode,G=E.value[P];if(G!==void 0){const{cmd:Q,param:be}=G;stopAndPrevent(w),N(Q,be,!1)}}function J(w){te(),l("click",w)}function oe(w){if(s.value!==null){const{scrollTop:P,scrollHeight:G}=s.value;B=G-P}I.caret.save(),l("blur",w)}function ee(w){nextTick(()=>{s.value!==null&&B!==void 0&&(s.value.scrollTop=s.value.scrollHeight-B)}),l("focus",w)}function he(w){const P=f.value;if(P!==null&&P.contains(w.target)===!0&&(w.relatedTarget===null||P.contains(w.relatedTarget)!==!0)){const G=`inner${m.value===!0?"Text":"HTML"}`;I.caret.restorePosition(s.value[G].length),te()}}function xe(w){const P=f.value;P!==null&&P.contains(w.target)===!0&&(w.relatedTarget===null||P.contains(w.relatedTarget)!==!0)&&(I.caret.savePosition(),te())}function Ve(){B=void 0}function Fe(w){I.caret.save()}function x(w,P){if(s.value!==null){P===!0&&I.caret.savePosition();const G=`inner${m.value===!0?"Text":"HTML"}`;s.value[G]=w,P===!0&&(I.caret.restorePosition(s.value[G].length),te())}}function N(w,P,G=!0){ve(),I.caret.restore(),I.caret.apply(w,P,()=>{ve(),I.caret.save(),G&&te()})}function te(){setTimeout(()=>{b.value=null,t.$forceUpdate()},1)}function ve(){addFocusFn(()=>{s.value!==null&&s.value.focus({preventScroll:!0})})}function ke(){return s.value}return onMounted(()=>{I.caret=t.caret=new Caret(s.value,I),x(e.modelValue),te(),document.addEventListener("selectionchange",Fe)}),onBeforeUnmount(()=>{document.removeEventListener("selectionchange",Fe)}),Object.assign(t,{runCmd:N,refreshToolbar:te,focus:ve,getContentEl:ke}),()=>{let w;if(H.value){const P=[h("div",{key:"qedt_top",class:"q-editor__toolbar row no-wrap scroll-x"+M.value},getToolbar(I))];b.value!==null&&P.push(h("div",{key:"qedt_btm",class:"q-editor__toolbar row no-wrap items-center scroll-x"+M.value},getLinkEditor(I))),w=h("div",{key:"toolbar_ctainer",class:"q-editor__toolbars-container"},P)}return h("div",{ref:f,class:L.value,style:{height:i.value===!0?"100%":null},...le.value,onFocusin:he,onFocusout:xe},[w,h("div",{ref:s,style:F.value,class:K.value,contenteditable:p.value,placeholder:e.placeholder,...u.listeners.value,onInput:de,onKeydown:ce,onClick:J,onBlur:oe,onFocus:ee,onMousedown:Ve,onTouchstartPassive:Ve})])}}}),_sfc_main$5=defineComponent({name:"EditorComponents",__name:"editor",props:{modelValue:{type:void 0,default:""},label:{type:String,default:""},readonly:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(e,{expose:n,emit:l}){n();const t=e,o=ref(!1),r=ref(""),i=ref(t.modelValue),a=ref(null);watch(()=>t.modelValue,p=>{i.value=p});const u=l,m={props:t,showColorPicker:o,hex:r,currentValue:i,editorRef:a,emits:u,updateValue:p=>{i.value=p=="<br>"||p==`
`?"":p,u("update:modelValue",i.value)},onUploaded:p=>{i.value+='<img src="'+imageSrc(p)+'" width="100%" alt="" />',u("update:modelValue",i.value)},editTextColor:p=>{const B=window.getSelection()?.toString().trim()||"";i.value=i.value.replace(B,`<span style="color: ${p}">${B}</span>`),o.value=!1,u("update:modelValue",i.value)},UploaderComponents};return Object.defineProperty(m,"__isScriptSetup",{enumerable:!1,value:!0}),m}}),_hoisted_1$4={style:{width:"50px"}},_hoisted_2$3={class:"flex justify-center items-center"};function _sfc_render$5(e,n,l,t,o,r){return openBlock(),createElementBlock("div",null,[createVNode(QEditor,{"min-height":"5rem",readonly:l.readonly,ref:"editorRef",placeholder:l.label,toolbar:[["uploader","colors"],[{label:e.$q.lang.editor.align,icon:e.$q.iconSet.editor.align,fixedLabel:!0,options:["left","center","right","justify"]}],["bold","italic","strike","underline","subscript","superscript"],["hr","link","custom_btn"],[{label:e.$q.lang.editor.fontSize,icon:e.$q.iconSet.editor.fontSize,fixedLabel:!0,fixedIcon:!0,list:"no-icons",options:["size-1","size-2","size-3","size-4","size-5","size-6","size-7"]},{label:e.$q.lang.editor.defaultFont,icon:e.$q.iconSet.editor.font,fixedIcon:!0,list:"no-icons",options:["default_font","arial","arial_black","comic_sans","courier_new","impact","times_new_roman","verdana"]}],["viewsource"]],fonts:{arial:"Arial",arial_black:"Arial Black",comic_sans:"Comic Sans MS",courier_new:"Courier New",impact:"Impact",times_new_roman:"Times New Roman",verdana:"Verdana"},modelValue:t.currentValue,"onUpdate:modelValue":[n[1]||(n[1]=i=>t.currentValue=i),t.updateValue],dense:e.$q.screen.lt.md},{uploader:withCtx(()=>[createBaseVNode("div",_hoisted_1$4,[createVNode(t.UploaderComponents,{type:"icon",onUploaded:t.onUploaded})])]),colors:withCtx(()=>[createBaseVNode("div",_hoisted_2$3,[createVNode(QBtn,{flat:"",dense:"",label:"颜色",size:"xs",class:"full-width bg-primary text-white",onClick:n[0]||(n[0]=i=>t.showColorPicker=!0)})])]),_:1},8,["readonly","placeholder","toolbar","modelValue","dense"]),createVNode(QDialog,{modelValue:t.showColorPicker,"onUpdate:modelValue":n[2]||(n[2]=i=>t.showColorPicker=i)},{default:withCtx(()=>[createVNode(QCard,null,{default:withCtx(()=>[createVNode(QCardSection,null,{default:withCtx(()=>[createVNode(QColor,{"model-value":t.hex,onChange:t.editTextColor,style:{"max-width":"250px"}},null,8,["model-value"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}const EditorComponents=_export_sfc(_sfc_main$5,[["render",_sfc_render$5],["__file","editor.vue"]]),symbolSize=8,_sfc_main$4=defineComponent({name:"EchartsLine",__name:"echartsLine",props:{params:{}},setup(e,{expose:n}){n();const l=ref(null);let t;const o=ref([]),r=e,i=s=>{t.dispatchAction({type:"showTip",seriesIndex:0,dataIndex:s})},a=()=>{t.dispatchAction({type:"hideTip"})},u=(s,b)=>{o.value[s][1]=t.convertFromPixel("grid",b)[1]??0,t.setOption({series:[{id:"a",data:o.value}]})};onMounted(()=>{t=init(l.value),o.value=r.params;let s=[];o.value.forEach(([,y])=>s.push(y));let b=Math.max(...s),m=Math.min(...s);t.setOption({tooltip:{triggerOn:"none",formatter:function(y){return"日期: "+date.formatDate(y.data[0],"YYYY-MM-DD HH:mm:ss")+"<br>数值: "+y.data[1].toFixed(2)}},grid:{top:"5%",left:"8%",right:"8%",bottom:"12%"},xAxis:{type:"time",axisLine:{onZero:!1}},yAxis:{min:m,max:b,type:"value",axisLine:{onZero:!1},splitLine:{show:!0,lineStyle:{color:"rgba(255,168,168,0.38)",type:"dashed"}}},dataZoom:[{type:"inside",xAxisIndex:0,filterMode:"none"},{type:"inside",filterMode:"none"}],series:[{id:"a",type:"line",smooth:!0,symbolSize,data:o.value}]}),window.addEventListener("resize",p),t.on("dataZoom",p);function p(){let[y]=t.getOption().dataZoom;const B=y.startValue,S=y.endValue,M=o.value.filter(([I])=>I>=B&&I<=S);let D=[];M.forEach(([,I])=>D.push(I));let C=Math.max(...D),d=Math.min(...D);t.setOption({graphic:o.value.map(function(I){return{position:t.convertToPixel("grid",I)}}),yAxis:{min:d,max:C}})}setTimeout(()=>{t.setOption({graphic:o.value.map(function(y,B){return{type:"circle",position:t.convertToPixel("grid",y),shape:{cx:0,cy:0,r:10},invisible:!0,draggable:!0,ondrag:S=>{u(B,[S.offsetX,S.offsetY])},onmousemove:()=>{i(B)},onmouseout:()=>{a()},z:100}})})},100)});const f={echartsLine:l,get echartsInstance(){return t},set echartsInstance(s){t=s},data:o,props:r,symbolSize,showTooltip:i,hideTooltip:a,onPointDragging:u};return Object.defineProperty(f,"__isScriptSetup",{enumerable:!1,value:!0}),f}}),_hoisted_1$3={ref:"echartsLine",style:{height:"460px"}};function _sfc_render$4(e,n,l,t,o,r){return openBlock(),createElementBlock("div",null,[createBaseVNode("div",_hoisted_1$3,null,512)])}const EchartsLine=_export_sfc(_sfc_main$4,[["render",_sfc_render$4],["__file","echartsLine.vue"]]),InputTypeText="text",InputTypeHtml="html",InputTypeTextarea="textarea",InputTypeNumber="number",InputTypeEditor="editor",InputTypeSelect="select",InputTypeSelectSearch="selectSearch",InputTypeRadio="radio",InputTypeCheckbox="checkbox",InputTypeDatePicker="date",InputTypeDateRangePicker="dateRange",InputTypeTimePicker="time",InputTypeDateTimePicker="dateTime",InputTypeFile="file",InputTypeImage="image",InputTypeIcon="icon",InputTypeMultipleImage="images",InputTypeToggle="toggle",InputTypeColor="color",InputTypeTranslate="translate",InputTypeStruct="struct",InputTypeSlice="slice",InputTypeDynamic="dynamic",InputTypeLine="line",_sfc_main$3=defineComponent({name:"InputFields",__name:"inputs",props:{childrenForm:{},inputs:{},params:{},commonOptions:{},showSearchButton:{type:Boolean},colsClass:{}},emits:["search","change"],setup(e,{expose:n,emit:l}){n();const t=l,o=useQuasar(),r=ref(!1),i=ref([]),a=ref("zh-CN"),u=ref({}),f=ref([]),s=e,b=ref({...s.params});watch(()=>s.params,E=>{b.value={...E}}),s.inputs.flat().forEach(E=>{b.value[E.field]===void 0&&(b.value[E.field]=E.default!==void 0?E.default:null,E.type===InputTypeEditor&&b.value[E.field]===null&&(b.value[E.field]=""))}),t("change",b.value);const m=(E,F,L,K)=>{const[le,de,ce]=F.split("#"),[J,oe]=de.split(">");requestOptions({model:le,label:J,value:oe,words:E,where:ce,merchant:L&&L.hasOwnProperty("merchant")?L.merchant:!1}).then(ee=>{f.value=[{label:"全部",value:null},...ee],K(()=>{},()=>{})})},p=()=>{s.showSearchButton&&t("search")},y=()=>{t("change",b.value)},H={emit:t,$q:o,showLangTabs:r,langTabs:i,currentTab:a,updateTranslateParams:u,searchOptions:f,props:s,currentParams:b,selectSearchFilterFn:m,handleSearch:p,handleChange:y,childrenSubmitFunc:(E,F,L,K)=>{F===-1?b.value[E]=K:L===-1?b.value[E][F]=K:b.value[E][F][L]=K,y()},showLangTabsFunc:E=>{requestLangTags({field:b.value[E.field]}).then(F=>{i.value=F,F.length>0&&(u.value=F[0],a.value=F[0].lang),r.value=!0})},handleTabChange:E=>{a.value=E;const F=i.value.find(L=>L.lang===E);F&&(u.value={...F})},handleUpdateTranslate:()=>{requestUpdateTranslate(u.value).then(()=>{const E=i.value.find(F=>F.lang===u.value.lang);E.value=u.value.value,o.notify({type:"positive",message:"翻译修改成功"})})},handleAddSliceItem:(E,F,L)=>{if(F===-1){b.value[E]===null&&(b.value[E]=[]),b.value[E].push({});return}const K={};L===-1?b.value[E].splice(F+1,0,K):b.value[E][L].splice(F+1,0,K)},handleDeleteSliceItem:(E,F,L)=>{L===-1?b.value[E].splice(F,1):b.value[E][L].splice(F,1),y()},changeDateRangePicker:E=>{typeof b.value[E]=="string"&&(b.value[E]={from:b.value[E],to:b.value[E]}),y()},FormInputs,UploaderComponents,EditorComponents,EchartsLine,get InputTypeText(){return InputTypeText},get InputTypeHtml(){return InputTypeHtml},get InputTypeTextarea(){return InputTypeTextarea},get InputTypeNumber(){return InputTypeNumber},get InputTypeEditor(){return InputTypeEditor},get InputTypeRadio(){return InputTypeRadio},get InputTypeCheckbox(){return InputTypeCheckbox},get InputTypeDatePicker(){return InputTypeDatePicker},get InputTypeDateTimePicker(){return InputTypeDateTimePicker},get InputTypeDateRangePicker(){return InputTypeDateRangePicker},get InputTypeTimePicker(){return InputTypeTimePicker},get InputTypeSelect(){return InputTypeSelect},get InputTypeSelectSearch(){return InputTypeSelectSearch},get InputTypeImage(){return InputTypeImage},get InputTypeMultipleImage(){return InputTypeMultipleImage},get InputTypeFile(){return InputTypeFile},get InputTypeIcon(){return InputTypeIcon},get InputTypeToggle(){return InputTypeToggle},get InputTypeColor(){return InputTypeColor},get InputTypeTranslate(){return InputTypeTranslate},get InputTypeStruct(){return InputTypeStruct},get InputTypeSlice(){return InputTypeSlice},get InputTypeLine(){return InputTypeLine}};return Object.defineProperty(H,"__isScriptSetup",{enumerable:!1,value:!0}),H}}),_hoisted_1$2=["innerHTML"],_hoisted_2$2={key:9},_hoisted_3$2={class:"row items-center justify-end"},_hoisted_4$2={class:"row items-center justify-end"},_hoisted_5$2=["readonly"],_hoisted_6$1=["readonly"],_hoisted_7$1={class:"text-caption text-bold text-grey"},_hoisted_8$1={key:0},_hoisted_9$1={key:1},_hoisted_10$1={class:"column"},_hoisted_11$1={key:1};function _sfc_render$3(e,n,l,t,o,r){return openBlock(),createElementBlock(Fragment,null,[createBaseVNode("div",{onKeyup:withKeys(t.handleSearch,["enter"])},[(openBlock(!0),createElementBlock(Fragment,null,renderList(l.inputs,i=>(openBlock(),createElementBlock("div",{key:i.id,class:"row q-col-gutter-sm q-mb-sm"},[(openBlock(!0),createElementBlock(Fragment,null,renderList(i,a=>(openBlock(),createElementBlock(Fragment,{key:a.field},[a.display?createCommentVNode("",!0):(openBlock(),createElementBlock("div",{key:0,class:normalizeClass(l.colsClass)},[a.type===t.InputTypeText?(openBlock(),createBlock(QInput,{key:0,"onUpdate:modelValue":[t.handleChange,u=>t.currentParams[a.field]=u],readonly:a.readonly,modelValue:t.currentParams[a.field],label:a.label,filled:"",dense:""},null,8,["readonly","modelValue","onUpdate:modelValue","label"])):a.type===t.InputTypeHtml?(openBlock(),createElementBlock("div",{key:1,innerHTML:t.currentParams[a.field]},null,8,_hoisted_1$2)):a.type===t.InputTypeLine?(openBlock(),createBlock(t.EchartsLine,{key:2,params:t.currentParams[a.field]??[]},null,8,["params"])):a.type===t.InputTypeTextarea?(openBlock(),createBlock(QInput,{key:3,"onUpdate:modelValue":[t.handleChange,u=>t.currentParams[a.field]=u],readonly:a.readonly,modelValue:t.currentParams[a.field],label:a.label,type:"textarea",filled:"",dense:""},null,8,["readonly","modelValue","onUpdate:modelValue","label"])):a.type===t.InputTypeNumber?(openBlock(),createBlock(QInput,{key:4,"onUpdate:modelValue":[()=>{e.$nextTick(()=>{t.currentParams[a.field]=t.currentParams[a.field]===""?null:t.currentParams[a.field],t.handleChange()})},u=>t.currentParams[a.field]=u],readonly:a.readonly,modelValue:t.currentParams[a.field],modelModifiers:{number:!0},label:a.label,type:"number",filled:"",dense:""},null,8,["onUpdate:modelValue","readonly","modelValue","label"])):a.type===t.InputTypeEditor?(openBlock(),createBlock(t.EditorComponents,{key:5,modelValue:t.currentParams[a.field],"onUpdate:modelValue":[u=>t.currentParams[a.field]=u,t.handleChange]},null,8,["modelValue","onUpdate:modelValue"])):a.type===t.InputTypeSelect?(openBlock(),createBlock(QSelect,{key:6,dense:"","options-dense":"","emit-value":"","map-options":"","onUpdate:modelValue":[t.handleChange,u=>t.currentParams[a.field]=u],modelValue:t.currentParams[a.field],label:a.label,options:a.options??l.commonOptions[a.scanField??a.field]??[],filled:""},null,8,["modelValue","onUpdate:modelValue","label","options"])):a.type===t.InputTypeSelectSearch?(openBlock(),createBlock(QSelect,{key:7,dense:"","options-dense":"","emit-value":"","map-options":"","onUpdate:modelValue":[t.handleChange,u=>t.currentParams[a.field]=u],onFilter:(u,f)=>t.selectSearchFilterFn(u,a.mask,a.options,f),"use-input":"","use-chip":"",style:{"min-width":"146px"},modelValue:t.currentParams[a.field],label:a.label,options:t.searchOptions,filled:""},null,8,["onFilter","modelValue","onUpdate:modelValue","label","options"])):a.type===t.InputTypeRadio?(openBlock(),createBlock(QOptionGroup,{key:8,"onUpdate:modelValue":[t.handleChange,u=>t.currentParams[a.field]=u],readonly:a.readonly,modelValue:t.currentParams[a.field],options:a.options??l.commonOptions[a.field]??[],type:"radio"},null,8,["readonly","modelValue","onUpdate:modelValue","options"])):a.type===t.InputTypeCheckbox?(openBlock(),createElementBlock("div",_hoisted_2$2,[createBaseVNode("div",null,toDisplayString(a.label),1),(openBlock(!0),createElementBlock(Fragment,null,renderList(t.currentParams[a.field],(u,f)=>(openBlock(),createBlock(QCheckbox,{"onUpdate:modelValue":[t.handleChange,s=>t.currentParams[a.field][f].checked=s],readonly:a.readonly,key:f,modelValue:t.currentParams[a.field][f].checked,label:u.label},null,8,["readonly","modelValue","onUpdate:modelValue","label"]))),128))])):a.type===t.InputTypeDatePicker?(openBlock(),createBlock(QDate,{key:10,"onUpdate:modelValue":[t.handleChange,u=>t.currentParams[a.field]=u],readonly:a.readonly,modelValue:t.currentParams[a.field],label:a.label,filled:""},null,8,["readonly","modelValue","onUpdate:modelValue","label"])):a.type===t.InputTypeTimePicker?(openBlock(),createBlock(QInput,{key:11,"onUpdate:modelValue":t.handleChange,readonly:a.readonly,"model-value":t.currentParams[a.field],label:a.label,dense:"",filled:""},{append:withCtx(()=>[createVNode(QIcon,{name:"access_time",class:"cursor-pointer"},{default:withCtx(()=>[createVNode(QPopupProxy,{cover:"","transition-show":"scale","transition-hide":"scale"},{default:withCtx(()=>[createVNode(QTime,{modelValue:t.currentParams[a.field],"onUpdate:modelValue":[u=>t.currentParams[a.field]=u,t.handleChange],"with-seconds":"",format24h:""},{default:withCtx(()=>[createBaseVNode("div",_hoisted_3$2,[withDirectives(createVNode(QBtn,{label:"Close",color:"primary",flat:""},null,512),[[ClosePopup]])])]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["readonly","model-value","label"])):a.type===t.InputTypeDateTimePicker?(openBlock(),createBlock(QInput,{key:12,readonly:a.readonly,"model-value":t.currentParams[a.field],label:a.label,dense:"",filled:""},{prepend:withCtx(()=>[createVNode(QIcon,{name:"event",class:"cursor-pointer"},{default:withCtx(()=>[createVNode(QPopupProxy,{cover:"","transition-show":"scale","transition-hide":"scale"},{default:withCtx(()=>[createVNode(QDate,{modelValue:t.currentParams[a.field],"onUpdate:modelValue":[u=>t.currentParams[a.field]=u,t.handleChange],mask:"YYYY/MM/DD HH:mm:ss"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),append:withCtx(()=>[createVNode(QIcon,{name:"access_time",class:"cursor-pointer"},{default:withCtx(()=>[createVNode(QPopupProxy,{cover:"","transition-show":"scale","transition-hide":"scale"},{default:withCtx(()=>[createVNode(QTime,{modelValue:t.currentParams[a.field],"onUpdate:modelValue":[u=>t.currentParams[a.field]=u,t.handleChange],mask:"YYYY/MM/DD HH:mm:ss",format24h:"","with-seconds":""},{default:withCtx(()=>[createBaseVNode("div",_hoisted_4$2,[withDirectives(createVNode(QBtn,{label:"关闭",color:"primary",flat:""},null,512),[[ClosePopup]])])]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["readonly","model-value","label"])):a.type===t.InputTypeDateRangePicker?(openBlock(),createBlock(QInput,{key:13,"model-value":t.currentParams[a.field]?t.currentParams[a.field].from+" - "+t.currentParams[a.field].to:"",label:a.label,dense:"",filled:""},{append:withCtx(()=>[createVNode(QIcon,{name:"event",class:"cursor-pointer"},{default:withCtx(()=>[createVNode(QPopupProxy,{cover:"","transition-show":"scale","transition-hide":"scale"},{default:withCtx(()=>[createVNode(QDate,{range:"",modelValue:t.currentParams[a.field],"onUpdate:modelValue":[u=>t.currentParams[a.field]=u,()=>t.changeDateRangePicker(a.field)]},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["model-value","label"])):a.type===t.InputTypeFile?(openBlock(),createBlock(t.UploaderComponents,{key:14,onUploaded:u=>{t.currentParams[a.field]=u,t.handleChange()},path:t.currentParams[a.field],type:"file",label:a.label},null,8,["onUploaded","path","label"])):a.type===t.InputTypeImage?(openBlock(),createBlock(t.UploaderComponents,{key:15,onUploaded:u=>{t.currentParams[a.field]=u,t.handleChange()},path:t.currentParams[a.field],label:a.label},null,8,["onUploaded","path","label"])):a.type===t.InputTypeIcon?(openBlock(),createBlock(t.UploaderComponents,{key:16,onUploaded:u=>{t.currentParams[a.field]=u,t.handleChange()},path:t.currentParams[a.field],type:"icon",label:a.label},null,8,["onUploaded","path","label"])):a.type===t.InputTypeMultipleImage?(openBlock(),createBlock(t.UploaderComponents,{key:17,onUploaded:u=>{t.currentParams[a.field]=u,t.handleChange()},path:t.currentParams[a.field],type:"multiple",label:a.label},null,8,["onUploaded","path","label"])):a.type===t.InputTypeToggle?(openBlock(),createBlock(QToggle,{key:18,"onUpdate:modelValue":[t.handleChange,u=>t.currentParams[a.field]=u],readonly:a.readonly,modelValue:t.currentParams[a.field],"true-value":!0,"false-value":!1,label:a.label},null,8,["readonly","modelValue","onUpdate:modelValue","label"])):a.type===t.InputTypeColor?(openBlock(),createBlock(QColor,{key:19,"onUpdate:modelValue":[t.handleChange,u=>t.currentParams[a.field]=u],readonly:a.readonly,modelValue:t.currentParams[a.field],label:a.label,filled:""},null,8,["readonly","modelValue","onUpdate:modelValue","label"])):a.type===t.InputTypeTranslate?(openBlock(),createBlock(QInput,{key:20,"onUpdate:modelValue":[t.handleChange,u=>t.currentParams[a.field]=u],dense:"",onClick:u=>t.showLangTabsFunc(a),readonly:"",modelValue:t.currentParams[a.field],label:a.label,filled:""},null,8,["onClick","modelValue","onUpdate:modelValue","label"])):a.type===t.InputTypeStruct?(openBlock(),createElementBlock("div",{key:21,readonly:a.readonly},[l.childrenForm?.[a.field]?(openBlock(),createBlock(t.FormInputs,{key:0,"show-inputs":!0,inputs:l.childrenForm[a.field].inputs,params:t.currentParams[a.field]??{},"children-form":l.childrenForm,"common-options":l.commonOptions,label:a.label,field:a.field,"scan-field":a.scanField,options:l.childrenForm[a.field].options||!1,"cols-class":"col",onSubmit:t.childrenSubmitFunc},null,8,["inputs","params","children-form","common-options","label","field","scan-field","options"])):createCommentVNode("",!0)],8,_hoisted_5$2)):a.type===t.InputTypeSlice?(openBlock(),createElementBlock("div",{key:22,readonly:a.readonly},[createVNode(QList,null,{default:withCtx(()=>[createVNode(QItem,{class:"no-padding",dense:""},{default:withCtx(()=>[createVNode(QItemSection,null,{default:withCtx(()=>[createBaseVNode("div",_hoisted_7$1,toDisplayString(a.label),1)]),_:2},1024),createVNode(QItemSection,{side:""},{default:withCtx(()=>[createVNode(QBtn,{flat:"",dense:"",size:"xs",icon:"add",onClick:u=>t.handleAddSliceItem(a.field,-1,-1),color:"primary"},null,8,["onClick"])]),_:2},1024)]),_:2},1024),(openBlock(!0),createElementBlock(Fragment,null,renderList(t.currentParams[a.field],(u,f)=>(openBlock(),createBlock(QItem,{key:f,class:"no-padding"},{default:withCtx(()=>[createVNode(QItemSection,{side:"",class:"no-padding q-mr-xs",style:{width:"18px"}},{default:withCtx(()=>[createBaseVNode("div",null,"#"+toDisplayString(f+1),1)]),_:2},1024),createVNode(QItemSection,{class:"no-padding"},{default:withCtx(()=>[Array.isArray(u)?(openBlock(),createElementBlock("div",_hoisted_8$1,[(openBlock(!0),createElementBlock(Fragment,null,renderList(u,(s,b)=>(openBlock(),createElementBlock(Fragment,{key:b},[l.childrenForm?.[a.field]?(openBlock(),createBlock(t.FormInputs,{key:0,index:f,"item-index":b,"show-inputs":!0,inputs:l.childrenForm[a.field].inputs,"children-form":l.childrenForm,params:s??{},"common-options":l.commonOptions,label:a.label,field:a.field,"scan-field":a.scanField,options:l.childrenForm[a.field].options||!1,"cols-class":"col",onSubmit:t.childrenSubmitFunc},null,8,["index","item-index","inputs","children-form","params","common-options","label","field","scan-field","options"])):createCommentVNode("",!0)],64))),128))])):(openBlock(),createElementBlock("div",_hoisted_9$1,[l.childrenForm?.[a.field]?(openBlock(),createBlock(t.FormInputs,{key:0,index:f,"item-index":-1,"show-inputs":!0,inputs:l.childrenForm[a.field].inputs,params:u??{},"children-form":l.childrenForm,"common-options":l.commonOptions,label:a.label,field:a.field,"scan-field":a.scanField,options:l.childrenForm[a.field].options||!1,"cols-class":"col",onSubmit:t.childrenSubmitFunc},null,8,["index","inputs","params","children-form","common-options","label","field","scan-field","options"])):createCommentVNode("",!0)]))]),_:2},1024),a.readonly?createCommentVNode("",!0):(openBlock(),createBlock(QItemSection,{key:0,side:"",class:"no-padding q-ml-sm",style:{width:"18px"}},{default:withCtx(()=>[createBaseVNode("div",_hoisted_10$1,[createVNode(QBtn,{flat:"",dense:"",size:"xs",icon:"add",onClick:s=>t.handleAddSliceItem(a.field,f,Array.isArray(u)?0:-1),color:"primary"},null,8,["onClick"]),createVNode(QBtn,{flat:"",dense:"",size:"xs",icon:"delete",onClick:s=>t.handleDeleteSliceItem(a.field,f,Array.isArray(u)?0:-1),color:"grey"},null,8,["onClick"])])]),_:2},1024))]),_:2},1024))),128))]),_:2},1024)],8,_hoisted_6$1)):(openBlock(),createBlock(QInput,{key:23,"onUpdate:modelValue":[t.handleChange,u=>t.currentParams[a.field]=u],readonly:a.readonly,modelValue:t.currentParams[a.field],label:a.label,filled:"",dense:""},null,8,["readonly","modelValue","onUpdate:modelValue","label"]))],2)),l.showSearchButton&&i===l.inputs[l.inputs.length-1]&&a===i[i.length-1]?(openBlock(),createElementBlock("div",_hoisted_11$1,[createVNode(QBtn,{color:"primary",icon:"search",label:"搜索",class:"full-height",onClick:t.handleSearch})])):createCommentVNode("",!0)],64))),128))]))),128))],32),createBaseVNode("div",null,[createVNode(QDialog,{modelValue:t.showLangTabs,"onUpdate:modelValue":n[2]||(n[2]=i=>t.showLangTabs=i)},{default:withCtx(()=>[createVNode(QCard,{class:"full-width q-pb-md"},{default:withCtx(()=>[createVNode(QCardSection,{style:{"border-bottom":"1px solid #e0e0e0"}},{default:withCtx(()=>[createVNode(QTabs,{modelValue:t.currentTab,"onUpdate:modelValue":n[0]||(n[0]=i=>t.currentTab=i),"inline-label":"","active-color":"primary",align:"left"},{default:withCtx(()=>[(openBlock(!0),createElementBlock(Fragment,null,renderList(t.langTabs,i=>(openBlock(),createBlock(QTab,{name:i.lang,label:i.label,key:i.lang,"no-caps":"",onClick:a=>t.handleTabChange(i.lang)},null,8,["name","label","onClick"]))),128))]),_:1},8,["modelValue"])]),_:1}),createVNode(QCardSection,null,{default:withCtx(()=>[createVNode(t.EditorComponents,{modelValue:t.updateTranslateParams.value,"onUpdate:modelValue":n[1]||(n[1]=i=>t.updateTranslateParams.value=i)},null,8,["modelValue"])]),_:1}),createVNode(QCardActions,{align:"right",class:"q-pa-md"},{default:withCtx(()=>[withDirectives(createVNode(QBtn,{flat:"",label:"取消"},null,512),[[ClosePopup]]),withDirectives(createVNode(QBtn,{color:"primary",label:"提交",onClick:t.handleUpdateTranslate},null,512),[[ClosePopup]])]),_:1})]),_:1})]),_:1},8,["modelValue"])])],64)}const InputFields=_export_sfc(_sfc_main$3,[["render",_sfc_render$3],["__scopeId","data-v-6de281ab"],["__file","inputs.vue"]]),_sfc_main$2=defineComponent({name:"FormInputs",__name:"form",props:{index:{default:-1},itemIndex:{default:-1},showInputs:{type:Boolean,default:!0},showSearchButton:{type:Boolean},colsClass:{default:""},style:{default:()=>({})},commonOptions:{default:()=>({})},label:{},field:{},scanField:{},inputs:{default:()=>[]},params:{default:()=>({})},options:{type:Boolean,default:!1},childrenForm:{default:()=>({})}},emits:["submit"],setup(e,{expose:n,emit:l}){const t=e,o=ref({...t.params});watch(()=>t.params,f=>{o.value={...f}});const r=l,i=()=>{r("submit",t.field,t.index,t.itemIndex,o.value)},a=f=>{o.value=f,t.field!==""&&i()};n({onSubmit:i});const u={props:t,formParams:o,emit:r,onSubmit:i,handleInputChange:a,InputFields};return Object.defineProperty(u,"__isScriptSetup",{enumerable:!1,value:!0}),u}});function _sfc_render$2(e,n,l,t,o,r){return l.showInputs?(openBlock(),createElementBlock("div",{key:0,style:normalizeStyle(l.style)},[createVNode(QForm,null,{default:withCtx(()=>[createVNode(t.InputFields,{"children-form":l.childrenForm,inputs:l.inputs,params:t.formParams,"common-options":l.commonOptions,"show-search-button":l.showSearchButton,"cols-class":l.colsClass,onChange:t.handleInputChange,onSearch:t.onSubmit},null,8,["children-form","inputs","params","common-options","show-search-button","cols-class"])]),_:1})],4)):createCommentVNode("",!0)}const FormInputs=_export_sfc(_sfc_main$2,[["render",_sfc_render$2],["__scopeId","data-v-4a1b8193"],["__file","form.vue"]]);var browser={},canPromise$1=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then},qrcode={},utils$1={};let toSJISFunction;const CODEWORDS_COUNT=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];utils$1.getSymbolSize=function(n){if(!n)throw new Error('"version" cannot be null or undefined');if(n<1||n>40)throw new Error('"version" should be in range from 1 to 40');return n*4+17};utils$1.getSymbolTotalCodewords=function(n){return CODEWORDS_COUNT[n]};utils$1.getBCHDigit=function(e){let n=0;for(;e!==0;)n++,e>>>=1;return n};utils$1.setToSJISFunction=function(n){if(typeof n!="function")throw new Error('"toSJISFunc" is not a valid function.');toSJISFunction=n};utils$1.isKanjiModeEnabled=function(){return typeof toSJISFunction<"u"};utils$1.toSJIS=function(n){return toSJISFunction(n)};var errorCorrectionLevel={};(function(e){e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2};function n(l){if(typeof l!="string")throw new Error("Param is not a string");switch(l.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+l)}}e.isValid=function(t){return t&&typeof t.bit<"u"&&t.bit>=0&&t.bit<4},e.from=function(t,o){if(e.isValid(t))return t;try{return n(t)}catch{return o}}})(errorCorrectionLevel);function BitBuffer$1(){this.buffer=[],this.length=0}BitBuffer$1.prototype={get:function(e){const n=Math.floor(e/8);return(this.buffer[n]>>>7-e%8&1)===1},put:function(e,n){for(let l=0;l<n;l++)this.putBit((e>>>n-l-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(e){const n=Math.floor(this.length/8);this.buffer.length<=n&&this.buffer.push(0),e&&(this.buffer[n]|=128>>>this.length%8),this.length++}};var bitBuffer=BitBuffer$1;function BitMatrix$1(e){if(!e||e<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=e,this.data=new Uint8Array(e*e),this.reservedBit=new Uint8Array(e*e)}BitMatrix$1.prototype.set=function(e,n,l,t){const o=e*this.size+n;this.data[o]=l,t&&(this.reservedBit[o]=!0)};BitMatrix$1.prototype.get=function(e,n){return this.data[e*this.size+n]};BitMatrix$1.prototype.xor=function(e,n,l){this.data[e*this.size+n]^=l};BitMatrix$1.prototype.isReserved=function(e,n){return this.reservedBit[e*this.size+n]};var bitMatrix=BitMatrix$1,alignmentPattern={};(function(e){const n=utils$1.getSymbolSize;e.getRowColCoords=function(t){if(t===1)return[];const o=Math.floor(t/7)+2,r=n(t),i=r===145?26:Math.ceil((r-13)/(2*o-2))*2,a=[r-7];for(let u=1;u<o-1;u++)a[u]=a[u-1]-i;return a.push(6),a.reverse()},e.getPositions=function(t){const o=[],r=e.getRowColCoords(t),i=r.length;for(let a=0;a<i;a++)for(let u=0;u<i;u++)a===0&&u===0||a===0&&u===i-1||a===i-1&&u===0||o.push([r[a],r[u]]);return o}})(alignmentPattern);var finderPattern={};const getSymbolSize=utils$1.getSymbolSize,FINDER_PATTERN_SIZE=7;finderPattern.getPositions=function(n){const l=getSymbolSize(n);return[[0,0],[l-FINDER_PATTERN_SIZE,0],[0,l-FINDER_PATTERN_SIZE]]};var maskPattern={};(function(e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const n={N1:3,N2:3,N3:40,N4:10};e.isValid=function(o){return o!=null&&o!==""&&!isNaN(o)&&o>=0&&o<=7},e.from=function(o){return e.isValid(o)?parseInt(o,10):void 0},e.getPenaltyN1=function(o){const r=o.size;let i=0,a=0,u=0,f=null,s=null;for(let b=0;b<r;b++){a=u=0,f=s=null;for(let m=0;m<r;m++){let p=o.get(b,m);p===f?a++:(a>=5&&(i+=n.N1+(a-5)),f=p,a=1),p=o.get(m,b),p===s?u++:(u>=5&&(i+=n.N1+(u-5)),s=p,u=1)}a>=5&&(i+=n.N1+(a-5)),u>=5&&(i+=n.N1+(u-5))}return i},e.getPenaltyN2=function(o){const r=o.size;let i=0;for(let a=0;a<r-1;a++)for(let u=0;u<r-1;u++){const f=o.get(a,u)+o.get(a,u+1)+o.get(a+1,u)+o.get(a+1,u+1);(f===4||f===0)&&i++}return i*n.N2},e.getPenaltyN3=function(o){const r=o.size;let i=0,a=0,u=0;for(let f=0;f<r;f++){a=u=0;for(let s=0;s<r;s++)a=a<<1&2047|o.get(f,s),s>=10&&(a===1488||a===93)&&i++,u=u<<1&2047|o.get(s,f),s>=10&&(u===1488||u===93)&&i++}return i*n.N3},e.getPenaltyN4=function(o){let r=0;const i=o.data.length;for(let u=0;u<i;u++)r+=o.data[u];return Math.abs(Math.ceil(r*100/i/5)-10)*n.N4};function l(t,o,r){switch(t){case e.Patterns.PATTERN000:return(o+r)%2===0;case e.Patterns.PATTERN001:return o%2===0;case e.Patterns.PATTERN010:return r%3===0;case e.Patterns.PATTERN011:return(o+r)%3===0;case e.Patterns.PATTERN100:return(Math.floor(o/2)+Math.floor(r/3))%2===0;case e.Patterns.PATTERN101:return o*r%2+o*r%3===0;case e.Patterns.PATTERN110:return(o*r%2+o*r%3)%2===0;case e.Patterns.PATTERN111:return(o*r%3+(o+r)%2)%2===0;default:throw new Error("bad maskPattern:"+t)}}e.applyMask=function(o,r){const i=r.size;for(let a=0;a<i;a++)for(let u=0;u<i;u++)r.isReserved(u,a)||r.xor(u,a,l(o,u,a))},e.getBestMask=function(o,r){const i=Object.keys(e.Patterns).length;let a=0,u=1/0;for(let f=0;f<i;f++){r(f),e.applyMask(f,o);const s=e.getPenaltyN1(o)+e.getPenaltyN2(o)+e.getPenaltyN3(o)+e.getPenaltyN4(o);e.applyMask(f,o),s<u&&(u=s,a=f)}return a}})(maskPattern);var errorCorrectionCode={};const ECLevel$1=errorCorrectionLevel,EC_BLOCKS_TABLE=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],EC_CODEWORDS_TABLE=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];errorCorrectionCode.getBlocksCount=function(n,l){switch(l){case ECLevel$1.L:return EC_BLOCKS_TABLE[(n-1)*4+0];case ECLevel$1.M:return EC_BLOCKS_TABLE[(n-1)*4+1];case ECLevel$1.Q:return EC_BLOCKS_TABLE[(n-1)*4+2];case ECLevel$1.H:return EC_BLOCKS_TABLE[(n-1)*4+3];default:return}};errorCorrectionCode.getTotalCodewordsCount=function(n,l){switch(l){case ECLevel$1.L:return EC_CODEWORDS_TABLE[(n-1)*4+0];case ECLevel$1.M:return EC_CODEWORDS_TABLE[(n-1)*4+1];case ECLevel$1.Q:return EC_CODEWORDS_TABLE[(n-1)*4+2];case ECLevel$1.H:return EC_CODEWORDS_TABLE[(n-1)*4+3];default:return}};var polynomial={},galoisField={};const EXP_TABLE=new Uint8Array(512),LOG_TABLE=new Uint8Array(256);(function(){let n=1;for(let l=0;l<255;l++)EXP_TABLE[l]=n,LOG_TABLE[n]=l,n<<=1,n&256&&(n^=285);for(let l=255;l<512;l++)EXP_TABLE[l]=EXP_TABLE[l-255]})();galoisField.log=function(n){if(n<1)throw new Error("log("+n+")");return LOG_TABLE[n]};galoisField.exp=function(n){return EXP_TABLE[n]};galoisField.mul=function(n,l){return n===0||l===0?0:EXP_TABLE[LOG_TABLE[n]+LOG_TABLE[l]]};(function(e){const n=galoisField;e.mul=function(t,o){const r=new Uint8Array(t.length+o.length-1);for(let i=0;i<t.length;i++)for(let a=0;a<o.length;a++)r[i+a]^=n.mul(t[i],o[a]);return r},e.mod=function(t,o){let r=new Uint8Array(t);for(;r.length-o.length>=0;){const i=r[0];for(let u=0;u<o.length;u++)r[u]^=n.mul(o[u],i);let a=0;for(;a<r.length&&r[a]===0;)a++;r=r.slice(a)}return r},e.generateECPolynomial=function(t){let o=new Uint8Array([1]);for(let r=0;r<t;r++)o=e.mul(o,new Uint8Array([1,n.exp(r)]));return o}})(polynomial);const Polynomial=polynomial;function ReedSolomonEncoder$1(e){this.genPoly=void 0,this.degree=e,this.degree&&this.initialize(this.degree)}ReedSolomonEncoder$1.prototype.initialize=function(n){this.degree=n,this.genPoly=Polynomial.generateECPolynomial(this.degree)};ReedSolomonEncoder$1.prototype.encode=function(n){if(!this.genPoly)throw new Error("Encoder not initialized");const l=new Uint8Array(n.length+this.degree);l.set(n);const t=Polynomial.mod(l,this.genPoly),o=this.degree-t.length;if(o>0){const r=new Uint8Array(this.degree);return r.set(t,o),r}return t};var reedSolomonEncoder=ReedSolomonEncoder$1,version={},mode={},versionCheck={};versionCheck.isValid=function(n){return!isNaN(n)&&n>=1&&n<=40};var regex={};const numeric="[0-9]+",alphanumeric="[A-Z $%*+\\-./:]+";let kanji="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";kanji=kanji.replace(/u/g,"\\u");const byte="(?:(?![A-Z0-9 $%*+\\-./:]|"+kanji+`)(?:.|[\r
]))+`;regex.KANJI=new RegExp(kanji,"g");regex.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g");regex.BYTE=new RegExp(byte,"g");regex.NUMERIC=new RegExp(numeric,"g");regex.ALPHANUMERIC=new RegExp(alphanumeric,"g");const TEST_KANJI=new RegExp("^"+kanji+"$"),TEST_NUMERIC=new RegExp("^"+numeric+"$"),TEST_ALPHANUMERIC=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");regex.testKanji=function(n){return TEST_KANJI.test(n)};regex.testNumeric=function(n){return TEST_NUMERIC.test(n)};regex.testAlphanumeric=function(n){return TEST_ALPHANUMERIC.test(n)};(function(e){const n=versionCheck,l=regex;e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(r,i){if(!r.ccBits)throw new Error("Invalid mode: "+r);if(!n.isValid(i))throw new Error("Invalid version: "+i);return i>=1&&i<10?r.ccBits[0]:i<27?r.ccBits[1]:r.ccBits[2]},e.getBestModeForData=function(r){return l.testNumeric(r)?e.NUMERIC:l.testAlphanumeric(r)?e.ALPHANUMERIC:l.testKanji(r)?e.KANJI:e.BYTE},e.toString=function(r){if(r&&r.id)return r.id;throw new Error("Invalid mode")},e.isValid=function(r){return r&&r.bit&&r.ccBits};function t(o){if(typeof o!="string")throw new Error("Param is not a string");switch(o.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+o)}}e.from=function(r,i){if(e.isValid(r))return r;try{return t(r)}catch{return i}}})(mode);(function(e){const n=utils$1,l=errorCorrectionCode,t=errorCorrectionLevel,o=mode,r=versionCheck,i=7973,a=n.getBCHDigit(i);function u(m,p,y){for(let B=1;B<=40;B++)if(p<=e.getCapacity(B,y,m))return B}function f(m,p){return o.getCharCountIndicator(m,p)+4}function s(m,p){let y=0;return m.forEach(function(B){const S=f(B.mode,p);y+=S+B.getBitsLength()}),y}function b(m,p){for(let y=1;y<=40;y++)if(s(m,y)<=e.getCapacity(y,p,o.MIXED))return y}e.from=function(p,y){return r.isValid(p)?parseInt(p,10):y},e.getCapacity=function(p,y,B){if(!r.isValid(p))throw new Error("Invalid QR Code version");typeof B>"u"&&(B=o.BYTE);const S=n.getSymbolTotalCodewords(p),M=l.getTotalCodewordsCount(p,y),D=(S-M)*8;if(B===o.MIXED)return D;const C=D-f(B,p);switch(B){case o.NUMERIC:return Math.floor(C/10*3);case o.ALPHANUMERIC:return Math.floor(C/11*2);case o.KANJI:return Math.floor(C/13);case o.BYTE:default:return Math.floor(C/8)}},e.getBestVersionForData=function(p,y){let B;const S=t.from(y,t.M);if(Array.isArray(p)){if(p.length>1)return b(p,S);if(p.length===0)return 1;B=p[0]}else B=p;return u(B.mode,B.getLength(),S)},e.getEncodedBits=function(p){if(!r.isValid(p)||p<7)throw new Error("Invalid QR Code version");let y=p<<12;for(;n.getBCHDigit(y)-a>=0;)y^=i<<n.getBCHDigit(y)-a;return p<<12|y}})(version);var formatInfo={};const Utils$3=utils$1,G15=1335,G15_MASK=21522,G15_BCH=Utils$3.getBCHDigit(G15);formatInfo.getEncodedBits=function(n,l){const t=n.bit<<3|l;let o=t<<10;for(;Utils$3.getBCHDigit(o)-G15_BCH>=0;)o^=G15<<Utils$3.getBCHDigit(o)-G15_BCH;return(t<<10|o)^G15_MASK};var segments={};const Mode$4=mode;function NumericData(e){this.mode=Mode$4.NUMERIC,this.data=e.toString()}NumericData.getBitsLength=function(n){return 10*Math.floor(n/3)+(n%3?n%3*3+1:0)};NumericData.prototype.getLength=function(){return this.data.length};NumericData.prototype.getBitsLength=function(){return NumericData.getBitsLength(this.data.length)};NumericData.prototype.write=function(n){let l,t,o;for(l=0;l+3<=this.data.length;l+=3)t=this.data.substr(l,3),o=parseInt(t,10),n.put(o,10);const r=this.data.length-l;r>0&&(t=this.data.substr(l),o=parseInt(t,10),n.put(o,r*3+1))};var numericData=NumericData;const Mode$3=mode,ALPHA_NUM_CHARS=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function AlphanumericData(e){this.mode=Mode$3.ALPHANUMERIC,this.data=e}AlphanumericData.getBitsLength=function(n){return 11*Math.floor(n/2)+6*(n%2)};AlphanumericData.prototype.getLength=function(){return this.data.length};AlphanumericData.prototype.getBitsLength=function(){return AlphanumericData.getBitsLength(this.data.length)};AlphanumericData.prototype.write=function(n){let l;for(l=0;l+2<=this.data.length;l+=2){let t=ALPHA_NUM_CHARS.indexOf(this.data[l])*45;t+=ALPHA_NUM_CHARS.indexOf(this.data[l+1]),n.put(t,11)}this.data.length%2&&n.put(ALPHA_NUM_CHARS.indexOf(this.data[l]),6)};var alphanumericData=AlphanumericData;const Mode$2=mode;function ByteData(e){this.mode=Mode$2.BYTE,typeof e=="string"?this.data=new TextEncoder().encode(e):this.data=new Uint8Array(e)}ByteData.getBitsLength=function(n){return n*8};ByteData.prototype.getLength=function(){return this.data.length};ByteData.prototype.getBitsLength=function(){return ByteData.getBitsLength(this.data.length)};ByteData.prototype.write=function(e){for(let n=0,l=this.data.length;n<l;n++)e.put(this.data[n],8)};var byteData=ByteData;const Mode$1=mode,Utils$2=utils$1;function KanjiData(e){this.mode=Mode$1.KANJI,this.data=e}KanjiData.getBitsLength=function(n){return n*13};KanjiData.prototype.getLength=function(){return this.data.length};KanjiData.prototype.getBitsLength=function(){return KanjiData.getBitsLength(this.data.length)};KanjiData.prototype.write=function(e){let n;for(n=0;n<this.data.length;n++){let l=Utils$2.toSJIS(this.data[n]);if(l>=33088&&l<=40956)l-=33088;else if(l>=57408&&l<=60351)l-=49472;else throw new Error("Invalid SJIS character: "+this.data[n]+`
Make sure your charset is UTF-8`);l=(l>>>8&255)*192+(l&255),e.put(l,13)}};var kanjiData=KanjiData,dijkstra={exports:{}};(function(e){var n={single_source_shortest_paths:function(l,t,o){var r={},i={};i[t]=0;var a=n.PriorityQueue.make();a.push(t,0);for(var u,f,s,b,m,p,y,B,S;!a.empty();){u=a.pop(),f=u.value,b=u.cost,m=l[f]||{};for(s in m)m.hasOwnProperty(s)&&(p=m[s],y=b+p,B=i[s],S=typeof i[s]>"u",(S||B>y)&&(i[s]=y,a.push(s,y),r[s]=f))}if(typeof o<"u"&&typeof i[o]>"u"){var M=["Could not find a path from ",t," to ",o,"."].join("");throw new Error(M)}return r},extract_shortest_path_from_predecessor_list:function(l,t){for(var o=[],r=t;r;)o.push(r),l[r],r=l[r];return o.reverse(),o},find_path:function(l,t,o){var r=n.single_source_shortest_paths(l,t,o);return n.extract_shortest_path_from_predecessor_list(r,o)},PriorityQueue:{make:function(l){var t=n.PriorityQueue,o={},r;l=l||{};for(r in t)t.hasOwnProperty(r)&&(o[r]=t[r]);return o.queue=[],o.sorter=l.sorter||t.default_sorter,o},default_sorter:function(l,t){return l.cost-t.cost},push:function(l,t){var o={value:l,cost:t};this.queue.push(o),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};e.exports=n})(dijkstra);var dijkstraExports=dijkstra.exports;(function(e){const n=mode,l=numericData,t=alphanumericData,o=byteData,r=kanjiData,i=regex,a=utils$1,u=dijkstraExports;function f(M){return unescape(encodeURIComponent(M)).length}function s(M,D,C){const d=[];let I;for(;(I=M.exec(C))!==null;)d.push({data:I[0],index:I.index,mode:D,length:I[0].length});return d}function b(M){const D=s(i.NUMERIC,n.NUMERIC,M),C=s(i.ALPHANUMERIC,n.ALPHANUMERIC,M);let d,I;return a.isKanjiModeEnabled()?(d=s(i.BYTE,n.BYTE,M),I=s(i.KANJI,n.KANJI,M)):(d=s(i.BYTE_KANJI,n.BYTE,M),I=[]),D.concat(C,d,I).sort(function(E,F){return E.index-F.index}).map(function(E){return{data:E.data,mode:E.mode,length:E.length}})}function m(M,D){switch(D){case n.NUMERIC:return l.getBitsLength(M);case n.ALPHANUMERIC:return t.getBitsLength(M);case n.KANJI:return r.getBitsLength(M);case n.BYTE:return o.getBitsLength(M)}}function p(M){return M.reduce(function(D,C){const d=D.length-1>=0?D[D.length-1]:null;return d&&d.mode===C.mode?(D[D.length-1].data+=C.data,D):(D.push(C),D)},[])}function y(M){const D=[];for(let C=0;C<M.length;C++){const d=M[C];switch(d.mode){case n.NUMERIC:D.push([d,{data:d.data,mode:n.ALPHANUMERIC,length:d.length},{data:d.data,mode:n.BYTE,length:d.length}]);break;case n.ALPHANUMERIC:D.push([d,{data:d.data,mode:n.BYTE,length:d.length}]);break;case n.KANJI:D.push([d,{data:d.data,mode:n.BYTE,length:f(d.data)}]);break;case n.BYTE:D.push([{data:d.data,mode:n.BYTE,length:f(d.data)}])}}return D}function B(M,D){const C={},d={start:{}};let I=["start"];for(let H=0;H<M.length;H++){const E=M[H],F=[];for(let L=0;L<E.length;L++){const K=E[L],le=""+H+L;F.push(le),C[le]={node:K,lastCount:0},d[le]={};for(let de=0;de<I.length;de++){const ce=I[de];C[ce]&&C[ce].node.mode===K.mode?(d[ce][le]=m(C[ce].lastCount+K.length,K.mode)-m(C[ce].lastCount,K.mode),C[ce].lastCount+=K.length):(C[ce]&&(C[ce].lastCount=K.length),d[ce][le]=m(K.length,K.mode)+4+n.getCharCountIndicator(K.mode,D))}}I=F}for(let H=0;H<I.length;H++)d[I[H]].end=0;return{map:d,table:C}}function S(M,D){let C;const d=n.getBestModeForData(M);if(C=n.from(D,d),C!==n.BYTE&&C.bit<d.bit)throw new Error('"'+M+'" cannot be encoded with mode '+n.toString(C)+`.
 Suggested mode is: `+n.toString(d));switch(C===n.KANJI&&!a.isKanjiModeEnabled()&&(C=n.BYTE),C){case n.NUMERIC:return new l(M);case n.ALPHANUMERIC:return new t(M);case n.KANJI:return new r(M);case n.BYTE:return new o(M)}}e.fromArray=function(D){return D.reduce(function(C,d){return typeof d=="string"?C.push(S(d,null)):d.data&&C.push(S(d.data,d.mode)),C},[])},e.fromString=function(D,C){const d=b(D,a.isKanjiModeEnabled()),I=y(d),H=B(I,C),E=u.find_path(H.map,"start","end"),F=[];for(let L=1;L<E.length-1;L++)F.push(H.table[E[L]].node);return e.fromArray(p(F))},e.rawSplit=function(D){return e.fromArray(b(D,a.isKanjiModeEnabled()))}})(segments);const Utils$1=utils$1,ECLevel=errorCorrectionLevel,BitBuffer=bitBuffer,BitMatrix=bitMatrix,AlignmentPattern=alignmentPattern,FinderPattern=finderPattern,MaskPattern=maskPattern,ECCode=errorCorrectionCode,ReedSolomonEncoder=reedSolomonEncoder,Version=version,FormatInfo=formatInfo,Mode=mode,Segments=segments;function setupFinderPattern(e,n){const l=e.size,t=FinderPattern.getPositions(n);for(let o=0;o<t.length;o++){const r=t[o][0],i=t[o][1];for(let a=-1;a<=7;a++)if(!(r+a<=-1||l<=r+a))for(let u=-1;u<=7;u++)i+u<=-1||l<=i+u||(a>=0&&a<=6&&(u===0||u===6)||u>=0&&u<=6&&(a===0||a===6)||a>=2&&a<=4&&u>=2&&u<=4?e.set(r+a,i+u,!0,!0):e.set(r+a,i+u,!1,!0))}}function setupTimingPattern(e){const n=e.size;for(let l=8;l<n-8;l++){const t=l%2===0;e.set(l,6,t,!0),e.set(6,l,t,!0)}}function setupAlignmentPattern(e,n){const l=AlignmentPattern.getPositions(n);for(let t=0;t<l.length;t++){const o=l[t][0],r=l[t][1];for(let i=-2;i<=2;i++)for(let a=-2;a<=2;a++)i===-2||i===2||a===-2||a===2||i===0&&a===0?e.set(o+i,r+a,!0,!0):e.set(o+i,r+a,!1,!0)}}function setupVersionInfo(e,n){const l=e.size,t=Version.getEncodedBits(n);let o,r,i;for(let a=0;a<18;a++)o=Math.floor(a/3),r=a%3+l-8-3,i=(t>>a&1)===1,e.set(o,r,i,!0),e.set(r,o,i,!0)}function setupFormatInfo(e,n,l){const t=e.size,o=FormatInfo.getEncodedBits(n,l);let r,i;for(r=0;r<15;r++)i=(o>>r&1)===1,r<6?e.set(r,8,i,!0):r<8?e.set(r+1,8,i,!0):e.set(t-15+r,8,i,!0),r<8?e.set(8,t-r-1,i,!0):r<9?e.set(8,15-r-1+1,i,!0):e.set(8,15-r-1,i,!0);e.set(t-8,8,1,!0)}function setupData(e,n){const l=e.size;let t=-1,o=l-1,r=7,i=0;for(let a=l-1;a>0;a-=2)for(a===6&&a--;;){for(let u=0;u<2;u++)if(!e.isReserved(o,a-u)){let f=!1;i<n.length&&(f=(n[i]>>>r&1)===1),e.set(o,a-u,f),r--,r===-1&&(i++,r=7)}if(o+=t,o<0||l<=o){o-=t,t=-t;break}}}function createData(e,n,l){const t=new BitBuffer;l.forEach(function(u){t.put(u.mode.bit,4),t.put(u.getLength(),Mode.getCharCountIndicator(u.mode,e)),u.write(t)});const o=Utils$1.getSymbolTotalCodewords(e),r=ECCode.getTotalCodewordsCount(e,n),i=(o-r)*8;for(t.getLengthInBits()+4<=i&&t.put(0,4);t.getLengthInBits()%8!==0;)t.putBit(0);const a=(i-t.getLengthInBits())/8;for(let u=0;u<a;u++)t.put(u%2?17:236,8);return createCodewords(t,e,n)}function createCodewords(e,n,l){const t=Utils$1.getSymbolTotalCodewords(n),o=ECCode.getTotalCodewordsCount(n,l),r=t-o,i=ECCode.getBlocksCount(n,l),a=t%i,u=i-a,f=Math.floor(t/i),s=Math.floor(r/i),b=s+1,m=f-s,p=new ReedSolomonEncoder(m);let y=0;const B=new Array(i),S=new Array(i);let M=0;const D=new Uint8Array(e.buffer);for(let E=0;E<i;E++){const F=E<u?s:b;B[E]=D.slice(y,y+F),S[E]=p.encode(B[E]),y+=F,M=Math.max(M,F)}const C=new Uint8Array(t);let d=0,I,H;for(I=0;I<M;I++)for(H=0;H<i;H++)I<B[H].length&&(C[d++]=B[H][I]);for(I=0;I<m;I++)for(H=0;H<i;H++)C[d++]=S[H][I];return C}function createSymbol(e,n,l,t){let o;if(Array.isArray(e))o=Segments.fromArray(e);else if(typeof e=="string"){let f=n;if(!f){const s=Segments.rawSplit(e);f=Version.getBestVersionForData(s,l)}o=Segments.fromString(e,f||40)}else throw new Error("Invalid data");const r=Version.getBestVersionForData(o,l);if(!r)throw new Error("The amount of data is too big to be stored in a QR Code");if(!n)n=r;else if(n<r)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+r+`.
`);const i=createData(n,l,o),a=Utils$1.getSymbolSize(n),u=new BitMatrix(a);return setupFinderPattern(u,n),setupTimingPattern(u),setupAlignmentPattern(u,n),setupFormatInfo(u,l,0),n>=7&&setupVersionInfo(u,n),setupData(u,i),isNaN(t)&&(t=MaskPattern.getBestMask(u,setupFormatInfo.bind(null,u,l))),MaskPattern.applyMask(t,u),setupFormatInfo(u,l,t),{modules:u,version:n,errorCorrectionLevel:l,maskPattern:t,segments:o}}qrcode.create=function(n,l){if(typeof n>"u"||n==="")throw new Error("No input text");let t=ECLevel.M,o,r;return typeof l<"u"&&(t=ECLevel.from(l.errorCorrectionLevel,ECLevel.M),o=Version.from(l.version),r=MaskPattern.from(l.maskPattern),l.toSJISFunc&&Utils$1.setToSJISFunction(l.toSJISFunc)),createSymbol(n,o,t,r)};var canvas={},utils={};(function(e){function n(l){if(typeof l=="number"&&(l=l.toString()),typeof l!="string")throw new Error("Color should be defined as hex string");let t=l.slice().replace("#","").split("");if(t.length<3||t.length===5||t.length>8)throw new Error("Invalid hex color: "+l);(t.length===3||t.length===4)&&(t=Array.prototype.concat.apply([],t.map(function(r){return[r,r]}))),t.length===6&&t.push("F","F");const o=parseInt(t.join(""),16);return{r:o>>24&255,g:o>>16&255,b:o>>8&255,a:o&255,hex:"#"+t.slice(0,6).join("")}}e.getOptions=function(t){t||(t={}),t.color||(t.color={});const o=typeof t.margin>"u"||t.margin===null||t.margin<0?4:t.margin,r=t.width&&t.width>=21?t.width:void 0,i=t.scale||4;return{width:r,scale:r?4:i,margin:o,color:{dark:n(t.color.dark||"#000000ff"),light:n(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},e.getScale=function(t,o){return o.width&&o.width>=t+o.margin*2?o.width/(t+o.margin*2):o.scale},e.getImageWidth=function(t,o){const r=e.getScale(t,o);return Math.floor((t+o.margin*2)*r)},e.qrToImageData=function(t,o,r){const i=o.modules.size,a=o.modules.data,u=e.getScale(i,r),f=Math.floor((i+r.margin*2)*u),s=r.margin*u,b=[r.color.light,r.color.dark];for(let m=0;m<f;m++)for(let p=0;p<f;p++){let y=(m*f+p)*4,B=r.color.light;if(m>=s&&p>=s&&m<f-s&&p<f-s){const S=Math.floor((m-s)/u),M=Math.floor((p-s)/u);B=b[a[S*i+M]?1:0]}t[y++]=B.r,t[y++]=B.g,t[y++]=B.b,t[y]=B.a}}})(utils);(function(e){const n=utils;function l(o,r,i){o.clearRect(0,0,r.width,r.height),r.style||(r.style={}),r.height=i,r.width=i,r.style.height=i+"px",r.style.width=i+"px"}function t(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}e.render=function(r,i,a){let u=a,f=i;typeof u>"u"&&(!i||!i.getContext)&&(u=i,i=void 0),i||(f=t()),u=n.getOptions(u);const s=n.getImageWidth(r.modules.size,u),b=f.getContext("2d"),m=b.createImageData(s,s);return n.qrToImageData(m.data,r,u),l(b,f,s),b.putImageData(m,0,0),f},e.renderToDataURL=function(r,i,a){let u=a;typeof u>"u"&&(!i||!i.getContext)&&(u=i,i=void 0),u||(u={});const f=e.render(r,i,u),s=u.type||"image/png",b=u.rendererOpts||{};return f.toDataURL(s,b.quality)}})(canvas);var svgTag={};const Utils=utils;function getColorAttrib(e,n){const l=e.a/255,t=n+'="'+e.hex+'"';return l<1?t+" "+n+'-opacity="'+l.toFixed(2).slice(1)+'"':t}function svgCmd(e,n,l){let t=e+n;return typeof l<"u"&&(t+=" "+l),t}function qrToPath(e,n,l){let t="",o=0,r=!1,i=0;for(let a=0;a<e.length;a++){const u=Math.floor(a%n),f=Math.floor(a/n);!u&&!r&&(r=!0),e[a]?(i++,a>0&&u>0&&e[a-1]||(t+=r?svgCmd("M",u+l,.5+f+l):svgCmd("m",o,0),o=0,r=!1),u+1<n&&e[a+1]||(t+=svgCmd("h",i),i=0)):o++}return t}svgTag.render=function(n,l,t){const o=Utils.getOptions(l),r=n.modules.size,i=n.modules.data,a=r+o.margin*2,u=o.color.light.a?"<path "+getColorAttrib(o.color.light,"fill")+' d="M0 0h'+a+"v"+a+'H0z"/>':"",f="<path "+getColorAttrib(o.color.dark,"stroke")+' d="'+qrToPath(i,r,o.margin)+'"/>',s='viewBox="0 0 '+a+" "+a+'"',m='<svg xmlns="http://www.w3.org/2000/svg" '+(o.width?'width="'+o.width+'" height="'+o.width+'" ':"")+s+' shape-rendering="crispEdges">'+u+f+`</svg>
`;return typeof t=="function"&&t(null,m),m};const canPromise=canPromise$1,QRCode=qrcode,CanvasRenderer=canvas,SvgRenderer=svgTag;function renderCanvas(e,n,l,t,o){const r=[].slice.call(arguments,1),i=r.length,a=typeof r[i-1]=="function";if(!a&&!canPromise())throw new Error("Callback required as last argument");if(a){if(i<2)throw new Error("Too few arguments provided");i===2?(o=l,l=n,n=t=void 0):i===3&&(n.getContext&&typeof o>"u"?(o=t,t=void 0):(o=t,t=l,l=n,n=void 0))}else{if(i<1)throw new Error("Too few arguments provided");return i===1?(l=n,n=t=void 0):i===2&&!n.getContext&&(t=l,l=n,n=void 0),new Promise(function(u,f){try{const s=QRCode.create(l,t);u(e(s,n,t))}catch(s){f(s)}})}try{const u=QRCode.create(l,t);o(null,e(u,n,t))}catch(u){o(u)}}browser.create=QRCode.create;browser.toCanvas=renderCanvas.bind(null,CanvasRenderer.render);browser.toDataURL=renderCanvas.bind(null,CanvasRenderer.renderToDataURL);browser.toString=renderCanvas.bind(null,function(e,n,l){return SvgRenderer.render(e,l)});const _sfc_main$1=defineComponent({name:"ButtonDialog",__name:"dialog",props:{commonOptions:{default:()=>({})},row:{},selected:{},selectOptions:{},id:{},type:{},display:{},url:{},title:{},small:{},content:{},size:{},fullWidth:{type:Boolean,default:!1},fullHeight:{type:Boolean,default:!1},form:{},button:{},options:{}},emits:["done","close"],setup(e,{expose:n,emit:l}){n();const t=useQuasar(),o=ref(),r=ref(""),i=ref({}),a=ref([]),u=ref({}),f=ref(!0),s=e;i.value={...s.form.params},u.value={...s.form.childrenForm};const b=ref(!1),m=ref(""),p=ref(!1),y=ref(""),B=ref(!1),S=computed(()=>{let E={};switch(s.size){case"small":E={...E,width:"300px"};break;case"medium":E={...E,width:"700px",maxWidth:"80vw"};break;case"fullWidth":E={...E,width:"100%"};break;case"smallFullHeigth":E={...E,width:"300px",maxHeight:"100vh"};break;case"mediumFullHeigth":E={...E,width:"700px",maxWidth:"80vw",maxHeight:"100vh"};break;case"fullHeigth":E={...E,height:"100%"};break;default:s.size&&(E={...E,width:s.size})}return s.fullWidth&&(E={...E,width:"100%"}),s.fullHeight&&(E={...E,height:"100%"}),E}),M=()=>{switch(r.value=s.content,s.type){case"route":y.value=s.url.replace(/row\.(\w+)/g,(F,L)=>s.row?.[L]||F),b.value=!0;break;case"copy":const E=s.content.replace(/row\.(\w+)/g,(F,L)=>s.row?.[L]||F);copyText(t,E);break;case"qrcode":p.value=!0,browser.toDataURL(s.content,{width:256},(F,L)=>{m.value=L});break;default:if(a.value=s.form.inputs.map(F=>F.map(L=>{if(L.type===InputTypeDynamic&&L.options){const K={...L,...L.options.input};return s.form.childrenForm&&L.options.childrenForm&&(u.value={...K.childrenForm,...L.options.childrenForm}),K}else if(L.type===InputTypeDynamic&&L.mask&&s.row&&s.row.hasOwnProperty(L.mask)){const K={...L,...s.row[L.mask].input};return s.form.childrenForm&&s.row[L.mask].childrenForm&&(u.value={...K.childrenForm,...s.row[L.mask].childrenForm}),K}return L})),s.row&&a.value.flat().forEach(F=>{if(F.field&&s.row){let L;if(F.scanField?L=F.scanField.split(".").reduce((le,de)=>le&&le[de],s.row):L=s.row[F.field],L!==void 0)switch(F.type){case InputTypeDateTimePicker:i.value[F.field]=date.formatDate(L,"YYYY/MM/DD HH:mm:ss");break;default:i.value[F.field]=L;break}}}),s.selectOptions?.[s.id]){if(!s.selected?.length){t.notify({type:"warning",message:"请先选择数据",position:"top"});return}const F=s.selectOptions[s.id];a.value.flat().forEach(L=>{L.field===F&&s.selected&&(i.value[L.field]=s.selected.map(K=>K[L.scanField]),r.value=i.value[L.field].join(","))})}f.value=a.value.flat().some(F=>!F.display),B.value=!0;break}},D=l,H={$q:t,dialogFormRef:o,contentData:r,formParams:i,formInputs:a,childrenForm:u,showInputs:f,props:s,routeDialog:b,qrcodeUrl:m,qrcodeDialog:p,routeUrl:y,isOpen:B,cardStyle:S,openDialog:M,emit:D,submitFunc:()=>{o.value.onSubmit()},closeDialog:()=>{B.value=!1,D("close")},eventSubmitFunc:(E,F,L,K)=>{api.post(s.url,K).then(()=>{B.value=!1,D("done"),t.notify({type:"positive",message:"操作成功",position:"top",timeout:1e3})})},FormInputs,TablePage};return Object.defineProperty(H,"__isScriptSetup",{enumerable:!1,value:!0}),H}}),_hoisted_1$1={class:"text-h6"},_hoisted_2$1=["innerHTML"],_hoisted_3$1={class:"text-h6"},_hoisted_4$1=["innerHTML"],_hoisted_5$1=["src"];function _sfc_render$1(e,n,l,t,o,r){return openBlock(),createElementBlock("div",null,[createVNode(QBtn,{label:l.button.label,size:l.button.size,color:l.button.color,class:normalizeClass(l.button.class),style:normalizeStyle(l.button.style),onClick:withModifiers(t.openDialog,["stop"])},null,8,["label","size","color","class","style"]),createVNode(QDialog,{modelValue:t.isOpen,"onUpdate:modelValue":n[0]||(n[0]=i=>t.isOpen=i),onHide:t.closeDialog},{default:withCtx(()=>[createVNode(QCard,{style:normalizeStyle(t.cardStyle)},{default:withCtx(()=>[createVNode(QCardSection,null,{default:withCtx(()=>[createBaseVNode("div",_hoisted_1$1,toDisplayString(l.title),1),l.small?(openBlock(),createElementBlock("div",{key:0,class:"text-caption text-grey-7",style:{"word-break":"break-all"},innerHTML:l.small},null,8,_hoisted_2$1)):createCommentVNode("",!0)]),_:1}),t.contentData?(openBlock(),createBlock(QCardSection,{key:0,style:{"word-break":"break-all"}},{default:withCtx(()=>[createTextVNode(toDisplayString(t.contentData),1)]),_:1})):createCommentVNode("",!0),createVNode(QCardSection,{class:"q-pa-none"},{default:withCtx(()=>[createVNode(t.FormInputs,{ref:"dialogFormRef","show-inputs":t.showInputs,"children-form":t.childrenForm,inputs:t.formInputs,params:t.formParams,"common-options":l.commonOptions,label:l.form.label,field:l.form.field,"scan-field":l.form.scanField,options:l.form.options,"cols-class":"col",style:{padding:"16px"},onSubmit:t.eventSubmitFunc},null,8,["show-inputs","children-form","inputs","params","common-options","label","field","scan-field","options"])]),_:1}),l.options&&(l.options?.submit||l.options?.cancel)?(openBlock(),createBlock(QCardActions,{key:1,align:"right",class:"q-pa-md",style:{position:"sticky",bottom:"0","background-color":"white","z-index":"1"}},{default:withCtx(()=>[l.options?.cancel?withDirectives((openBlock(),createBlock(QBtn,{key:0,label:l.options.cancel.label,size:l.options.cancel.size,color:l.options.cancel.color},null,8,["label","size","color"])),[[ClosePopup]]):createCommentVNode("",!0),l.options?.submit?(openBlock(),createBlock(QBtn,{key:1,label:l.options.submit.label,size:l.options.cancel.size,color:l.options.submit.color,onClick:t.submitFunc},null,8,["label","size","color"])):createCommentVNode("",!0)]),_:1})):createCommentVNode("",!0)]),_:1},8,["style"])]),_:1},8,["modelValue"]),createVNode(QDialog,{modelValue:t.routeDialog,"onUpdate:modelValue":n[1]||(n[1]=i=>t.routeDialog=i),"full-height":"","full-width":""},{default:withCtx(()=>[createVNode(QCard,null,{default:withCtx(()=>[createVNode(QCardSection,null,{default:withCtx(()=>[createVNode(t.TablePage,{url:t.routeUrl},null,8,["url"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),createVNode(QDialog,{modelValue:t.qrcodeDialog,"onUpdate:modelValue":n[2]||(n[2]=i=>t.qrcodeDialog=i)},{default:withCtx(()=>[createVNode(QCard,null,{default:withCtx(()=>[createVNode(QCardSection,null,{default:withCtx(()=>[createBaseVNode("div",_hoisted_3$1,toDisplayString(l.title),1),l.small?(openBlock(),createElementBlock("div",{key:0,class:"text-caption text-grey-7",style:{"word-break":"break-all"},innerHTML:l.small},null,8,_hoisted_4$1)):createCommentVNode("",!0)]),_:1}),createVNode(QCardSection,null,{default:withCtx(()=>[createBaseVNode("img",{src:t.qrcodeUrl},null,8,_hoisted_5$1)]),_:1})]),_:1})]),_:1},8,["modelValue"])])}const ButtonDialogComponents=_export_sfc(_sfc_main$1,[["render",_sfc_render$1],["__file","dialog.vue"]]),autoRefreshIntervalTime=1e4,_sfc_main=defineComponent({name:"TablePage",__name:"table",props:{url:{type:String,default:""}},setup(__props,{expose:__expose}){__expose();const $userStore=useUserStore(),currentPath=useRoute(),props=__props;let defaultPath=currentPath.path,defaultQuery=currentPath.query;if(props.url){const e=new URL(props.url,window.location.origin);defaultPath=e.pathname,defaultQuery=Object.fromEntries(e.searchParams)}defaultQuery=Object.fromEntries(Object.entries(defaultQuery).map(([e,n])=>[e,isNaN(Number(n))?n:Number(n)]));const refreshInterval=ref(null),autoRefresh=ref(!1),requestUrl=ref(""),commonOptions=ref({}),searchForm=ref({label:"",field:"",scanField:"",inputs:[],params:{pagination:{}},options:!1}),tableTools=ref(),columns=ref([]),columnOptions=ref([]),rows=ref([]),tableLoading=ref(!1),selected=ref([]),buttonDialogSelected=ref(),searchSubmitFunc=(e,n,l,t)=>{searchForm.value.params=t,onRequest(searchForm.value.params)},fetchData=()=>api.post(requestUrl.value,{...searchForm.value.params,...defaultQuery},{headers:{"X-Skip-Loading":autoRefresh.value?"false":"true"}}).then(e=>{rows.value=e.items,searchForm.value.params.pagination.rowsNumber=e.count}),onRequest=e=>{const{page:n,rowsPerPage:l,sortBy:t,descending:o}=e.pagination;searchForm.value.params.pagination={...searchForm.value.params.pagination,page:n,rowsPerPage:l,sortBy:t,descending:o},fetchData()};onMounted(()=>{$userStore.isAuthenticated&&requestConfigure(defaultPath,defaultQuery).then(res=>{requestUrl.value=res.url,searchForm.value=res.searchs,commonOptions.value=res.commonOptions,Object.keys(commonOptions.value).forEach(e=>{Array.isArray(commonOptions.value[e])&&commonOptions.value[e].unshift({label:"全部",value:null})}),buttonDialogSelected.value=res.selected,tableTools.value=res.tools,columns.value=res.columns,columns.value=columns.value.map(column=>{if(typeof column.format=="string"&&column.format.trim()!=="")try{column.format=eval(`(${column.format})`)}catch(e){column.format=n=>n}return column}),columnOptions.value=res.columnOptions,columnOptions.value&&columnOptions.value.length>0&&columns.value.push({name:"options",label:"操作按钮",field:"options",align:"center",sortable:!1,required:!0}),fetchData().then(()=>{tableLoading.value=!0,autoRefresh.value=res.autoRefresh,startAutoRefresh(),addClickImage()})})}),onBeforeUpdate(()=>{startAutoRefresh()}),onDeactivated(()=>{stopAutoRefresh()});const startAutoRefresh=()=>{autoRefresh.value&&refreshInterval.value===null&&(refreshInterval.value=setInterval(()=>{fetchData()},autoRefreshIntervalTime))},stopAutoRefresh=()=>{refreshInterval.value!==null&&(clearInterval(refreshInterval.value),refreshInterval.value=null)},displayEvalFunc=(row,display)=>display===""||eval(display),closeDialogFunc=()=>{selected.value=[]},imagesDialog=ref(!1),imagesList=ref([]),addClickImage=()=>{setTimeout(()=>{document.querySelectorAll(".images").forEach(e=>{e.addEventListener("click",function(){imagesList.value=[],e.querySelectorAll("img").forEach(n=>{imagesList.value.push(n.src),imagesDialog.value=!0})})})})},__returned__={$userStore,currentPath,props,get defaultPath(){return defaultPath},set defaultPath(e){defaultPath=e},get defaultQuery(){return defaultQuery},set defaultQuery(e){defaultQuery=e},refreshInterval,autoRefreshIntervalTime,autoRefresh,requestUrl,commonOptions,searchForm,tableTools,columns,columnOptions,rows,tableLoading,selected,buttonDialogSelected,searchSubmitFunc,fetchData,onRequest,startAutoRefresh,stopAutoRefresh,displayEvalFunc,closeDialogFunc,imagesDialog,imagesList,addClickImage,FormInputs,ButtonDialogComponents};return Object.defineProperty(__returned__,"__isScriptSetup",{enumerable:!1,value:!0}),__returned__}}),_hoisted_1={key:0,class:"q-pa-md"},_hoisted_2={class:"row q-mb-md"},_hoisted_3={key:0,class:"row q-mb-md"},_hoisted_4={key:0},_hoisted_5=["innerHTML"],_hoisted_6={key:1,class:"q-pa-md"},_hoisted_7={class:"row q-gutter-sm q-mb-md"},_hoisted_8={class:"row q-gutter-sm q-mb-md"},_hoisted_9={class:"text-left",style:{width:"150px"}},_hoisted_10={class:"text-right"},_hoisted_11={class:"text-right"},_hoisted_12={class:"text-right"},_hoisted_13={class:"text-right"},_hoisted_14={class:"text-right"},_hoisted_15={class:"text-left"},_hoisted_16={class:"text-right"},_hoisted_17={class:"text-right"},_hoisted_18={class:"text-right"},_hoisted_19={class:"text-right"},_hoisted_20={class:"text-right"},_hoisted_21=["src"];function _sfc_render(e,n,l,t,o,r){return openBlock(),createElementBlock(Fragment,null,[t.tableLoading?(openBlock(),createElementBlock("div",_hoisted_1,[createBaseVNode("div",_hoisted_2,[createVNode(t.FormInputs,{inputs:t.searchForm.inputs,params:t.searchForm.params,"common-options":t.commonOptions,label:t.searchForm.label,field:t.searchForm.field,"scan-field":t.searchForm.scanField,options:t.searchForm.options,"show-search-button":!0,onSubmit:t.searchSubmitFunc},null,8,["inputs","params","common-options","label","field","scan-field","options"])]),t.tableTools&&t.tableTools.length>0?(openBlock(),createElementBlock("div",_hoisted_3,[(openBlock(!0),createElementBlock(Fragment,null,renderList(t.tableTools,i=>(openBlock(),createElementBlock("div",{key:i.id,class:"row q-col-gutter-sm"},[(openBlock(!0),createElementBlock(Fragment,null,renderList(i,a=>(openBlock(),createElementBlock(Fragment,{key:a.id},[t.$userStore.hasRoute(a.url)?(openBlock(),createBlock(t.ButtonDialogComponents,{key:0,id:a.id,row:t.defaultQuery,title:a.title,button:a.button,form:a.form,url:a.url,options:a.options,"common-options":t.commonOptions,small:a.small,content:a.content,size:a.size,"full-width":a.fullWidth,"full-height":a.fullHeight,selected:t.selected,"select-options":t.buttonDialogSelected,onDone:t.fetchData,onClose:t.closeDialogFunc},null,8,["id","row","title","button","form","url","options","common-options","small","content","size","full-width","full-height","selected","select-options"])):createCommentVNode("",!0)],64))),128))]))),128))])):createCommentVNode("",!0),createVNode(QTable,{class:normalizeClass(t.columnOptions&&t.columnOptions.length>0?"my-sticky-last-column-table":""),rows:t.rows,columns:t.columns,"row-key":"id",pagination:t.searchForm.params.pagination,"onUpdate:pagination":n[0]||(n[0]=i=>t.searchForm.params.pagination=i),onRequest:t.onRequest,selection:"multiple",selected:t.selected,"onUpdate:selected":n[1]||(n[1]=i=>t.selected=i)},{body:withCtx(i=>[createVNode(QTr,{props:i},{default:withCtx(()=>[createVNode(QTd,{"auto-width":""},{default:withCtx(()=>[createVNode(QCheckbox,{modelValue:i.selected,"onUpdate:modelValue":a=>i.selected=a},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),(openBlock(!0),createElementBlock(Fragment,null,renderList(i.cols,a=>(openBlock(),createBlock(QTd,{key:a.name,props:i},{default:withCtx(()=>[a.name==="options"?(openBlock(),createElementBlock("div",_hoisted_4,[t.columnOptions&&t.columnOptions.length>0?(openBlock(!0),createElementBlock(Fragment,{key:0},renderList(t.columnOptions,u=>(openBlock(),createElementBlock("div",{key:u.id,class:"row q-col-gutter-sm",style:normalizeStyle({minWidth:u.length*60+"px"})},[(openBlock(!0),createElementBlock(Fragment,null,renderList(u,(f,s)=>(openBlock(),createElementBlock(Fragment,{key:s},[t.displayEvalFunc(i.row,f.display)&&t.$userStore.hasRoute(f.url)?(openBlock(),createBlock(t.ButtonDialogComponents,{key:0,class:"q-mb-xs",id:f.id,title:f.title,type:f.type,button:f.button,form:f.form,url:f.url,options:f.options,"common-options":t.commonOptions,small:f.small,content:f.content,size:f.size,"full-width":f.fullWidth,"full-height":f.fullHeight,selected:t.selected,"select-options":t.buttonDialogSelected,row:i.row,onDone:t.fetchData},null,8,["id","title","type","button","form","url","options","common-options","small","content","size","full-width","full-height","selected","select-options","row"])):createCommentVNode("",!0)],64))),128))],4))),128)):createCommentVNode("",!0)])):(openBlock(),createElementBlock("div",{key:1,innerHTML:a.value},null,8,_hoisted_5))]),_:2},1032,["props"]))),128))]),_:2},1032,["props"])]),_:1},8,["class","rows","columns","pagination","selected"])])):(openBlock(),createElementBlock("div",_hoisted_6,[createBaseVNode("div",_hoisted_7,[(openBlock(),createElementBlock(Fragment,null,renderList(10,i=>createBaseVNode("div",{key:i},[createVNode(QSkeleton,{height:"32px",style:{width:"160px"},type:"QInput"})])),64))]),createBaseVNode("div",_hoisted_8,[createVNode(QSkeleton,{height:"32px",type:"QBtn"}),createVNode(QSkeleton,{height:"32px",type:"QBtn"})]),createVNode(QMarkupTable,null,{default:withCtx(()=>[createBaseVNode("thead",null,[createBaseVNode("tr",null,[createBaseVNode("th",_hoisted_9,[createVNode(QSkeleton,{animation:"blink",type:"text"})]),createBaseVNode("th",_hoisted_10,[createVNode(QSkeleton,{animation:"blink",type:"text"})]),createBaseVNode("th",_hoisted_11,[createVNode(QSkeleton,{animation:"blink",type:"text"})]),createBaseVNode("th",_hoisted_12,[createVNode(QSkeleton,{animation:"blink",type:"text"})]),createBaseVNode("th",_hoisted_13,[createVNode(QSkeleton,{animation:"blink",type:"text"})]),createBaseVNode("th",_hoisted_14,[createVNode(QSkeleton,{animation:"blink",type:"text"})])])]),createBaseVNode("tbody",null,[(openBlock(),createElementBlock(Fragment,null,renderList(5,i=>createBaseVNode("tr",{key:i},[createBaseVNode("td",_hoisted_15,[createVNode(QSkeleton,{animation:"blink",type:"text",width:"85px"})]),createBaseVNode("td",_hoisted_16,[createVNode(QSkeleton,{animation:"blink",type:"text",width:"50px"})]),createBaseVNode("td",_hoisted_17,[createVNode(QSkeleton,{animation:"blink",type:"text",width:"35px"})]),createBaseVNode("td",_hoisted_18,[createVNode(QSkeleton,{animation:"blink",type:"text",width:"65px"})]),createBaseVNode("td",_hoisted_19,[createVNode(QSkeleton,{animation:"blink",type:"text",width:"25px"})]),createBaseVNode("td",_hoisted_20,[createVNode(QSkeleton,{animation:"blink",type:"text",width:"85px"})])])),64))])]),_:1})])),createVNode(QDialog,{modelValue:t.imagesDialog,"onUpdate:modelValue":n[2]||(n[2]=i=>t.imagesDialog=i)},{default:withCtx(()=>[createVNode(QCard,{style:{"max-width":"650px"}},{default:withCtx(()=>[createVNode(QCardSection,{class:"q-gutter-y-lg"},{default:withCtx(()=>[(openBlock(!0),createElementBlock(Fragment,null,renderList(t.imagesList,i=>(openBlock(),createElementBlock("img",{key:i,src:i,alt:"",class:"full-width"},null,8,_hoisted_21))),128))]),_:1})]),_:1})]),_:1},8,["modelValue"])],64)}const TablePage=_export_sfc(_sfc_main,[["render",_sfc_render],["__scopeId","data-v-d24a17d8"],["__file","table.vue"]]);export{TablePage as default};
