import{i as Bt,q as Ne,r as T,c as b,V as ge,o as Pt,aX as dn,am as Vn,a3 as Kn,h as w,a9 as Xe,ab as Xn,a4 as ze,M as Qe,Z as Zn,g as de,p as re,S as fn,x as ot,an as vn,w as te,aB as hn,U as Gn,t as Fe,aH as Jn,aY as ea,N as ta,ad as Xt,ac as na,aZ as tt,a as aa,ai as $t,a_ as oa,a$ as la,b0 as ra,b1 as ia,ap as De,ak as lt,b2 as mn,b3 as ua,a7 as rt,ar as Me,at as Ie,Y as Ct,aV as sa,W as gn,al as it,X as bn,ao as yn,b4 as pn,$ as ca,a0 as da,a2 as fa,aa as We,b as va,ah as ha,b5 as Dt,Q as ke,ag as ma,b6 as ga,d as ba,I as ya,_ as pa,l as le,m as Ce,j as ee,z as V,y as pe,B as Ee,E as _e,n as we,A as Ae,F as _a,D as wa,aR as Sa,b7 as _n,b8 as ka,af as wn,O as xa,aq as qa,as as bt}from"./index-H1mtFlU6.js";import{r as Zt,n as Ca,l as Sn}from"./QInput-F165baog.js";import{Q as Da}from"./QResizeObserver-DJfGZaa6.js";import{a as Ke,Q as yt,b as Ma,c as Ta,d as Fa}from"./use-checkbox-ZuTr6CRM.js";import{r as Mt,b as kn,i as Ea,A as Ba}from"./axios-JqZ6Qsjg.js";import{u as Ye,a as je}from"./use-dark-BZZjkWkU.js";let Pa=0;const $a=["click","keydown"],za={icon:String,label:[Number,String],alert:[Boolean,String],alertIcon:String,name:{type:[Number,String],default:()=>`t_${Pa++}`},noCaps:Boolean,tabindex:[String,Number],disable:Boolean,contentClass:String,ripple:{type:[Boolean,Object],default:!0}};function Aa(e,t,o,a){const n=Bt(dn,Ne);if(n===Ne)return console.error("QTab/QRouteTab component needs to be child of QTabs"),Ne;const{proxy:l}=de(),r=T(null),i=T(null),s=T(null),f=b(()=>e.disable===!0||e.ripple===!1?!1:Object.assign({keyCodes:[13,32],early:!0},e.ripple===!0?{}:e.ripple)),c=b(()=>n.currentModel.value===e.name),m=b(()=>"q-tab relative-position self-stretch flex flex-center text-center"+(c.value===!0?" q-tab--active"+(n.tabProps.value.activeClass?" "+n.tabProps.value.activeClass:"")+(n.tabProps.value.activeColor?` text-${n.tabProps.value.activeColor}`:"")+(n.tabProps.value.activeBgColor?` bg-${n.tabProps.value.activeBgColor}`:""):" q-tab--inactive")+(e.icon&&e.label&&n.tabProps.value.inlineLabel===!1?" q-tab--full":"")+(e.noCaps===!0||n.tabProps.value.noCaps===!0?" q-tab--no-caps":"")+(e.disable===!0?" disabled":" q-focusable q-hoverable cursor-pointer")),_=b(()=>"q-tab__content self-stretch flex-center relative-position q-anchor--skip non-selectable "+(n.tabProps.value.inlineLabel===!0?"row no-wrap q-tab__content--inline":"column")+(e.contentClass!==void 0?` ${e.contentClass}`:"")),d=b(()=>e.disable===!0||n.hasFocus.value===!0||c.value===!1&&n.hasActiveTab.value===!0?-1:e.tabindex||0);function u(g,B){if(B!==!0&&r.value!==null&&r.value.focus(),e.disable!==!0){n.updateModel({name:e.name}),o("click",g);return}}function y(g){Xe(g,[13,32])?u(g,!0):Xn(g)!==!0&&g.keyCode>=35&&g.keyCode<=40&&g.altKey!==!0&&g.metaKey!==!0&&n.onKbdNavigate(g.keyCode,l.$el)===!0&&ze(g),o("keydown",g)}function E(){const g=n.tabProps.value.narrowIndicator,B=[],x=w("div",{ref:s,class:["q-tab__indicator",n.tabProps.value.indicatorClass]});e.icon!==void 0&&B.push(w(Qe,{class:"q-tab__icon",name:e.icon})),e.label!==void 0&&B.push(w("div",{class:"q-tab__label"},e.label)),e.alert!==!1&&B.push(e.alertIcon!==void 0?w(Qe,{class:"q-tab__alert-icon",color:e.alert!==!0?e.alert:void 0,name:e.alertIcon}):w("div",{class:"q-tab__alert"+(e.alert!==!0?` text-${e.alert}`:"")})),g===!0&&B.push(x);const k=[w("div",{class:"q-focus-helper",tabindex:-1,ref:r}),w("div",{class:_.value},Zn(t.default,B))];return g===!1&&k.push(x),k}const D={name:b(()=>e.name),rootRef:i,tabIndicatorRef:s,routeData:a};ge(()=>{n.unregisterTab(D)}),Pt(()=>{n.registerTab(D)});function h(g,B){const x={ref:i,class:m.value,tabindex:d.value,role:"tab","aria-selected":c.value===!0?"true":"false","aria-disabled":e.disable===!0?"true":void 0,onClick:u,onKeydown:y,...B};return Vn(w(g,x,E()),[[Kn,f.value]])}return{renderTab:h,$tabs:n}}const xl=re({name:"QTab",props:za,emits:$a,setup(e,{slots:t,emit:o}){const{renderTab:a}=Aa(e,t,o);return()=>a("div")}});function Re(){let e;const t=de();function o(){e=void 0}return fn(o),ge(o),{removeTick:o,registerTick(a){e=a,ot(()=>{e===a&&(vn(t)===!1&&e(),e=void 0)})}}}let xn=!1;{const e=document.createElement("div");e.setAttribute("dir","rtl"),Object.assign(e.style,{width:"1px",height:"1px",overflow:"auto"});const t=document.createElement("div");Object.assign(t.style,{width:"1000px",height:"1px"}),document.body.appendChild(e),e.appendChild(t),e.scrollLeft=-1e3,xn=e.scrollLeft>=0,e.remove()}function La(e,t,o){const a=o===!0?["left","right"]:["top","bottom"];return`absolute-${t===!0?a[0]:a[1]}${e?` text-${e}`:""}`}const Ha=["left","center","right","justify"],ql=re({name:"QTabs",props:{modelValue:[Number,String],align:{type:String,default:"center",validator:e=>Ha.includes(e)},breakpoint:{type:[String,Number],default:600},vertical:Boolean,shrink:Boolean,stretch:Boolean,activeClass:String,activeColor:String,activeBgColor:String,indicatorColor:String,leftIcon:String,rightIcon:String,outsideArrows:Boolean,mobileArrows:Boolean,switchIndicator:Boolean,narrowIndicator:Boolean,inlineLabel:Boolean,noCaps:Boolean,dense:Boolean,contentClass:String,"onUpdate:modelValue":[Function,Array]},setup(e,{slots:t,emit:o}){const{proxy:a}=de(),{$q:n}=a,{registerTick:l}=Re(),{registerTick:r}=Re(),{registerTick:i}=Re(),{registerTimeout:s,removeTimeout:f}=Ke(),{registerTimeout:c,removeTimeout:m}=Ke(),_=T(null),d=T(null),u=T(e.modelValue),y=T(!1),E=T(!0),D=T(!1),h=T(!1),g=[],B=T(0),x=T(!1);let k=null,q=null,$;const M=b(()=>({activeClass:e.activeClass,activeColor:e.activeColor,activeBgColor:e.activeBgColor,indicatorClass:La(e.indicatorColor,e.switchIndicator,e.vertical),narrowIndicator:e.narrowIndicator,inlineLabel:e.inlineLabel,noCaps:e.noCaps})),A=b(()=>{const p=B.value,S=u.value;for(let z=0;z<p;z++)if(g[z].name.value===S)return!0;return!1}),N=b(()=>`q-tabs__content--align-${y.value===!0?"left":h.value===!0?"justify":e.align}`),F=b(()=>`q-tabs row no-wrap items-center q-tabs--${y.value===!0?"":"not-"}scrollable q-tabs--${e.vertical===!0?"vertical":"horizontal"} q-tabs__arrows--${e.outsideArrows===!0?"outside":"inside"} q-tabs--mobile-with${e.mobileArrows===!0?"":"out"}-arrows`+(e.dense===!0?" q-tabs--dense":"")+(e.shrink===!0?" col-shrink":"")+(e.stretch===!0?" self-stretch":"")),ne=b(()=>"q-tabs__content scroll--mobile row no-wrap items-center self-stretch hide-scrollbar relative-position "+N.value+(e.contentClass!==void 0?` ${e.contentClass}`:"")),K=b(()=>e.vertical===!0?{container:"height",content:"offsetHeight",scroll:"scrollHeight"}:{container:"width",content:"offsetWidth",scroll:"scrollWidth"}),X=b(()=>e.vertical!==!0&&n.lang.rtl===!0),Q=b(()=>xn===!1&&X.value===!0);te(X,se),te(()=>e.modelValue,p=>{Z({name:p,setCurrent:!0,skipEmit:!0})}),te(()=>e.outsideArrows,ie);function Z({name:p,setCurrent:S,skipEmit:z}){u.value!==p&&(z!==!0&&e["onUpdate:modelValue"]!==void 0&&o("update:modelValue",p),(S===!0||e["onUpdate:modelValue"]===void 0)&&(me(u.value,p),u.value=p))}function ie(){l(()=>{ue({width:_.value.offsetWidth,height:_.value.offsetHeight})})}function ue(p){if(K.value===void 0||d.value===null)return;const S=p[K.value.container],z=Math.min(d.value[K.value.scroll],Array.prototype.reduce.call(d.value.children,(Y,H)=>Y+(H[K.value.content]||0),0)),U=S>0&&z>S;y.value=U,U===!0&&r(se),h.value=S<parseInt(e.breakpoint,10)}function me(p,S){const z=p!=null&&p!==""?g.find(Y=>Y.name.value===p):null,U=S!=null&&S!==""?g.find(Y=>Y.name.value===S):null;if(Ze===!0)Ze=!1;else if(z&&U){const Y=z.tabIndicatorRef.value,H=U.tabIndicatorRef.value;k!==null&&(clearTimeout(k),k=null),Y.style.transition="none",Y.style.transform="none",H.style.transition="none",H.style.transform="none";const L=Y.getBoundingClientRect(),fe=H.getBoundingClientRect();H.style.transform=e.vertical===!0?`translate3d(0,${L.top-fe.top}px,0) scale3d(1,${fe.height?L.height/fe.height:1},1)`:`translate3d(${L.left-fe.left}px,0,0) scale3d(${fe.width?L.width/fe.width:1},1,1)`,i(()=>{k=setTimeout(()=>{k=null,H.style.transition="transform .25s cubic-bezier(.4, 0, .2, 1)",H.style.transform="none"},70)})}U&&y.value===!0&&ae(U.rootRef.value)}function ae(p){const{left:S,width:z,top:U,height:Y}=d.value.getBoundingClientRect(),H=p.getBoundingClientRect();let L=e.vertical===!0?H.top-U:H.left-S;if(L<0){d.value[e.vertical===!0?"scrollTop":"scrollLeft"]+=Math.floor(L),se();return}L+=e.vertical===!0?H.height-Y:H.width-z,L>0&&(d.value[e.vertical===!0?"scrollTop":"scrollLeft"]+=Math.ceil(L),se())}function se(){const p=d.value;if(p===null)return;const S=p.getBoundingClientRect(),z=e.vertical===!0?p.scrollTop:Math.abs(p.scrollLeft);X.value===!0?(E.value=Math.ceil(z+S.width)<p.scrollWidth-1,D.value=z>0):(E.value=z>0,D.value=e.vertical===!0?Math.ceil(z+S.height)<p.scrollHeight:Math.ceil(z+S.width)<p.scrollWidth)}function R(p){q!==null&&clearInterval(q),q=setInterval(()=>{v(p)===!0&&C()},5)}function G(){R(Q.value===!0?Number.MAX_SAFE_INTEGER:0)}function J(){R(Q.value===!0?0:Number.MAX_SAFE_INTEGER)}function C(){q!==null&&(clearInterval(q),q=null)}function I(p,S){const z=Array.prototype.filter.call(d.value.children,fe=>fe===S||fe.matches&&fe.matches(".q-tab.q-focusable")===!0),U=z.length;if(U===0)return;if(p===36)return ae(z[0]),z[0].focus(),!0;if(p===35)return ae(z[U-1]),z[U-1].focus(),!0;const Y=p===(e.vertical===!0?38:37),H=p===(e.vertical===!0?40:39),L=Y===!0?-1:H===!0?1:void 0;if(L!==void 0){const fe=X.value===!0?-1:1,ye=z.indexOf(S)+L*fe;return ye>=0&&ye<U&&(ae(z[ye]),z[ye].focus({preventScroll:!0})),!0}}const oe=b(()=>Q.value===!0?{get:p=>Math.abs(p.scrollLeft),set:(p,S)=>{p.scrollLeft=-S}}:e.vertical===!0?{get:p=>p.scrollTop,set:(p,S)=>{p.scrollTop=S}}:{get:p=>p.scrollLeft,set:(p,S)=>{p.scrollLeft=S}});function v(p){const S=d.value,{get:z,set:U}=oe.value;let Y=!1,H=z(S);const L=p<H?-1:1;return H+=L*5,H<0?(Y=!0,H=0):(L===-1&&H<=p||L===1&&H>=p)&&(Y=!0,H=p),U(S,H),se(),Y}function P(p,S){for(const z in p)if(p[z]!==S[z])return!1;return!0}function W(){let p=null,S={matchedLen:0,queryDiff:9999,hrefLen:0};const z=g.filter(L=>L.routeData!==void 0&&L.routeData.hasRouterLink.value===!0),{hash:U,query:Y}=a.$route,H=Object.keys(Y).length;for(const L of z){const fe=L.routeData.exact.value===!0;if(L.routeData[fe===!0?"linkIsExactActive":"linkIsActive"].value!==!0)continue;const{hash:ye,query:mt,matched:Nn,href:Wn}=L.routeData.resolvedLink.value,gt=Object.keys(mt).length;if(fe===!0){if(ye!==U||gt!==H||P(Y,mt)===!1)continue;p=L.name.value;break}if(ye!==""&&ye!==U||gt!==0&&P(mt,Y)===!1)continue;const qe={matchedLen:Nn.length,queryDiff:H-gt,hrefLen:Wn.length-ye.length};if(qe.matchedLen>S.matchedLen){p=L.name.value,S=qe;continue}else if(qe.matchedLen!==S.matchedLen)continue;if(qe.queryDiff<S.queryDiff)p=L.name.value,S=qe;else if(qe.queryDiff!==S.queryDiff)continue;qe.hrefLen>S.hrefLen&&(p=L.name.value,S=qe)}if(p===null&&g.some(L=>L.routeData===void 0&&L.name.value===u.value)===!0){Ze=!1;return}Z({name:p,setCurrent:!0})}function ve(p){if(f(),x.value!==!0&&_.value!==null&&p.target&&typeof p.target.closest=="function"){const S=p.target.closest(".q-tab");S&&_.value.contains(S)===!0&&(x.value=!0,y.value===!0&&ae(S))}}function ce(){s(()=>{x.value=!1},30)}function xe(){Wt.avoidRouteWatcher===!1?c(W):m()}function Nt(){if($===void 0){const p=te(()=>a.$route.fullPath,xe);$=()=>{p(),$=void 0}}}function Yn(p){g.push(p),B.value++,ie(),p.routeData===void 0||a.$route===void 0?c(()=>{if(y.value===!0){const S=u.value,z=S!=null&&S!==""?g.find(U=>U.name.value===S):null;z&&ae(z.rootRef.value)}}):(Nt(),p.routeData.hasRouterLink.value===!0&&xe())}function jn(p){g.splice(g.indexOf(p),1),B.value--,ie(),$!==void 0&&p.routeData!==void 0&&(g.every(S=>S.routeData===void 0)===!0&&$(),xe())}const Wt={currentModel:u,tabProps:M,hasFocus:x,hasActiveTab:A,registerTab:Yn,unregisterTab:jn,verifyRouteModel:xe,updateModel:Z,onKbdNavigate:I,avoidRouteWatcher:!1};hn(dn,Wt);function Vt(){k!==null&&clearTimeout(k),C(),$!==void 0&&$()}let Kt,Ze;return ge(Vt),fn(()=>{Kt=$!==void 0,Vt()}),Gn(()=>{Kt===!0&&(Nt(),Ze=!0,xe()),ie()}),()=>w("div",{ref:_,class:F.value,role:"tablist",onFocusin:ve,onFocusout:ce},[w(Da,{onResize:ue}),w("div",{ref:d,class:ne.value,onScroll:se},Fe(t.default)),w(Qe,{class:"q-tabs__arrow q-tabs__arrow--left absolute q-tab__icon"+(E.value===!0?"":" q-tabs__arrow--faded"),name:e.leftIcon||n.iconSet.tabs[e.vertical===!0?"up":"left"],onMousedownPassive:G,onTouchstartPassive:G,onMouseupPassive:C,onMouseleavePassive:C,onTouchendPassive:C}),w(Qe,{class:"q-tabs__arrow q-tabs__arrow--right absolute q-tab__icon"+(D.value===!0?"":" q-tabs__arrow--faded"),name:e.rightIcon||n.iconSet.tabs[e.vertical===!0?"down":"right"],onMousedownPassive:J,onTouchstartPassive:J,onMouseupPassive:C,onMouseleavePassive:C,onTouchendPassive:C})])}}),pt=re({name:"QItemSection",props:{avatar:Boolean,thumbnail:Boolean,side:Boolean,top:Boolean,noWrap:Boolean},setup(e,{slots:t}){const o=b(()=>`q-item__section column q-item__section--${e.avatar===!0||e.side===!0||e.thumbnail===!0?"side":"main"}`+(e.top===!0?" q-item__section--top justify-start":" justify-center")+(e.avatar===!0?" q-item__section--avatar":"")+(e.thumbnail===!0?" q-item__section--thumbnail":"")+(e.noWrap===!0?" q-item__section--nowrap":""));return()=>w("div",{class:o.value},Fe(t.default))}}),Ia=re({name:"QItem",props:{...Ye,...Jn,tag:{type:String,default:"div"},active:{type:Boolean,default:null},clickable:Boolean,dense:Boolean,insetLevel:Number,tabindex:[String,Number],focused:Boolean,manualFocus:Boolean},emits:["click","keyup"],setup(e,{slots:t,emit:o}){const{proxy:{$q:a}}=de(),n=je(e,a),{hasLink:l,linkAttrs:r,linkClass:i,linkTag:s,navigateOnClick:f}=ea(),c=T(null),m=T(null),_=b(()=>e.clickable===!0||l.value===!0||e.tag==="label"),d=b(()=>e.disable!==!0&&_.value===!0),u=b(()=>"q-item q-item-type row no-wrap"+(e.dense===!0?" q-item--dense":"")+(n.value===!0?" q-item--dark":"")+(l.value===!0&&e.active===null?i.value:e.active===!0?` q-item--active${e.activeClass!==void 0?` ${e.activeClass}`:""}`:"")+(e.disable===!0?" disabled":"")+(d.value===!0?" q-item--clickable q-link cursor-pointer "+(e.manualFocus===!0?"q-manual-focusable":"q-focusable q-hoverable")+(e.focused===!0?" q-manual-focusable--focused":""):"")),y=b(()=>e.insetLevel===void 0?null:{["padding"+(a.lang.rtl===!0?"Right":"Left")]:16+e.insetLevel*56+"px"});function E(g){d.value===!0&&(m.value!==null&&(g.qKeyEvent!==!0&&document.activeElement===c.value?m.value.focus():document.activeElement===m.value&&c.value.focus()),f(g))}function D(g){if(d.value===!0&&Xe(g,[13,32])===!0){ze(g),g.qKeyEvent=!0;const B=new MouseEvent("click",g);B.qKeyEvent=!0,c.value.dispatchEvent(B)}o("keyup",g)}function h(){const g=ta(t.default,[]);return d.value===!0&&g.unshift(w("div",{class:"q-focus-helper",tabindex:-1,ref:m})),g}return()=>{const g={ref:c,class:u.value,style:y.value,role:"listitem",onClick:E,onKeyup:D};return d.value===!0?(g.tabindex=e.tabindex||"0",Object.assign(g,r.value)):_.value===!0&&(g["aria-disabled"]="true"),w(s.value,g,h())}}}),Ra=["ul","ol"],Oa=re({name:"QList",props:{...Ye,bordered:Boolean,dense:Boolean,separator:Boolean,padding:Boolean,tag:{type:String,default:"div"}},setup(e,{slots:t}){const o=de(),a=je(e,o.proxy.$q),n=b(()=>Ra.includes(e.tag)?null:"list"),l=b(()=>"q-list"+(e.bordered===!0?" q-list--bordered":"")+(e.dense===!0?" q-list--dense":"")+(e.separator===!0?" q-list--separator":"")+(a.value===!0?" q-list--dark":"")+(e.padding===!0?" q-list--padding":""));return()=>w(e.tag,{class:l.value,role:n.value},Fe(t.default))}});function Qa(e,t,o){let a;function n(){a!==void 0&&(Xt.remove(a),a=void 0)}return ge(()=>{e.value===!0&&n()}),{removeFromHistory:n,addToHistory(){a={condition:()=>o.value===!0,handler:t},Xt.add(a)}}}const zt={modelValue:{type:Boolean,default:null},"onUpdate:modelValue":[Function,Array]},At=["beforeShow","show","beforeHide","hide"];function Lt({showing:e,canShow:t,hideOnRouteChange:o,handleShow:a,handleHide:n,processOnMount:l}){const r=de(),{props:i,emit:s,proxy:f}=r;let c;function m(h){e.value===!0?u(h):_(h)}function _(h){if(i.disable===!0||h!==void 0&&h.qAnchorHandled===!0||t!==void 0&&t(h)!==!0)return;const g=i["onUpdate:modelValue"]!==void 0;g===!0&&(s("update:modelValue",!0),c=h,ot(()=>{c===h&&(c=void 0)})),(i.modelValue===null||g===!1)&&d(h)}function d(h){e.value!==!0&&(e.value=!0,s("beforeShow",h),a!==void 0?a(h):s("show",h))}function u(h){if(i.disable===!0)return;const g=i["onUpdate:modelValue"]!==void 0;g===!0&&(s("update:modelValue",!1),c=h,ot(()=>{c===h&&(c=void 0)})),(i.modelValue===null||g===!1)&&y(h)}function y(h){e.value!==!1&&(e.value=!1,s("beforeHide",h),n!==void 0?n(h):s("hide",h))}function E(h){i.disable===!0&&h===!0?i["onUpdate:modelValue"]!==void 0&&s("update:modelValue",!1):h===!0!==e.value&&(h===!0?d:y)(c)}te(()=>i.modelValue,E),o!==void 0&&na(r)===!0&&te(()=>f.$route.fullPath,()=>{o.value===!0&&e.value===!0&&u()}),l===!0&&Pt(()=>{E(i.modelValue)});const D={show:_,hide:u,toggle:m};return Object.assign(f,D),D}const Ve={transitionShow:{type:String,default:"fade"},transitionHide:{type:String,default:"fade"},transitionDuration:{type:[String,Number],default:300}};function Ht(e,t=()=>{},o=()=>{}){return{transitionProps:b(()=>{const a=`q-transition--${e.transitionShow||t()}`,n=`q-transition--${e.transitionHide||o()}`;return{appear:!0,enterFromClass:`${a}-enter-from`,enterActiveClass:`${a}-enter-active`,enterToClass:`${a}-enter-to`,leaveFromClass:`${n}-leave-from`,leaveActiveClass:`${n}-leave-active`,leaveToClass:`${n}-leave-to`}}),transitionStyle:b(()=>`--q-transition-duration: ${e.transitionDuration}ms`)}}const Oe=[];function Ua(e){return Oe.find(t=>t.contentEl!==null&&t.contentEl.contains(e))}function qn(e,t){do{if(e.$options.name==="QMenu"){if(e.hide(t),e.$props.separateClosePopup===!0)return tt(e)}else if(e.__qPortal===!0){const o=tt(e);return o!==void 0&&o.$options.name==="QPopupProxy"?(e.hide(t),o):e}e=tt(e)}while(e!=null)}function Ya(e,t,o){for(;o!==0&&e!==void 0&&e!==null;){if(e.__qPortal===!0){if(o--,e.$options.name==="QMenu"){e=qn(e,t);continue}e.hide(t)}e=tt(e)}}const ja=re({name:"QPortal",setup(e,{slots:t}){return()=>t.default()}});function Na(e){for(e=e.parent;e!=null;){if(e.type.name==="QGlobalDialog")return!0;if(e.type.name==="QDialog"||e.type.name==="QMenu")return!1;e=e.parent}return!1}function It(e,t,o,a){const n=T(!1),l=T(!1);let r=null;const i={},s=a==="dialog"&&Na(e);function f(m){if(m===!0){Zt(i),l.value=!0;return}l.value=!1,n.value===!1&&(s===!1&&r===null&&(r=la(!1,a)),n.value=!0,Oe.push(e.proxy),Ca(i))}function c(m){if(l.value=!1,m!==!0)return;Zt(i),n.value=!1;const _=Oe.indexOf(e.proxy);_!==-1&&Oe.splice(_,1),r!==null&&(ra(r),r=null)}return aa(()=>{c(!0)}),e.proxy.__qPortal=!0,$t(e.proxy,"contentEl",()=>t.value),{showPortal:f,hidePortal:c,portalIsActive:n,portalIsAccessible:l,renderPortal:()=>s===!0?o():n.value===!0?[w(oa,{to:r},w(ja,o))]:void 0}}function Wa(){let e;return{preventBodyScroll(t){t!==e&&(e!==void 0||t===!0)&&(e=t,ia(t))}}}const Pe=[];let Ue;function Va(e){Ue=e.keyCode===27}function Ka(){Ue===!0&&(Ue=!1)}function Xa(e){Ue===!0&&(Ue=!1,Xe(e,27)===!0&&Pe[Pe.length-1](e))}function Cn(e){window[e]("keydown",Va),window[e]("blur",Ka),window[e]("keyup",Xa),Ue=!1}function Dn(e){De.is.desktop===!0&&(Pe.push(e),Pe.length===1&&Cn("addEventListener"))}function ut(e){const t=Pe.indexOf(e);t!==-1&&(Pe.splice(t,1),Pe.length===0&&Cn("removeEventListener"))}let Ge=0;const Za={standard:"fixed-full flex-center",top:"fixed-top justify-center",bottom:"fixed-bottom justify-center",right:"fixed-right items-center",left:"fixed-left items-center"},Gt={standard:["scale","scale"],top:["slide-down","slide-up"],bottom:["slide-up","slide-down"],right:["slide-left","slide-right"],left:["slide-right","slide-left"]},Ga=re({name:"QDialog",inheritAttrs:!1,props:{...zt,...Ve,transitionShow:String,transitionHide:String,persistent:Boolean,autoClose:Boolean,allowFocusOutside:Boolean,noEscDismiss:Boolean,noBackdropDismiss:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,noShake:Boolean,seamless:Boolean,maximized:Boolean,fullWidth:Boolean,fullHeight:Boolean,square:Boolean,backdropFilter:String,position:{type:String,default:"standard",validator:e=>["standard","top","bottom","left","right"].includes(e)}},emits:[...At,"shake","click","escapeKey"],setup(e,{slots:t,emit:o,attrs:a}){const n=de(),l=T(null),r=T(!1),i=T(!1);let s=null,f=null,c,m;const _=b(()=>e.persistent!==!0&&e.noRouteDismiss!==!0&&e.seamless!==!0),{preventBodyScroll:d}=Wa(),{registerTimeout:u}=Ke(),{registerTick:y,removeTick:E}=Re(),{transitionProps:D,transitionStyle:h}=Ht(e,()=>Gt[e.position][0],()=>Gt[e.position][1]),g=b(()=>h.value+(e.backdropFilter!==void 0?`;backdrop-filter:${e.backdropFilter};-webkit-backdrop-filter:${e.backdropFilter}`:"")),{showPortal:B,hidePortal:x,portalIsAccessible:k,renderPortal:q}=It(n,l,J,"dialog"),{hide:$}=Lt({showing:r,hideOnRouteChange:_,handleShow:X,handleHide:Q,processOnMount:!0}),{addToHistory:M,removeFromHistory:A}=Qa(r,$,_),N=b(()=>`q-dialog__inner flex no-pointer-events q-dialog__inner--${e.maximized===!0?"maximized":"minimized"} q-dialog__inner--${e.position} ${Za[e.position]}`+(i.value===!0?" q-dialog__inner--animating":"")+(e.fullWidth===!0?" q-dialog__inner--fullwidth":"")+(e.fullHeight===!0?" q-dialog__inner--fullheight":"")+(e.square===!0?" q-dialog__inner--square":"")),F=b(()=>r.value===!0&&e.seamless!==!0),ne=b(()=>e.autoClose===!0?{onClick:se}:{}),K=b(()=>[`q-dialog fullscreen no-pointer-events q-dialog--${F.value===!0?"modal":"seamless"}`,a.class]);te(()=>e.maximized,C=>{r.value===!0&&ae(C)}),te(F,C=>{d(C),C===!0?(kn(G),Dn(ue)):(Mt(G),ut(ue))});function X(C){M(),f=e.noRefocus===!1&&document.activeElement!==null?document.activeElement:null,ae(e.maximized),B(),i.value=!0,e.noFocus!==!0?(document.activeElement!==null&&document.activeElement.blur(),y(Z)):E(),u(()=>{if(n.proxy.$q.platform.is.ios===!0){if(e.seamless!==!0&&document.activeElement){const{top:I,bottom:oe}=document.activeElement.getBoundingClientRect(),{innerHeight:v}=window,P=window.visualViewport!==void 0?window.visualViewport.height:v;I>0&&oe>P/2&&(document.scrollingElement.scrollTop=Math.min(document.scrollingElement.scrollHeight-P,oe>=v?1/0:Math.ceil(document.scrollingElement.scrollTop+oe-P/2))),document.activeElement.scrollIntoView()}m=!0,l.value.click(),m=!1}B(!0),i.value=!1,o("show",C)},e.transitionDuration)}function Q(C){E(),A(),me(!0),i.value=!0,x(),f!==null&&(((C&&C.type.indexOf("key")===0?f.closest('[tabindex]:not([tabindex^="-"])'):void 0)||f).focus(),f=null),u(()=>{x(!0),i.value=!1,o("hide",C)},e.transitionDuration)}function Z(C){Sn(()=>{let I=l.value;if(I!==null){if(C!==void 0){const oe=I.querySelector(C);if(oe!==null){oe.focus({preventScroll:!0});return}}I.contains(document.activeElement)!==!0&&(I=I.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||I.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||I.querySelector("[autofocus], [data-autofocus]")||I,I.focus({preventScroll:!0}))}})}function ie(C){C&&typeof C.focus=="function"?C.focus({preventScroll:!0}):Z(),o("shake");const I=l.value;I!==null&&(I.classList.remove("q-animate--scale"),I.classList.add("q-animate--scale"),s!==null&&clearTimeout(s),s=setTimeout(()=>{s=null,l.value!==null&&(I.classList.remove("q-animate--scale"),Z())},170))}function ue(){e.seamless!==!0&&(e.persistent===!0||e.noEscDismiss===!0?e.maximized!==!0&&e.noShake!==!0&&ie():(o("escapeKey"),$()))}function me(C){s!==null&&(clearTimeout(s),s=null),(C===!0||r.value===!0)&&(ae(!1),e.seamless!==!0&&(d(!1),Mt(G),ut(ue))),C!==!0&&(f=null)}function ae(C){C===!0?c!==!0&&(Ge<1&&document.body.classList.add("q-body--dialog"),Ge++,c=!0):c===!0&&(Ge<2&&document.body.classList.remove("q-body--dialog"),Ge--,c=!1)}function se(C){m!==!0&&($(C),o("click",C))}function R(C){e.persistent!==!0&&e.noBackdropDismiss!==!0?$(C):e.noShake!==!0&&ie()}function G(C){e.allowFocusOutside!==!0&&k.value===!0&&mn(l.value,C.target)!==!0&&Z('[tabindex]:not([tabindex="-1"])')}Object.assign(n.proxy,{focus:Z,shake:ie,__updateRefocusTarget(C){f=C||null}}),ge(me);function J(){return w("div",{role:"dialog","aria-modal":F.value===!0?"true":"false",...a,class:K.value},[w(lt,{name:"q-transition--fade",appear:!0},()=>F.value===!0?w("div",{class:"q-dialog__backdrop fixed-full",style:g.value,"aria-hidden":"true",tabindex:-1,onClick:R}):null),w(lt,D.value,()=>r.value===!0?w("div",{ref:l,class:N.value,style:h.value,tabindex:-1,...ne.value},Fe(t.default)):null)])}return q}});function st(){if(window.getSelection!==void 0){const e=window.getSelection();e.empty!==void 0?e.empty():e.removeAllRanges!==void 0&&(e.removeAllRanges(),ua.is.mobile!==!0&&e.addRange(document.createRange()))}else document.selection!==void 0&&document.selection.empty()}const Mn={target:{type:[Boolean,String,Element],default:!0},noParentEvent:Boolean},Tn={...Mn,contextMenu:Boolean};function Rt({showing:e,avoidEmit:t,configureAnchorEl:o}){const{props:a,proxy:n,emit:l}=de(),r=T(null);let i=null;function s(d){return r.value===null?!1:d===void 0||d.touches===void 0||d.touches.length<=1}const f={};o===void 0&&(Object.assign(f,{hide(d){n.hide(d)},toggle(d){n.toggle(d),d.qAnchorHandled=!0},toggleKey(d){Xe(d,13)===!0&&f.toggle(d)},contextClick(d){n.hide(d),rt(d),ot(()=>{n.show(d),d.qAnchorHandled=!0})},prevent:rt,mobileTouch(d){if(f.mobileCleanup(d),s(d)!==!0)return;n.hide(d),r.value.classList.add("non-selectable");const u=d.target;Me(f,"anchor",[[u,"touchmove","mobileCleanup","passive"],[u,"touchend","mobileCleanup","passive"],[u,"touchcancel","mobileCleanup","passive"],[r.value,"contextmenu","prevent","notPassive"]]),i=setTimeout(()=>{i=null,n.show(d),d.qAnchorHandled=!0},300)},mobileCleanup(d){r.value.classList.remove("non-selectable"),i!==null&&(clearTimeout(i),i=null),e.value===!0&&d!==void 0&&st()}}),o=function(d=a.contextMenu){if(a.noParentEvent===!0||r.value===null)return;let u;d===!0?n.$q.platform.is.mobile===!0?u=[[r.value,"touchstart","mobileTouch","passive"]]:u=[[r.value,"mousedown","hide","passive"],[r.value,"contextmenu","contextClick","notPassive"]]:u=[[r.value,"click","toggle","passive"],[r.value,"keyup","toggleKey","passive"]],Me(f,"anchor",u)});function c(){Ie(f,"anchor")}function m(d){for(r.value=d;r.value.classList.contains("q-anchor--skip");)r.value=r.value.parentNode;o()}function _(){if(a.target===!1||a.target===""||n.$el.parentNode===null)r.value=null;else if(a.target===!0)m(n.$el.parentNode);else{let d=a.target;if(typeof a.target=="string")try{d=document.querySelector(a.target)}catch{d=void 0}d!=null?(r.value=d.$el||d,o()):(r.value=null,console.error(`Anchor: target "${a.target}" not found`))}}return te(()=>a.contextMenu,d=>{r.value!==null&&(c(),o(d))}),te(()=>a.target,()=>{r.value!==null&&c(),_()}),te(()=>a.noParentEvent,d=>{r.value!==null&&(d===!0?c():o())}),Pt(()=>{_(),t!==!0&&a.modelValue===!0&&r.value===null&&l("update:modelValue",!1)}),ge(()=>{i!==null&&clearTimeout(i),c()}),{anchorEl:r,canShow:s,anchorEvents:f}}function Fn(e,t){const o=T(null);let a;function n(i,s){const f=`${s!==void 0?"add":"remove"}EventListener`,c=s!==void 0?s:a;i!==window&&i[f]("scroll",c,Ct.passive),window[f]("scroll",c,Ct.passive),a=s}function l(){o.value!==null&&(n(o.value),o.value=null)}const r=te(()=>e.noParentEvent,()=>{o.value!==null&&(l(),t())});return ge(r),{localScrollTarget:o,unconfigureScrollTarget:l,changeScrollEvent:n}}const{notPassiveCapture:ct}=Ct,$e=[];function dt(e){const t=e.target;if(t===void 0||t.nodeType===8||t.classList.contains("no-pointer-events")===!0)return;let o=Oe.length-1;for(;o>=0;){const a=Oe[o].$;if(a.type.name==="QTooltip"){o--;continue}if(a.type.name!=="QDialog")break;if(a.props.seamless!==!0)return;o--}for(let a=$e.length-1;a>=0;a--){const n=$e[a];if((n.anchorEl.value===null||n.anchorEl.value.contains(t)===!1)&&(t===document.body||n.innerRef.value!==null&&n.innerRef.value.contains(t)===!1))e.qClickOutside=!0,n.onClickOutside(e);else return}}function En(e){$e.push(e),$e.length===1&&(document.addEventListener("mousedown",dt,ct),document.addEventListener("touchstart",dt,ct))}function ft(e){const t=$e.findIndex(o=>o===e);t!==-1&&($e.splice(t,1),$e.length===0&&(document.removeEventListener("mousedown",dt,ct),document.removeEventListener("touchstart",dt,ct)))}let Jt,en;function vt(e){const t=e.split(" ");return t.length!==2?!1:["top","center","bottom"].includes(t[0])!==!0?(console.error("Anchor/Self position must start with one of top/center/bottom"),!1):["left","middle","right","start","end"].includes(t[1])!==!0?(console.error("Anchor/Self position must end with one of left/middle/right/start/end"),!1):!0}function Bn(e){return e?!(e.length!==2||typeof e[0]!="number"||typeof e[1]!="number"):!0}const Tt={"start#ltr":"left","start#rtl":"right","end#ltr":"right","end#rtl":"left"};["left","middle","right"].forEach(e=>{Tt[`${e}#ltr`]=e,Tt[`${e}#rtl`]=e});function ht(e,t){const o=e.split(" ");return{vertical:o[0],horizontal:Tt[`${o[1]}#${t===!0?"rtl":"ltr"}`]}}function Ja(e,t){let{top:o,left:a,right:n,bottom:l,width:r,height:i}=e.getBoundingClientRect();return t!==void 0&&(o-=t[1],a-=t[0],l+=t[1],n+=t[0],r+=t[0],i+=t[1]),{top:o,bottom:l,height:i,left:a,right:n,width:r,middle:a+(n-a)/2,center:o+(l-o)/2}}function eo(e,t,o){let{top:a,left:n}=e.getBoundingClientRect();return a+=t.top,n+=t.left,o!==void 0&&(a+=o[1],n+=o[0]),{top:a,bottom:a+1,height:1,left:n,right:n+1,width:1,middle:n,center:a}}function to(e,t){return{top:0,center:t/2,bottom:t,left:0,middle:e/2,right:e}}function tn(e,t,o,a){return{top:e[o.vertical]-t[a.vertical],left:e[o.horizontal]-t[a.horizontal]}}function Ot(e,t=0){if(e.targetEl===null||e.anchorEl===null||t>5)return;if(e.targetEl.offsetHeight===0||e.targetEl.offsetWidth===0){setTimeout(()=>{Ot(e,t+1)},10);return}const{targetEl:o,offset:a,anchorEl:n,anchorOrigin:l,selfOrigin:r,absoluteOffset:i,fit:s,cover:f,maxHeight:c,maxWidth:m}=e;if(De.is.ios===!0&&window.visualViewport!==void 0){const k=document.body.style,{offsetLeft:q,offsetTop:$}=window.visualViewport;q!==Jt&&(k.setProperty("--q-pe-left",q+"px"),Jt=q),$!==en&&(k.setProperty("--q-pe-top",$+"px"),en=$)}const{scrollLeft:_,scrollTop:d}=o,u=i===void 0?Ja(n,f===!0?[0,0]:a):eo(n,i,a);Object.assign(o.style,{top:0,left:0,minWidth:null,minHeight:null,maxWidth:m,maxHeight:c,visibility:"visible"});const{offsetWidth:y,offsetHeight:E}=o,{elWidth:D,elHeight:h}=s===!0||f===!0?{elWidth:Math.max(u.width,y),elHeight:f===!0?Math.max(u.height,E):E}:{elWidth:y,elHeight:E};let g={maxWidth:m,maxHeight:c};(s===!0||f===!0)&&(g.minWidth=u.width+"px",f===!0&&(g.minHeight=u.height+"px")),Object.assign(o.style,g);const B=to(D,h);let x=tn(u,B,l,r);if(i===void 0||a===void 0)_t(x,u,B,l,r);else{const{top:k,left:q}=x;_t(x,u,B,l,r);let $=!1;if(x.top!==k){$=!0;const M=2*a[1];u.center=u.top-=M,u.bottom-=M+2}if(x.left!==q){$=!0;const M=2*a[0];u.middle=u.left-=M,u.right-=M+2}$===!0&&(x=tn(u,B,l,r),_t(x,u,B,l,r))}g={top:x.top+"px",left:x.left+"px"},x.maxHeight!==void 0&&(g.maxHeight=x.maxHeight+"px",u.height>x.maxHeight&&(g.minHeight=g.maxHeight)),x.maxWidth!==void 0&&(g.maxWidth=x.maxWidth+"px",u.width>x.maxWidth&&(g.minWidth=g.maxWidth)),Object.assign(o.style,g),o.scrollTop!==d&&(o.scrollTop=d),o.scrollLeft!==_&&(o.scrollLeft=_)}function _t(e,t,o,a,n){const l=o.bottom,r=o.right,i=sa(),s=window.innerHeight-i,f=document.body.clientWidth;if(e.top<0||e.top+l>s)if(n.vertical==="center")e.top=t[a.vertical]>s/2?Math.max(0,s-l):0,e.maxHeight=Math.min(l,s);else if(t[a.vertical]>s/2){const c=Math.min(s,a.vertical==="center"?t.center:a.vertical===n.vertical?t.bottom:t.top);e.maxHeight=Math.min(l,c),e.top=Math.max(0,c-l)}else e.top=Math.max(0,a.vertical==="center"?t.center:a.vertical===n.vertical?t.top:t.bottom),e.maxHeight=Math.min(l,s-e.top);if(e.left<0||e.left+r>f)if(e.maxWidth=Math.min(r,f),n.horizontal==="middle")e.left=t[a.horizontal]>f/2?Math.max(0,f-r):0;else if(t[a.horizontal]>f/2){const c=Math.min(f,a.horizontal==="middle"?t.middle:a.horizontal===n.horizontal?t.right:t.left);e.maxWidth=Math.min(r,c),e.left=Math.max(0,c-e.maxWidth)}else e.left=Math.max(0,a.horizontal==="middle"?t.middle:a.horizontal===n.horizontal?t.left:t.right),e.maxWidth=Math.min(r,f-e.left)}const no=re({name:"QMenu",inheritAttrs:!1,props:{...Tn,...zt,...Ye,...Ve,persistent:Boolean,autoClose:Boolean,separateClosePopup:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,fit:Boolean,cover:Boolean,square:Boolean,anchor:{type:String,validator:vt},self:{type:String,validator:vt},offset:{type:Array,validator:Bn},scrollTarget:gn,touchPosition:Boolean,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null}},emits:[...At,"click","escapeKey"],setup(e,{slots:t,emit:o,attrs:a}){let n=null,l,r,i;const s=de(),{proxy:f}=s,{$q:c}=f,m=T(null),_=T(!1),d=b(()=>e.persistent!==!0&&e.noRouteDismiss!==!0),u=je(e,c),{registerTick:y,removeTick:E}=Re(),{registerTimeout:D}=Ke(),{transitionProps:h,transitionStyle:g}=Ht(e),{localScrollTarget:B,changeScrollEvent:x,unconfigureScrollTarget:k}=Fn(e,R),{anchorEl:q,canShow:$}=Rt({showing:_}),{hide:M}=Lt({showing:_,canShow:$,handleShow:me,handleHide:ae,hideOnRouteChange:d,processOnMount:!0}),{showPortal:A,hidePortal:N,renderPortal:F}=It(s,m,oe,"menu"),ne={anchorEl:q,innerRef:m,onClickOutside(v){if(e.persistent!==!0&&_.value===!0)return M(v),(v.type==="touchstart"||v.target.classList.contains("q-dialog__backdrop"))&&ze(v),!0}},K=b(()=>ht(e.anchor||(e.cover===!0?"center middle":"bottom start"),c.lang.rtl)),X=b(()=>e.cover===!0?K.value:ht(e.self||"top start",c.lang.rtl)),Q=b(()=>(e.square===!0?" q-menu--square":"")+(u.value===!0?" q-menu--dark q-dark":"")),Z=b(()=>e.autoClose===!0?{onClick:G}:{}),ie=b(()=>_.value===!0&&e.persistent!==!0);te(ie,v=>{v===!0?(Dn(C),En(ne)):(ut(C),ft(ne))});function ue(){Sn(()=>{let v=m.value;v&&v.contains(document.activeElement)!==!0&&(v=v.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||v.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||v.querySelector("[autofocus], [data-autofocus]")||v,v.focus({preventScroll:!0}))})}function me(v){if(n=e.noRefocus===!1?document.activeElement:null,kn(J),A(),R(),l=void 0,v!==void 0&&(e.touchPosition||e.contextMenu)){const P=it(v);if(P.left!==void 0){const{top:W,left:ve}=q.value.getBoundingClientRect();l={left:P.left-ve,top:P.top-W}}}r===void 0&&(r=te(()=>c.screen.width+"|"+c.screen.height+"|"+e.self+"|"+e.anchor+"|"+c.lang.rtl,I)),e.noFocus!==!0&&document.activeElement.blur(),y(()=>{I(),e.noFocus!==!0&&ue()}),D(()=>{c.platform.is.ios===!0&&(i=e.autoClose,m.value.click()),I(),A(!0),o("show",v)},e.transitionDuration)}function ae(v){E(),N(),se(!0),n!==null&&(v===void 0||v.qClickOutside!==!0)&&(((v&&v.type.indexOf("key")===0?n.closest('[tabindex]:not([tabindex^="-"])'):void 0)||n).focus(),n=null),D(()=>{N(!0),o("hide",v)},e.transitionDuration)}function se(v){l=void 0,r!==void 0&&(r(),r=void 0),(v===!0||_.value===!0)&&(Mt(J),k(),ft(ne),ut(C)),v!==!0&&(n=null)}function R(){(q.value!==null||e.scrollTarget!==void 0)&&(B.value=bn(q.value,e.scrollTarget),x(B.value,I))}function G(v){i!==!0?(qn(f,v),o("click",v)):i=!1}function J(v){ie.value===!0&&e.noFocus!==!0&&mn(m.value,v.target)!==!0&&ue()}function C(v){o("escapeKey"),M(v)}function I(){Ot({targetEl:m.value,offset:e.offset,anchorEl:q.value,anchorOrigin:K.value,selfOrigin:X.value,absoluteOffset:l,fit:e.fit,cover:e.cover,maxHeight:e.maxHeight,maxWidth:e.maxWidth})}function oe(){return w(lt,h.value,()=>_.value===!0?w("div",{role:"menu",...a,ref:m,tabindex:-1,class:["q-menu q-position-engine scroll"+Q.value,a.class],style:[a.style,g.value],...Z.value},Fe(t.default)):null)}return ge(se),Object.assign(f,{focus:ue,updatePosition:I}),F}}),Cl=re({name:"QPopupProxy",props:{...Tn,breakpoint:{type:[String,Number],default:450}},emits:["show","hide"],setup(e,{slots:t,emit:o,attrs:a}){const{proxy:n}=de(),{$q:l}=n,r=T(!1),i=T(null),s=b(()=>parseInt(e.breakpoint,10)),{canShow:f}=Rt({showing:r});function c(){return l.screen.width<s.value||l.screen.height<s.value?"dialog":"menu"}const m=T(c()),_=b(()=>m.value==="menu"?{maxHeight:"99vh"}:{});te(()=>c(),y=>{r.value!==!0&&(m.value=y)});function d(y){r.value=!0,o("show",y)}function u(y){r.value=!1,m.value=c(),o("hide",y)}return Object.assign(n,{show(y){f(y)===!0&&i.value.show(y)},hide(y){i.value.hide(y)},toggle(y){i.value.toggle(y)}}),$t(n,"currentComponent",()=>({type:m.value,ref:i.value})),()=>{const y={ref:i,..._.value,...a,onShow:d,onHide:u};let E;return m.value==="dialog"?E=Ga:(E=no,Object.assign(y,{target:e.target,contextMenu:e.contextMenu,noParentEvent:!0,separateClosePopup:!0})),w(E,y,t.default)}}});function nn(e){if(e===!1)return 0;if(e===!0||e===void 0)return 1;const t=parseInt(e,10);return isNaN(t)?0:t}const Dl=yn({name:"close-popup",beforeMount(e,{value:t}){const o={depth:nn(t),handler(a){o.depth!==0&&setTimeout(()=>{const n=Ua(e);n!==void 0&&Ya(n,a,o.depth)})},handlerKey(a){Xe(a,13)===!0&&o.handler(a)}};e.__qclosepopup=o,e.addEventListener("click",o.handler),e.addEventListener("keyup",o.handlerKey)},updated(e,{value:t,oldValue:o}){t!==o&&(e.__qclosepopup.depth=nn(t))},beforeUnmount(e){const t=e.__qclosepopup;e.removeEventListener("click",t.handler),e.removeEventListener("keyup",t.handlerKey),delete e.__qclosepopup}}),Be=re({name:"QTooltip",inheritAttrs:!1,props:{...Mn,...zt,...Ve,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null},transitionShow:{...Ve.transitionShow,default:"jump-down"},transitionHide:{...Ve.transitionHide,default:"jump-up"},anchor:{type:String,default:"bottom middle",validator:vt},self:{type:String,default:"top middle",validator:vt},offset:{type:Array,default:()=>[14,14],validator:Bn},scrollTarget:gn,delay:{type:Number,default:0},hideDelay:{type:Number,default:0},persistent:Boolean},emits:[...At],setup(e,{slots:t,emit:o,attrs:a}){let n,l;const r=de(),{proxy:{$q:i}}=r,s=T(null),f=T(!1),c=b(()=>ht(e.anchor,i.lang.rtl)),m=b(()=>ht(e.self,i.lang.rtl)),_=b(()=>e.persistent!==!0),{registerTick:d,removeTick:u}=Re(),{registerTimeout:y}=Ke(),{transitionProps:E,transitionStyle:D}=Ht(e),{localScrollTarget:h,changeScrollEvent:g,unconfigureScrollTarget:B}=Fn(e,me),{anchorEl:x,canShow:k,anchorEvents:q}=Rt({showing:f,configureAnchorEl:ue}),{show:$,hide:M}=Lt({showing:f,canShow:k,handleShow:ne,handleHide:K,hideOnRouteChange:_,processOnMount:!0});Object.assign(q,{delayShow:Z,delayHide:ie});const{showPortal:A,hidePortal:N,renderPortal:F}=It(r,s,se,"tooltip");if(i.platform.is.mobile===!0){const R={anchorEl:x,innerRef:s,onClickOutside(J){return M(J),J.target.classList.contains("q-dialog__backdrop")&&ze(J),!0}},G=b(()=>e.modelValue===null&&e.persistent!==!0&&f.value===!0);te(G,J=>{(J===!0?En:ft)(R)}),ge(()=>{ft(R)})}function ne(R){A(),d(()=>{l=new MutationObserver(()=>Q()),l.observe(s.value,{attributes:!1,childList:!0,characterData:!0,subtree:!0}),Q(),me()}),n===void 0&&(n=te(()=>i.screen.width+"|"+i.screen.height+"|"+e.self+"|"+e.anchor+"|"+i.lang.rtl,Q)),y(()=>{A(!0),o("show",R)},e.transitionDuration)}function K(R){u(),N(),X(),y(()=>{N(!0),o("hide",R)},e.transitionDuration)}function X(){l!==void 0&&(l.disconnect(),l=void 0),n!==void 0&&(n(),n=void 0),B(),Ie(q,"tooltipTemp")}function Q(){Ot({targetEl:s.value,offset:e.offset,anchorEl:x.value,anchorOrigin:c.value,selfOrigin:m.value,maxHeight:e.maxHeight,maxWidth:e.maxWidth})}function Z(R){if(i.platform.is.mobile===!0){st(),document.body.classList.add("non-selectable");const G=x.value,J=["touchmove","touchcancel","touchend","click"].map(C=>[G,C,"delayHide","passiveCapture"]);Me(q,"tooltipTemp",J)}y(()=>{$(R)},e.delay)}function ie(R){i.platform.is.mobile===!0&&(Ie(q,"tooltipTemp"),st(),setTimeout(()=>{document.body.classList.remove("non-selectable")},10)),y(()=>{M(R)},e.hideDelay)}function ue(){if(e.noParentEvent===!0||x.value===null)return;const R=i.platform.is.mobile===!0?[[x.value,"touchstart","delayShow","passive"]]:[[x.value,"mouseenter","delayShow","passive"],[x.value,"mouseleave","delayHide","passive"]];Me(q,"anchor",R)}function me(){if(x.value!==null||e.scrollTarget!==void 0){h.value=bn(x.value,e.scrollTarget);const R=e.noParentEvent===!0?Q:M;g(h.value,R)}}function ae(){return f.value===!0?w("div",{...a,ref:s,class:["q-tooltip q-tooltip--style q-position-engine no-pointer-events",a.class],style:[a.style,D.value],role:"tooltip"},Fe(t.default)):null}function se(){return w(lt,E.value,ae)}return ge(X),Object.assign(r.proxy,{updatePosition:Q}),F}}),wt=re({name:"QUploaderAddTrigger",setup(){const e=Bt(pn,Ne);return e===Ne&&console.error("QUploaderAddTrigger needs to be child of QUploader"),e}}),ao={...ca,min:{type:Number,default:0},max:{type:Number,default:100},color:String,centerColor:String,trackColor:String,fontSize:String,rounded:Boolean,thickness:{type:Number,default:.2,validator:e=>e>=0&&e<=1},angle:{type:Number,default:0},showValue:Boolean,reverse:Boolean,instantFeedback:Boolean},an=["B","KB","MB","GB","TB","PB"];function on(e,t=1){let o=0;for(;parseInt(e,10)>=1024&&o<an.length-1;)e/=1024,++o;return`${e.toFixed(t)}${an[o]}`}function oo(e){return e.charAt(0).toUpperCase()+e.slice(1)}function lo(e,t,o){return o<=t?t:Math.min(o,Math.max(t,e))}function Ml(e,t,o){if(o<=t)return t;const a=o-t+1;let n=t+(e-t)%a;return n<t&&(n=a+n),n===0?0:n}function j(e,t=2,o="0"){if(e==null)return e;const a=""+e;return a.length>=t?a:new Array(t-a.length+1).join(o)+a}const Ft=50,Pn=2*Ft,$n=Pn*Math.PI,ro=Math.round($n*1e3)/1e3,io=re({name:"QCircularProgress",props:{...ao,value:{type:Number,default:0},animationSpeed:{type:[String,Number],default:600},indeterminate:Boolean},setup(e,{slots:t}){const{proxy:{$q:o}}=de(),a=da(e),n=b(()=>{const d=(o.lang.rtl===!0?-1:1)*e.angle;return{transform:e.reverse!==(o.lang.rtl===!0)?`scale3d(-1, 1, 1) rotate3d(0, 0, 1, ${-90-d}deg)`:`rotate3d(0, 0, 1, ${d-90}deg)`}}),l=b(()=>e.instantFeedback!==!0&&e.indeterminate!==!0?{transition:`stroke-dashoffset ${e.animationSpeed}ms ease 0s, stroke ${e.animationSpeed}ms ease`}:""),r=b(()=>Pn/(1-e.thickness/2)),i=b(()=>`${r.value/2} ${r.value/2} ${r.value} ${r.value}`),s=b(()=>lo(e.value,e.min,e.max)),f=b(()=>e.max-e.min),c=b(()=>e.thickness/2*r.value),m=b(()=>{const d=(e.max-s.value)/f.value,u=e.rounded===!0&&s.value<e.max&&d<.25?c.value/2*(1-d/.25):0;return $n*d+u});function _({thickness:d,offset:u,color:y,cls:E,rounded:D}){return w("circle",{class:"q-circular-progress__"+E+(y!==void 0?` text-${y}`:""),style:l.value,fill:"transparent",stroke:"currentColor","stroke-width":d,"stroke-dasharray":ro,"stroke-dashoffset":u,"stroke-linecap":D,cx:r.value,cy:r.value,r:Ft})}return()=>{const d=[];e.centerColor!==void 0&&e.centerColor!=="transparent"&&d.push(w("circle",{class:`q-circular-progress__center text-${e.centerColor}`,fill:"currentColor",r:Ft-c.value/2,cx:r.value,cy:r.value})),e.trackColor!==void 0&&e.trackColor!=="transparent"&&d.push(_({cls:"track",thickness:c.value,offset:0,color:e.trackColor})),d.push(_({cls:"circle",thickness:c.value,offset:m.value,color:e.color,rounded:e.rounded===!0?"round":void 0}));const u=[w("svg",{class:"q-circular-progress__svg",style:n.value,viewBox:i.value,"aria-hidden":"true"},d)];return e.showValue===!0&&u.push(w("div",{class:"q-circular-progress__text absolute-full row flex-center content-center",style:{fontSize:e.fontSize}},t.default!==void 0?t.default():[w("div",s.value)])),w("div",{class:`q-circular-progress q-circular-progress--${e.indeterminate===!0?"in":""}determinate`,style:a.value,role:"progressbar","aria-valuemin":e.min,"aria-valuemax":e.max,"aria-valuenow":e.indeterminate===!0?void 0:s.value},fa(t.internal,u))}}});function Le(e,t,o,a){const n=[];return e.forEach(l=>{a(l)===!0?n.push(l):t.push({failedPropValidation:o,file:l})}),n}function Je(e){e&&e.dataTransfer&&(e.dataTransfer.dropEffect="copy"),ze(e)}const uo={multiple:Boolean,accept:String,capture:String,maxFileSize:[Number,String],maxTotalSize:[Number,String],maxFiles:[Number,String],filter:Function},so=["rejected"];function co({editable:e,dnd:t,getFileInput:o,addFilesToQueue:a}){const{props:n,emit:l,proxy:r}=de(),i=T(null),s=b(()=>n.accept!==void 0?n.accept.split(",").map(h=>(h=h.trim(),h==="*"?"*/":(h.endsWith("/*")&&(h=h.slice(0,h.length-1)),h.toUpperCase()))):null),f=b(()=>parseInt(n.maxFiles,10)),c=b(()=>parseInt(n.maxTotalSize,10));function m(h){if(e.value)if(h!==Object(h)&&(h={target:null}),h.target!==null&&h.target.matches('input[type="file"]')===!0)h.clientX===0&&h.clientY===0&&We(h);else{const g=o();g&&g!==h.target&&g.click(h)}}function _(h){e.value&&h&&a(null,h)}function d(h,g,B,x){let k=Array.from(g||h.target.files);const q=[],$=()=>{q.length!==0&&l("rejected",q)};if(n.accept!==void 0&&s.value.indexOf("*/")===-1&&(k=Le(k,q,"accept",M=>s.value.some(A=>M.type.toUpperCase().startsWith(A)||M.name.toUpperCase().endsWith(A))),k.length===0))return $();if(n.maxFileSize!==void 0){const M=parseInt(n.maxFileSize,10);if(k=Le(k,q,"max-file-size",A=>A.size<=M),k.length===0)return $()}if(n.multiple!==!0&&k.length!==0&&(k=[k[0]]),k.forEach(M=>{M.__key=M.webkitRelativePath+M.lastModified+M.name+M.size}),x===!0){const M=B.map(A=>A.__key);k=Le(k,q,"duplicate",A=>M.includes(A.__key)===!1)}if(k.length===0)return $();if(n.maxTotalSize!==void 0){let M=x===!0?B.reduce((A,N)=>A+N.size,0):0;if(k=Le(k,q,"max-total-size",A=>(M+=A.size,M<=c.value)),k.length===0)return $()}if(typeof n.filter=="function"){const M=n.filter(k);k=Le(k,q,"filter",A=>M.includes(A))}if(n.maxFiles!==void 0){let M=x===!0?B.length:0;if(k=Le(k,q,"max-files",()=>(M++,M<=f.value)),k.length===0)return $()}if($(),k.length!==0)return k}function u(h){Je(h),t.value!==!0&&(t.value=!0)}function y(h){ze(h),(h.relatedTarget!==null||De.is.safari!==!0?h.relatedTarget!==i.value:document.elementsFromPoint(h.clientX,h.clientY).includes(i.value)===!1)===!0&&(t.value=!1)}function E(h){Je(h);const g=h.dataTransfer.files;g.length!==0&&a(null,g),t.value=!1}function D(h){if(t.value===!0)return w("div",{ref:i,class:`q-${h}__dnd absolute-full`,onDragenter:Je,onDragover:Je,onDragleave:y,onDrop:E})}return Object.assign(r,{pickFiles:m,addFiles:_}),{pickFiles:m,addFiles:_,onDragover:u,onDragleave:y,processFiles:d,getDndNode:D,maxFilesNumber:f,maxTotalSizeNumber:c}}function ln(e){return(e*100).toFixed(2)+"%"}const fo={...Ye,...uo,label:String,color:String,textColor:String,square:Boolean,flat:Boolean,bordered:Boolean,noThumbnails:Boolean,thumbnailFit:{type:String,default:"cover"},autoUpload:Boolean,hideUploadBtn:Boolean,disable:Boolean,readonly:Boolean},zn=[...so,"start","finish","added","removed"];function vo(e,t){const o=de(),{props:a,slots:n,emit:l,proxy:r}=o,{$q:i}=r,s=je(a,i);function f(v,P,W){if(v.__status=P,P==="idle"){v.__uploaded=0,v.__progress=0,v.__sizeLabel=on(v.size),v.__progressLabel="0.00%";return}if(P==="failed"){r.$forceUpdate();return}v.__uploaded=P==="uploaded"?v.size:W,v.__progress=P==="uploaded"?1:Math.min(.9999,v.__uploaded/v.size),v.__progressLabel=ln(v.__progress),r.$forceUpdate()}const c=b(()=>a.disable!==!0&&a.readonly!==!0),m=T(!1),_=T(null),d=T(null),u={files:T([]),queuedFiles:T([]),uploadedFiles:T([]),uploadedSize:T(0),updateFileStatus:f,isAlive:()=>vn(o)===!1},{pickFiles:y,addFiles:E,onDragover:D,onDragleave:h,processFiles:g,getDndNode:B,maxFilesNumber:x,maxTotalSizeNumber:k}=co({editable:c,dnd:m,getFileInput:ae,addFilesToQueue:se});Object.assign(u,e({props:a,slots:n,emit:l,helpers:u,exposeApi:v=>{Object.assign(u,v)}})),u.isBusy===void 0&&(u.isBusy=T(!1));const q=T(0),$=b(()=>q.value===0?0:u.uploadedSize.value/q.value),M=b(()=>ln($.value)),A=b(()=>on(q.value)),N=b(()=>c.value===!0&&u.isUploading.value!==!0&&(a.multiple===!0||u.queuedFiles.value.length===0)&&(a.maxFiles===void 0||u.files.value.length<x.value)&&(a.maxTotalSize===void 0||q.value<k.value)),F=b(()=>c.value===!0&&u.isBusy.value!==!0&&u.isUploading.value!==!0&&u.queuedFiles.value.length!==0);hn(pn,J);const ne=b(()=>"q-uploader column no-wrap"+(s.value===!0?" q-uploader--dark q-dark":"")+(a.bordered===!0?" q-uploader--bordered":"")+(a.square===!0?" q-uploader--square no-border-radius":"")+(a.flat===!0?" q-uploader--flat no-shadow":"")+(a.disable===!0?" disabled q-uploader--disable":"")+(m.value===!0?" q-uploader--dnd":"")),K=b(()=>"q-uploader__header"+(a.color!==void 0?` bg-${a.color}`:"")+(a.textColor!==void 0?` text-${a.textColor}`:""));te(u.isUploading,(v,P)=>{P===!1&&v===!0?l("start"):P===!0&&v===!1&&l("finish")});function X(){a.disable===!1&&(u.abort(),u.uploadedSize.value=0,q.value=0,me(),u.files.value=[],u.queuedFiles.value=[],u.uploadedFiles.value=[])}function Q(){a.disable===!1&&ie(["uploaded"],()=>{u.uploadedFiles.value=[]})}function Z(){ie(["idle","failed"],({size:v})=>{q.value-=v,u.queuedFiles.value=[]})}function ie(v,P){if(a.disable===!0)return;const W={files:[],size:0},ve=u.files.value.filter(ce=>v.indexOf(ce.__status)===-1?!0:(W.size+=ce.size,W.files.push(ce),ce.__img!==void 0&&window.URL.revokeObjectURL(ce.__img.src),!1));W.files.length!==0&&(u.files.value=ve,P(W),l("removed",W.files))}function ue(v){a.disable||(v.__status==="uploaded"?u.uploadedFiles.value=u.uploadedFiles.value.filter(P=>P.__key!==v.__key):v.__status==="uploading"?v.__abort():q.value-=v.size,u.files.value=u.files.value.filter(P=>P.__key!==v.__key?!0:(P.__img!==void 0&&window.URL.revokeObjectURL(P.__img.src),!1)),u.queuedFiles.value=u.queuedFiles.value.filter(P=>P.__key!==v.__key),l("removed",[v]))}function me(){u.files.value.forEach(v=>{v.__img!==void 0&&window.URL.revokeObjectURL(v.__img.src)})}function ae(){return d.value||_.value.getElementsByClassName("q-uploader__input")[0]}function se(v,P){const W=g(v,P,u.files.value,!0),ve=ae();ve!=null&&(ve.value=""),W!==void 0&&(W.forEach(ce=>{if(u.updateFileStatus(ce,"idle"),q.value+=ce.size,a.noThumbnails!==!0&&ce.type.toUpperCase().startsWith("IMAGE")){const xe=new Image;xe.src=window.URL.createObjectURL(ce),ce.__img=xe}}),u.files.value=u.files.value.concat(W),u.queuedFiles.value=u.queuedFiles.value.concat(W),l("added",W),a.autoUpload===!0&&u.upload())}function R(){F.value===!0&&u.upload()}function G(v,P,W){if(v===!0){const ve={type:"a",key:P,icon:i.iconSet.uploader[P],flat:!0,dense:!0};let ce;return P==="add"?(ve.onClick=y,ce=J):ve.onClick=W,w(ke,ve,ce)}}function J(){return w("input",{ref:d,class:"q-uploader__input overflow-hidden absolute-full",tabindex:-1,type:"file",title:"",accept:a.accept,multiple:a.multiple===!0?"multiple":void 0,capture:a.capture,onMousedown:We,onClick:y,onChange:se})}function C(){return n.header!==void 0?n.header(oe):[w("div",{class:"q-uploader__header-content column"},[w("div",{class:"flex flex-center no-wrap q-gutter-xs"},[G(u.queuedFiles.value.length!==0,"removeQueue",Z),G(u.uploadedFiles.value.length!==0,"removeUploaded",Q),u.isUploading.value===!0?w(Dt,{class:"q-uploader__spinner"}):null,w("div",{class:"col column justify-center"},[a.label!==void 0?w("div",{class:"q-uploader__title"},[a.label]):null,w("div",{class:"q-uploader__subtitle"},[A.value+" / "+M.value])]),G(N.value,"add"),G(a.hideUploadBtn===!1&&F.value===!0,"upload",u.upload),G(u.isUploading.value,"clear",u.abort)])])]}function I(){return n.list!==void 0?n.list(oe):u.files.value.map(v=>w("div",{key:v.__key,class:"q-uploader__file relative-position"+(a.noThumbnails!==!0&&v.__img!==void 0?" q-uploader__file--img":"")+(v.__status==="failed"?" q-uploader__file--failed":v.__status==="uploaded"?" q-uploader__file--uploaded":""),style:a.noThumbnails!==!0&&v.__img!==void 0?{backgroundImage:'url("'+v.__img.src+'")',backgroundSize:a.thumbnailFit}:null},[w("div",{class:"q-uploader__file-header row flex-center no-wrap"},[v.__status==="failed"?w(Qe,{class:"q-uploader__file-status",name:i.iconSet.type.negative,color:"negative"}):null,w("div",{class:"q-uploader__file-header-content col"},[w("div",{class:"q-uploader__title"},[v.name]),w("div",{class:"q-uploader__subtitle row items-center no-wrap"},[v.__sizeLabel+" / "+v.__progressLabel])]),v.__status==="uploading"?w(io,{value:v.__progress,min:0,max:1,indeterminate:v.__progress===0}):w(ke,{round:!0,dense:!0,flat:!0,icon:i.iconSet.uploader[v.__status==="uploaded"?"done":"clear"],onClick:()=>{ue(v)}})])]))}ge(()=>{u.isUploading.value===!0&&u.abort(),u.files.value.length!==0&&me()});const oe={};for(const v in u)va(u[v])===!0?$t(oe,v,()=>u[v].value):oe[v]=u[v];return Object.assign(oe,{upload:R,reset:X,removeUploadedFiles:Q,removeQueuedFiles:Z,removeFile:ue,pickFiles:y,addFiles:E}),ha(oe,{canAddFiles:()=>N.value,canUpload:()=>F.value,uploadSizeLabel:()=>A.value,uploadProgressLabel:()=>M.value}),t({...u,upload:R,reset:X,removeUploadedFiles:Q,removeQueuedFiles:Z,removeFile:ue,pickFiles:y,addFiles:E,canAddFiles:N,canUpload:F,uploadSizeLabel:A,uploadProgressLabel:M}),()=>{const v=[w("div",{class:K.value},C()),w("div",{class:"q-uploader__list scroll"},I()),B("uploader")];u.isBusy.value===!0&&v.push(w("div",{class:"q-uploader__overlay absolute-full flex flex-center"},[w(Dt)]));const P={ref:_,class:ne.value};return N.value===!0&&Object.assign(P,{onDragover:D,onDragleave:h}),w("div",P,v)}}const ho=()=>!0;function mo(e){const t={};return e.forEach(o=>{t[o]=ho}),t}const go=mo(zn),bo=({name:e,props:t,emits:o,injectPlugin:a})=>re({name:e,props:{...fo,...t},emits:ma(o)===!0?{...go,...o}:[...zn,...o],setup(n,{expose:l}){return vo(a,l)}});function Se(e){return typeof e=="function"?e:()=>e}const yo="QUploader",po={url:[Function,String],method:{type:[Function,String],default:"POST"},fieldName:{type:[Function,String],default:()=>e=>e.name},headers:[Function,Array],formFields:[Function,Array],withCredentials:[Function,Boolean],sendRaw:[Function,Boolean],batch:[Function,Boolean],factory:Function},_o=["factoryFailed","uploaded","failed","uploading"];function wo({props:e,emit:t,helpers:o}){const a=T([]),n=T([]),l=T(0),r=b(()=>({url:Se(e.url),method:Se(e.method),headers:Se(e.headers),formFields:Se(e.formFields),fieldName:Se(e.fieldName),withCredentials:Se(e.withCredentials),sendRaw:Se(e.sendRaw),batch:Se(e.batch)})),i=b(()=>l.value>0),s=b(()=>n.value.length!==0);let f;function c(){a.value.forEach(u=>{u.abort()}),n.value.length!==0&&(f=!0)}function m(){const u=o.queuedFiles.value.slice(0);o.queuedFiles.value=[],r.value.batch(u)?_(u):u.forEach(y=>{_([y])})}function _(u){if(l.value++,typeof e.factory!="function"){d(u,{});return}const y=e.factory(u);if(!y)t("factoryFailed",new Error("QUploader: factory() does not return properly"),u),l.value--;else if(typeof y.catch=="function"&&typeof y.then=="function"){n.value.push(y);const E=D=>{o.isAlive()===!0&&(n.value=n.value.filter(h=>h!==y),n.value.length===0&&(f=!1),o.queuedFiles.value=o.queuedFiles.value.concat(u),u.forEach(h=>{o.updateFileStatus(h,"failed")}),t("factoryFailed",D,u),l.value--)};y.then(D=>{f===!0?E(new Error("Aborted")):o.isAlive()===!0&&(n.value=n.value.filter(h=>h!==y),d(u,D))}).catch(E)}else d(u,y||{})}function d(u,y){const E=new FormData,D=new XMLHttpRequest,h=(F,ne)=>y[F]!==void 0?Se(y[F])(ne):r.value[F](ne),g=h("url",u);if(!g){console.error("q-uploader: invalid or no URL specified"),l.value--;return}const B=h("formFields",u);B!==void 0&&B.forEach(F=>{E.append(F.name,F.value)});let x=0,k=0,q=0,$=0,M;D.upload.addEventListener("progress",F=>{if(M===!0)return;const ne=Math.min($,F.loaded);o.uploadedSize.value+=ne-q,q=ne;let K=q-k;for(let X=x;K>0&&X<u.length;X++){const Q=u[X];if(K>Q.size)K-=Q.size,x++,k+=Q.size,o.updateFileStatus(Q,"uploading",Q.size);else{o.updateFileStatus(Q,"uploading",K);return}}},!1),D.onreadystatechange=()=>{D.readyState<4||(D.status&&D.status<400?(o.uploadedFiles.value=o.uploadedFiles.value.concat(u),u.forEach(F=>{o.updateFileStatus(F,"uploaded")}),t("uploaded",{files:u,xhr:D})):(M=!0,o.uploadedSize.value-=q,o.queuedFiles.value=o.queuedFiles.value.concat(u),u.forEach(F=>{o.updateFileStatus(F,"failed")}),t("failed",{files:u,xhr:D})),l.value--,a.value=a.value.filter(F=>F!==D))},D.open(h("method",u),g),h("withCredentials",u)===!0&&(D.withCredentials=!0);const A=h("headers",u);A!==void 0&&A.forEach(F=>{D.setRequestHeader(F.name,F.value)});const N=h("sendRaw",u);u.forEach(F=>{o.updateFileStatus(F,"uploading",0),N!==!0&&E.append(h("fieldName",F),F,F.name),F.xhr=D,F.__abort=()=>{D.abort()},$+=F.size}),t("uploading",{files:u,xhr:D}),a.value.push(D),N===!0?D.send(new Blob(u)):D.send(E)}return{isUploading:i,isBusy:s,abort:c,upload:m}}const So={name:yo,props:po,emits:_o,injectPlugin:wo},ko=bo(So);function xo(){return Bt(ga)}const qo=ba({name:"UploaderComponents",__name:"uploader",props:{label:{},type:{default:"image"},path:{}},emits:["uploaded"],setup(e,{expose:t,emit:o}){t();const a=xo(),n=T(null),l=ya(),r="/admin",i=T(""),s=e;i.value=s.type==="multiple"?s.path||[]:s.path||"";const f=o,d={$q:a,uploaderRef:n,$userStore:l,baseURL:r,currentPath:i,props:s,emit:f,onUploaded:u=>{a.notify({type:"positive",message:"上传成功"});const y=JSON.parse(u.xhr.response);if(n.value.reset(),y.code!==200){a.notify({type:"negative",message:y.msg||"上传失败"});return}Array.isArray(i.value)?i.value.push(y.data):i.value=y.data,f("uploaded",i.value)},onUploadFailed:()=>{a.notify({type:"negative",message:"上传失败, 请检查网络状态"})},deleteValueFunc:u=>{Array.isArray(i.value)?(i.value.splice(u,1),f("uploaded",i.value)):console.error("Cannot delete from non-array value")},get imageSrc(){return Ea},get API_PREFIX(){return Ba}};return Object.defineProperty(d,"__isScriptSetup",{enumerable:!1,value:!0}),d}}),Co={key:0,class:"row no-wrap items-center q-pa-sm q-gutter-xs"},Do={class:"col"},Mo={class:"q-uploader__subtitle"},To={key:0,class:"q-ma-md"},Fo={key:1},Eo={class:"row justify-between"},Bo=["src"],Po={key:0,class:"flex justify-center items-center cursor-pointer q-py-md"},$o=["onClick"],zo={key:0},Ao={class:"flex justify-center items-center cursor-pointer q-py-md"};function Lo(e,t,o,a,n,l){return le(),Ce("div",null,[ee(ko,{ref:"uploaderRef","field-name":"file",url:a.baseURL+a.API_PREFIX+"/upload",headers:[{name:"Authorization",value:"Bearer "+a.$userStore.userToken}],accept:"*","max-files":o.type=="multiple"?5:1,"max-file-size":o.type=="file"?200*1024*1024:5*1024*1024,multiple:o.type=="multiple","auto-upload":"",style:{width:"100%",background:"transparent"},onUploaded:a.onUploaded,onFailed:a.onUploadFailed},{header:V(r=>[o.type=="file"?(le(),Ce("div",Co,[r.queuedFiles.length>0?(le(),pe(ke,{key:0,icon:"clear_all",onClick:r.removeQueuedFiles,round:"",dense:"",flat:""},{default:V(()=>[ee(Be,null,{default:V(()=>t[0]||(t[0]=[Ee("Clear All")])),_:1})]),_:2},1032,["onClick"])):_e("",!0),r.uploadedFiles.length>0?(le(),pe(ke,{key:1,icon:"done_all",onClick:r.removeUploadedFiles,round:"",dense:"",flat:""},{default:V(()=>[ee(Be,null,{default:V(()=>t[1]||(t[1]=[Ee("删除已上传的文件")])),_:1})]),_:2},1032,["onClick"])):_e("",!0),r.isUploading?(le(),pe(Dt,{key:2,class:"q-uploader__spinner"})):_e("",!0),we("div",Do,[t[2]||(t[2]=we("div",{class:"q-uploader__title"},"上传您的文件",-1)),we("div",Mo,Ae(r.uploadSizeLabel)+" / "+Ae(r.uploadProgressLabel),1)]),r.canAddFiles?(le(),pe(ke,{key:3,type:"a",icon:"add_box",onClick:r.pickFiles,round:"",dense:"",flat:""},{default:V(()=>[ee(wt),ee(Be,null,{default:V(()=>t[3]||(t[3]=[Ee("选择文件")])),_:1})]),_:2},1032,["onClick"])):_e("",!0),r.canUpload?(le(),pe(ke,{key:4,icon:"cloud_upload",onClick:r.upload,round:"",dense:"",flat:""},{default:V(()=>[ee(Be,null,{default:V(()=>t[4]||(t[4]=[Ee("上传文件")])),_:1})]),_:2},1032,["onClick"])):_e("",!0),r.isUploading?(le(),pe(ke,{key:5,icon:"clear",onClick:r.abort,round:"",dense:"",flat:""},{default:V(()=>[ee(Be,null,{default:V(()=>t[5]||(t[5]=[Ee("终止上传")])),_:1})]),_:2},1032,["onClick"])):_e("",!0)])):_e("",!0)]),list:V(r=>[o.type=="file"?(le(),Ce("div",To,Ae(a.currentPath),1)):o.type=="multiple"?(le(),Ce("div",Fo,[we("div",null,Ae(o.label),1),we("div",Eo,[t[7]||(t[7]=we("div",null,null,-1)),we("div",null,[ee(ke,{flat:"",icon:"add_box",onClick:r.pickFiles,color:"primary"},{default:V(()=>[ee(wt),ee(Be,null,{default:V(()=>t[6]||(t[6]=[Ee("Pick Files")])),_:1})]),_:2},1032,["onClick"])])]),ee(Oa,{separator:""},{default:V(()=>[(le(!0),Ce(_a,null,wa(a.currentPath,(i,s)=>(le(),pe(Ia,{key:s},{default:V(()=>[ee(pt,{thumbnail:""},{default:V(()=>[we("img",{src:a.imageSrc(i),class:"q-mx-auto"},null,8,Bo)]),_:2},1024),ee(pt),ee(pt,{side:""},{default:V(()=>[ee(ke,{flat:"",round:"",dense:"",icon:"delete",color:"red",onClick:f=>a.deleteValueFunc(s)},null,8,["onClick"])]),_:2},1024)]),_:2},1024))),128)),a.currentPath.length==0?(le(),Ce("div",Po,[ee(yt,{src:a.imageSrc("/icon.png"),"no-spinner":"",width:"80px",height:"80px",class:"q-mx-auto"},null,8,["src"])])):_e("",!0)]),_:1})])):(le(),Ce("div",{key:2,onClick:i=>{r.removeFile,r.pickFiles}},[ee(wt),Sa(e.$slots,"default",{},()=>[o.type!=="icon"?(le(),Ce("div",zo,Ae(o.label),1)):_e("",!0),we("div",Ao,[o.type=="icon"?(le(),pe(yt,{key:0,src:a.imageSrc(a.currentPath),"no-spinner":"",width:"26px",height:"26px",class:"q-mx-auto"},null,8,["src"])):(le(),pe(yt,{key:1,src:a.imageSrc(a.currentPath),"no-spinner":"",width:"80px",height:"80px",class:"q-mx-auto"},null,8,["src"])),ee(Be,null,{default:V(()=>[Ee(Ae(o.label??"点击上传图片"),1)]),_:1})])],!0)],8,$o))]),_:3},8,["url","headers","max-files","max-file-size","multiple"])])}const Tl=pa(qo,[["render",Lo],["__scopeId","data-v-dc2faec2"],["__file","uploader.vue"]]),Te=[-61,9,38,199,426,686,756,818,1111,1181,1210,1635,2060,2097,2192,2262,2324,2394,2456,3178];function Fl(e,t,o){return Object.prototype.toString.call(e)==="[object Date]"&&(o=e.getDate(),t=e.getMonth()+1,e=e.getFullYear()),Qo(Qt(e,t,o))}function El(e,t,o){return Ln(Oo(e,t,o))}function Ho(e){return Ro(e)===0}function Io(e,t){return t<=6?31:t<=11||Ho(e)?30:29}function Ro(e){const t=Te.length;let o=Te[0],a,n,l,r,i;if(e<o||e>=Te[t-1])throw new Error("Invalid Jalaali year "+e);for(i=1;i<t&&(a=Te[i],n=a-o,!(e<a));i+=1)o=a;return r=e-o,n-r<6&&(r=r-n+O(n+4,33)*33),l=he(he(r+1,33)-1,4),l===-1&&(l=4),l}function An(e,t){const o=Te.length,a=e+621;let n=-14,l=Te[0],r,i,s,f,c;if(e<l||e>=Te[o-1])throw new Error("Invalid Jalaali year "+e);for(c=1;c<o&&(r=Te[c],i=r-l,!(e<r));c+=1)n=n+O(i,33)*8+O(he(i,33),4),l=r;f=e-l,n=n+O(f,33)*8+O(he(f,33)+3,4),he(i,33)===4&&i-f===4&&(n+=1);const m=O(a,4)-O((O(a,100)+1)*3,4)-150,_=20+n-m;return t||(i-f<6&&(f=f-i+O(i+4,33)*33),s=he(he(f+1,33)-1,4),s===-1&&(s=4)),{leap:s,gy:a,march:_}}function Oo(e,t,o){const a=An(e,!0);return Qt(a.gy,3,a.march)+(t-1)*31-O(t,7)*(t-7)+o-1}function Qo(e){const t=Ln(e).gy;let o=t-621,a,n,l;const r=An(o,!1),i=Qt(t,3,r.march);if(l=e-i,l>=0){if(l<=185)return n=1+O(l,31),a=he(l,31)+1,{jy:o,jm:n,jd:a};l-=186}else o-=1,l+=179,r.leap===1&&(l+=1);return n=7+O(l,30),a=he(l,30)+1,{jy:o,jm:n,jd:a}}function Qt(e,t,o){let a=O((e+O(t-8,6)+100100)*1461,4)+O(153*he(t+9,12)+2,5)+o-34840408;return a=a-O(O(e+100100+O(t-8,6),100)*3,4)+752,a}function Ln(e){let t=4*e+139361631;t=t+O(O(4*e+183187720,146097)*3,4)*4-3908;const o=O(he(t,1461),4)*5+308,a=O(he(o,153),5)+1,n=he(O(o,153),12)+1;return{gy:O(t,1461)-100100+O(8-n,6),gm:n,gd:a}}function O(e,t){return~~(e/t)}function he(e,t){return e-~~(e/t)*t}const Hn=864e5,Uo=36e5,Et=6e4,In="YYYY-MM-DDTHH:mm:ss.SSSZ",Yo=/\[((?:[^\]\\]|\\]|\\)*)\]|do|d{1,4}|Mo|M{1,4}|m{1,2}|wo|w{1,2}|Qo|Do|DDDo|D{1,4}|YY(?:YY)?|H{1,2}|h{1,2}|s{1,2}|S{1,3}|Z{1,2}|a{1,2}|[AQExX]/g,jo=/(\[[^\]]*\])|do|d{1,4}|Mo|M{1,4}|m{1,2}|wo|w{1,2}|Qo|Do|DDDo|D{1,4}|YY(?:YY)?|H{1,2}|h{1,2}|s{1,2}|S{1,3}|Z{1,2}|a{1,2}|[AQExX]|([.*+:?^,\s${}()|\\]+)/g,St={};function No(e,t){const o="("+t.days.join("|")+")",a=e+o;if(St[a]!==void 0)return St[a];const n="("+t.daysShort.join("|")+")",l="("+t.months.join("|")+")",r="("+t.monthsShort.join("|")+")",i={};let s=0;const f=e.replace(jo,m=>{switch(s++,m){case"YY":return i.YY=s,"(-?\\d{1,2})";case"YYYY":return i.YYYY=s,"(-?\\d{1,4})";case"M":return i.M=s,"(\\d{1,2})";case"Mo":return i.M=s++,"(\\d{1,2}(st|nd|rd|th))";case"MM":return i.M=s,"(\\d{2})";case"MMM":return i.MMM=s,r;case"MMMM":return i.MMMM=s,l;case"D":return i.D=s,"(\\d{1,2})";case"Do":return i.D=s++,"(\\d{1,2}(st|nd|rd|th))";case"DD":return i.D=s,"(\\d{2})";case"H":return i.H=s,"(\\d{1,2})";case"HH":return i.H=s,"(\\d{2})";case"h":return i.h=s,"(\\d{1,2})";case"hh":return i.h=s,"(\\d{2})";case"m":return i.m=s,"(\\d{1,2})";case"mm":return i.m=s,"(\\d{2})";case"s":return i.s=s,"(\\d{1,2})";case"ss":return i.s=s,"(\\d{2})";case"S":return i.S=s,"(\\d{1})";case"SS":return i.S=s,"(\\d{2})";case"SSS":return i.S=s,"(\\d{3})";case"A":return i.A=s,"(AM|PM)";case"a":return i.a=s,"(am|pm)";case"aa":return i.aa=s,"(a\\.m\\.|p\\.m\\.)";case"ddd":return n;case"dddd":return o;case"Q":case"d":case"E":return"(\\d{1})";case"do":return s++,"(\\d{1}(st|nd|rd|th))";case"Qo":return"(1st|2nd|3rd|4th)";case"DDD":case"DDDD":return"(\\d{1,3})";case"DDDo":return s++,"(\\d{1,3}(st|nd|rd|th))";case"w":return"(\\d{1,2})";case"wo":return s++,"(\\d{1,2}(st|nd|rd|th))";case"ww":return"(\\d{2})";case"Z":return i.Z=s,"(Z|[+-]\\d{2}:\\d{2})";case"ZZ":return i.ZZ=s,"(Z|[+-]\\d{2}\\d{2})";case"X":return i.X=s,"(-?\\d+)";case"x":return i.x=s,"(-?\\d{4,})";default:return s--,m[0]==="["&&(m=m.substring(1,m.length-1)),m.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}}),c={map:i,regex:new RegExp("^"+f)};return St[a]=c,c}function Rn(e,t){return e!==void 0?e:t!==void 0?t.date:ka.date}function rn(e,t=""){const o=e>0?"-":"+",a=Math.abs(e),n=Math.floor(a/60),l=a%60;return o+j(n)+t+j(l)}function Wo(e,t,o){let a=e.getFullYear(),n=e.getMonth();const l=e.getDate();return t.year!==void 0&&(a+=o*t.year,delete t.year),t.month!==void 0&&(n+=o*t.month,delete t.month),e.setDate(1),e.setMonth(2),e.setFullYear(a),e.setMonth(n),e.setDate(Math.min(l,Yt(e))),t.date!==void 0&&(e.setDate(e.getDate()+o*t.date),delete t.date),e}function Vo(e,t,o){const a=t.year!==void 0?t.year:e[`get${o}FullYear`](),n=t.month!==void 0?t.month-1:e[`get${o}Month`](),l=new Date(a,n+1,0).getDate(),r=Math.min(l,t.date!==void 0?t.date:e[`get${o}Date`]());return e[`set${o}Date`](1),e[`set${o}Month`](2),e[`set${o}FullYear`](a),e[`set${o}Month`](n),e[`set${o}Date`](r),delete t.year,delete t.month,delete t.date,e}function Ut(e,t,o){const a=On(t),n=new Date(e),l=a.year!==void 0||a.month!==void 0||a.date!==void 0?Wo(n,a,o):n;for(const r in a){const i=oo(r);l[`set${i}`](l[`get${i}`]()+o*a[r])}return l}function On(e){const t={...e};return e.years!==void 0&&(t.year=e.years,delete t.years),e.months!==void 0&&(t.month=e.months,delete t.months),e.days!==void 0&&(t.date=e.days,delete t.days),e.day!==void 0&&(t.date=e.day,delete t.day),e.hour!==void 0&&(t.hours=e.hour,delete t.hour),e.minute!==void 0&&(t.minutes=e.minute,delete t.minute),e.second!==void 0&&(t.seconds=e.second,delete t.second),e.millisecond!==void 0&&(t.milliseconds=e.millisecond,delete t.millisecond),t}function Qn(e,t,o){const a=On(t),n=o===!0?"UTC":"",l=new Date(e),r=a.year!==void 0||a.month!==void 0||a.date!==void 0?Vo(l,a,n):l;for(const i in a){const s=i.charAt(0).toUpperCase()+i.slice(1);r[`set${n}${s}`](a[i])}return r}function Ko(e,t,o){const a=Xo(e,t,o),n=new Date(a.year,a.month===null?null:a.month-1,a.day===null?1:a.day,a.hour,a.minute,a.second,a.millisecond),l=n.getTimezoneOffset();return a.timezoneOffset===null||a.timezoneOffset===l?n:Ut(n,{minutes:a.timezoneOffset-l},1)}function Xo(e,t,o,a,n){const l={year:null,month:null,day:null,hour:null,minute:null,second:null,millisecond:null,timezoneOffset:null,dateHash:null,timeHash:null};if(n!==void 0&&Object.assign(l,n),e==null||e===""||typeof e!="string")return l;t===void 0&&(t=In);const r=Rn(o,_n.props),i=r.months,s=r.monthsShort,{regex:f,map:c}=No(t,r),m=e.match(f);if(m===null)return l;let _="";if(c.X!==void 0||c.x!==void 0){const d=parseInt(m[c.X!==void 0?c.X:c.x],10);if(isNaN(d)===!0||d<0)return l;const u=new Date(d*(c.X!==void 0?1e3:1));l.year=u.getFullYear(),l.month=u.getMonth()+1,l.day=u.getDate(),l.hour=u.getHours(),l.minute=u.getMinutes(),l.second=u.getSeconds(),l.millisecond=u.getMilliseconds()}else{if(c.YYYY!==void 0)l.year=parseInt(m[c.YYYY],10);else if(c.YY!==void 0){const d=parseInt(m[c.YY],10);l.year=d<0?d:2e3+d}if(c.M!==void 0){if(l.month=parseInt(m[c.M],10),l.month<1||l.month>12)return l}else c.MMM!==void 0?l.month=s.indexOf(m[c.MMM])+1:c.MMMM!==void 0&&(l.month=i.indexOf(m[c.MMMM])+1);if(c.D!==void 0){if(l.day=parseInt(m[c.D],10),l.year===null||l.month===null||l.day<1)return l;const d=a!=="persian"?new Date(l.year,l.month,0).getDate():Io(l.year,l.month);if(l.day>d)return l}c.H!==void 0?l.hour=parseInt(m[c.H],10)%24:c.h!==void 0&&(l.hour=parseInt(m[c.h],10)%12,(c.A&&m[c.A]==="PM"||c.a&&m[c.a]==="pm"||c.aa&&m[c.aa]==="p.m.")&&(l.hour+=12),l.hour=l.hour%24),c.m!==void 0&&(l.minute=parseInt(m[c.m],10)%60),c.s!==void 0&&(l.second=parseInt(m[c.s],10)%60),c.S!==void 0&&(l.millisecond=parseInt(m[c.S],10)*10**(3-m[c.S].length)),(c.Z!==void 0||c.ZZ!==void 0)&&(_=c.Z!==void 0?m[c.Z].replace(":",""):m[c.ZZ],l.timezoneOffset=(_[0]==="+"?-1:1)*(60*_.slice(1,3)+1*_.slice(3,5)))}return l.dateHash=j(l.year,6)+"/"+j(l.month)+"/"+j(l.day),l.timeHash=j(l.hour)+":"+j(l.minute)+":"+j(l.second)+_,l}function Zo(e){return typeof e=="number"?!0:isNaN(Date.parse(e))===!1}function Go(e,t){return Qn(new Date,e,t)}function Jo(e){const t=new Date(e).getDay();return t===0?7:t}function nt(e){const t=new Date(e.getFullYear(),e.getMonth(),e.getDate());t.setDate(t.getDate()-(t.getDay()+6)%7+3);const o=new Date(t.getFullYear(),0,4);o.setDate(o.getDate()-(o.getDay()+6)%7+3);const a=t.getTimezoneOffset()-o.getTimezoneOffset();t.setHours(t.getHours()-a);const n=(t-o)/(Hn*7);return 1+Math.floor(n)}function el(e){return e.getFullYear()*1e4+e.getMonth()*100+e.getDate()}function kt(e,t){const o=new Date(e);return t===!0?el(o):o.getTime()}function tl(e,t,o,a={}){const n=kt(t,a.onlyDate),l=kt(o,a.onlyDate),r=kt(e,a.onlyDate);return(r>n||a.inclusiveFrom===!0&&r===n)&&(r<l||a.inclusiveTo===!0&&r===l)}function nl(e,t){return Ut(e,t,1)}function al(e,t){return Ut(e,t,-1)}function be(e,t,o){const a=new Date(e),n=`set${o===!0?"UTC":""}`;switch(t){case"year":case"years":a[`${n}Month`](0);case"month":case"months":a[`${n}Date`](1);case"day":case"days":case"date":a[`${n}Hours`](0);case"hour":case"hours":a[`${n}Minutes`](0);case"minute":case"minutes":a[`${n}Seconds`](0);case"second":case"seconds":a[`${n}Milliseconds`](0)}return a}function ol(e,t,o){const a=new Date(e),n=`set${o===!0?"UTC":""}`;switch(t){case"year":case"years":a[`${n}Month`](11);case"month":case"months":a[`${n}Date`](Yt(a));case"day":case"days":case"date":a[`${n}Hours`](23);case"hour":case"hours":a[`${n}Minutes`](59);case"minute":case"minutes":a[`${n}Seconds`](59);case"second":case"seconds":a[`${n}Milliseconds`](999)}return a}function ll(e){let t=new Date(e);return Array.prototype.slice.call(arguments,1).forEach(o=>{t=Math.max(t,new Date(o))}),t}function rl(e){let t=new Date(e);return Array.prototype.slice.call(arguments,1).forEach(o=>{t=Math.min(t,new Date(o))}),t}function et(e,t,o){return(e.getTime()-e.getTimezoneOffset()*Et-(t.getTime()-t.getTimezoneOffset()*Et))/o}function Un(e,t,o="days"){const a=new Date(e),n=new Date(t);switch(o){case"years":case"year":return a.getFullYear()-n.getFullYear();case"months":case"month":return(a.getFullYear()-n.getFullYear())*12+a.getMonth()-n.getMonth();case"days":case"day":case"date":return et(be(a,"day"),be(n,"day"),Hn);case"hours":case"hour":return et(be(a,"hour"),be(n,"hour"),Uo);case"minutes":case"minute":return et(be(a,"minute"),be(n,"minute"),Et);case"seconds":case"second":return et(be(a,"second"),be(n,"second"),1e3)}}function at(e){return Un(e,be(e,"year"),"days")+1}function il(e){return wn(e)===!0?"date":typeof e=="number"?"number":"string"}function ul(e,t,o){const a=new Date(e);if(t){const n=new Date(t);if(a<n)return n}if(o){const n=new Date(o);if(a>n)return n}return a}function sl(e,t,o){const a=new Date(e),n=new Date(t);if(o===void 0)return a.getTime()===n.getTime();switch(o){case"second":case"seconds":if(a.getSeconds()!==n.getSeconds())return!1;case"minute":case"minutes":if(a.getMinutes()!==n.getMinutes())return!1;case"hour":case"hours":if(a.getHours()!==n.getHours())return!1;case"day":case"days":case"date":if(a.getDate()!==n.getDate())return!1;case"month":case"months":if(a.getMonth()!==n.getMonth())return!1;case"year":case"years":if(a.getFullYear()!==n.getFullYear())return!1;break;default:throw new Error(`date isSameDate unknown unit ${o}`)}return!0}function Yt(e){return new Date(e.getFullYear(),e.getMonth()+1,0).getDate()}function He(e){if(e>=11&&e<=13)return`${e}th`;switch(e%10){case 1:return`${e}st`;case 2:return`${e}nd`;case 3:return`${e}rd`}return`${e}th`}const un={YY(e,t,o){const a=this.YYYY(e,t,o)%100;return a>=0?j(a):"-"+j(Math.abs(a))},YYYY(e,t,o){return o??e.getFullYear()},M(e){return e.getMonth()+1},Mo(e){return He(e.getMonth()+1)},MM(e){return j(e.getMonth()+1)},MMM(e,t){return t.monthsShort[e.getMonth()]},MMMM(e,t){return t.months[e.getMonth()]},Q(e){return Math.ceil((e.getMonth()+1)/3)},Qo(e){return He(this.Q(e))},D(e){return e.getDate()},Do(e){return He(e.getDate())},DD(e){return j(e.getDate())},DDD(e){return at(e)},DDDo(e){return He(at(e))},DDDD(e){return j(at(e),3)},d(e){return e.getDay()},do(e){return He(e.getDay())},dd(e,t){return t.days[e.getDay()].slice(0,2)},ddd(e,t){return t.daysShort[e.getDay()]},dddd(e,t){return t.days[e.getDay()]},E(e){return e.getDay()||7},w(e){return nt(e)},wo(e){return He(nt(e))},ww(e){return j(nt(e))},H(e){return e.getHours()},HH(e){return j(e.getHours())},h(e){const t=e.getHours();return t===0?12:t>12?t%12:t},hh(e){return j(this.h(e))},m(e){return e.getMinutes()},mm(e){return j(e.getMinutes())},s(e){return e.getSeconds()},ss(e){return j(e.getSeconds())},S(e){return Math.floor(e.getMilliseconds()/100)},SS(e){return j(Math.floor(e.getMilliseconds()/10))},SSS(e){return j(e.getMilliseconds(),3)},A(e){return e.getHours()<12?"AM":"PM"},a(e){return e.getHours()<12?"am":"pm"},aa(e){return e.getHours()<12?"a.m.":"p.m."},Z(e,t,o,a){const n=a??e.getTimezoneOffset();return rn(n,":")},ZZ(e,t,o,a){const n=a??e.getTimezoneOffset();return rn(n)},X(e){return Math.floor(e.getTime()/1e3)},x(e){return e.getTime()}};function cl(e,t,o,a,n){if(e!==0&&!e||e===1/0||e===-1/0)return;const l=new Date(e);if(isNaN(l))return;t===void 0&&(t=In);const r=Rn(o,_n.props);return t.replace(Yo,(i,s)=>i in un?un[i](l,r,a,n):s===void 0?i:s.split("\\]").join("]"))}function dl(e){return wn(e)===!0?new Date(e.getTime()):e}const Bl={isValid:Zo,extractDate:Ko,buildDate:Go,getDayOfWeek:Jo,getWeekOfYear:nt,isBetweenDates:tl,addToDate:nl,subtractFromDate:al,adjustDate:Qn,startOfDate:be,endOfDate:ol,getMaxDate:ll,getMinDate:rl,getDateDiff:Un,getDayOfYear:at,inferDateFormat:il,getDateBetween:ul,isSameDate:sl,daysInMonth:Yt,formatDate:cl,clone:dl},jt={left:!0,right:!0,up:!0,down:!0,horizontal:!0,vertical:!0},fl=Object.keys(jt);jt.all=!0;function sn(e){const t={};for(const o of fl)e[o]===!0&&(t[o]=!0);return Object.keys(t).length===0?jt:(t.horizontal===!0?t.left=t.right=!0:t.left===!0&&t.right===!0&&(t.horizontal=!0),t.vertical===!0?t.up=t.down=!0:t.up===!0&&t.down===!0&&(t.vertical=!0),t.horizontal===!0&&t.vertical===!0&&(t.all=!0),t)}const vl=["INPUT","TEXTAREA"];function cn(e,t){return t.event===void 0&&e.target!==void 0&&e.target.draggable!==!0&&typeof t.handler=="function"&&vl.includes(e.target.nodeName.toUpperCase())===!1&&(e.qClonedBy===void 0||e.qClonedBy.indexOf(t.uid)===-1)}function xt(e,t,o){const a=it(e);let n,l=a.left-t.event.x,r=a.top-t.event.y,i=Math.abs(l),s=Math.abs(r);const f=t.direction;f.horizontal===!0&&f.vertical!==!0?n=l<0?"left":"right":f.horizontal!==!0&&f.vertical===!0?n=r<0?"up":"down":f.up===!0&&r<0?(n="up",i>s&&(f.left===!0&&l<0?n="left":f.right===!0&&l>0&&(n="right"))):f.down===!0&&r>0?(n="down",i>s&&(f.left===!0&&l<0?n="left":f.right===!0&&l>0&&(n="right"))):f.left===!0&&l<0?(n="left",i<s&&(f.up===!0&&r<0?n="up":f.down===!0&&r>0&&(n="down"))):f.right===!0&&l>0&&(n="right",i<s&&(f.up===!0&&r<0?n="up":f.down===!0&&r>0&&(n="down")));let c=!1;if(n===void 0&&o===!1){if(t.event.isFirst===!0||t.event.lastDir===void 0)return{};n=t.event.lastDir,c=!0,n==="left"||n==="right"?(a.left-=l,i=0,l=0):(a.top-=r,s=0,r=0)}return{synthetic:c,payload:{evt:e,touch:t.event.mouse!==!0,mouse:t.event.mouse===!0,position:a,direction:n,isFirst:t.event.isFirst,isFinal:o===!0,duration:Date.now()-t.event.time,distance:{x:i,y:s},offset:{x:l,y:r},delta:{x:a.left-t.event.lastX,y:a.top-t.event.lastY}}}}let hl=0;const Pl=yn({name:"touch-pan",beforeMount(e,{value:t,modifiers:o}){if(o.mouse!==!0&&De.has.touch!==!0)return;function a(l,r){o.mouse===!0&&r===!0?ze(l):(o.stop===!0&&We(l),o.prevent===!0&&rt(l))}const n={uid:"qvtp_"+hl++,handler:t,modifiers:o,direction:sn(o),noop:xa,mouseStart(l){cn(l,n)&&qa(l)&&(Me(n,"temp",[[document,"mousemove","move","notPassiveCapture"],[document,"mouseup","end","passiveCapture"]]),n.start(l,!0))},touchStart(l){if(cn(l,n)){const r=l.target;Me(n,"temp",[[r,"touchmove","move","notPassiveCapture"],[r,"touchcancel","end","passiveCapture"],[r,"touchend","end","passiveCapture"]]),n.start(l)}},start(l,r){if(De.is.firefox===!0&&bt(e,!0),n.lastEvt=l,r===!0||o.stop===!0){if(n.direction.all!==!0&&(r!==!0||n.modifiers.mouseAllDir!==!0&&n.modifiers.mousealldir!==!0)){const f=l.type.indexOf("mouse")!==-1?new MouseEvent(l.type,l):new TouchEvent(l.type,l);l.defaultPrevented===!0&&rt(f),l.cancelBubble===!0&&We(f),Object.assign(f,{qKeyEvent:l.qKeyEvent,qClickOutside:l.qClickOutside,qAnchorHandled:l.qAnchorHandled,qClonedBy:l.qClonedBy===void 0?[n.uid]:l.qClonedBy.concat(n.uid)}),n.initialEvent={target:l.target,event:f}}We(l)}const{left:i,top:s}=it(l);n.event={x:i,y:s,time:Date.now(),mouse:r===!0,detected:!1,isFirst:!0,isFinal:!1,lastX:i,lastY:s}},move(l){if(n.event===void 0)return;const r=it(l),i=r.left-n.event.x,s=r.top-n.event.y;if(i===0&&s===0)return;n.lastEvt=l;const f=n.event.mouse===!0,c=()=>{a(l,f);let d;o.preserveCursor!==!0&&o.preservecursor!==!0&&(d=document.documentElement.style.cursor||"",document.documentElement.style.cursor="grabbing"),f===!0&&document.body.classList.add("no-pointer-events--children"),document.body.classList.add("non-selectable"),st(),n.styleCleanup=u=>{if(n.styleCleanup=void 0,d!==void 0&&(document.documentElement.style.cursor=d),document.body.classList.remove("non-selectable"),f===!0){const y=()=>{document.body.classList.remove("no-pointer-events--children")};u!==void 0?setTimeout(()=>{y(),u()},50):y()}else u!==void 0&&u()}};if(n.event.detected===!0){n.event.isFirst!==!0&&a(l,n.event.mouse);const{payload:d,synthetic:u}=xt(l,n,!1);d!==void 0&&(n.handler(d)===!1?n.end(l):(n.styleCleanup===void 0&&n.event.isFirst===!0&&c(),n.event.lastX=d.position.left,n.event.lastY=d.position.top,n.event.lastDir=u===!0?void 0:d.direction,n.event.isFirst=!1));return}if(n.direction.all===!0||f===!0&&(n.modifiers.mouseAllDir===!0||n.modifiers.mousealldir===!0)){c(),n.event.detected=!0,n.move(l);return}const m=Math.abs(i),_=Math.abs(s);m!==_&&(n.direction.horizontal===!0&&m>_||n.direction.vertical===!0&&m<_||n.direction.up===!0&&m<_&&s<0||n.direction.down===!0&&m<_&&s>0||n.direction.left===!0&&m>_&&i<0||n.direction.right===!0&&m>_&&i>0?(n.event.detected=!0,n.move(l)):n.end(l,!0))},end(l,r){if(n.event!==void 0){if(Ie(n,"temp"),De.is.firefox===!0&&bt(e,!1),r===!0)n.styleCleanup!==void 0&&n.styleCleanup(),n.event.detected!==!0&&n.initialEvent!==void 0&&n.initialEvent.target.dispatchEvent(n.initialEvent.event);else if(n.event.detected===!0){n.event.isFirst===!0&&n.handler(xt(l===void 0?n.lastEvt:l,n).payload);const{payload:i}=xt(l===void 0?n.lastEvt:l,n,!0),s=()=>{n.handler(i)};n.styleCleanup!==void 0?n.styleCleanup(s):s()}n.event=void 0,n.initialEvent=void 0,n.lastEvt=void 0}}};if(e.__qtouchpan=n,o.mouse===!0){const l=o.mouseCapture===!0||o.mousecapture===!0?"Capture":"";Me(n,"main",[[e,"mousedown","mouseStart",`passive${l}`]])}De.has.touch===!0&&Me(n,"main",[[e,"touchstart","touchStart",`passive${o.capture===!0?"Capture":""}`],[e,"touchmove","noop","notPassiveCapture"]])},updated(e,t){const o=e.__qtouchpan;o!==void 0&&(t.oldValue!==t.value&&(typeof value!="function"&&o.end(),o.handler=t.value),o.direction=sn(t.modifiers))},beforeUnmount(e){const t=e.__qtouchpan;t!==void 0&&(t.event!==void 0&&t.end(),Ie(t,"main"),Ie(t,"temp"),De.is.firefox===!0&&bt(e,!1),t.styleCleanup!==void 0&&t.styleCleanup(),delete e.__qtouchpan)}}),$l=re({name:"QItemLabel",props:{overline:Boolean,caption:Boolean,header:Boolean,lines:[Number,String]},setup(e,{slots:t}){const o=b(()=>parseInt(e.lines,10)),a=b(()=>"q-item__label"+(e.overline===!0?" q-item__label--overline text-overline":"")+(e.caption===!0?" q-item__label--caption text-caption":"")+(e.header===!0?" q-item__label--header":"")+(o.value===1?" ellipsis":"")),n=b(()=>e.lines!==void 0&&o.value>1?{overflow:"hidden",display:"-webkit-box","-webkit-box-orient":"vertical","-webkit-line-clamp":o.value}:null);return()=>w("div",{style:n.value,class:a.value},Fe(t.default))}}),ml={true:"inset",item:"item-inset","item-thumbnail":"item-thumbnail-inset"},qt={xs:2,sm:4,md:8,lg:16,xl:24},zl=re({name:"QSeparator",props:{...Ye,spaced:[Boolean,String],inset:[Boolean,String],vertical:Boolean,color:String,size:String},setup(e){const t=de(),o=je(e,t.proxy.$q),a=b(()=>e.vertical===!0?"vertical":"horizontal"),n=b(()=>` q-separator--${a.value}`),l=b(()=>e.inset!==!1?`${n.value}-${ml[e.inset]}`:""),r=b(()=>`q-separator${n.value}${l.value}`+(e.color!==void 0?` bg-${e.color}`:"")+(o.value===!0?" q-separator--dark":"")),i=b(()=>{const s={};if(e.size!==void 0&&(s[e.vertical===!0?"width":"height"]=e.size),e.spaced!==!1){const f=e.spaced===!0?`${qt.md}px`:e.spaced in qt?`${qt[e.spaced]}px`:e.spaced,c=e.vertical===!0?["Left","Right"]:["Top","Bottom"];s[`margin${c[0]}`]=s[`margin${c[1]}`]=f}return s});return()=>w("hr",{class:r.value,style:i.value,"aria-orientation":a.value})}}),Al=re({name:"QToggle",props:{...Ma,icon:String,iconColor:String},emits:Ta,setup(e){function t(o,a){const n=b(()=>(o.value===!0?e.checkedIcon:a.value===!0?e.indeterminateIcon:e.uncheckedIcon)||e.icon),l=b(()=>o.value===!0?e.iconColor:null);return()=>[w("div",{class:"q-toggle__track"}),w("div",{class:"q-toggle__thumb absolute flex flex-center no-wrap"},n.value!==void 0?[w(Qe,{name:n.value,color:l.value})]:void 0)]}return Fa("toggle",t)}}),gl=["text","rect","circle","QBtn","QBadge","QChip","QToolbar","QCheckbox","QRadio","QToggle","QSlider","QRange","QInput","QAvatar"],bl=["wave","pulse","pulse-x","pulse-y","fade","blink","none"],Ll=re({name:"QSkeleton",props:{...Ye,tag:{type:String,default:"div"},type:{type:String,validator:e=>gl.includes(e),default:"rect"},animation:{type:String,validator:e=>bl.includes(e),default:"wave"},animationSpeed:{type:[String,Number],default:1500},square:Boolean,bordered:Boolean,size:String,width:String,height:String},setup(e,{slots:t}){const o=de(),a=je(e,o.proxy.$q),n=b(()=>{const r=e.size!==void 0?[e.size,e.size]:[e.width,e.height];return{"--q-skeleton-speed":`${e.animationSpeed}ms`,width:r[0],height:r[1]}}),l=b(()=>`q-skeleton q-skeleton--${a.value===!0?"dark":"light"} q-skeleton--type-${e.type}`+(e.animation!=="none"?` q-skeleton--anim q-skeleton--anim-${e.animation}`:"")+(e.square===!0?" q-skeleton--square":"")+(e.bordered===!0?" q-skeleton--bordered":""));return()=>w(e.tag,{class:l.value,style:n.value},Fe(t.default))}});export{Ll as A,zt as B,Dl as C,At as D,Lt as E,Qa as F,Wa as G,Oa as Q,Pl as T,Tl as U,Xo as _,no as a,Ga as b,pt as c,$l as d,Ia as e,zl as f,Al as g,El as h,cl as i,Un as j,Io as k,lo as l,sn as m,Ml as n,st as o,j as p,xl as q,xn as r,cn as s,Fl as t,ql as u,Ve as v,Be as w,Bl as x,xo as y,Cl as z};
