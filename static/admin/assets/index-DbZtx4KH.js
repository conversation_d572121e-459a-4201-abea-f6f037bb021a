import{Q as g,a as b,b as w}from"./QCardActions-CYjdimmU.js";import{r as A}from"./index-CuHAsf8O.js";import{i as C}from"./index-DFpWVYxE.js";import{d as E,r as d,o as I,_ as q,l as c,m as _,n as t,F as S,D as z,j as u,z as p,A as n,H as B}from"./index-H1mtFlU6.js";import"./use-dark-BZZjkWkU.js";import"./axios-JqZ6Qsjg.js";const L=E({name:"IndexPage",__name:"index",setup(y,{expose:m}){m();const i=d("echarts"),s=d([]),a=d([]);I(()=>{A().then(o=>{s.value=o.statis,a.value=o.category,l()})});const l=()=>{const o=document.getElementById(i.value),h=C(o);let f;const x=[],v=[];for(const r of s.value)x.push(r.label),v.push({name:r.label,data:r.data,type:"line"});f={tooltip:{trigger:"axis"},legend:{data:x},grid:{left:"0",right:"0",bottom:"0",containLabel:!0},toolbox:{feature:{saveAsImage:{}}},xAxis:{type:"category",boundaryGap:!1,data:a.value},yAxis:{type:"value"},series:v},h.setOption(f),window.addEventListener("resize",()=>{setTimeout(h.resize,300)})},e={echartsId:i,statis:s,echatsXAxis:a,chartSetOptions:l};return Object.defineProperty(e,"__isScriptSetup",{enumerable:!1,value:!0}),e}}),Q={class:"q-ma-sm"},j={class:"row"},k={class:"row full-width justify-between items-center"},O={class:"text-body1"},D={class:"text-h6"},F={class:"row full-width items-center"},N={class:"q-ml-md"},P={class:"q-mt-lg q-mx-sm"},V=["id"];function $(y,m,i,s,a,l){return c(),_("div",Q,[t("div",j,[(c(!0),_(S,null,z(s.statis,e=>(c(),_("div",{key:e.label,class:"col-md-3 col-sm-6 col-xs-12 q-pa-xs"},[u(w,{class:B(e.class)},{default:p(()=>[u(g,null,{default:p(()=>[t("div",k,[t("div",O,n(e.label),1),t("div",D,n(e.total),1)])]),_:2},1024),u(b,{style:{padding:"8px"}},{default:p(()=>[t("div",F,[t("div",null,"今日: "+n(e.today),1),t("div",N,"昨日: "+n(e.yesterday),1)])]),_:2},1024)]),_:2},1032,["class"])]))),128))]),t("div",P,[t("div",{id:s.echartsId,style:{height:"400px",width:"100%"}},null,8,V)])])}const K=q(L,[["render",$],["__file","index.vue"]]);export{K as default};
