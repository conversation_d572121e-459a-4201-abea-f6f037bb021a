import{S as A,V as F,an as w,g as O,c as n,p as M,r as m,aU as Q,o as H,h as s,ak as K,t as P,b5 as U,w as W,a0 as Z,Z as G,$ as J,aj as b,a4 as D}from"./index-H1mtFlU6.js";import{a as X,u as Y}from"./use-dark-BZZjkWkU.js";import{d as p,h as ee}from"./QInput-F165baog.js";function E(){let e=null;const o=O();function t(){e!==null&&(clearTimeout(e),e=null)}return A(t),F(t),{removeTimeout:t,registerTimeout(f,v){t(),w(o)===!1&&(e=setTimeout(()=>{e=null,f()},v))}}}const te={ratio:[String,Number]};function ae(e,o){return n(()=>{const t=Number(e.ratio||(o!==void 0?o.value:void 0));return isNaN(t)!==!0&&t>0?{paddingBottom:`${100/t}%`}:null})}const ne=1.7778,se=M({name:"QImg",props:{...te,src:String,srcset:String,sizes:String,alt:String,crossorigin:String,decoding:String,referrerpolicy:String,draggable:Boolean,loading:{type:String,default:"lazy"},loadingShowDelay:{type:[Number,String],default:0},fetchpriority:{type:String,default:"auto"},width:String,height:String,initialRatio:{type:[Number,String],default:ne},placeholderSrc:String,errorSrc:String,fit:{type:String,default:"cover"},position:{type:String,default:"50% 50%"},imgClass:String,imgStyle:Object,noSpinner:Boolean,noNativeMenu:Boolean,noTransition:Boolean,spinnerColor:String,spinnerSize:String},emits:["load","error"],setup(e,{slots:o,emit:t}){const f=m(e.initialRatio),v=ae(e,f),c=O(),{registerTimeout:d,removeTimeout:y}=E(),{registerTimeout:C,removeTimeout:k}=E(),_=n(()=>e.placeholderSrc!==void 0?{src:e.placeholderSrc}:null),x=n(()=>e.errorSrc!==void 0?{src:e.errorSrc,__qerror:!0}:null),r=[m(null),m(_.value)],u=m(0),i=m(!1),g=m(!1),T=n(()=>`q-img q-img--${e.noNativeMenu===!0?"no-":""}menu`),I=n(()=>({width:e.width,height:e.height})),$=n(()=>`q-img__image ${e.imgClass!==void 0?e.imgClass+" ":""}q-img__image--with${e.noTransition===!0?"out":""}-transition q-img__image--`),z=n(()=>({...e.imgStyle,objectFit:e.fit,objectPosition:e.position}));function B(){if(k(),e.loadingShowDelay===0){i.value=!0;return}C(()=>{i.value=!0},e.loadingShowDelay)}function q(){k(),i.value=!1}function R({target:a}){w(c)===!1&&(y(),f.value=a.naturalHeight===0?.5:a.naturalWidth/a.naturalHeight,S(a,1))}function S(a,l){l===1e3||w(c)===!0||(a.complete===!0?L(a):d(()=>{S(a,l+1)},50))}function L(a){w(c)!==!0&&(u.value=u.value^1,r[u.value].value=null,q(),a.getAttribute("__qerror")!=="true"&&(g.value=!1),t("load",a.currentSrc||a.src))}function N(a){y(),q(),g.value=!0,r[u.value].value=x.value,r[u.value^1].value=_.value,t("error",a)}function V(a){const l=r[a].value,h={key:"img_"+a,class:$.value,style:z.value,alt:e.alt,crossorigin:e.crossorigin,decoding:e.decoding,referrerpolicy:e.referrerpolicy,height:e.height,width:e.width,loading:e.loading,fetchpriority:e.fetchpriority,"aria-hidden":"true",draggable:e.draggable,...l};return u.value===a?Object.assign(h,{class:h.class+"current",onLoad:R,onError:N}):h.class+="loaded",s("div",{class:"q-img__container absolute-full",key:"img"+a},s("img",h))}function j(){return i.value===!1?s("div",{key:"content",class:"q-img__content absolute-full q-anchor--skip"},P(o[g.value===!0?"error":"default"])):s("div",{key:"loading",class:"q-img__loading absolute-full flex flex-center"},o.loading!==void 0?o.loading():e.noSpinner===!0?void 0:[s(U,{color:e.spinnerColor,size:e.spinnerSize})])}{let a=function(){W(()=>e.src||e.srcset||e.sizes?{src:e.src,srcset:e.srcset,sizes:e.sizes}:null,l=>{y(),g.value=!1,l===null?(q(),r[u.value^1].value=_.value):B(),r[u.value].value=l},{immediate:!0})};Q.value===!0?H(a):a()}return()=>{const a=[];return v.value!==null&&a.push(s("div",{key:"filler",style:v.value})),r[0].value!==null&&a.push(V(0)),r[1].value!==null&&a.push(V(1)),a.push(s(K,{name:"q-transition--fade"},j)),s("div",{key:"main",class:T.value,style:I.value,role:"img","aria-label":e.alt},a)}}});function le(e,o){const t=m(null),f=n(()=>e.disable===!0?null:s("span",{ref:t,class:"no-outline",tabindex:-1}));function v(c){const d=o.value;c!==void 0&&c.type.indexOf("key")===0?d!==null&&document.activeElement!==d&&d.contains(document.activeElement)===!0&&d.focus():t.value!==null&&(c===void 0||d!==null&&d.contains(c.target)===!0)&&t.value.focus()}return{refocusTargetEl:f,refocusTarget:v}}const re={xs:30,sm:35,md:40,lg:50,xl:60},ce={...Y,...J,...p,modelValue:{required:!0,default:null},val:{},trueValue:{default:!0},falseValue:{default:!1},indeterminateValue:{default:null},checkedIcon:String,uncheckedIcon:String,indeterminateIcon:String,toggleOrder:{type:String,validator:e=>e==="tf"||e==="ft"},toggleIndeterminate:Boolean,label:String,leftLabel:Boolean,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},de=["update:modelValue"];function fe(e,o){const{props:t,slots:f,emit:v,proxy:c}=O(),{$q:d}=c,y=X(t,d),C=m(null),{refocusTargetEl:k,refocusTarget:_}=le(t,C),x=Z(t,re),r=n(()=>t.val!==void 0&&Array.isArray(t.modelValue)),u=n(()=>{const a=b(t.val);return r.value===!0?t.modelValue.findIndex(l=>b(l)===a):-1}),i=n(()=>r.value===!0?u.value!==-1:b(t.modelValue)===b(t.trueValue)),g=n(()=>r.value===!0?u.value===-1:b(t.modelValue)===b(t.falseValue)),T=n(()=>i.value===!1&&g.value===!1),I=n(()=>t.disable===!0?-1:t.tabindex||0),$=n(()=>`q-${e} cursor-pointer no-outline row inline no-wrap items-center`+(t.disable===!0?" disabled":"")+(y.value===!0?` q-${e}--dark`:"")+(t.dense===!0?` q-${e}--dense`:"")+(t.leftLabel===!0?" reverse":"")),z=n(()=>{const a=i.value===!0?"truthy":g.value===!0?"falsy":"indet",l=t.color!==void 0&&(t.keepColor===!0||(e==="toggle"?i.value===!0:g.value!==!0))?` text-${t.color}`:"";return`q-${e}__inner relative-position non-selectable q-${e}__inner--${a}${l}`}),B=n(()=>{const a={type:"checkbox"};return t.name!==void 0&&Object.assign(a,{".checked":i.value,"^checked":i.value===!0?"checked":void 0,name:t.name,value:r.value===!0?t.val:t.trueValue}),a}),q=ee(B),R=n(()=>{const a={tabindex:I.value,role:e==="toggle"?"switch":"checkbox","aria-label":t.label,"aria-checked":T.value===!0?"mixed":i.value===!0?"true":"false"};return t.disable===!0&&(a["aria-disabled"]="true"),a});function S(a){a!==void 0&&(D(a),_(a)),t.disable!==!0&&v("update:modelValue",L(),a)}function L(){if(r.value===!0){if(i.value===!0){const a=t.modelValue.slice();return a.splice(u.value,1),a}return t.modelValue.concat([t.val])}if(i.value===!0){if(t.toggleOrder!=="ft"||t.toggleIndeterminate===!1)return t.falseValue}else if(g.value===!0){if(t.toggleOrder==="ft"||t.toggleIndeterminate===!1)return t.trueValue}else return t.toggleOrder!=="ft"?t.trueValue:t.falseValue;return t.indeterminateValue}function N(a){(a.keyCode===13||a.keyCode===32)&&D(a)}function V(a){(a.keyCode===13||a.keyCode===32)&&S(a)}const j=o(i,T);return Object.assign(c,{toggle:S}),()=>{const a=j();t.disable!==!0&&q(a,"unshift",` q-${e}__native absolute q-ma-none q-pa-none`);const l=[s("div",{class:z.value,style:x.value,"aria-hidden":"true"},a)];k.value!==null&&l.push(k.value);const h=t.label!==void 0?G(f.default,[t.label]):P(f.default);return h!==void 0&&l.push(s("div",{class:`q-${e}__label q-anchor--skip`},h)),s("div",{ref:C,class:$.value,...R.value,onClick:S,onKeydown:N,onKeyup:V},l)}}export{se as Q,E as a,ce as b,de as c,fe as d,re as o,le as u};
