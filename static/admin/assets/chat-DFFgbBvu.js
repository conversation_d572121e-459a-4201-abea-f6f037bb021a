import{p as M,i as C,q as c,c as Q,h as S,t as V,u as D,v as L,g as q,d as z,r as u,x as k,_ as E,l as d,y as f,z as n,j as o,n as l,Q as v,A as H,B,C as K,m as F,D as N,E as j,F as A,G as O}from"./index-H1mtFlU6.js";import{Q as R,a as $,b as G,c as b,d as J,e as W}from"./QChatMessage-o4w2c2DK.js";import{Q as X}from"./QInput-F165baog.js";import"./QResizeObserver-DJfGZaa6.js";import"./use-dark-BZZjkWkU.js";const Y=M({name:"QPage",props:{padding:Boolean,styleFn:Function},setup(i,{slots:a}){const{proxy:{$q:r}}=q(),e=C(D,c);if(e===c)return console.error("QPage needs to be a deep child of QLayout"),c;if(C(L,c)===c)return console.error("QPage needs to be child of QPageContainer"),c;const g=Q(()=>{const s=(e.header.space===!0?e.header.size:0)+(e.footer.space===!0?e.footer.size:0);if(typeof i.styleFn=="function"){const h=e.isContainer.value===!0?e.containerHeight.value:r.screen.height;return i.styleFn(s,h)}return{minHeight:e.isContainer.value===!0?e.containerHeight.value-s+"px":r.screen.height===0?s!==0?`calc(100vh - ${s}px)`:"100vh":r.screen.height-s+"px"}}),t=Q(()=>`q-page${i.padding===!0?" q-layout-padding":""}`);return()=>S("main",{class:t.value,style:g.value},V(a.default))}}),Z=z({name:"ChatPageIndex",__name:"chat",setup(i,{expose:a}){a();const r=u([]),e=u(""),m=u("Setupdo 001客服"),g=u(!0),t=u(null),s=u(null),h=()=>{e.value.trim()&&(r.value.push({text:e.value,type:"sent",time:new Date().toLocaleTimeString()}),e.value="",k(()=>{_()}))},I=()=>{s.value?.click()},w=T=>{const p=T.target;if(p.files&&p.files[0]){const P=p.files[0],x=new FileReader;x.onload=U=>{r.value.push({image:U.target?.result,type:"sent",time:new Date().toLocaleTimeString()}),k(()=>{_()})},x.readAsDataURL(P),p.value=""}},_=()=>{t.value&&(t.value.scrollTop=t.value.scrollHeight)},y={messages:r,newMessage:e,chatUser:m,isOnline:g,messageContainer:t,imageInput:s,sendMessage:h,triggerImageUpload:I,handleImageUpload:w,scrollToBottom:_};return Object.defineProperty(y,"__isScriptSetup",{enumerable:!1,value:!0}),y}}),ee={class:"chat-container"},te={class:"chat-header"},ae={class:"user-info"},ne={class:"username"},se={class:"chat-messages",ref:"messageContainer"},oe=["src"],re={class:"chat-input"};function le(i,a,r,e,m,g){return d(),f(R,null,{default:n(()=>[o(W,null,{default:n(()=>[o(Y,{class:"flex flex-center"},{default:n(()=>[l("div",ee,[l("div",te,[o($,null,{default:n(()=>[o(v,{flat:"",round:"",icon:"arrow_back",onClick:a[0]||(a[0]=t=>i.$router.back())}),o(G,{class:"text-center"},{default:n(()=>[l("div",ae,[l("span",ne,H(e.chatUser),1),e.isOnline?(d(),f(b,{key:0,rounded:"",color:"green",class:"online-status"},{default:n(()=>a[2]||(a[2]=[B(" 在线 ")])),_:1})):(d(),f(b,{key:1,rounded:"",color:"grey",class:"online-status"},{default:n(()=>a[3]||(a[3]=[B(" 离线 ")])),_:1}))])]),_:1}),o(K,{size:"32px"},{default:n(()=>a[4]||(a[4]=[l("img",{src:"https://cdn.quasar.dev/img/avatar.png"},null,-1)])),_:1})]),_:1})]),l("div",se,[(d(!0),F(A,null,N(e.messages,(t,s)=>(d(),f(J,{key:s,sent:t.type==="sent",text:t.text?[t.text]:void 0,stamp:t.time},{default:n(()=>[t.image?(d(),F("img",{key:0,src:t.image,class:"chat-image"},null,8,oe)):j("",!0)]),_:2},1032,["sent","text","stamp"]))),128))],512),l("div",re,[o(X,{modelValue:e.newMessage,"onUpdate:modelValue":a[1]||(a[1]=t=>e.newMessage=t),dense:"",outlined:"",placeholder:"输入消息...",onKeyup:O(e.sendMessage,["enter"])},{before:n(()=>[o(v,{round:"",flat:"",icon:"image",onClick:e.triggerImageUpload},{default:n(()=>[l("input",{type:"file",ref:"imageInput",accept:"image/*",style:{display:"none"},onChange:e.handleImageUpload},null,544)]),_:1})]),after:n(()=>[o(v,{round:"",flat:"",icon:"send",onClick:e.sendMessage})]),_:1},8,["modelValue"])])])]),_:1})]),_:1})]),_:1})}const pe=E(Z,[["render",le],["__scopeId","data-v-36dc2be8"],["__file","chat.vue"]]);export{pe as default};
