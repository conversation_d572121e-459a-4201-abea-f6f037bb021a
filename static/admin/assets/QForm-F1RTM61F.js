import{p as q,h as i,c as I,M as E,r as F,aB as P,S as B,U as Q,o as A,t as V,g as M,an as R,a4 as _,x as j,aC as w}from"./index-H1mtFlU6.js";import{b as D,c as H,d as N}from"./use-checkbox-ZuTr6CRM.js";import{l as O}from"./QInput-F165baog.js";const $=()=>i("div",{key:"svg",class:"q-checkbox__bg absolute"},[i("svg",{class:"q-checkbox__svg fit absolute-full",viewBox:"0 0 24 24"},[i("path",{class:"q-checkbox__truthy",fill:"none",d:"M1.73,12.91 8.1,19.28 22.79,4.59"}),i("path",{class:"q-checkbox__indet",d:"M4,14H20V10H4"})])]),G=q({name:"QCheckbox",props:D,emits:H,setup(o){const v=$();function l(m,r){const a=I(()=>(m.value===!0?o.checkedIcon:r.value===!0?o.indeterminateIcon:o.uncheckedIcon)||null);return()=>a.value!==null?[i("div",{key:"icon",class:"q-checkbox__icon-container absolute-full flex flex-center no-wrap"},[i(E,{class:"q-checkbox__icon",name:a.value})])]:[v]}return N("checkbox",l)}}),J=q({name:"QForm",props:{autofocus:Boolean,noErrorFocus:Boolean,noResetFocus:Boolean,greedy:Boolean,onSubmit:Function},emits:["reset","validationSuccess","validationError"],setup(o,{slots:v,emit:l}){const m=M(),r=F(null);let a=0;const u=[];function b(e){const c=typeof e=="boolean"?e:o.noErrorFocus!==!0,d=++a,k=(t,n)=>{l(`validation${t===!0?"Success":"Error"}`,n)},p=t=>{const n=t.validate();return typeof n.then=="function"?n.then(s=>({valid:s,comp:t}),s=>({valid:!1,comp:t,err:s})):Promise.resolve({valid:n,comp:t})};return(o.greedy===!0?Promise.all(u.map(p)).then(t=>t.filter(n=>n.valid!==!0)):u.reduce((t,n)=>t.then(()=>p(n).then(s=>{if(s.valid===!1)return Promise.reject(s)})),Promise.resolve()).catch(t=>[t])).then(t=>{if(t===void 0||t.length===0)return d===a&&k(!0),!0;if(d===a){const{comp:n,err:s}=t[0];if(s!==void 0&&console.error(s),k(!1,n),c===!0){const C=t.find(({comp:S})=>typeof S.focus=="function"&&R(S.$)===!1);C!==void 0&&C.comp.focus()}}return!1})}function h(){a++,u.forEach(e=>{typeof e.resetValidation=="function"&&e.resetValidation()})}function x(e){e!==void 0&&_(e);const c=a+1;b().then(d=>{c===a&&d===!0&&(o.onSubmit!==void 0?l("submit",e):e!==void 0&&e.target!==void 0&&typeof e.target.submit=="function"&&e.target.submit())})}function g(e){e!==void 0&&_(e),l("reset"),j(()=>{h(),o.autofocus===!0&&o.noResetFocus!==!0&&f()})}function f(){O(()=>{if(r.value===null)return;const e=r.value.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||r.value.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||r.value.querySelector("[autofocus], [data-autofocus]")||Array.prototype.find.call(r.value.querySelectorAll("[tabindex]"),c=>c.tabIndex!==-1);e?.focus({preventScroll:!0})})}P(w,{bindComponent(e){u.push(e)},unbindComponent(e){const c=u.indexOf(e);c!==-1&&u.splice(c,1)}});let y=!1;return B(()=>{y=!0}),Q(()=>{y===!0&&o.autofocus===!0&&f()}),A(()=>{o.autofocus===!0&&f()}),Object.assign(m.proxy,{validate:b,resetValidation:h,submit:x,reset:g,focus:f,getValidationComponents:()=>u}),()=>i("form",{class:"q-form",ref:r,onSubmit:x,onReset:g},V(v.default))}});export{J as Q,G as a};
