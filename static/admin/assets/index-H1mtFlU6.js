const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/main-DBHZUh_w.js","assets/QChatMessage-o4w2c2DK.js","assets/QResizeObserver-DJfGZaa6.js","assets/QSkeleton-G0ihNNrd.js","assets/QInput-F165baog.js","assets/use-dark-BZZjkWkU.js","assets/use-checkbox-ZuTr6CRM.js","assets/axios-JqZ6Qsjg.js","assets/QSkeleton-BVed1aeW.css","assets/QCardActions-CYjdimmU.js","assets/index-CuHAsf8O.js","assets/main-Dr3rlm00.css","assets/index-DbZtx4KH.js","assets/index-DFpWVYxE.js","assets/chat-DFFgbBvu.js","assets/chat-BDrL7ISP.css","assets/login-DSMOSE6r.js","assets/QForm-F1RTM61F.js","assets/login-BluPcJQf.css","assets/table-9vg9nFPe.js","assets/table-DIYF66QE.css"])))=>i.map(i=>d[i]);
const Su=function(){const t=typeof document<"u"&&document.createElement("link").relList;return t&&t.supports&&t.supports("modulepreload")?"modulepreload":"preload"}(),Eu=function(e){return"/admin/"+e},ci={},Ge=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),l=i?.nonce||i?.getAttribute("nonce");s=Promise.allSettled(n.map(a=>{if(a=Eu(a),a in ci)return;ci[a]=!0;const u=a.endsWith(".css"),c=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${a}"]${c}`))return;const d=document.createElement("link");if(d.rel=u?"stylesheet":Su,u||(d.as="script"),d.crossOrigin="",d.href=a,l&&d.setAttribute("nonce",l),document.head.appendChild(d),u)return new Promise((f,g)=>{d.addEventListener("load",f),d.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${a}`)))})}))}function o(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return s.then(i=>{for(const l of i||[])l.status==="rejected"&&o(l.reason);return t().catch(o)})};/**
* @vue/shared v3.5.6
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function To(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ue={},xn=[],ft=()=>{},xu=()=>!1,ns=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Po=e=>e.startsWith("onUpdate:"),ve=Object.assign,Ro=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Cu=Object.prototype.hasOwnProperty,oe=(e,t)=>Cu.call(e,t),W=Array.isArray,Cn=e=>ur(e)==="[object Map]",ql=e=>ur(e)==="[object Set]",Tu=e=>ur(e)==="[object RegExp]",J=e=>typeof e=="function",he=e=>typeof e=="string",Vt=e=>typeof e=="symbol",de=e=>e!==null&&typeof e=="object",Hl=e=>(de(e)||J(e))&&J(e.then)&&J(e.catch),Bl=Object.prototype.toString,ur=e=>Bl.call(e),Pu=e=>ur(e).slice(8,-1),Vl=e=>ur(e)==="[object Object]",Ao=e=>he(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,zn=To(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),rs=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ru=/-(\w)/g,rt=rs(e=>e.replace(Ru,(t,n)=>n?n.toUpperCase():"")),Au=/\B([A-Z])/g,Ut=rs(e=>e.replace(Au,"-$1").toLowerCase()),ss=rs(e=>e.charAt(0).toUpperCase()+e.slice(1)),ws=rs(e=>e?`on${ss(e)}`:""),qt=(e,t)=>!Object.is(e,t),Wn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},qr=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},Ou=e=>{const t=parseFloat(e);return isNaN(t)?e:t},ku=e=>{const t=he(e)?Number(e):NaN;return isNaN(t)?e:t};let ui;const Ul=()=>ui||(ui=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Oo(e){if(W(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=he(r)?Mu(r):Oo(r);if(s)for(const o in s)t[o]=s[o]}return t}else if(he(e)||de(e))return e}const Lu=/;(?![^(]*\))/g,Iu=/:([^]+)/,$u=/\/\*[^]*?\*\//g;function Mu(e){const t={};return e.replace($u,"").split(Lu).forEach(n=>{if(n){const r=n.split(Iu);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function ko(e){let t="";if(he(e))t=e;else if(W(e))for(let n=0;n<e.length;n++){const r=ko(e[n]);r&&(t+=r+" ")}else if(de(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Nu="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",ju=To(Nu);function Kl(e){return!!e||e===""}const zl=e=>!!(e&&e.__v_isRef===!0),Du=e=>he(e)?e:e==null?"":W(e)||de(e)&&(e.toString===Bl||!J(e.toString))?zl(e)?Du(e.value):JSON.stringify(e,Wl,2):String(e),Wl=(e,t)=>zl(t)?Wl(e,t.value):Cn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,s],o)=>(n[Ss(r,o)+" =>"]=s,n),{})}:ql(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Ss(n))}:Vt(t)?Ss(t):de(t)&&!W(t)&&!Vl(t)?String(t):t,Ss=(e,t="")=>{var n;return Vt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.6
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Le;class Gl{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Le,!t&&Le&&(this.index=(Le.scopes||(Le.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Le;try{return Le=this,t()}finally{Le=n}}}on(){Le=this}off(){Le=this.parent}stop(t){if(this._active){let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.scopes)for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0,this._active=!1}}}function Yl(e){return new Gl(e)}function Jl(){return Le}function Fu(e,t=!1){Le&&Le.cleanups.push(e)}let ce;const Es=new WeakSet;class Ql{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Le&&Le.active&&Le.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Es.has(this)&&(Es.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Zl(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,fi(this),ea(this);const t=ce,n=nt;ce=this,nt=!0;try{return this.fn()}finally{ta(this),ce=t,nt=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)$o(t);this.deps=this.depsTail=void 0,fi(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Es.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Js(this)&&this.run()}get dirty(){return Js(this)}}let Xl=0,Gn;function Zl(e){e.flags|=8,e.next=Gn,Gn=e}function Lo(){Xl++}function Io(){if(--Xl>0)return;let e;for(;Gn;){let t=Gn;for(Gn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function ea(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function ta(e){let t,n=e.depsTail,r=n;for(;r;){const s=r.prevDep;r.version===-1?(r===n&&(n=s),$o(r),qu(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=s}e.deps=t,e.depsTail=n}function Js(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(na(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function na(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===nr))return;e.globalVersion=nr;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Js(e)){e.flags&=-3;return}const n=ce,r=nt;ce=e,nt=!0;try{ea(e);const s=e.fn(e._value);(t.version===0||qt(s,e._value))&&(e._value=s,t.version++)}catch(s){throw t.version++,s}finally{ce=n,nt=r,ta(e),e.flags&=-3}}function $o(e){const{dep:t,prevSub:n,nextSub:r}=e;if(n&&(n.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=n,e.nextSub=void 0),t.subs===e&&(t.subs=n),!t.subs&&t.computed){t.computed.flags&=-5;for(let s=t.computed.deps;s;s=s.nextDep)$o(s)}}function qu(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let nt=!0;const ra=[];function Kt(){ra.push(nt),nt=!1}function zt(){const e=ra.pop();nt=e===void 0?!0:e}function fi(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ce;ce=void 0;try{t()}finally{ce=n}}}let nr=0;class Hu{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Mo{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0}track(t){if(!ce||!nt||ce===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ce)n=this.activeLink=new Hu(ce,this),ce.deps?(n.prevDep=ce.depsTail,ce.depsTail.nextDep=n,ce.depsTail=n):ce.deps=ce.depsTail=n,ce.flags&4&&sa(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=ce.depsTail,n.nextDep=void 0,ce.depsTail.nextDep=n,ce.depsTail=n,ce.deps===n&&(ce.deps=r)}return n}trigger(t){this.version++,nr++,this.notify(t)}notify(t){Lo();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Io()}}}function sa(e){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)sa(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}const Hr=new WeakMap,nn=Symbol(""),Qs=Symbol(""),rr=Symbol("");function Oe(e,t,n){if(nt&&ce){let r=Hr.get(e);r||Hr.set(e,r=new Map);let s=r.get(n);s||r.set(n,s=new Mo),s.track()}}function St(e,t,n,r,s,o){const i=Hr.get(e);if(!i){nr++;return}const l=a=>{a&&a.trigger()};if(Lo(),t==="clear")i.forEach(l);else{const a=W(e),u=a&&Ao(n);if(a&&n==="length"){const c=Number(r);i.forEach((d,f)=>{(f==="length"||f===rr||!Vt(f)&&f>=c)&&l(d)})}else switch(n!==void 0&&l(i.get(n)),u&&l(i.get(rr)),t){case"add":a?u&&l(i.get("length")):(l(i.get(nn)),Cn(e)&&l(i.get(Qs)));break;case"delete":a||(l(i.get(nn)),Cn(e)&&l(i.get(Qs)));break;case"set":Cn(e)&&l(i.get(nn));break}}Io()}function Bu(e,t){var n;return(n=Hr.get(e))==null?void 0:n.get(t)}function pn(e){const t=Z(e);return t===e?t:(Oe(t,"iterate",rr),Ye(e)?t:t.map(Ae))}function os(e){return Oe(e=Z(e),"iterate",rr),e}const Vu={__proto__:null,[Symbol.iterator](){return xs(this,Symbol.iterator,Ae)},concat(...e){return pn(this).concat(...e.map(t=>W(t)?pn(t):t))},entries(){return xs(this,"entries",e=>(e[1]=Ae(e[1]),e))},every(e,t){return pt(this,"every",e,t,void 0,arguments)},filter(e,t){return pt(this,"filter",e,t,n=>n.map(Ae),arguments)},find(e,t){return pt(this,"find",e,t,Ae,arguments)},findIndex(e,t){return pt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return pt(this,"findLast",e,t,Ae,arguments)},findLastIndex(e,t){return pt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return pt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Cs(this,"includes",e)},indexOf(...e){return Cs(this,"indexOf",e)},join(e){return pn(this).join(e)},lastIndexOf(...e){return Cs(this,"lastIndexOf",e)},map(e,t){return pt(this,"map",e,t,void 0,arguments)},pop(){return In(this,"pop")},push(...e){return In(this,"push",e)},reduce(e,...t){return di(this,"reduce",e,t)},reduceRight(e,...t){return di(this,"reduceRight",e,t)},shift(){return In(this,"shift")},some(e,t){return pt(this,"some",e,t,void 0,arguments)},splice(...e){return In(this,"splice",e)},toReversed(){return pn(this).toReversed()},toSorted(e){return pn(this).toSorted(e)},toSpliced(...e){return pn(this).toSpliced(...e)},unshift(...e){return In(this,"unshift",e)},values(){return xs(this,"values",Ae)}};function xs(e,t,n){const r=os(e),s=r[t]();return r!==e&&!Ye(e)&&(s._next=s.next,s.next=()=>{const o=s._next();return o.value&&(o.value=n(o.value)),o}),s}const Uu=Array.prototype;function pt(e,t,n,r,s,o){const i=os(e),l=i!==e&&!Ye(e),a=i[t];if(a!==Uu[t]){const d=a.apply(e,o);return l?Ae(d):d}let u=n;i!==e&&(l?u=function(d,f){return n.call(this,Ae(d),f,e)}:n.length>2&&(u=function(d,f){return n.call(this,d,f,e)}));const c=a.call(i,u,r);return l&&s?s(c):c}function di(e,t,n,r){const s=os(e);let o=n;return s!==e&&(Ye(e)?n.length>3&&(o=function(i,l,a){return n.call(this,i,l,a,e)}):o=function(i,l,a){return n.call(this,i,Ae(l),a,e)}),s[t](o,...r)}function Cs(e,t,n){const r=Z(e);Oe(r,"iterate",rr);const s=r[t](...n);return(s===-1||s===!1)&&Fo(n[0])?(n[0]=Z(n[0]),r[t](...n)):s}function In(e,t,n=[]){Kt(),Lo();const r=Z(e)[t].apply(e,n);return Io(),zt(),r}const Ku=To("__proto__,__v_isRef,__isVue"),oa=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Vt));function zu(e){Vt(e)||(e=String(e));const t=Z(this);return Oe(t,"has",e),t.hasOwnProperty(e)}class ia{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){const s=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return o;if(n==="__v_raw")return r===(s?o?of:ua:o?ca:aa).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=W(t);if(!s){let a;if(i&&(a=Vu[n]))return a;if(n==="hasOwnProperty")return zu}const l=Reflect.get(t,n,me(t)?t:r);return(Vt(n)?oa.has(n):Ku(n))||(s||Oe(t,"get",n),o)?l:me(l)?i&&Ao(n)?l:l.value:de(l)?s?da(l):un(l):l}}class la extends ia{constructor(t=!1){super(!1,t)}set(t,n,r,s){let o=t[n];if(!this._isShallow){const a=on(o);if(!Ye(r)&&!on(r)&&(o=Z(o),r=Z(r)),!W(t)&&me(o)&&!me(r))return a?!1:(o.value=r,!0)}const i=W(t)&&Ao(n)?Number(n)<t.length:oe(t,n),l=Reflect.set(t,n,r,me(t)?t:s);return t===Z(s)&&(i?qt(r,o)&&St(t,"set",n,r):St(t,"add",n,r)),l}deleteProperty(t,n){const r=oe(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&r&&St(t,"delete",n,void 0),s}has(t,n){const r=Reflect.has(t,n);return(!Vt(n)||!oa.has(n))&&Oe(t,"has",n),r}ownKeys(t){return Oe(t,"iterate",W(t)?"length":nn),Reflect.ownKeys(t)}}class Wu extends ia{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Gu=new la,Yu=new Wu,Ju=new la(!0);const No=e=>e,is=e=>Reflect.getPrototypeOf(e);function mr(e,t,n=!1,r=!1){e=e.__v_raw;const s=Z(e),o=Z(t);n||(qt(t,o)&&Oe(s,"get",t),Oe(s,"get",o));const{has:i}=is(s),l=r?No:n?qo:Ae;if(i.call(s,t))return l(e.get(t));if(i.call(s,o))return l(e.get(o));e!==s&&e.get(t)}function vr(e,t=!1){const n=this.__v_raw,r=Z(n),s=Z(e);return t||(qt(e,s)&&Oe(r,"has",e),Oe(r,"has",s)),e===s?n.has(e):n.has(e)||n.has(s)}function _r(e,t=!1){return e=e.__v_raw,!t&&Oe(Z(e),"iterate",nn),Reflect.get(e,"size",e)}function hi(e,t=!1){!t&&!Ye(e)&&!on(e)&&(e=Z(e));const n=Z(this);return is(n).has.call(n,e)||(n.add(e),St(n,"add",e,e)),this}function pi(e,t,n=!1){!n&&!Ye(t)&&!on(t)&&(t=Z(t));const r=Z(this),{has:s,get:o}=is(r);let i=s.call(r,e);i||(e=Z(e),i=s.call(r,e));const l=o.call(r,e);return r.set(e,t),i?qt(t,l)&&St(r,"set",e,t):St(r,"add",e,t),this}function gi(e){const t=Z(this),{has:n,get:r}=is(t);let s=n.call(t,e);s||(e=Z(e),s=n.call(t,e)),r&&r.call(t,e);const o=t.delete(e);return s&&St(t,"delete",e,void 0),o}function mi(){const e=Z(this),t=e.size!==0,n=e.clear();return t&&St(e,"clear",void 0,void 0),n}function yr(e,t){return function(r,s){const o=this,i=o.__v_raw,l=Z(i),a=t?No:e?qo:Ae;return!e&&Oe(l,"iterate",nn),i.forEach((u,c)=>r.call(s,a(u),a(c),o))}}function br(e,t,n){return function(...r){const s=this.__v_raw,o=Z(s),i=Cn(o),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,u=s[e](...r),c=n?No:t?qo:Ae;return!t&&Oe(o,"iterate",a?Qs:nn),{next(){const{value:d,done:f}=u.next();return f?{value:d,done:f}:{value:l?[c(d[0]),c(d[1])]:c(d),done:f}},[Symbol.iterator](){return this}}}}function Tt(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Qu(){const e={get(o){return mr(this,o)},get size(){return _r(this)},has:vr,add:hi,set:pi,delete:gi,clear:mi,forEach:yr(!1,!1)},t={get(o){return mr(this,o,!1,!0)},get size(){return _r(this)},has:vr,add(o){return hi.call(this,o,!0)},set(o,i){return pi.call(this,o,i,!0)},delete:gi,clear:mi,forEach:yr(!1,!0)},n={get(o){return mr(this,o,!0)},get size(){return _r(this,!0)},has(o){return vr.call(this,o,!0)},add:Tt("add"),set:Tt("set"),delete:Tt("delete"),clear:Tt("clear"),forEach:yr(!0,!1)},r={get(o){return mr(this,o,!0,!0)},get size(){return _r(this,!0)},has(o){return vr.call(this,o,!0)},add:Tt("add"),set:Tt("set"),delete:Tt("delete"),clear:Tt("clear"),forEach:yr(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(o=>{e[o]=br(o,!1,!1),n[o]=br(o,!0,!1),t[o]=br(o,!1,!0),r[o]=br(o,!0,!0)}),[e,n,t,r]}const[Xu,Zu,ef,tf]=Qu();function jo(e,t){const n=t?e?tf:ef:e?Zu:Xu;return(r,s,o)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(oe(n,s)&&s in r?n:r,s,o)}const nf={get:jo(!1,!1)},rf={get:jo(!1,!0)},sf={get:jo(!0,!1)};const aa=new WeakMap,ca=new WeakMap,ua=new WeakMap,of=new WeakMap;function lf(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function af(e){return e.__v_skip||!Object.isExtensible(e)?0:lf(Pu(e))}function un(e){return on(e)?e:Do(e,!1,Gu,nf,aa)}function fa(e){return Do(e,!1,Ju,rf,ca)}function da(e){return Do(e,!0,Yu,sf,ua)}function Do(e,t,n,r,s){if(!de(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=s.get(e);if(o)return o;const i=af(e);if(i===0)return e;const l=new Proxy(e,i===2?r:n);return s.set(e,l),l}function Dt(e){return on(e)?Dt(e.__v_raw):!!(e&&e.__v_isReactive)}function on(e){return!!(e&&e.__v_isReadonly)}function Ye(e){return!!(e&&e.__v_isShallow)}function Fo(e){return e?!!e.__v_raw:!1}function Z(e){const t=e&&e.__v_raw;return t?Z(t):e}function ht(e){return!oe(e,"__v_skip")&&Object.isExtensible(e)&&qr(e,"__v_skip",!0),e}const Ae=e=>de(e)?un(e):e,qo=e=>de(e)?da(e):e;function me(e){return e?e.__v_isRef===!0:!1}function ln(e){return ha(e,!1)}function cf(e){return ha(e,!0)}function ha(e,t){return me(e)?e:new uf(e,t)}class uf{constructor(t,n){this.dep=new Mo,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:Z(t),this._value=n?t:Ae(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||Ye(t)||on(t);t=r?t:Z(t),qt(t,n)&&(this._rawValue=t,this._value=r?t:Ae(t),this.dep.trigger())}}function wt(e){return me(e)?e.value:e}const ff={get:(e,t,n)=>t==="__v_raw"?e:wt(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return me(s)&&!me(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function pa(e){return Dt(e)?e:new Proxy(e,ff)}function df(e){const t=W(e)?new Array(e.length):{};for(const n in e)t[n]=pf(e,n);return t}class hf{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Bu(Z(this._object),this._key)}}function pf(e,t,n){const r=e[t];return me(r)?r:new hf(e,t,n)}class gf{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Mo(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=nr-1,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&ce!==this)return Zl(this),!0}get value(){const t=this.dep.track();return na(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function mf(e,t,n=!1){let r,s;return J(e)?r=e:(r=e.get,s=e.set),new gf(r,s,n)}const wr={},Br=new WeakMap;let Xt;function vf(e,t=!1,n=Xt){if(n){let r=Br.get(n);r||Br.set(n,r=[]),r.push(e)}}function _f(e,t,n=ue){const{immediate:r,deep:s,once:o,scheduler:i,augmentJob:l,call:a}=n,u=y=>s?y:Ye(y)||s===!1||s===0?bt(y,1):bt(y);let c,d,f,g,v=!1,x=!1;if(me(e)?(d=()=>e.value,v=Ye(e)):Dt(e)?(d=()=>u(e),v=!0):W(e)?(x=!0,v=e.some(y=>Dt(y)||Ye(y)),d=()=>e.map(y=>{if(me(y))return y.value;if(Dt(y))return u(y);if(J(y))return a?a(y,2):y()})):J(e)?t?d=a?()=>a(e,2):e:d=()=>{if(f){Kt();try{f()}finally{zt()}}const y=Xt;Xt=c;try{return a?a(e,3,[g]):e(g)}finally{Xt=y}}:d=ft,t&&s){const y=d,I=s===!0?1/0:s;d=()=>bt(y(),I)}const T=Jl(),R=()=>{c.stop(),T&&Ro(T.effects,c)};if(o&&t){const y=t;t=(...I)=>{y(...I),R()}}let w=x?new Array(e.length).fill(wr):wr;const S=y=>{if(!(!(c.flags&1)||!c.dirty&&!y))if(t){const I=c.run();if(s||v||(x?I.some((B,j)=>qt(B,w[j])):qt(I,w))){f&&f();const B=Xt;Xt=c;try{const j=[I,w===wr?void 0:x&&w[0]===wr?[]:w,g];a?a(t,3,j):t(...j),w=I}finally{Xt=B}}}else c.run()};return l&&l(S),c=new Ql(d),c.scheduler=i?()=>i(S,!1):S,g=y=>vf(y,!1,c),f=c.onStop=()=>{const y=Br.get(c);if(y){if(a)a(y,4);else for(const I of y)I();Br.delete(c)}},t?r?S(!0):w=c.run():i?i(S.bind(null,!0),!0):c.run(),R.pause=c.pause.bind(c),R.resume=c.resume.bind(c),R.stop=R,R}function bt(e,t=1/0,n){if(t<=0||!de(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,me(e))bt(e.value,t,n);else if(W(e))for(let r=0;r<e.length;r++)bt(e[r],t,n);else if(ql(e)||Cn(e))e.forEach(r=>{bt(r,t,n)});else if(Vl(e)){for(const r in e)bt(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&bt(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.6
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function fr(e,t,n,r){try{return r?e(...r):e()}catch(s){ls(s,t,n)}}function st(e,t,n,r){if(J(e)){const s=fr(e,t,n,r);return s&&Hl(s)&&s.catch(o=>{ls(o,t,n)}),s}if(W(e)){const s=[];for(let o=0;o<e.length;o++)s.push(st(e[o],t,n,r));return s}}function ls(e,t,n,r=!0){const s=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ue;if(t){let l=t.parent;const a=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const c=l.ec;if(c){for(let d=0;d<c.length;d++)if(c[d](e,a,u)===!1)return}l=l.parent}if(o){Kt(),fr(o,null,10,[e,a,u]),zt();return}}yf(e,n,s,r,i)}function yf(e,t,n,r=!0,s=!1){if(s)throw e;console.error(e)}let sr=!1,Xs=!1;const Ie=[];let lt=0;const Tn=[];let kt=null,wn=0;const ga=Promise.resolve();let Ho=null;function Bo(e){const t=Ho||ga;return e?t.then(this?e.bind(this):e):t}function bf(e){let t=sr?lt+1:0,n=Ie.length;for(;t<n;){const r=t+n>>>1,s=Ie[r],o=or(s);o<e||o===e&&s.flags&2?t=r+1:n=r}return t}function Vo(e){if(!(e.flags&1)){const t=or(e),n=Ie[Ie.length-1];!n||!(e.flags&2)&&t>=or(n)?Ie.push(e):Ie.splice(bf(t),0,e),e.flags|=1,ma()}}function ma(){!sr&&!Xs&&(Xs=!0,Ho=ga.then(_a))}function wf(e){W(e)?Tn.push(...e):kt&&e.id===-1?kt.splice(wn+1,0,e):e.flags&1||(Tn.push(e),e.flags|=1),ma()}function vi(e,t,n=sr?lt+1:0){for(;n<Ie.length;n++){const r=Ie[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;Ie.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&=-2}}}function va(e){if(Tn.length){const t=[...new Set(Tn)].sort((n,r)=>or(n)-or(r));if(Tn.length=0,kt){kt.push(...t);return}for(kt=t,wn=0;wn<kt.length;wn++){const n=kt[wn];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}kt=null,wn=0}}const or=e=>e.id==null?e.flags&2?-1:1/0:e.id;function _a(e){Xs=!1,sr=!0;try{for(lt=0;lt<Ie.length;lt++){const t=Ie[lt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),fr(t,t.i,t.i?15:14),t.flags&=-2)}}finally{for(;lt<Ie.length;lt++){const t=Ie[lt];t&&(t.flags&=-2)}lt=0,Ie.length=0,va(),sr=!1,Ho=null,(Ie.length||Tn.length)&&_a()}}let ct,Hn=[],Zs=!1;function as(e,...t){ct?ct.emit(e,...t):Zs||Hn.push({event:e,args:t})}function ya(e,t){var n,r;ct=e,ct?(ct.enabled=!0,Hn.forEach(({event:s,args:o})=>ct.emit(s,...o)),Hn=[]):typeof window<"u"&&window.HTMLElement&&!((r=(n=window.navigator)==null?void 0:n.userAgent)!=null&&r.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(o=>{ya(o,t)}),setTimeout(()=>{ct||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,Zs=!0,Hn=[])},3e3)):(Zs=!0,Hn=[])}function Sf(e,t){as("app:init",e,t,{Fragment:$e,Text:dr,Comment:xe,Static:kr})}function Ef(e){as("app:unmount",e)}const eo=Uo("component:added"),ba=Uo("component:updated"),xf=Uo("component:removed"),Cf=e=>{ct&&typeof ct.cleanupBuffer=="function"&&!ct.cleanupBuffer(e)&&xf(e)};/*! #__NO_SIDE_EFFECTS__ */function Uo(e){return t=>{as(e,t.appContext.app,t.uid,t.parent?t.parent.uid:void 0,t)}}function Tf(e,t,n){as("component:emit",e.appContext.app,e,t,n)}let _e=null,wa=null;function Vr(e){const t=_e;return _e=e,wa=e&&e.type.__scopeId||null,t}function Pf(e,t=_e,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&Ri(-1);const o=Vr(t);let i;try{i=e(...s)}finally{Vr(o),r._d&&Ri(1)}return ba(t),i};return r._n=!0,r._c=!0,r._d=!0,r}function Sa(e,t){if(_e===null)return e;const n=ms(_e),r=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[o,i,l,a=ue]=t[s];o&&(J(o)&&(o={mounted:o,updated:o}),o.deep&&bt(i),r.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function Wt(e,t,n,r){const s=e.dirs,o=t&&t.dirs;for(let i=0;i<s.length;i++){const l=s[i];o&&(l.oldValue=o[i].value);let a=l.dir[r];a&&(Kt(),st(a,n,8,[e.el,l,e,t]),zt())}}const Ea=Symbol("_vte"),xa=e=>e.__isTeleport,Yn=e=>e&&(e.disabled||e.disabled===""),Rf=e=>e&&(e.defer||e.defer===""),_i=e=>typeof SVGElement<"u"&&e instanceof SVGElement,yi=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,to=(e,t)=>{const n=e&&e.to;return he(n)?t?t(n):null:n},Af={name:"Teleport",__isTeleport:!0,process(e,t,n,r,s,o,i,l,a,u){const{mc:c,pc:d,pbc:f,o:{insert:g,querySelector:v,createText:x,createComment:T}}=u,R=Yn(t.props);let{shapeFlag:w,children:S,dynamicChildren:y}=t;if(e==null){const I=t.el=x(""),B=t.anchor=x("");g(I,n,r),g(B,n,r);const j=(F,$)=>{w&16&&(s&&s.isCE&&(s.ce._teleportTarget=F),c(S,F,$,s,o,i,l,a))},X=()=>{const F=t.target=to(t.props,v),$=Ca(F,t,x,g);F&&(i!=="svg"&&_i(F)?i="svg":i!=="mathml"&&yi(F)&&(i="mathml"),R||(j(F,$),Ar(t)))};R&&(j(n,B),Ar(t)),Rf(t.props)?be(X,o):X()}else{t.el=e.el,t.targetStart=e.targetStart;const I=t.anchor=e.anchor,B=t.target=e.target,j=t.targetAnchor=e.targetAnchor,X=Yn(e.props),F=X?n:B,$=X?I:j;if(i==="svg"||_i(B)?i="svg":(i==="mathml"||yi(B))&&(i="mathml"),y?(f(e.dynamicChildren,y,F,s,o,i,l),Yo(e,t,!0)):a||d(e,t,F,$,s,o,i,l,!1),R)X?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Sr(t,n,I,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const L=t.target=to(t.props,v);L&&Sr(t,L,null,u,0)}else X&&Sr(t,B,j,u,1);Ar(t)}},remove(e,t,n,{um:r,o:{remove:s}},o){const{shapeFlag:i,children:l,anchor:a,targetStart:u,targetAnchor:c,target:d,props:f}=e;if(d&&(s(u),s(c)),o&&s(a),i&16){const g=o||!Yn(f);for(let v=0;v<l.length;v++){const x=l[v];r(x,t,n,g,!!x.dynamicChildren)}}},move:Sr,hydrate:Of};function Sr(e,t,n,{o:{insert:r},m:s},o=2){o===0&&r(e.targetAnchor,t,n);const{el:i,anchor:l,shapeFlag:a,children:u,props:c}=e,d=o===2;if(d&&r(i,t,n),(!d||Yn(c))&&a&16)for(let f=0;f<u.length;f++)s(u[f],t,n,2);d&&r(l,t,n)}function Of(e,t,n,r,s,o,{o:{nextSibling:i,parentNode:l,querySelector:a,insert:u,createText:c}},d){const f=t.target=to(t.props,a);if(f){const g=f._lpa||f.firstChild;if(t.shapeFlag&16)if(Yn(t.props))t.anchor=d(i(e),t,l(e),n,r,s,o),t.targetStart=g,t.targetAnchor=g&&i(g);else{t.anchor=i(e);let v=g;for(;v;){if(v&&v.nodeType===8){if(v.data==="teleport start anchor")t.targetStart=v;else if(v.data==="teleport anchor"){t.targetAnchor=v,f._lpa=t.targetAnchor&&i(t.targetAnchor);break}}v=i(v)}t.targetAnchor||Ca(f,t,c,u),d(g&&i(g),t,f,n,r,s,o)}Ar(t)}return t.anchor&&i(t.anchor)}const Km=Af;function Ar(e){const t=e.ctx;if(t&&t.ut){let n=e.targetStart;for(;n&&n!==e.targetAnchor;)n.nodeType===1&&n.setAttribute("data-v-owner",t.uid),n=n.nextSibling;t.ut()}}function Ca(e,t,n,r){const s=t.targetStart=n(""),o=t.targetAnchor=n("");return s[Ea]=o,e&&(r(s,e),r(o,e)),o}const Lt=Symbol("_leaveCb"),Er=Symbol("_enterCb");function Ta(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return ds(()=>{e.isMounted=!0}),hs(()=>{e.isUnmounting=!0}),e}const Ke=[Function,Array],Pa={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Ke,onEnter:Ke,onAfterEnter:Ke,onEnterCancelled:Ke,onBeforeLeave:Ke,onLeave:Ke,onAfterLeave:Ke,onLeaveCancelled:Ke,onBeforeAppear:Ke,onAppear:Ke,onAfterAppear:Ke,onAppearCancelled:Ke},Ra=e=>{const t=e.subTree;return t.component?Ra(t.component):t},kf={name:"BaseTransition",props:Pa,setup(e,{slots:t}){const n=fn(),r=Ta();return()=>{const s=t.default&&Ko(t.default(),!0);if(!s||!s.length)return;const o=Aa(s),i=Z(e),{mode:l}=i;if(r.isLeaving)return Ts(o);const a=bi(o);if(!a)return Ts(o);let u=ir(a,i,r,n,f=>u=f);a.type!==xe&&Ht(a,u);const c=n.subTree,d=c&&bi(c);if(d&&d.type!==xe&&!Nt(a,d)&&Ra(n).type!==xe){const f=ir(d,i,r,n);if(Ht(d,f),l==="out-in"&&a.type!==xe)return r.isLeaving=!0,f.afterLeave=()=>{r.isLeaving=!1,n.job.flags&8||n.update(),delete f.afterLeave},Ts(o);l==="in-out"&&a.type!==xe&&(f.delayLeave=(g,v,x)=>{const T=Oa(r,d);T[String(d.key)]=d,g[Lt]=()=>{v(),g[Lt]=void 0,delete u.delayedLeave},u.delayedLeave=x})}return o}}};function Aa(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==xe){t=n;break}}return t}const Lf=kf;function Oa(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function ir(e,t,n,r,s){const{appear:o,mode:i,persisted:l=!1,onBeforeEnter:a,onEnter:u,onAfterEnter:c,onEnterCancelled:d,onBeforeLeave:f,onLeave:g,onAfterLeave:v,onLeaveCancelled:x,onBeforeAppear:T,onAppear:R,onAfterAppear:w,onAppearCancelled:S}=t,y=String(e.key),I=Oa(n,e),B=(F,$)=>{F&&st(F,r,9,$)},j=(F,$)=>{const L=$[1];B(F,$),W(F)?F.every(O=>O.length<=1)&&L():F.length<=1&&L()},X={mode:i,persisted:l,beforeEnter(F){let $=a;if(!n.isMounted)if(o)$=T||a;else return;F[Lt]&&F[Lt](!0);const L=I[y];L&&Nt(e,L)&&L.el[Lt]&&L.el[Lt](),B($,[F])},enter(F){let $=u,L=c,O=d;if(!n.isMounted)if(o)$=R||u,L=w||c,O=S||d;else return;let Q=!1;const M=F[Er]=ee=>{Q||(Q=!0,ee?B(O,[F]):B(L,[F]),X.delayedLeave&&X.delayedLeave(),F[Er]=void 0)};$?j($,[F,M]):M()},leave(F,$){const L=String(e.key);if(F[Er]&&F[Er](!0),n.isUnmounting)return $();B(f,[F]);let O=!1;const Q=F[Lt]=M=>{O||(O=!0,$(),M?B(x,[F]):B(v,[F]),F[Lt]=void 0,I[L]===e&&delete I[L])};I[L]=e,g?j(g,[F,Q]):Q()},clone(F){const $=ir(F,t,n,r,s);return s&&s($),$}};return X}function Ts(e){if(us(e))return e=Et(e),e.children=null,e}function bi(e){if(!us(e))return xa(e.type)&&e.children?Aa(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&J(n.default))return n.default()}}function Ht(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ht(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ko(e,t=!1,n){let r=[],s=0;for(let o=0;o<e.length;o++){let i=e[o];const l=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===$e?(i.patchFlag&128&&s++,r=r.concat(Ko(i.children,t,l))):(t||i.type!==xe)&&r.push(l!=null?Et(i,{key:l}):i)}if(s>1)for(let o=0;o<r.length;o++)r[o].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function cs(e,t){return J(e)?ve({name:e.name},t,{setup:e}):e}function ka(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function no(e,t,n,r,s=!1){if(W(e)){e.forEach((v,x)=>no(v,t&&(W(t)?t[x]:t),n,r,s));return}if(rn(r)&&!s)return;const o=r.shapeFlag&4?ms(r.component):r.el,i=s?null:o,{i:l,r:a}=e,u=t&&t.r,c=l.refs===ue?l.refs={}:l.refs,d=l.setupState,f=Z(d),g=d===ue?()=>!1:v=>oe(f,v);if(u!=null&&u!==a&&(he(u)?(c[u]=null,g(u)&&(d[u]=null)):me(u)&&(u.value=null)),J(a))fr(a,l,12,[i,c]);else{const v=he(a),x=me(a);if(v||x){const T=()=>{if(e.f){const R=v?g(a)?d[a]:c[a]:a.value;s?W(R)&&Ro(R,o):W(R)?R.includes(o)||R.push(o):v?(c[a]=[o],g(a)&&(d[a]=c[a])):(a.value=[o],e.k&&(c[e.k]=a.value))}else v?(c[a]=i,g(a)&&(d[a]=i)):x&&(a.value=i,e.k&&(c[e.k]=i))};i?(T.id=-1,be(T,n)):T()}}}const rn=e=>!!e.type.__asyncLoader,us=e=>e.type.__isKeepAlive,If={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=fn(),r=n.ctx;if(!r.renderer)return()=>{const w=t.default&&t.default();return w&&w.length===1?w[0]:w};const s=new Map,o=new Set;let i=null;n.__v_cache=s;const l=n.suspense,{renderer:{p:a,m:u,um:c,o:{createElement:d}}}=r,f=d("div");r.activate=(w,S,y,I,B)=>{const j=w.component;u(w,S,y,0,l),a(j.vnode,w,S,y,j,l,I,w.slotScopeIds,B),be(()=>{j.isDeactivated=!1,j.a&&Wn(j.a);const X=w.props&&w.props.onVnodeMounted;X&&We(X,j.parent,w)},l),eo(j)},r.deactivate=w=>{const S=w.component;Kr(S.m),Kr(S.a),u(w,f,null,1,l),be(()=>{S.da&&Wn(S.da);const y=w.props&&w.props.onVnodeUnmounted;y&&We(y,S.parent,w),S.isDeactivated=!0},l),eo(S)};function g(w){Ps(w),c(w,n,l,!0)}function v(w){s.forEach((S,y)=>{const I=ao(S.type);I&&!w(I)&&x(y)})}function x(w){const S=s.get(w);S&&(!i||!Nt(S,i))?g(S):i&&Ps(i),s.delete(w),o.delete(w)}Ft(()=>[e.include,e.exclude],([w,S])=>{w&&v(y=>Bn(w,y)),S&&v(y=>!Bn(S,y))},{flush:"post",deep:!0});let T=null;const R=()=>{T!=null&&(zr(n.subTree.type)?be(()=>{s.set(T,xr(n.subTree))},n.subTree.suspense):s.set(T,xr(n.subTree)))};return ds(R),zo(R),hs(()=>{s.forEach(w=>{const{subTree:S,suspense:y}=n,I=xr(S);if(w.type===I.type&&w.key===I.key){Ps(I);const B=I.component.da;B&&be(B,y);return}g(w)})}),()=>{if(T=null,!t.default)return i=null;const w=t.default(),S=w[0];if(w.length>1)return i=null,w;if(!ar(S)||!(S.shapeFlag&4)&&!(S.shapeFlag&128))return i=null,S;let y=xr(S);if(y.type===xe)return i=null,y;const I=y.type,B=ao(rn(y)?y.type.__asyncResolved||{}:I),{include:j,exclude:X,max:F}=e;if(j&&(!B||!Bn(j,B))||X&&B&&Bn(X,B))return y.shapeFlag&=-257,i=y,S;const $=y.key==null?I:y.key,L=s.get($);return y.el&&(y=Et(y),S.shapeFlag&128&&(S.ssContent=y)),T=$,L?(y.el=L.el,y.component=L.component,y.transition&&Ht(y,y.transition),y.shapeFlag|=512,o.delete($),o.add($)):(o.add($),F&&o.size>parseInt(F,10)&&x(o.values().next().value)),y.shapeFlag|=256,i=y,zr(S.type)?S:y}}},zm=If;function Bn(e,t){return W(e)?e.some(n=>Bn(n,t)):he(e)?e.split(",").includes(t):Tu(e)?(e.lastIndex=0,e.test(t)):!1}function $f(e,t){La(e,"a",t)}function Mf(e,t){La(e,"da",t)}function La(e,t,n=Se){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(fs(t,r,n),n){let s=n.parent;for(;s&&s.parent;)us(s.parent.vnode)&&Nf(r,t,n,s),s=s.parent}}function Nf(e,t,n,r){const s=fs(t,e,r,!0);Ia(()=>{Ro(r[t],s)},n)}function Ps(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function xr(e){return e.shapeFlag&128?e.ssContent:e}function fs(e,t,n=Se,r=!1){if(n){const s=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{Kt();const l=hr(n),a=st(t,n,e,i);return l(),zt(),a});return r?s.unshift(o):s.push(o),o}}const xt=e=>(t,n=Se)=>{(!gs||e==="sp")&&fs(e,(...r)=>t(...r),n)},jf=xt("bm"),ds=xt("m"),Df=xt("bu"),zo=xt("u"),hs=xt("bum"),Ia=xt("um"),Ff=xt("sp"),qf=xt("rtg"),Hf=xt("rtc");function Bf(e,t=Se){fs("ec",e,t)}const $a="components";function Vf(e,t){return Na($a,e,!0,t)||e}const Ma=Symbol.for("v-ndc");function Wm(e){return he(e)?Na($a,e,!1)||e:e||Ma}function Na(e,t,n=!0,r=!1){const s=_e||Se;if(s){const o=s.type;{const l=ao(o,!1);if(l&&(l===t||l===rt(t)||l===ss(rt(t))))return o}const i=wi(s[e]||o[e],t)||wi(s.appContext[e],t);return!i&&r?o:i}}function wi(e,t){return e&&(e[t]||e[rt(t)]||e[ss(rt(t))])}function Gm(e,t,n,r){let s;const o=n,i=W(e);if(i||he(e)){const l=i&&Dt(e);let a=!1;l&&(a=!Ye(e),e=os(e)),s=new Array(e.length);for(let u=0,c=e.length;u<c;u++)s[u]=t(a?Ae(e[u]):e[u],u,void 0,o)}else if(typeof e=="number"){s=new Array(e);for(let l=0;l<e;l++)s[l]=t(l+1,l,void 0,o)}else if(de(e))if(e[Symbol.iterator])s=Array.from(e,(l,a)=>t(l,a,void 0,o));else{const l=Object.keys(e);s=new Array(l.length);for(let a=0,u=l.length;a<u;a++){const c=l[a];s[a]=t(e[c],c,a,o)}}else s=[];return s}function Ym(e,t,n={},r,s){if(_e.ce||_e.parent&&rn(_e.parent)&&_e.parent.ce)return t!=="default"&&(n.name=t),Wr(),Gr($e,null,[Me("slot",n,r&&r())],64);let o=e[t];o&&o._c&&(o._d=!1),Wr();const i=o&&ja(o(n)),l=Gr($e,{key:(n.key||i&&i.key||`_${t}`)+(!i&&r?"_fb":"")},i||(r?r():[]),i&&e._===1?64:-2);return!s&&l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),o&&o._c&&(o._d=!0),l}function ja(e){return e.some(t=>ar(t)?!(t.type===xe||t.type===$e&&!ja(t.children)):!0)?e:null}const ro=e=>e?tc(e)?ms(e):ro(e.parent):null,Jn=ve(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ro(e.parent),$root:e=>ro(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Wo(e),$forceUpdate:e=>e.f||(e.f=()=>{Vo(e.update)}),$nextTick:e=>e.n||(e.n=Bo.bind(e.proxy)),$watch:e=>dd.bind(e)}),Rs=(e,t)=>e!==ue&&!e.__isScriptSetup&&oe(e,t),Uf={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:s,props:o,accessCache:i,type:l,appContext:a}=e;let u;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return o[t]}else{if(Rs(r,t))return i[t]=1,r[t];if(s!==ue&&oe(s,t))return i[t]=2,s[t];if((u=e.propsOptions[0])&&oe(u,t))return i[t]=3,o[t];if(n!==ue&&oe(n,t))return i[t]=4,n[t];so&&(i[t]=0)}}const c=Jn[t];let d,f;if(c)return t==="$attrs"&&Oe(e.attrs,"get",""),c(e);if((d=l.__cssModules)&&(d=d[t]))return d;if(n!==ue&&oe(n,t))return i[t]=4,n[t];if(f=a.config.globalProperties,oe(f,t))return f[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:o}=e;return Rs(s,t)?(s[t]=n,!0):r!==ue&&oe(r,t)?(r[t]=n,!0):oe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:o}},i){let l;return!!n[i]||e!==ue&&oe(e,i)||Rs(t,i)||(l=o[0])&&oe(l,i)||oe(r,i)||oe(Jn,i)||oe(s.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:oe(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Si(e){return W(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let so=!0;function Kf(e){const t=Wo(e),n=e.proxy,r=e.ctx;so=!1,t.beforeCreate&&Ei(t.beforeCreate,e,"bc");const{data:s,computed:o,methods:i,watch:l,provide:a,inject:u,created:c,beforeMount:d,mounted:f,beforeUpdate:g,updated:v,activated:x,deactivated:T,beforeDestroy:R,beforeUnmount:w,destroyed:S,unmounted:y,render:I,renderTracked:B,renderTriggered:j,errorCaptured:X,serverPrefetch:F,expose:$,inheritAttrs:L,components:O,directives:Q,filters:M}=t;if(u&&zf(u,r,null),i)for(const re in i){const te=i[re];J(te)&&(r[re]=te.bind(n))}if(s){const re=s.call(n,n);de(re)&&(e.data=un(re))}if(so=!0,o)for(const re in o){const te=o[re],ge=J(te)?te.bind(n,n):J(te.get)?te.get.bind(n,n):ft,Ct=!J(te)&&J(te.set)?te.set.bind(n):ft,ot=G({get:ge,set:Ct});Object.defineProperty(r,re,{enumerable:!0,configurable:!0,get:()=>ot.value,set:Ne=>ot.value=Ne})}if(l)for(const re in l)Da(l[re],r,n,re);if(a){const re=J(a)?a.call(n):a;Reflect.ownKeys(re).forEach(te=>{Or(te,re[te])})}c&&Ei(c,e,"c");function ae(re,te){W(te)?te.forEach(ge=>re(ge.bind(n))):te&&re(te.bind(n))}if(ae(jf,d),ae(ds,f),ae(Df,g),ae(zo,v),ae($f,x),ae(Mf,T),ae(Bf,X),ae(Hf,B),ae(qf,j),ae(hs,w),ae(Ia,y),ae(Ff,F),W($))if($.length){const re=e.exposed||(e.exposed={});$.forEach(te=>{Object.defineProperty(re,te,{get:()=>n[te],set:ge=>n[te]=ge})})}else e.exposed||(e.exposed={});I&&e.render===ft&&(e.render=I),L!=null&&(e.inheritAttrs=L),O&&(e.components=O),Q&&(e.directives=Q),F&&ka(e)}function zf(e,t,n=ft){W(e)&&(e=oo(e));for(const r in e){const s=e[r];let o;de(s)?"default"in s?o=Je(s.from||r,s.default,!0):o=Je(s.from||r):o=Je(s),me(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[r]=o}}function Ei(e,t,n){st(W(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Da(e,t,n,r){let s=r.includes(".")?Ja(n,r):()=>n[r];if(he(e)){const o=t[e];J(o)&&Ft(s,o)}else if(J(e))Ft(s,e.bind(n));else if(de(e))if(W(e))e.forEach(o=>Da(o,t,n,r));else{const o=J(e.handler)?e.handler.bind(n):t[e.handler];J(o)&&Ft(s,o,e)}}function Wo(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let a;return l?a=l:!s.length&&!n&&!r?a=t:(a={},s.length&&s.forEach(u=>Ur(a,u,i,!0)),Ur(a,t,i)),de(t)&&o.set(t,a),a}function Ur(e,t,n,r=!1){const{mixins:s,extends:o}=t;o&&Ur(e,o,n,!0),s&&s.forEach(i=>Ur(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const l=Wf[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Wf={data:xi,props:Ci,emits:Ci,methods:Vn,computed:Vn,beforeCreate:ke,created:ke,beforeMount:ke,mounted:ke,beforeUpdate:ke,updated:ke,beforeDestroy:ke,beforeUnmount:ke,destroyed:ke,unmounted:ke,activated:ke,deactivated:ke,errorCaptured:ke,serverPrefetch:ke,components:Vn,directives:Vn,watch:Yf,provide:xi,inject:Gf};function xi(e,t){return t?e?function(){return ve(J(e)?e.call(this,this):e,J(t)?t.call(this,this):t)}:t:e}function Gf(e,t){return Vn(oo(e),oo(t))}function oo(e){if(W(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ke(e,t){return e?[...new Set([].concat(e,t))]:t}function Vn(e,t){return e?ve(Object.create(null),e,t):t}function Ci(e,t){return e?W(e)&&W(t)?[...new Set([...e,...t])]:ve(Object.create(null),Si(e),Si(t??{})):t}function Yf(e,t){if(!e)return t;if(!t)return e;const n=ve(Object.create(null),e);for(const r in t)n[r]=ke(e[r],t[r]);return n}function Fa(){return{app:null,config:{isNativeTag:xu,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Jf=0;function Qf(e,t){return function(r,s=null){J(r)||(r=ve({},r)),s!=null&&!de(s)&&(s=null);const o=Fa(),i=new WeakSet,l=[];let a=!1;const u=o.app={_uid:Jf++,_component:r,_props:s,_container:null,_context:o,_instance:null,version:Li,get config(){return o.config},set config(c){},use(c,...d){return i.has(c)||(c&&J(c.install)?(i.add(c),c.install(u,...d)):J(c)&&(i.add(c),c(u,...d))),u},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),u},component(c,d){return d?(o.components[c]=d,u):o.components[c]},directive(c,d){return d?(o.directives[c]=d,u):o.directives[c]},mount(c,d,f){if(!a){const g=u._ceVNode||Me(r,s);return g.appContext=o,f===!0?f="svg":f===!1&&(f=void 0),d&&t?t(g,c):e(g,c,f),a=!0,u._container=c,c.__vue_app__=u,u._instance=g.component,Sf(u,Li),ms(g.component)}},onUnmount(c){l.push(c)},unmount(){a&&(st(l,u._instance,16),e(null,u._container),u._instance=null,Ef(u),delete u._container.__vue_app__)},provide(c,d){return o.provides[c]=d,u},runWithContext(c){const d=sn;sn=u;try{return c()}finally{sn=d}}};return u}}let sn=null;function Or(e,t){if(Se){let n=Se.provides;const r=Se.parent&&Se.parent.provides;r===n&&(n=Se.provides=Object.create(r)),n[e]=t}}function Je(e,t,n=!1){const r=Se||_e;if(r||sn){const s=sn?sn._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&J(t)?t.call(r&&r.proxy):t}}function Xf(){return!!(Se||_e||sn)}const qa={},Ha=()=>Object.create(qa),Ba=e=>Object.getPrototypeOf(e)===qa;function Zf(e,t,n,r=!1){const s={},o=Ha();e.propsDefaults=Object.create(null),Va(e,t,s,o);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=r?s:fa(s):e.type.props?e.props=s:e.props=o,e.attrs=o}function ed(e,t,n,r){const{props:s,attrs:o,vnode:{patchFlag:i}}=e,l=Z(s),[a]=e.propsOptions;let u=!1;if((r||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let d=0;d<c.length;d++){let f=c[d];if(ps(e.emitsOptions,f))continue;const g=t[f];if(a)if(oe(o,f))g!==o[f]&&(o[f]=g,u=!0);else{const v=rt(f);s[v]=io(a,l,v,g,e,!1)}else g!==o[f]&&(o[f]=g,u=!0)}}}else{Va(e,t,s,o)&&(u=!0);let c;for(const d in l)(!t||!oe(t,d)&&((c=Ut(d))===d||!oe(t,c)))&&(a?n&&(n[d]!==void 0||n[c]!==void 0)&&(s[d]=io(a,l,d,void 0,e,!0)):delete s[d]);if(o!==l)for(const d in o)(!t||!oe(t,d))&&(delete o[d],u=!0)}u&&St(e.attrs,"set","")}function Va(e,t,n,r){const[s,o]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(zn(a))continue;const u=t[a];let c;s&&oe(s,c=rt(a))?!o||!o.includes(c)?n[c]=u:(l||(l={}))[c]=u:ps(e.emitsOptions,a)||(!(a in r)||u!==r[a])&&(r[a]=u,i=!0)}if(o){const a=Z(n),u=l||ue;for(let c=0;c<o.length;c++){const d=o[c];n[d]=io(s,a,d,u[d],e,!oe(u,d))}}return i}function io(e,t,n,r,s,o){const i=e[n];if(i!=null){const l=oe(i,"default");if(l&&r===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&J(a)){const{propsDefaults:u}=s;if(n in u)r=u[n];else{const c=hr(s);r=u[n]=a.call(null,t),c()}}else r=a;s.ce&&s.ce._setProp(n,r)}i[0]&&(o&&!l?r=!1:i[1]&&(r===""||r===Ut(n))&&(r=!0))}return r}const td=new WeakMap;function Ua(e,t,n=!1){const r=n?td:t.propsCache,s=r.get(e);if(s)return s;const o=e.props,i={},l=[];let a=!1;if(!J(e)){const c=d=>{a=!0;const[f,g]=Ua(d,t,!0);ve(i,f),g&&l.push(...g)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!a)return de(e)&&r.set(e,xn),xn;if(W(o))for(let c=0;c<o.length;c++){const d=rt(o[c]);Ti(d)&&(i[d]=ue)}else if(o)for(const c in o){const d=rt(c);if(Ti(d)){const f=o[c],g=i[d]=W(f)||J(f)?{type:f}:ve({},f),v=g.type;let x=!1,T=!0;if(W(v))for(let R=0;R<v.length;++R){const w=v[R],S=J(w)&&w.name;if(S==="Boolean"){x=!0;break}else S==="String"&&(T=!1)}else x=J(v)&&v.name==="Boolean";g[0]=x,g[1]=T,(x||oe(g,"default"))&&l.push(d)}}const u=[i,l];return de(e)&&r.set(e,u),u}function Ti(e){return e[0]!=="$"&&!zn(e)}const Ka=e=>e[0]==="_"||e==="$stable",Go=e=>W(e)?e.map(at):[at(e)],nd=(e,t,n)=>{if(t._n)return t;const r=Pf((...s)=>Go(t(...s)),n);return r._c=!1,r},za=(e,t,n)=>{const r=e._ctx;for(const s in e){if(Ka(s))continue;const o=e[s];if(J(o))t[s]=nd(s,o,r);else if(o!=null){const i=Go(o);t[s]=()=>i}}},Wa=(e,t)=>{const n=Go(t);e.slots.default=()=>n},Ga=(e,t,n)=>{for(const r in t)(n||r!=="_")&&(e[r]=t[r])},rd=(e,t,n)=>{const r=e.slots=Ha();if(e.vnode.shapeFlag&32){const s=t._;s?(Ga(r,t,n),n&&qr(r,"_",s,!0)):za(t,r)}else t&&Wa(e,t)},sd=(e,t,n)=>{const{vnode:r,slots:s}=e;let o=!0,i=ue;if(r.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:Ga(s,t,n):(o=!t.$stable,za(t,s)),i=t}else t&&(Wa(e,t),i={default:1});if(o)for(const l in s)!Ka(l)&&i[l]==null&&delete s[l]},be=yd;function od(e){return id(e)}function id(e,t){const n=Ul();n.__VUE__=!0,ya(n.__VUE_DEVTOOLS_GLOBAL_HOOK__,n);const{insert:r,remove:s,patchProp:o,createElement:i,createText:l,createComment:a,setText:u,setElementText:c,parentNode:d,nextSibling:f,setScopeId:g=ft,insertStaticContent:v}=e,x=(h,p,m,E=null,_=null,C=null,N=void 0,k=null,A=!!p.dynamicChildren)=>{if(h===p)return;h&&!Nt(h,p)&&(E=b(h),Ne(h,_,C,!0),h=null),p.patchFlag===-2&&(A=!1,p.dynamicChildren=null);const{type:P,ref:z,shapeFlag:q}=p;switch(P){case dr:T(h,p,m,E);break;case xe:R(h,p,m,E);break;case kr:h==null&&w(p,m,E,N);break;case $e:O(h,p,m,E,_,C,N,k,A);break;default:q&1?I(h,p,m,E,_,C,N,k,A):q&6?Q(h,p,m,E,_,C,N,k,A):(q&64||q&128)&&P.process(h,p,m,E,_,C,N,k,A,V)}z!=null&&_&&no(z,h&&h.ref,C,p||h,!p)},T=(h,p,m,E)=>{if(h==null)r(p.el=l(p.children),m,E);else{const _=p.el=h.el;p.children!==h.children&&u(_,p.children)}},R=(h,p,m,E)=>{h==null?r(p.el=a(p.children||""),m,E):p.el=h.el},w=(h,p,m,E)=>{[h.el,h.anchor]=v(h.children,p,m,E,h.el,h.anchor)},S=({el:h,anchor:p},m,E)=>{let _;for(;h&&h!==p;)_=f(h),r(h,m,E),h=_;r(p,m,E)},y=({el:h,anchor:p})=>{let m;for(;h&&h!==p;)m=f(h),s(h),h=m;s(p)},I=(h,p,m,E,_,C,N,k,A)=>{p.type==="svg"?N="svg":p.type==="math"&&(N="mathml"),h==null?B(p,m,E,_,C,N,k,A):F(h,p,_,C,N,k,A)},B=(h,p,m,E,_,C,N,k)=>{let A,P;const{props:z,shapeFlag:q,transition:U,dirs:Y}=h;if(A=h.el=i(h.type,C,z&&z.is,z),q&8?c(A,h.children):q&16&&X(h.children,A,null,E,_,As(h,C),N,k),Y&&Wt(h,null,E,"created"),j(A,h,h.scopeId,N,E),z){for(const fe in z)fe!=="value"&&!zn(fe)&&o(A,fe,null,z[fe],C,E);"value"in z&&o(A,"value",null,z.value,C),(P=z.onVnodeBeforeMount)&&We(P,E,h)}qr(A,"__vnode",h,!0),qr(A,"__vueParentComponent",E,!0),Y&&Wt(h,null,E,"beforeMount");const ne=ld(_,U);ne&&U.beforeEnter(A),r(A,p,m),((P=z&&z.onVnodeMounted)||ne||Y)&&be(()=>{P&&We(P,E,h),ne&&U.enter(A),Y&&Wt(h,null,E,"mounted")},_)},j=(h,p,m,E,_)=>{if(m&&g(h,m),E)for(let C=0;C<E.length;C++)g(h,E[C]);if(_){let C=_.subTree;if(p===C||zr(C.type)&&(C.ssContent===p||C.ssFallback===p)){const N=_.vnode;j(h,N,N.scopeId,N.slotScopeIds,_.parent)}}},X=(h,p,m,E,_,C,N,k,A=0)=>{for(let P=A;P<h.length;P++){const z=h[P]=k?It(h[P]):at(h[P]);x(null,z,p,m,E,_,C,N,k)}},F=(h,p,m,E,_,C,N)=>{const k=p.el=h.el;k.__vnode=p;let{patchFlag:A,dynamicChildren:P,dirs:z}=p;A|=h.patchFlag&16;const q=h.props||ue,U=p.props||ue;let Y;if(m&&Gt(m,!1),(Y=U.onVnodeBeforeUpdate)&&We(Y,m,p,h),z&&Wt(p,h,m,"beforeUpdate"),m&&Gt(m,!0),(q.innerHTML&&U.innerHTML==null||q.textContent&&U.textContent==null)&&c(k,""),P?$(h.dynamicChildren,P,k,m,E,As(p,_),C):N||te(h,p,k,null,m,E,As(p,_),C,!1),A>0){if(A&16)L(k,q,U,m,_);else if(A&2&&q.class!==U.class&&o(k,"class",null,U.class,_),A&4&&o(k,"style",q.style,U.style,_),A&8){const ne=p.dynamicProps;for(let fe=0;fe<ne.length;fe++){const le=ne[fe],Fe=q[le],Te=U[le];(Te!==Fe||le==="value")&&o(k,le,Fe,Te,_,m)}}A&1&&h.children!==p.children&&c(k,p.children)}else!N&&P==null&&L(k,q,U,m,_);((Y=U.onVnodeUpdated)||z)&&be(()=>{Y&&We(Y,m,p,h),z&&Wt(p,h,m,"updated")},E)},$=(h,p,m,E,_,C,N)=>{for(let k=0;k<p.length;k++){const A=h[k],P=p[k],z=A.el&&(A.type===$e||!Nt(A,P)||A.shapeFlag&70)?d(A.el):m;x(A,P,z,null,E,_,C,N,!0)}},L=(h,p,m,E,_)=>{if(p!==m){if(p!==ue)for(const C in p)!zn(C)&&!(C in m)&&o(h,C,p[C],null,_,E);for(const C in m){if(zn(C))continue;const N=m[C],k=p[C];N!==k&&C!=="value"&&o(h,C,k,N,_,E)}"value"in m&&o(h,"value",p.value,m.value,_)}},O=(h,p,m,E,_,C,N,k,A)=>{const P=p.el=h?h.el:l(""),z=p.anchor=h?h.anchor:l("");let{patchFlag:q,dynamicChildren:U,slotScopeIds:Y}=p;Y&&(k=k?k.concat(Y):Y),h==null?(r(P,m,E),r(z,m,E),X(p.children||[],m,z,_,C,N,k,A)):q>0&&q&64&&U&&h.dynamicChildren?($(h.dynamicChildren,U,m,_,C,N,k),(p.key!=null||_&&p===_.subTree)&&Yo(h,p,!0)):te(h,p,m,z,_,C,N,k,A)},Q=(h,p,m,E,_,C,N,k,A)=>{p.slotScopeIds=k,h==null?p.shapeFlag&512?_.ctx.activate(p,m,E,N,A):M(p,m,E,_,C,N,A):ee(h,p,A)},M=(h,p,m,E,_,C,N)=>{const k=h.component=Pd(h,E,_);if(us(h)&&(k.ctx.renderer=V),Rd(k,!1,N),k.asyncDep){if(_&&_.registerDep(k,ae,N),!h.el){const A=k.subTree=Me(xe);R(null,A,p,m)}}else ae(k,h,p,m,_,C,N)},ee=(h,p,m)=>{const E=p.component=h.component;if(vd(h,p,m))if(E.asyncDep&&!E.asyncResolved){re(E,p,m);return}else E.next=p,E.update();else p.el=h.el,E.vnode=p},ae=(h,p,m,E,_,C,N)=>{const k=()=>{if(h.isMounted){let{next:q,bu:U,u:Y,parent:ne,vnode:fe}=h;{const qe=Ya(h);if(qe){q&&(q.el=fe.el,re(h,q,N)),qe.asyncDep.then(()=>{h.isUnmounted||k()});return}}let le=q,Fe;Gt(h,!1),q?(q.el=fe.el,re(h,q,N)):q=fe,U&&Wn(U),(Fe=q.props&&q.props.onVnodeBeforeUpdate)&&We(Fe,ne,q,fe),Gt(h,!0);const Te=Os(h),Qe=h.subTree;h.subTree=Te,x(Qe,Te,d(Qe.el),b(Qe),h,_,C),q.el=Te.el,le===null&&_d(h,Te.el),Y&&be(Y,_),(Fe=q.props&&q.props.onVnodeUpdated)&&be(()=>We(Fe,ne,q,fe),_),ba(h)}else{let q;const{el:U,props:Y}=p,{bm:ne,m:fe,parent:le,root:Fe,type:Te}=h,Qe=rn(p);if(Gt(h,!1),ne&&Wn(ne),!Qe&&(q=Y&&Y.onVnodeBeforeMount)&&We(q,le,p),Gt(h,!0),U&&pe){const qe=()=>{h.subTree=Os(h),pe(U,h.subTree,h,_,null)};Qe&&Te.__asyncHydrate?Te.__asyncHydrate(U,h,qe):qe()}else{Fe.ce&&Fe.ce._injectChildStyle(Te);const qe=h.subTree=Os(h);x(null,qe,m,E,h,_,C),p.el=qe.el}if(fe&&be(fe,_),!Qe&&(q=Y&&Y.onVnodeMounted)){const qe=p;be(()=>We(q,le,qe),_)}(p.shapeFlag&256||le&&rn(le.vnode)&&le.vnode.shapeFlag&256)&&h.a&&be(h.a,_),h.isMounted=!0,eo(h),p=m=E=null}};h.scope.on();const A=h.effect=new Ql(k);h.scope.off();const P=h.update=A.run.bind(A),z=h.job=A.runIfDirty.bind(A);z.i=h,z.id=h.uid,A.scheduler=()=>Vo(z),Gt(h,!0),P()},re=(h,p,m)=>{p.component=h;const E=h.vnode.props;h.vnode=p,h.next=null,ed(h,p.props,E,m),sd(h,p.children,m),Kt(),vi(h),zt()},te=(h,p,m,E,_,C,N,k,A=!1)=>{const P=h&&h.children,z=h?h.shapeFlag:0,q=p.children,{patchFlag:U,shapeFlag:Y}=p;if(U>0){if(U&128){Ct(P,q,m,E,_,C,N,k,A);return}else if(U&256){ge(P,q,m,E,_,C,N,k,A);return}}Y&8?(z&16&&Ue(P,_,C),q!==P&&c(m,q)):z&16?Y&16?Ct(P,q,m,E,_,C,N,k,A):Ue(P,_,C,!0):(z&8&&c(m,""),Y&16&&X(q,m,E,_,C,N,k,A))},ge=(h,p,m,E,_,C,N,k,A)=>{h=h||xn,p=p||xn;const P=h.length,z=p.length,q=Math.min(P,z);let U;for(U=0;U<q;U++){const Y=p[U]=A?It(p[U]):at(p[U]);x(h[U],Y,m,null,_,C,N,k,A)}P>z?Ue(h,_,C,!0,!1,q):X(p,m,E,_,C,N,k,A,q)},Ct=(h,p,m,E,_,C,N,k,A)=>{let P=0;const z=p.length;let q=h.length-1,U=z-1;for(;P<=q&&P<=U;){const Y=h[P],ne=p[P]=A?It(p[P]):at(p[P]);if(Nt(Y,ne))x(Y,ne,m,null,_,C,N,k,A);else break;P++}for(;P<=q&&P<=U;){const Y=h[q],ne=p[U]=A?It(p[U]):at(p[U]);if(Nt(Y,ne))x(Y,ne,m,null,_,C,N,k,A);else break;q--,U--}if(P>q){if(P<=U){const Y=U+1,ne=Y<z?p[Y].el:E;for(;P<=U;)x(null,p[P]=A?It(p[P]):at(p[P]),m,ne,_,C,N,k,A),P++}}else if(P>U)for(;P<=q;)Ne(h[P],_,C,!0),P++;else{const Y=P,ne=P,fe=new Map;for(P=ne;P<=U;P++){const He=p[P]=A?It(p[P]):at(p[P]);He.key!=null&&fe.set(He.key,P)}let le,Fe=0;const Te=U-ne+1;let Qe=!1,qe=0;const Ln=new Array(Te);for(P=0;P<Te;P++)Ln[P]=0;for(P=Y;P<=q;P++){const He=h[P];if(Fe>=Te){Ne(He,_,C,!0);continue}let it;if(He.key!=null)it=fe.get(He.key);else for(le=ne;le<=U;le++)if(Ln[le-ne]===0&&Nt(He,p[le])){it=le;break}it===void 0?Ne(He,_,C,!0):(Ln[it-ne]=P+1,it>=qe?qe=it:Qe=!0,x(He,p[it],m,null,_,C,N,k,A),Fe++)}const li=Qe?ad(Ln):xn;for(le=li.length-1,P=Te-1;P>=0;P--){const He=ne+P,it=p[He],ai=He+1<z?p[He+1].el:E;Ln[P]===0?x(null,it,m,ai,_,C,N,k,A):Qe&&(le<0||P!==li[le]?ot(it,m,ai,2):le--)}}},ot=(h,p,m,E,_=null)=>{const{el:C,type:N,transition:k,children:A,shapeFlag:P}=h;if(P&6){ot(h.component.subTree,p,m,E);return}if(P&128){h.suspense.move(p,m,E);return}if(P&64){N.move(h,p,m,V);return}if(N===$e){r(C,p,m);for(let q=0;q<A.length;q++)ot(A[q],p,m,E);r(h.anchor,p,m);return}if(N===kr){S(h,p,m);return}if(E!==2&&P&1&&k)if(E===0)k.beforeEnter(C),r(C,p,m),be(()=>k.enter(C),_);else{const{leave:q,delayLeave:U,afterLeave:Y}=k,ne=()=>r(C,p,m),fe=()=>{q(C,()=>{ne(),Y&&Y()})};U?U(C,ne,fe):fe()}else r(C,p,m)},Ne=(h,p,m,E=!1,_=!1)=>{const{type:C,props:N,ref:k,children:A,dynamicChildren:P,shapeFlag:z,patchFlag:q,dirs:U,cacheIndex:Y}=h;if(q===-2&&(_=!1),k!=null&&no(k,null,m,h,!0),Y!=null&&(p.renderCache[Y]=void 0),z&256){p.ctx.deactivate(h);return}const ne=z&1&&U,fe=!rn(h);let le;if(fe&&(le=N&&N.onVnodeBeforeUnmount)&&We(le,p,h),z&6)gr(h.component,m,E);else{if(z&128){h.suspense.unmount(m,E);return}ne&&Wt(h,null,p,"beforeUnmount"),z&64?h.type.remove(h,p,m,V,E):P&&!P.hasOnce&&(C!==$e||q>0&&q&64)?Ue(P,p,m,!1,!0):(C===$e&&q&384||!_&&z&16)&&Ue(A,p,m),E&&dn(h)}(fe&&(le=N&&N.onVnodeUnmounted)||ne)&&be(()=>{le&&We(le,p,h),ne&&Wt(h,null,p,"unmounted")},m)},dn=h=>{const{type:p,el:m,anchor:E,transition:_}=h;if(p===$e){hn(m,E);return}if(p===kr){y(h);return}const C=()=>{s(m),_&&!_.persisted&&_.afterLeave&&_.afterLeave()};if(h.shapeFlag&1&&_&&!_.persisted){const{leave:N,delayLeave:k}=_,A=()=>N(m,C);k?k(h.el,C,A):A()}else C()},hn=(h,p)=>{let m;for(;h!==p;)m=f(h),s(h),h=m;s(p)},gr=(h,p,m)=>{const{bum:E,scope:_,job:C,subTree:N,um:k,m:A,a:P}=h;Kr(A),Kr(P),E&&Wn(E),_.stop(),C&&(C.flags|=8,Ne(N,h,p,m)),k&&be(k,p),be(()=>{h.isUnmounted=!0},p),p&&p.pendingBranch&&!p.isUnmounted&&h.asyncDep&&!h.asyncResolved&&h.suspenseId===p.pendingId&&(p.deps--,p.deps===0&&p.resolve()),Cf(h)},Ue=(h,p,m,E=!1,_=!1,C=0)=>{for(let N=C;N<h.length;N++)Ne(h[N],p,m,E,_)},b=h=>{if(h.shapeFlag&6)return b(h.component.subTree);if(h.shapeFlag&128)return h.suspense.next();const p=f(h.anchor||h.el),m=p&&p[Ea];return m?f(m):p};let H=!1;const D=(h,p,m)=>{h==null?p._vnode&&Ne(p._vnode,null,null,!0):x(p._vnode||null,h,p,null,null,null,m),p._vnode=h,H||(H=!0,vi(),va(),H=!1)},V={p:x,um:Ne,m:ot,r:dn,mt:M,mc:X,pc:te,pbc:$,n:b,o:e};let ie,pe;return{render:D,hydrate:ie,createApp:Qf(D,ie)}}function As({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Gt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function ld(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Yo(e,t,n=!1){const r=e.children,s=t.children;if(W(r)&&W(s))for(let o=0;o<r.length;o++){const i=r[o];let l=s[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=s[o]=It(s[o]),l.el=i.el),!n&&l.patchFlag!==-2&&Yo(i,l)),l.type===dr&&(l.el=i.el)}}function ad(e){const t=e.slice(),n=[0];let r,s,o,i,l;const a=e.length;for(r=0;r<a;r++){const u=e[r];if(u!==0){if(s=n[n.length-1],e[s]<u){t[r]=s,n.push(r);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<u?o=l+1:i=l;u<e[n[o]]&&(o>0&&(t[r]=n[o-1]),n[o]=r)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Ya(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ya(t)}function Kr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const cd=Symbol.for("v-scx"),ud=()=>Je(cd);function fd(e,t){return Jo(e,null,t)}function Ft(e,t,n){return Jo(e,t,n)}function Jo(e,t,n=ue){const{immediate:r,deep:s,flush:o,once:i}=n,l=ve({},n);let a;if(gs)if(o==="sync"){const f=ud();a=f.__watcherHandles||(f.__watcherHandles=[])}else if(!t||r)l.once=!0;else{const f=()=>{};return f.stop=ft,f.resume=ft,f.pause=ft,f}const u=Se;l.call=(f,g,v)=>st(f,u,g,v);let c=!1;o==="post"?l.scheduler=f=>{be(f,u&&u.suspense)}:o!=="sync"&&(c=!0,l.scheduler=(f,g)=>{g?f():Vo(f)}),l.augmentJob=f=>{t&&(f.flags|=4),c&&(f.flags|=2,u&&(f.id=u.uid,f.i=u))};const d=_f(e,t,l);return a&&a.push(d),d}function dd(e,t,n){const r=this.proxy,s=he(e)?e.includes(".")?Ja(r,e):()=>r[e]:e.bind(r,r);let o;J(t)?o=t:(o=t.handler,n=t);const i=hr(this),l=Jo(s,o.bind(r),n);return i(),l}function Ja(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}const hd=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${rt(t)}Modifiers`]||e[`${Ut(t)}Modifiers`];function pd(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||ue;let s=n;const o=t.startsWith("update:"),i=o&&hd(r,t.slice(7));i&&(i.trim&&(s=n.map(c=>he(c)?c.trim():c)),i.number&&(s=n.map(Ou))),Tf(e,t,s);let l,a=r[l=ws(t)]||r[l=ws(rt(t))];!a&&o&&(a=r[l=ws(Ut(t))]),a&&st(a,e,6,s);const u=r[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,st(u,e,6,s)}}function Qa(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const o=e.emits;let i={},l=!1;if(!J(e)){const a=u=>{const c=Qa(u,t,!0);c&&(l=!0,ve(i,c))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!o&&!l?(de(e)&&r.set(e,null),null):(W(o)?o.forEach(a=>i[a]=null):ve(i,o),de(e)&&r.set(e,i),i)}function ps(e,t){return!e||!ns(t)?!1:(t=t.slice(2).replace(/Once$/,""),oe(e,t[0].toLowerCase()+t.slice(1))||oe(e,Ut(t))||oe(e,t))}function Os(e){const{type:t,vnode:n,proxy:r,withProxy:s,propsOptions:[o],slots:i,attrs:l,emit:a,render:u,renderCache:c,props:d,data:f,setupState:g,ctx:v,inheritAttrs:x}=e,T=Vr(e);let R,w;try{if(n.shapeFlag&4){const y=s||r,I=y;R=at(u.call(I,y,c,d,g,f,v)),w=l}else{const y=t;R=at(y.length>1?y(d,{attrs:l,slots:i,emit:a}):y(d,null)),w=t.props?l:gd(l)}}catch(y){Qn.length=0,ls(y,e,1),R=Me(xe)}let S=R;if(w&&x!==!1){const y=Object.keys(w),{shapeFlag:I}=S;y.length&&I&7&&(o&&y.some(Po)&&(w=md(w,o)),S=Et(S,w,!1,!0))}return n.dirs&&(S=Et(S,null,!1,!0),S.dirs=S.dirs?S.dirs.concat(n.dirs):n.dirs),n.transition&&Ht(S,n.transition),R=S,Vr(T),R}const gd=e=>{let t;for(const n in e)(n==="class"||n==="style"||ns(n))&&((t||(t={}))[n]=e[n]);return t},md=(e,t)=>{const n={};for(const r in e)(!Po(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function vd(e,t,n){const{props:r,children:s,component:o}=e,{props:i,children:l,patchFlag:a}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return r?Pi(r,i,u):!!i;if(a&8){const c=t.dynamicProps;for(let d=0;d<c.length;d++){const f=c[d];if(i[f]!==r[f]&&!ps(u,f))return!0}}}else return(s||l)&&(!l||!l.$stable)?!0:r===i?!1:r?i?Pi(r,i,u):!0:!!i;return!1}function Pi(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const o=r[s];if(t[o]!==e[o]&&!ps(n,o))return!0}return!1}function _d({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const zr=e=>e.__isSuspense;function yd(e,t){t&&t.pendingBranch?W(e)?t.effects.push(...e):t.effects.push(e):wf(e)}const $e=Symbol.for("v-fgt"),dr=Symbol.for("v-txt"),xe=Symbol.for("v-cmt"),kr=Symbol.for("v-stc"),Qn=[];let Be=null;function Wr(e=!1){Qn.push(Be=e?null:[])}function bd(){Qn.pop(),Be=Qn[Qn.length-1]||null}let lr=1;function Ri(e){lr+=e,e<0&&Be&&(Be.hasOnce=!0)}function Xa(e){return e.dynamicChildren=lr>0?Be||xn:null,bd(),lr>0&&Be&&Be.push(e),e}function Jm(e,t,n,r,s,o){return Xa(ec(e,t,n,r,s,o,!0))}function Gr(e,t,n,r,s){return Xa(Me(e,t,n,r,s,!0))}function ar(e){return e?e.__v_isVNode===!0:!1}function Nt(e,t){return e.type===t.type&&e.key===t.key}const Za=({key:e})=>e??null,Lr=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?he(e)||me(e)||J(e)?{i:_e,r:e,k:t,f:!!n}:e:null);function ec(e,t=null,n=null,r=0,s=null,o=e===$e?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Za(t),ref:t&&Lr(t),scopeId:wa,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:_e};return l?(Qo(a,n),o&128&&e.normalize(a)):n&&(a.shapeFlag|=he(n)?8:16),lr>0&&!i&&Be&&(a.patchFlag>0||o&6)&&a.patchFlag!==32&&Be.push(a),a}const Me=wd;function wd(e,t=null,n=null,r=0,s=null,o=!1){if((!e||e===Ma)&&(e=xe),ar(e)){const l=Et(e,t,!0);return n&&Qo(l,n),lr>0&&!o&&Be&&(l.shapeFlag&6?Be[Be.indexOf(e)]=l:Be.push(l)),l.patchFlag=-2,l}if(Ld(e)&&(e=e.__vccOpts),t){t=Sd(t);let{class:l,style:a}=t;l&&!he(l)&&(t.class=ko(l)),de(a)&&(Fo(a)&&!W(a)&&(a=ve({},a)),t.style=Oo(a))}const i=he(e)?1:zr(e)?128:xa(e)?64:de(e)?4:J(e)?2:0;return ec(e,t,n,r,s,i,o,!0)}function Sd(e){return e?Fo(e)||Ba(e)?ve({},e):e:null}function Et(e,t,n=!1,r=!1){const{props:s,ref:o,patchFlag:i,children:l,transition:a}=e,u=t?xd(s||{},t):s,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Za(u),ref:t&&t.ref?n&&o?W(o)?o.concat(Lr(t)):[o,Lr(t)]:Lr(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==$e?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Et(e.ssContent),ssFallback:e.ssFallback&&Et(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&r&&Ht(c,a.clone(c)),c}function Ed(e=" ",t=0){return Me(dr,null,e,t)}function Qm(e="",t=!1){return t?(Wr(),Gr(xe,null,e)):Me(xe,null,e)}function at(e){return e==null||typeof e=="boolean"?Me(xe):W(e)?Me($e,null,e.slice()):typeof e=="object"?It(e):Me(dr,null,String(e))}function It(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Et(e)}function Qo(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(W(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),Qo(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!Ba(t)?t._ctx=_e:s===3&&_e&&(_e.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else J(t)?(t={default:t,_ctx:_e},n=32):(t=String(t),r&64?(n=16,t=[Ed(t)]):n=8);e.children=t,e.shapeFlag|=n}function xd(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=ko([t.class,r.class]));else if(s==="style")t.style=Oo([t.style,r.style]);else if(ns(s)){const o=t[s],i=r[s];i&&o!==i&&!(W(o)&&o.includes(i))&&(t[s]=o?[].concat(o,i):i)}else s!==""&&(t[s]=r[s])}return t}function We(e,t,n,r=null){st(e,t,7,[n,r])}const Cd=Fa();let Td=0;function Pd(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||Cd,o={uid:Td++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Gl(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Ua(r,s),emitsOptions:Qa(r,s),emit:null,emitted:null,propsDefaults:ue,inheritAttrs:r.inheritAttrs,ctx:ue,data:ue,props:ue,attrs:ue,slots:ue,refs:ue,setupState:ue,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=pd.bind(null,o),e.ce&&e.ce(o),o}let Se=null;const fn=()=>Se||_e;let Yr,lo;{const e=Ul(),t=(n,r)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(r),o=>{s.length>1?s.forEach(i=>i(o)):s[0](o)}};Yr=t("__VUE_INSTANCE_SETTERS__",n=>Se=n),lo=t("__VUE_SSR_SETTERS__",n=>gs=n)}const hr=e=>{const t=Se;return Yr(e),e.scope.on(),()=>{e.scope.off(),Yr(t)}},Ai=()=>{Se&&Se.scope.off(),Yr(null)};function tc(e){return e.vnode.shapeFlag&4}let gs=!1;function Rd(e,t=!1,n=!1){t&&lo(t);const{props:r,children:s}=e.vnode,o=tc(e);Zf(e,r,o,t),rd(e,s,n);const i=o?Ad(e,t):void 0;return t&&lo(!1),i}function Ad(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Uf);const{setup:r}=n;if(r){const s=e.setupContext=r.length>1?kd(e):null,o=hr(e);Kt();const i=fr(r,e,0,[e.props,s]);if(zt(),o(),Hl(i)){if(rn(e)||ka(e),i.then(Ai,Ai),t)return i.then(l=>{Oi(e,l,t)}).catch(l=>{ls(l,e,0)});e.asyncDep=i}else Oi(e,i,t)}else nc(e,t)}function Oi(e,t,n){J(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:de(t)&&(e.devtoolsRawSetupState=t,e.setupState=pa(t)),nc(e,n)}let ki;function nc(e,t,n){const r=e.type;if(!e.render){if(!t&&ki&&!r.render){const s=r.template||Wo(e).template;if(s){const{isCustomElement:o,compilerOptions:i}=e.appContext.config,{delimiters:l,compilerOptions:a}=r,u=ve(ve({isCustomElement:o,delimiters:l},i),a);r.render=ki(s,u)}}e.render=r.render||ft}{const s=hr(e);Kt();try{Kf(e)}finally{zt(),s()}}}const Od={get(e,t){return Oe(e,"get",""),e[t]}};function kd(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Od),slots:e.slots,emit:e.emit,expose:t}}function ms(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(pa(ht(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Jn)return Jn[n](e)},has(t,n){return n in t||n in Jn}})):e.proxy}function ao(e,t=!0){return J(e)?e.displayName||e.name:e.name||t&&e.__name}function Ld(e){return J(e)&&"__vccOpts"in e}const G=(e,t)=>mf(e,t,gs);function K(e,t,n){const r=arguments.length;return r===2?de(t)&&!W(t)?ar(t)?Me(e,null,[t]):Me(e,t):Me(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&ar(n)&&(n=[n]),Me(e,t,n))}const Li="3.5.6";/**
* @vue/runtime-dom v3.5.6
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let co;const Ii=typeof window<"u"&&window.trustedTypes;if(Ii)try{co=Ii.createPolicy("vue",{createHTML:e=>e})}catch{}const rc=co?e=>co.createHTML(e):e=>e,Id="http://www.w3.org/2000/svg",$d="http://www.w3.org/1998/Math/MathML",_t=typeof document<"u"?document:null,$i=_t&&_t.createElement("template"),Md={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t==="svg"?_t.createElementNS(Id,e):t==="mathml"?_t.createElementNS($d,e):n?_t.createElement(e,{is:n}):_t.createElement(e);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>_t.createTextNode(e),createComment:e=>_t.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>_t.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,o){const i=n?n.previousSibling:t.lastChild;if(s&&(s===o||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===o||!(s=s.nextSibling)););else{$i.innerHTML=rc(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const l=$i.content;if(r==="svg"||r==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Pt="transition",$n="animation",Rn=Symbol("_vtc"),sc={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},oc=ve({},Pa,sc),Nd=e=>(e.displayName="Transition",e.props=oc,e),ic=Nd((e,{slots:t})=>K(Lf,lc(e),t)),Yt=(e,t=[])=>{W(e)?e.forEach(n=>n(...t)):e&&e(...t)},Mi=e=>e?W(e)?e.some(t=>t.length>1):e.length>1:!1;function lc(e){const t={};for(const O in e)O in sc||(t[O]=e[O]);if(e.css===!1)return t;const{name:n="v",type:r,duration:s,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:a=o,appearActiveClass:u=i,appearToClass:c=l,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:g=`${n}-leave-to`}=e,v=jd(s),x=v&&v[0],T=v&&v[1],{onBeforeEnter:R,onEnter:w,onEnterCancelled:S,onLeave:y,onLeaveCancelled:I,onBeforeAppear:B=R,onAppear:j=w,onAppearCancelled:X=S}=t,F=(O,Q,M)=>{At(O,Q?c:l),At(O,Q?u:i),M&&M()},$=(O,Q)=>{O._isLeaving=!1,At(O,d),At(O,g),At(O,f),Q&&Q()},L=O=>(Q,M)=>{const ee=O?j:w,ae=()=>F(Q,O,M);Yt(ee,[Q,ae]),Ni(()=>{At(Q,O?a:o),vt(Q,O?c:l),Mi(ee)||ji(Q,r,x,ae)})};return ve(t,{onBeforeEnter(O){Yt(R,[O]),vt(O,o),vt(O,i)},onBeforeAppear(O){Yt(B,[O]),vt(O,a),vt(O,u)},onEnter:L(!1),onAppear:L(!0),onLeave(O,Q){O._isLeaving=!0;const M=()=>$(O,Q);vt(O,d),vt(O,f),cc(),Ni(()=>{O._isLeaving&&(At(O,d),vt(O,g),Mi(y)||ji(O,r,T,M))}),Yt(y,[O,M])},onEnterCancelled(O){F(O,!1),Yt(S,[O])},onAppearCancelled(O){F(O,!0),Yt(X,[O])},onLeaveCancelled(O){$(O),Yt(I,[O])}})}function jd(e){if(e==null)return null;if(de(e))return[ks(e.enter),ks(e.leave)];{const t=ks(e);return[t,t]}}function ks(e){return ku(e)}function vt(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Rn]||(e[Rn]=new Set)).add(t)}function At(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const n=e[Rn];n&&(n.delete(t),n.size||(e[Rn]=void 0))}function Ni(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Dd=0;function ji(e,t,n,r){const s=e._endId=++Dd,o=()=>{s===e._endId&&r()};if(n)return setTimeout(o,n);const{type:i,timeout:l,propCount:a}=ac(e,t);if(!i)return r();const u=i+"end";let c=0;const d=()=>{e.removeEventListener(u,f),o()},f=g=>{g.target===e&&++c>=a&&d()};setTimeout(()=>{c<a&&d()},l+1),e.addEventListener(u,f)}function ac(e,t){const n=window.getComputedStyle(e),r=v=>(n[v]||"").split(", "),s=r(`${Pt}Delay`),o=r(`${Pt}Duration`),i=Di(s,o),l=r(`${$n}Delay`),a=r(`${$n}Duration`),u=Di(l,a);let c=null,d=0,f=0;t===Pt?i>0&&(c=Pt,d=i,f=o.length):t===$n?u>0&&(c=$n,d=u,f=a.length):(d=Math.max(i,u),c=d>0?i>u?Pt:$n:null,f=c?c===Pt?o.length:a.length:0);const g=c===Pt&&/\b(transform|all)(,|$)/.test(r(`${Pt}Property`).toString());return{type:c,timeout:d,propCount:f,hasTransform:g}}function Di(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>Fi(n)+Fi(e[r])))}function Fi(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function cc(){return document.body.offsetHeight}function Fd(e,t,n){const r=e[Rn];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Jr=Symbol("_vod"),uc=Symbol("_vsh"),Xm={beforeMount(e,{value:t},{transition:n}){e[Jr]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):Mn(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),Mn(e,!0),r.enter(e)):r.leave(e,()=>{Mn(e,!1)}):Mn(e,t))},beforeUnmount(e,{value:t}){Mn(e,t)}};function Mn(e,t){e.style.display=t?e[Jr]:"none",e[uc]=!t}const qd=Symbol(""),Hd=/(^|;)\s*display\s*:/;function Bd(e,t,n){const r=e.style,s=he(n);let o=!1;if(n&&!s){if(t)if(he(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&Ir(r,l,"")}else for(const i in t)n[i]==null&&Ir(r,i,"");for(const i in n)i==="display"&&(o=!0),Ir(r,i,n[i])}else if(s){if(t!==n){const i=r[qd];i&&(n+=";"+i),r.cssText=n,o=Hd.test(n)}}else t&&e.removeAttribute("style");Jr in e&&(e[Jr]=o?r.display:"",e[uc]&&(r.display="none"))}const qi=/\s*!important$/;function Ir(e,t,n){if(W(n))n.forEach(r=>Ir(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=Vd(e,t);qi.test(n)?e.setProperty(Ut(r),n.replace(qi,""),"important"):e[r]=n}}const Hi=["Webkit","Moz","ms"],Ls={};function Vd(e,t){const n=Ls[t];if(n)return n;let r=rt(t);if(r!=="filter"&&r in e)return Ls[t]=r;r=ss(r);for(let s=0;s<Hi.length;s++){const o=Hi[s]+r;if(o in e)return Ls[t]=o}return t}const Bi="http://www.w3.org/1999/xlink";function Vi(e,t,n,r,s,o=ju(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Bi,t.slice(6,t.length)):e.setAttributeNS(Bi,t,n):n==null||o&&!Kl(n)?e.removeAttribute(t):e.setAttribute(t,o?"":Vt(n)?String(n):n)}function Ud(e,t,n,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?rc(n):n);return}const s=e.tagName;if(t==="value"&&s!=="PROGRESS"&&!s.includes("-")){const i=s==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(i!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const i=typeof e[t];i==="boolean"?n=Kl(n):n==null&&i==="string"?(n="",o=!0):i==="number"&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(t)}function Kd(e,t,n,r){e.addEventListener(t,n,r)}function zd(e,t,n,r){e.removeEventListener(t,n,r)}const Ui=Symbol("_vei");function Wd(e,t,n,r,s=null){const o=e[Ui]||(e[Ui]={}),i=o[t];if(r&&i)i.value=r;else{const[l,a]=Gd(t);if(r){const u=o[t]=Qd(r,s);Kd(e,l,u,a)}else i&&(zd(e,l,i,a),o[t]=void 0)}}const Ki=/(?:Once|Passive|Capture)$/;function Gd(e){let t;if(Ki.test(e)){t={};let r;for(;r=e.match(Ki);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Ut(e.slice(2)),t]}let Is=0;const Yd=Promise.resolve(),Jd=()=>Is||(Yd.then(()=>Is=0),Is=Date.now());function Qd(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;st(Xd(r,n.value),t,5,[r])};return n.value=e,n.attached=Jd(),n}function Xd(e,t){if(W(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const zi=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Zd=(e,t,n,r,s,o)=>{const i=s==="svg";t==="class"?Fd(e,r,i):t==="style"?Bd(e,n,r):ns(t)?Po(t)||Wd(e,t,n,r,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):eh(e,t,r,i))?(Ud(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Vi(e,t,r,i,o,t!=="value")):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Vi(e,t,r,i))};function eh(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&zi(t)&&J(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return zi(t)&&he(n)?!1:!!(t in e||e._isVueCE&&(/[A-Z]/.test(t)||!he(n)))}const fc=new WeakMap,dc=new WeakMap,Qr=Symbol("_moveCb"),Wi=Symbol("_enterCb"),th=e=>(delete e.props.mode,e),nh=th({name:"TransitionGroup",props:ve({},oc,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=fn(),r=Ta();let s,o;return zo(()=>{if(!s.length)return;const i=e.moveClass||`${e.name||"v"}-move`;if(!lh(s[0].el,n.vnode.el,i))return;s.forEach(sh),s.forEach(oh);const l=s.filter(ih);cc(),l.forEach(a=>{const u=a.el,c=u.style;vt(u,i),c.transform=c.webkitTransform=c.transitionDuration="";const d=u[Qr]=f=>{f&&f.target!==u||(!f||/transform$/.test(f.propertyName))&&(u.removeEventListener("transitionend",d),u[Qr]=null,At(u,i))};u.addEventListener("transitionend",d)})}),()=>{const i=Z(e),l=lc(i);let a=i.tag||$e;if(s=[],o)for(let u=0;u<o.length;u++){const c=o[u];c.el&&c.el instanceof Element&&(s.push(c),Ht(c,ir(c,l,r,n)),fc.set(c,c.el.getBoundingClientRect()))}o=t.default?Ko(t.default()):[];for(let u=0;u<o.length;u++){const c=o[u];c.key!=null&&Ht(c,ir(c,l,r,n))}return Me(a,null,o)}}}),rh=nh;function sh(e){const t=e.el;t[Qr]&&t[Qr](),t[Wi]&&t[Wi]()}function oh(e){dc.set(e,e.el.getBoundingClientRect())}function ih(e){const t=fc.get(e),n=dc.get(e),r=t.left-n.left,s=t.top-n.top;if(r||s){const o=e.el.style;return o.transform=o.webkitTransform=`translate(${r}px,${s}px)`,o.transitionDuration="0s",e}}function lh(e,t,n){const r=e.cloneNode(),s=e[Rn];s&&s.forEach(l=>{l.split(/\s+/).forEach(a=>a&&r.classList.remove(a))}),n.split(/\s+/).forEach(l=>l&&r.classList.add(l)),r.style.display="none";const o=t.nodeType===1?t:t.parentNode;o.appendChild(r);const{hasTransform:i}=ac(r);return o.removeChild(r),i}const ah=["ctrl","shift","alt","meta"],ch={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>ah.some(n=>e[`${n}Key`]&&!t.includes(n))},Zm=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(s,...o)=>{for(let i=0;i<t.length;i++){const l=ch[t[i]];if(l&&l(s,t))return}return e(s,...o)})},uh={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},ev=(e,t)=>{const n=e._withKeys||(e._withKeys={}),r=t.join(".");return n[r]||(n[r]=s=>{if(!("key"in s))return;const o=Ut(s.key);if(t.some(i=>i===o||uh[i]===o))return e(s)})},fh=ve({patchProp:Zd},Md);let Gi;function dh(){return Gi||(Gi=od(fh))}const hc=(...e)=>{const t=dh().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=ph(r);if(!s)return;const o=t._component;!J(o)&&!o.render&&!o.template&&(o.template=s.innerHTML),s.nodeType===1&&(s.textContent="");const i=n(s,!1,hh(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),i},t};function hh(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function ph(e){return he(e)?document.querySelector(e):e}function vs(e,t,n,r){return Object.defineProperty(e,t,{get:n,set:r,enumerable:!0}),e}function tv(e,t){for(const n in t)vs(e,n,t[n]);return e}const an=ln(!1);let uo;function gh(e,t){const n=/(edg|edge|edga|edgios)\/([\w.]+)/.exec(e)||/(opr)[\/]([\w.]+)/.exec(e)||/(vivaldi)[\/]([\w.]+)/.exec(e)||/(chrome|crios)[\/]([\w.]+)/.exec(e)||/(version)(applewebkit)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+).*(version)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(firefox|fxios)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[\/]([\w.]+)/.exec(e)||[];return{browser:n[5]||n[3]||n[1]||"",version:n[4]||n[2]||"0",platform:t[0]||""}}function mh(e){return/(ipad)/.exec(e)||/(ipod)/.exec(e)||/(windows phone)/.exec(e)||/(iphone)/.exec(e)||/(kindle)/.exec(e)||/(silk)/.exec(e)||/(android)/.exec(e)||/(win)/.exec(e)||/(mac)/.exec(e)||/(linux)/.exec(e)||/(cros)/.exec(e)||/(playbook)/.exec(e)||/(bb)/.exec(e)||/(blackberry)/.exec(e)||[]}const pc="ontouchstart"in window||window.navigator.maxTouchPoints>0;function vh(e){const t=e.toLowerCase(),n=mh(t),r=gh(t,n),s={mobile:!1,desktop:!1,cordova:!1,capacitor:!1,nativeMobile:!1,electron:!1,bex:!1,linux:!1,mac:!1,win:!1,cros:!1,chrome:!1,firefox:!1,opera:!1,safari:!1,vivaldi:!1,edge:!1,edgeChromium:!1,ie:!1,webkit:!1,android:!1,ios:!1,ipad:!1,iphone:!1,ipod:!1,kindle:!1,winphone:!1,blackberry:!1,playbook:!1,silk:!1};r.browser&&(s[r.browser]=!0,s.version=r.version,s.versionNumber=parseInt(r.version,10)),r.platform&&(s[r.platform]=!0);const o=s.android||s.ios||s.bb||s.blackberry||s.ipad||s.iphone||s.ipod||s.kindle||s.playbook||s.silk||s["windows phone"];if(o===!0||t.indexOf("mobile")!==-1?s.mobile=!0:s.desktop=!0,s["windows phone"]&&(s.winphone=!0,delete s["windows phone"]),s.edga||s.edgios||s.edg?(s.edge=!0,r.browser="edge"):s.crios?(s.chrome=!0,r.browser="chrome"):s.fxios&&(s.firefox=!0,r.browser="firefox"),(s.ipod||s.ipad||s.iphone)&&(s.ios=!0),s.vivaldi&&(r.browser="vivaldi",s.vivaldi=!0),(s.chrome||s.opr||s.safari||s.vivaldi||s.mobile===!0&&s.ios!==!0&&o!==!0)&&(s.webkit=!0),s.opr&&(r.browser="opera",s.opera=!0),s.safari&&(s.blackberry||s.bb?(r.browser="blackberry",s.blackberry=!0):s.playbook?(r.browser="playbook",s.playbook=!0):s.android?(r.browser="android",s.android=!0):s.kindle?(r.browser="kindle",s.kindle=!0):s.silk&&(r.browser="silk",s.silk=!0)),s.name=r.browser,s.platform=r.platform,t.indexOf("electron")!==-1)s.electron=!0;else if(document.location.href.indexOf("-extension://")!==-1)s.bex=!0;else{if(window.Capacitor!==void 0?(s.capacitor=!0,s.nativeMobile=!0,s.nativeMobileWrapper="capacitor"):(window._cordovaNative!==void 0||window.cordova!==void 0)&&(s.cordova=!0,s.nativeMobile=!0,s.nativeMobileWrapper="cordova"),an.value===!0&&(uo={is:{...s}}),pc===!0&&s.mac===!0&&(s.desktop===!0&&s.safari===!0||s.nativeMobile===!0&&s.android!==!0&&s.ios!==!0&&s.ipad!==!0)){delete s.mac,delete s.desktop;const i=Math.min(window.innerHeight,window.innerWidth)>414?"ipad":"iphone";Object.assign(s,{mobile:!0,ios:!0,platform:i,[i]:!0})}s.mobile!==!0&&window.navigator.userAgentData&&window.navigator.userAgentData.mobile&&(delete s.desktop,s.mobile=!0)}return s}const Yi=navigator.userAgent||navigator.vendor||window.opera,_h={has:{touch:!1,webStorage:!1},within:{iframe:!1}},Ce={userAgent:Yi,is:vh(Yi),has:{touch:pc},within:{iframe:window.self!==window.top}},fo={install(e){const{$q:t}=e;an.value===!0?(e.onSSRHydrated.push(()=>{Object.assign(t.platform,Ce),an.value=!1}),t.platform=un(this)):t.platform=this}};{let e;vs(Ce.has,"webStorage",()=>{if(e!==void 0)return e;try{if(window.localStorage)return e=!0,!0}catch{}return e=!1,!1}),Object.assign(fo,Ce),an.value===!0&&(Object.assign(fo,uo,_h),uo=null)}function pr(e){return ht(cs(e))}function yh(e){return ht(e)}const kn=(e,t)=>{const n=un(e);for(const r in e)vs(t,r,()=>n[r],s=>{n[r]=s});return t},De={hasPassive:!1,passiveCapture:!0,notPassiveCapture:!0};try{const e=Object.defineProperty({},"passive",{get(){Object.assign(De,{hasPassive:!0,passive:{passive:!0},notPassive:{passive:!1},passiveCapture:{passive:!0,capture:!0},notPassiveCapture:{passive:!1,capture:!0}})}});window.addEventListener("qtest",null,e),window.removeEventListener("qtest",null,e)}catch{}function ut(){}function nv(e){return e.button===0}function bh(e){return e.touches&&e.touches[0]?e=e.touches[0]:e.changedTouches&&e.changedTouches[0]?e=e.changedTouches[0]:e.targetTouches&&e.targetTouches[0]&&(e=e.targetTouches[0]),{top:e.clientY,left:e.clientX}}function wh(e){if(e.path)return e.path;if(e.composedPath)return e.composedPath();const t=[];let n=e.target;for(;n;){if(t.push(n),n.tagName==="HTML")return t.push(document),t.push(window),t;n=n.parentElement}}function gc(e){e.stopPropagation()}function ho(e){e.cancelable!==!1&&e.preventDefault()}function Zt(e){e.cancelable!==!1&&e.preventDefault(),e.stopPropagation()}function rv(e,t){if(e===void 0||t===!0&&e.__dragPrevented===!0)return;const n=t===!0?r=>{r.__dragPrevented=!0,r.addEventListener("dragstart",ho,De.notPassiveCapture)}:r=>{delete r.__dragPrevented,r.removeEventListener("dragstart",ho,De.notPassiveCapture)};e.querySelectorAll("a, img").forEach(n)}function Sh(e,t,n){const r=`__q_${t}_evt`;e[r]=e[r]!==void 0?e[r].concat(n):n,n.forEach(s=>{s[0].addEventListener(s[1],e[s[2]],De[s[3]])})}function Eh(e,t){const n=`__q_${t}_evt`;e[n]!==void 0&&(e[n].forEach(r=>{r[0].removeEventListener(r[1],e[r[2]],De[r[3]])}),e[n]=void 0)}function xh(e,t=250,n){let r=null;function s(){const o=arguments,i=()=>{r=null,e.apply(this,o)};r!==null&&clearTimeout(r),r=setTimeout(i,t)}return s.cancel=()=>{r!==null&&clearTimeout(r)},s}const $s=["sm","md","lg","xl"],{passive:Ji}=De,Ch=kn({width:0,height:0,name:"xs",sizes:{sm:600,md:1024,lg:1440,xl:1920},lt:{sm:!0,md:!0,lg:!0,xl:!0},gt:{xs:!1,sm:!1,md:!1,lg:!1},xs:!0,sm:!1,md:!1,lg:!1,xl:!1},{setSizes:ut,setDebounce:ut,install({$q:e,onSSRHydrated:t}){if(e.screen=this,this.__installed===!0){e.config.screen!==void 0&&(e.config.screen.bodyClasses===!1?document.body.classList.remove(`screen--${this.name}`):this.__update(!0));return}const{visualViewport:n}=window,r=n||window,s=document.scrollingElement||document.documentElement,o=n===void 0||Ce.is.mobile===!0?()=>[Math.max(window.innerWidth,s.clientWidth),Math.max(window.innerHeight,s.clientHeight)]:()=>[n.width*n.scale+window.innerWidth-s.clientWidth,n.height*n.scale+window.innerHeight-s.clientHeight],i=e.config.screen!==void 0&&e.config.screen.bodyClasses===!0;this.__update=d=>{const[f,g]=o();if(g!==this.height&&(this.height=g),f!==this.width)this.width=f;else if(d!==!0)return;let v=this.sizes;this.gt.xs=f>=v.sm,this.gt.sm=f>=v.md,this.gt.md=f>=v.lg,this.gt.lg=f>=v.xl,this.lt.sm=f<v.sm,this.lt.md=f<v.md,this.lt.lg=f<v.lg,this.lt.xl=f<v.xl,this.xs=this.lt.sm,this.sm=this.gt.xs===!0&&this.lt.md===!0,this.md=this.gt.sm===!0&&this.lt.lg===!0,this.lg=this.gt.md===!0&&this.lt.xl===!0,this.xl=this.gt.lg,v=this.xs===!0&&"xs"||this.sm===!0&&"sm"||this.md===!0&&"md"||this.lg===!0&&"lg"||"xl",v!==this.name&&(i===!0&&(document.body.classList.remove(`screen--${this.name}`),document.body.classList.add(`screen--${v}`)),this.name=v)};let l,a={},u=16;this.setSizes=d=>{$s.forEach(f=>{d[f]!==void 0&&(a[f]=d[f])})},this.setDebounce=d=>{u=d};const c=()=>{const d=getComputedStyle(document.body);d.getPropertyValue("--q-size-sm")&&$s.forEach(f=>{this.sizes[f]=parseInt(d.getPropertyValue(`--q-size-${f}`),10)}),this.setSizes=f=>{$s.forEach(g=>{f[g]&&(this.sizes[g]=f[g])}),this.__update(!0)},this.setDebounce=f=>{l!==void 0&&r.removeEventListener("resize",l,Ji),l=f>0?xh(this.__update,f):this.__update,r.addEventListener("resize",l,Ji)},this.setDebounce(u),Object.keys(a).length!==0?(this.setSizes(a),a=void 0):this.__update(),i===!0&&this.name==="xs"&&document.body.classList.add("screen--xs")};an.value===!0?t.push(c):c()}}),Pe=kn({isActive:!1,mode:!1},{__media:void 0,set(e){Pe.mode=e,e==="auto"?(Pe.__media===void 0&&(Pe.__media=window.matchMedia("(prefers-color-scheme: dark)"),Pe.__updateMedia=()=>{Pe.set("auto")},Pe.__media.addListener(Pe.__updateMedia)),e=Pe.__media.matches):Pe.__media!==void 0&&(Pe.__media.removeListener(Pe.__updateMedia),Pe.__media=void 0),Pe.isActive=e===!0,document.body.classList.remove(`body--${e===!0?"light":"dark"}`),document.body.classList.add(`body--${e===!0?"dark":"light"}`)},toggle(){Pe.set(Pe.isActive===!1)},install({$q:e,ssrContext:t}){const{dark:n}=e.config;e.dark=this,this.__installed!==!0&&this.set(n!==void 0?n:!1)}});function Th(e,t,n=document.body){if(typeof e!="string")throw new TypeError("Expected a string as propName");if(typeof t!="string")throw new TypeError("Expected a string as value");if(!(n instanceof Element))throw new TypeError("Expected a DOM element");n.style.setProperty(`--q-${e}`,t)}let mc=!1;function Ph(e){mc=e.isComposing===!0}function Rh(e){return mc===!0||e!==Object(e)||e.isComposing===!0||e.qKeyEvent===!0}function po(e,t){return Rh(e)===!0?!1:[].concat(t).includes(e.keyCode)}function vc(e){if(e.ios===!0)return"ios";if(e.android===!0)return"android"}function Ah({is:e,has:t,within:n},r){const s=[e.desktop===!0?"desktop":"mobile",`${t.touch===!1?"no-":""}touch`];if(e.mobile===!0){const o=vc(e);o!==void 0&&s.push("platform-"+o)}if(e.nativeMobile===!0){const o=e.nativeMobileWrapper;s.push(o),s.push("native-mobile"),e.ios===!0&&(r[o]===void 0||r[o].iosStatusBarPadding!==!1)&&s.push("q-ios-padding")}else e.electron===!0?s.push("electron"):e.bex===!0&&s.push("bex");return n.iframe===!0&&s.push("within-iframe"),s}function Oh(){const{is:e}=Ce,t=document.body.className,n=new Set(t.replace(/ {2}/g," ").split(" "));if(e.nativeMobile!==!0&&e.electron!==!0&&e.bex!==!0){if(e.desktop===!0)n.delete("mobile"),n.delete("platform-ios"),n.delete("platform-android"),n.add("desktop");else if(e.mobile===!0){n.delete("desktop"),n.add("mobile"),n.delete("platform-ios"),n.delete("platform-android");const s=vc(e);s!==void 0&&n.add(`platform-${s}`)}}Ce.has.touch===!0&&(n.delete("no-touch"),n.add("touch")),Ce.within.iframe===!0&&n.add("within-iframe");const r=Array.from(n).join(" ");t!==r&&(document.body.className=r)}function kh(e){for(const t in e)Th(t,e[t])}const Lh={install(e){if(this.__installed!==!0){if(an.value===!0)Oh();else{const{$q:t}=e;t.config.brand!==void 0&&kh(t.config.brand);const n=Ah(Ce,t.config);document.body.classList.add.apply(document.body.classList,n)}Ce.is.ios===!0&&document.body.addEventListener("touchstart",ut),window.addEventListener("keydown",Ph,!0)}}},_c=()=>!0;function Ih(e){return typeof e=="string"&&e!==""&&e!=="/"&&e!=="#/"}function $h(e){return e.startsWith("#")===!0&&(e=e.substring(1)),e.startsWith("/")===!1&&(e="/"+e),e.endsWith("/")===!0&&(e=e.substring(0,e.length-1)),"#"+e}function Mh(e){if(e.backButtonExit===!1)return()=>!1;if(e.backButtonExit==="*")return _c;const t=["#/"];return Array.isArray(e.backButtonExit)===!0&&t.push(...e.backButtonExit.filter(Ih).map($h)),()=>t.includes(window.location.hash)}const Nh={__history:[],add:ut,remove:ut,install({$q:e}){if(this.__installed===!0)return;const{cordova:t,capacitor:n}=Ce.is;if(t!==!0&&n!==!0)return;const r=e.config[t===!0?"cordova":"capacitor"];if(r!==void 0&&r.backButton===!1||n===!0&&(window.Capacitor===void 0||window.Capacitor.Plugins.App===void 0))return;this.add=i=>{i.condition===void 0&&(i.condition=_c),this.__history.push(i)},this.remove=i=>{const l=this.__history.indexOf(i);l>=0&&this.__history.splice(l,1)};const s=Mh(Object.assign({backButtonExit:!0},r)),o=()=>{if(this.__history.length){const i=this.__history[this.__history.length-1];i.condition()===!0&&(this.__history.pop(),i.handler())}else s()===!0?navigator.app.exitApp():window.history.back()};t===!0?document.addEventListener("deviceready",()=>{document.addEventListener("backbutton",o,!1)}):window.Capacitor.Plugins.App.addListener("backButton",o)}},Qi={isoName:"en-US",nativeName:"English (US)",label:{clear:"Clear",ok:"OK",cancel:"Cancel",close:"Close",set:"Set",select:"Select",reset:"Reset",remove:"Remove",update:"Update",create:"Create",search:"Search",filter:"Filter",refresh:"Refresh",expand:e=>e?`Expand "${e}"`:"Expand",collapse:e=>e?`Collapse "${e}"`:"Collapse"},date:{days:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),daysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),firstDayOfWeek:0,format24h:!1,pluralDay:"days"},table:{noData:"No data available",noResults:"No matching records found",loading:"Loading...",selectedRecords:e=>e===1?"1 record selected.":(e===0?"No":e)+" records selected.",recordsPerPage:"Records per page:",allRows:"All",pagination:(e,t,n)=>e+"-"+t+" of "+n,columns:"Columns"},editor:{url:"URL",bold:"Bold",italic:"Italic",strikethrough:"Strikethrough",underline:"Underline",unorderedList:"Unordered List",orderedList:"Ordered List",subscript:"Subscript",superscript:"Superscript",hyperlink:"Hyperlink",toggleFullscreen:"Toggle Fullscreen",quote:"Quote",left:"Left align",center:"Center align",right:"Right align",justify:"Justify align",print:"Print",outdent:"Decrease indentation",indent:"Increase indentation",removeFormat:"Remove formatting",formatting:"Formatting",fontSize:"Font Size",align:"Align",hr:"Insert Horizontal Rule",undo:"Undo",redo:"Redo",heading1:"Heading 1",heading2:"Heading 2",heading3:"Heading 3",heading4:"Heading 4",heading5:"Heading 5",heading6:"Heading 6",paragraph:"Paragraph",code:"Code",size1:"Very small",size2:"A bit small",size3:"Normal",size4:"Medium-large",size5:"Big",size6:"Very big",size7:"Maximum",defaultFont:"Default Font",viewSource:"View Source"},tree:{noNodes:"No nodes available",noResults:"No matching nodes found"}};function Xi(){const e=Array.isArray(navigator.languages)===!0&&navigator.languages.length!==0?navigator.languages[0]:navigator.language;if(typeof e=="string")return e.split(/[-_]/).map((t,n)=>n===0?t.toLowerCase():n>1||t.length<4?t.toUpperCase():t[0].toUpperCase()+t.slice(1).toLowerCase()).join("-")}const $t=kn({__qLang:{}},{getLocale:Xi,set(e=Qi,t){const n={...e,rtl:e.rtl===!0,getLocale:Xi};{if(n.set=$t.set,$t.__langConfig===void 0||$t.__langConfig.noHtmlAttrs!==!0){const r=document.documentElement;r.setAttribute("dir",n.rtl===!0?"rtl":"ltr"),r.setAttribute("lang",n.isoName)}Object.assign($t.__qLang,n)}},install({$q:e,lang:t,ssrContext:n}){e.lang=$t.__qLang,$t.__langConfig=e.config.lang,this.__installed===!0?t!==void 0&&this.set(t):(this.props=new Proxy(this.__qLang,{get(){return Reflect.get(...arguments)},ownKeys(r){return Reflect.ownKeys(r).filter(s=>s!=="set"&&s!=="getLocale")}}),this.set(t||Qi))}}),jh={name:"material-icons",type:{positive:"check_circle",negative:"warning",info:"info",warning:"priority_high"},arrow:{up:"arrow_upward",right:"arrow_forward",down:"arrow_downward",left:"arrow_back",dropdown:"arrow_drop_down"},chevron:{left:"chevron_left",right:"chevron_right"},colorPicker:{spectrum:"gradient",tune:"tune",palette:"style"},pullToRefresh:{icon:"refresh"},carousel:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down",navigationIcon:"lens"},chip:{remove:"cancel",selected:"check"},datetime:{arrowLeft:"chevron_left",arrowRight:"chevron_right",now:"access_time",today:"today"},editor:{bold:"format_bold",italic:"format_italic",strikethrough:"strikethrough_s",underline:"format_underlined",unorderedList:"format_list_bulleted",orderedList:"format_list_numbered",subscript:"vertical_align_bottom",superscript:"vertical_align_top",hyperlink:"link",toggleFullscreen:"fullscreen",quote:"format_quote",left:"format_align_left",center:"format_align_center",right:"format_align_right",justify:"format_align_justify",print:"print",outdent:"format_indent_decrease",indent:"format_indent_increase",removeFormat:"format_clear",formatting:"text_format",fontSize:"format_size",align:"format_align_left",hr:"remove",undo:"undo",redo:"redo",heading:"format_size",code:"code",size:"format_size",font:"font_download",viewSource:"code"},expansionItem:{icon:"keyboard_arrow_down",denseIcon:"arrow_drop_down"},fab:{icon:"add",activeIcon:"close"},field:{clear:"cancel",error:"error"},pagination:{first:"first_page",prev:"keyboard_arrow_left",next:"keyboard_arrow_right",last:"last_page"},rating:{icon:"grade"},stepper:{done:"check",active:"edit",error:"warning"},tabs:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down"},table:{arrowUp:"arrow_upward",warning:"warning",firstPage:"first_page",prevPage:"chevron_left",nextPage:"chevron_right",lastPage:"last_page"},tree:{icon:"play_arrow"},uploader:{done:"done",clear:"clear",add:"add_box",upload:"cloud_upload",removeQueue:"clear_all",removeUploaded:"done_all"}},Xr=kn({iconMapFn:null,__qIconSet:{}},{set(e,t){const n={...e};n.set=Xr.set,Object.assign(Xr.__qIconSet,n)},install({$q:e,iconSet:t,ssrContext:n}){e.config.iconMapFn!==void 0&&(this.iconMapFn=e.config.iconMapFn),e.iconSet=this.__qIconSet,vs(e,"iconMapFn",()=>this.iconMapFn,r=>{this.iconMapFn=r}),this.__installed===!0?t!==void 0&&this.set(t):(this.props=new Proxy(this.__qIconSet,{get(){return Reflect.get(...arguments)},ownKeys(r){return Reflect.ownKeys(r).filter(s=>s!=="set")}}),this.set(t||jh))}}),Dh="_q_",sv="_q_l_",ov="_q_pc_",iv="_q_fo_",lv="_q_tabs_",av="_q_u_";function cv(){}const Zr={};let yc=!1;function Fh(){yc=!0}function Ms(e,t){if(e===t)return!0;if(e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;let n,r;if(e.constructor===Array){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(Ms(e[r],t[r])!==!0)return!1;return!0}if(e.constructor===Map){if(e.size!==t.size)return!1;let o=e.entries();for(r=o.next();r.done!==!0;){if(t.has(r.value[0])!==!0)return!1;r=o.next()}for(o=e.entries(),r=o.next();r.done!==!0;){if(Ms(r.value[1],t.get(r.value[0]))!==!0)return!1;r=o.next()}return!0}if(e.constructor===Set){if(e.size!==t.size)return!1;const o=e.entries();for(r=o.next();r.done!==!0;){if(t.has(r.value[0])!==!0)return!1;r=o.next()}return!0}if(e.buffer!=null&&e.buffer.constructor===ArrayBuffer){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(e[r]!==t[r])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();const s=Object.keys(e).filter(o=>e[o]!==void 0);if(n=s.length,n!==Object.keys(t).filter(o=>t[o]!==void 0).length)return!1;for(r=n;r--!==0;){const o=s[r];if(Ms(e[o],t[o])!==!0)return!1}return!0}return e!==e&&t!==t}function cn(e){return e!==null&&typeof e=="object"&&Array.isArray(e)!==!0}function qh(e){return Object.prototype.toString.call(e)==="[object Date]"}function Hh(e){return Object.prototype.toString.call(e)==="[object RegExp]"}function uv(e){return typeof e=="number"&&isFinite(e)}const Zi=[fo,Lh,Pe,Ch,Nh,$t,Xr];function bc(e,t){const n=hc(e);n.config.globalProperties=t.config.globalProperties;const{reload:r,...s}=t._context;return Object.assign(n._context,s),n}function el(e,t){t.forEach(n=>{n.install(e),n.__installed=!0})}function Bh(e,t,n){e.config.globalProperties.$q=n.$q,e.provide(Dh,n.$q),el(n,Zi),t.components!==void 0&&Object.values(t.components).forEach(r=>{cn(r)===!0&&r.name!==void 0&&e.component(r.name,r)}),t.directives!==void 0&&Object.values(t.directives).forEach(r=>{cn(r)===!0&&r.name!==void 0&&e.directive(r.name,r)}),t.plugins!==void 0&&el(n,Object.values(t.plugins).filter(r=>typeof r.install=="function"&&Zi.includes(r)===!1)),an.value===!0&&(n.$q.onSSRHydrated=()=>{n.onSSRHydrated.forEach(r=>{r()}),n.$q.onSSRHydrated=()=>{}})}const Vh=function(e,t={}){const n={version:"2.17.4"};yc===!1?(t.config!==void 0&&Object.assign(Zr,t.config),n.config={...Zr},Fh()):n.config=t.config||{},Bh(e,t,{parentApp:e,$q:n,lang:t.lang,iconSet:t.iconSet,onSSRHydrated:[]})},Uh={name:"Quasar",version:"2.17.4",install:Vh,lang:$t,iconSet:Xr},Kh=cs({name:"App",__name:"App",setup(e,{expose:t}){t();const n={};return Object.defineProperty(n,"__isScriptSetup",{enumerable:!1,value:!0}),n}}),zh=(e,t)=>{const n=e.__vccOpts||e;for(const[r,s]of t)n[r]=s;return n};function Wh(e,t,n,r,s,o){const i=Vf("router-view");return Wr(),Gr(i)}const Gh=zh(Kh,[["render",Wh],["__file","App.vue"]]);function fv(e){return e}var Yh=!1;function Jh(){return wc().__VUE_DEVTOOLS_GLOBAL_HOOK__}function wc(){return typeof navigator<"u"&&typeof window<"u"?window:typeof globalThis<"u"?globalThis:{}}const Qh=typeof Proxy=="function",Xh="devtools-plugin:setup",Zh="plugin:settings:set";let gn,go;function ep(){var e;return gn!==void 0||(typeof window<"u"&&window.performance?(gn=!0,go=window.performance):typeof globalThis<"u"&&(!((e=globalThis.perf_hooks)===null||e===void 0)&&e.performance)?(gn=!0,go=globalThis.perf_hooks.performance):gn=!1),gn}function tp(){return ep()?go.now():Date.now()}class np{constructor(t,n){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=n;const r={};if(t.settings)for(const i in t.settings){const l=t.settings[i];r[i]=l.defaultValue}const s=`__vue-devtools-plugin-settings__${t.id}`;let o=Object.assign({},r);try{const i=localStorage.getItem(s),l=JSON.parse(i);Object.assign(o,l)}catch{}this.fallbacks={getSettings(){return o},setSettings(i){try{localStorage.setItem(s,JSON.stringify(i))}catch{}o=i},now(){return tp()}},n&&n.on(Zh,(i,l)=>{i===this.plugin.id&&this.fallbacks.setSettings(l)}),this.proxiedOn=new Proxy({},{get:(i,l)=>this.target?this.target.on[l]:(...a)=>{this.onQueue.push({method:l,args:a})}}),this.proxiedTarget=new Proxy({},{get:(i,l)=>this.target?this.target[l]:l==="on"?this.proxiedOn:Object.keys(this.fallbacks).includes(l)?(...a)=>(this.targetQueue.push({method:l,args:a,resolve:()=>{}}),this.fallbacks[l](...a)):(...a)=>new Promise(u=>{this.targetQueue.push({method:l,args:a,resolve:u})})})}async setRealTarget(t){this.target=t;for(const n of this.onQueue)this.target.on[n.method](...n.args);for(const n of this.targetQueue)n.resolve(await this.target[n.method](...n.args))}}function Xo(e,t){const n=e,r=wc(),s=Jh(),o=Qh&&n.enableEarlyProxy;if(s&&(r.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!o))s.emit(Xh,e,t);else{const i=o?new np(n,s):null;(r.__VUE_DEVTOOLS_PLUGINS__=r.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:n,setupFn:t,proxy:i}),i&&t(i.proxiedTarget)}}/*!
 * pinia v2.2.2
 * (c) 2024 Eduardo San Martin Morote
 * @license MIT
 */let Sc;const _s=e=>Sc=e,Ec=Symbol();function mo(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var dt;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(dt||(dt={}));const Xn=typeof window<"u",tl=typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof global=="object"&&global.global===global?global:typeof globalThis=="object"?globalThis:{HTMLElement:null};function rp(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob(["\uFEFF",e],{type:e.type}):e}function Zo(e,t,n){const r=new XMLHttpRequest;r.open("GET",e),r.responseType="blob",r.onload=function(){Tc(r.response,t,n)},r.onerror=function(){console.error("could not download file")},r.send()}function xc(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch{}return t.status>=200&&t.status<=299}function $r(e){try{e.dispatchEvent(new MouseEvent("click"))}catch{const n=document.createEvent("MouseEvents");n.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(n)}}const Mr=typeof navigator=="object"?navigator:{userAgent:""},Cc=/Macintosh/.test(Mr.userAgent)&&/AppleWebKit/.test(Mr.userAgent)&&!/Safari/.test(Mr.userAgent),Tc=Xn?typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype&&!Cc?sp:"msSaveOrOpenBlob"in Mr?op:ip:()=>{};function sp(e,t="download",n){const r=document.createElement("a");r.download=t,r.rel="noopener",typeof e=="string"?(r.href=e,r.origin!==location.origin?xc(r.href)?Zo(e,t,n):(r.target="_blank",$r(r)):$r(r)):(r.href=URL.createObjectURL(e),setTimeout(function(){URL.revokeObjectURL(r.href)},4e4),setTimeout(function(){$r(r)},0))}function op(e,t="download",n){if(typeof e=="string")if(xc(e))Zo(e,t,n);else{const r=document.createElement("a");r.href=e,r.target="_blank",setTimeout(function(){$r(r)})}else navigator.msSaveOrOpenBlob(rp(e,n),t)}function ip(e,t,n,r){if(r=r||open("","_blank"),r&&(r.document.title=r.document.body.innerText="downloading..."),typeof e=="string")return Zo(e,t,n);const s=e.type==="application/octet-stream",o=/constructor/i.test(String(tl.HTMLElement))||"safari"in tl,i=/CriOS\/[\d]+/.test(navigator.userAgent);if((i||s&&o||Cc)&&typeof FileReader<"u"){const l=new FileReader;l.onloadend=function(){let a=l.result;if(typeof a!="string")throw r=null,new Error("Wrong reader.result type");a=i?a:a.replace(/^data:[^;]*;/,"data:attachment/file;"),r?r.location.href=a:location.assign(a),r=null},l.readAsDataURL(e)}else{const l=URL.createObjectURL(e);r?r.location.assign(l):location.href=l,r=null,setTimeout(function(){URL.revokeObjectURL(l)},4e4)}}function we(e,t){const n="🍍 "+e;typeof __VUE_DEVTOOLS_TOAST__=="function"?__VUE_DEVTOOLS_TOAST__(n,t):t==="error"?console.error(n):t==="warn"?console.warn(n):console.log(n)}function ei(e){return"_a"in e&&"install"in e}function Pc(){if(!("clipboard"in navigator))return we("Your browser doesn't support the Clipboard API","error"),!0}function Rc(e){return e instanceof Error&&e.message.toLowerCase().includes("document is not focused")?(we('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.',"warn"),!0):!1}async function lp(e){if(!Pc())try{await navigator.clipboard.writeText(JSON.stringify(e.state.value)),we("Global state copied to clipboard.")}catch(t){if(Rc(t))return;we("Failed to serialize the state. Check the console for more details.","error"),console.error(t)}}async function ap(e){if(!Pc())try{Ac(e,JSON.parse(await navigator.clipboard.readText())),we("Global state pasted from clipboard.")}catch(t){if(Rc(t))return;we("Failed to deserialize the state from clipboard. Check the console for more details.","error"),console.error(t)}}async function cp(e){try{Tc(new Blob([JSON.stringify(e.state.value)],{type:"text/plain;charset=utf-8"}),"pinia-state.json")}catch(t){we("Failed to export the state as JSON. Check the console for more details.","error"),console.error(t)}}let gt;function up(){gt||(gt=document.createElement("input"),gt.type="file",gt.accept=".json");function e(){return new Promise((t,n)=>{gt.onchange=async()=>{const r=gt.files;if(!r)return t(null);const s=r.item(0);return t(s?{text:await s.text(),file:s}:null)},gt.oncancel=()=>t(null),gt.onerror=n,gt.click()})}return e}async function fp(e){try{const n=await up()();if(!n)return;const{text:r,file:s}=n;Ac(e,JSON.parse(r)),we(`Global state imported from "${s.name}".`)}catch(t){we("Failed to import the state from JSON. Check the console for more details.","error"),console.error(t)}}function Ac(e,t){for(const n in t){const r=e.state.value[n];r?Object.assign(r,t[n]):e.state.value[n]=t[n]}}function et(e){return{_custom:{display:e}}}const Oc="🍍 Pinia (root)",Nr="_root";function dp(e){return ei(e)?{id:Nr,label:Oc}:{id:e.$id,label:e.$id}}function hp(e){if(ei(e)){const n=Array.from(e._s.keys()),r=e._s;return{state:n.map(o=>({editable:!0,key:o,value:e.state.value[o]})),getters:n.filter(o=>r.get(o)._getters).map(o=>{const i=r.get(o);return{editable:!1,key:o,value:i._getters.reduce((l,a)=>(l[a]=i[a],l),{})}})}}const t={state:Object.keys(e.$state).map(n=>({editable:!0,key:n,value:e.$state[n]}))};return e._getters&&e._getters.length&&(t.getters=e._getters.map(n=>({editable:!1,key:n,value:e[n]}))),e._customProperties.size&&(t.customProperties=Array.from(e._customProperties).map(n=>({editable:!0,key:n,value:e[n]}))),t}function pp(e){return e?Array.isArray(e)?e.reduce((t,n)=>(t.keys.push(n.key),t.operations.push(n.type),t.oldValue[n.key]=n.oldValue,t.newValue[n.key]=n.newValue,t),{oldValue:{},keys:[],operations:[],newValue:{}}):{operation:et(e.type),key:et(e.key),oldValue:e.oldValue,newValue:e.newValue}:{}}function gp(e){switch(e){case dt.direct:return"mutation";case dt.patchFunction:return"$patch";case dt.patchObject:return"$patch";default:return"unknown"}}let En=!0;const jr=[],en="pinia:mutations",Re="pinia",{assign:mp}=Object,es=e=>"🍍 "+e;function vp(e,t){Xo({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:jr,app:e},n=>{typeof n.now!="function"&&we("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),n.addTimelineLayer({id:en,label:"Pinia 🍍",color:15064968}),n.addInspector({id:Re,label:"Pinia 🍍",icon:"storage",treeFilterPlaceholder:"Search stores",actions:[{icon:"content_copy",action:()=>{lp(t)},tooltip:"Serialize and copy the state"},{icon:"content_paste",action:async()=>{await ap(t),n.sendInspectorTree(Re),n.sendInspectorState(Re)},tooltip:"Replace the state with the content of your clipboard"},{icon:"save",action:()=>{cp(t)},tooltip:"Save the state as a JSON file"},{icon:"folder_open",action:async()=>{await fp(t),n.sendInspectorTree(Re),n.sendInspectorState(Re)},tooltip:"Import the state from a JSON file"}],nodeActions:[{icon:"restore",tooltip:'Reset the state (with "$reset")',action:r=>{const s=t._s.get(r);s?typeof s.$reset!="function"?we(`Cannot reset "${r}" store because it doesn't have a "$reset" method implemented.`,"warn"):(s.$reset(),we(`Store "${r}" reset.`)):we(`Cannot reset "${r}" store because it wasn't found.`,"warn")}}]}),n.on.inspectComponent((r,s)=>{const o=r.componentInstance&&r.componentInstance.proxy;if(o&&o._pStores){const i=r.componentInstance.proxy._pStores;Object.values(i).forEach(l=>{r.instanceData.state.push({type:es(l.$id),key:"state",editable:!0,value:l._isOptionsAPI?{_custom:{value:Z(l.$state),actions:[{icon:"restore",tooltip:"Reset the state of this store",action:()=>l.$reset()}]}}:Object.keys(l.$state).reduce((a,u)=>(a[u]=l.$state[u],a),{})}),l._getters&&l._getters.length&&r.instanceData.state.push({type:es(l.$id),key:"getters",editable:!1,value:l._getters.reduce((a,u)=>{try{a[u]=l[u]}catch(c){a[u]=c}return a},{})})})}}),n.on.getInspectorTree(r=>{if(r.app===e&&r.inspectorId===Re){let s=[t];s=s.concat(Array.from(t._s.values())),r.rootNodes=(r.filter?s.filter(o=>"$id"in o?o.$id.toLowerCase().includes(r.filter.toLowerCase()):Oc.toLowerCase().includes(r.filter.toLowerCase())):s).map(dp)}}),globalThis.$pinia=t,n.on.getInspectorState(r=>{if(r.app===e&&r.inspectorId===Re){const s=r.nodeId===Nr?t:t._s.get(r.nodeId);if(!s)return;s&&(r.nodeId!==Nr&&(globalThis.$store=Z(s)),r.state=hp(s))}}),n.on.editInspectorState((r,s)=>{if(r.app===e&&r.inspectorId===Re){const o=r.nodeId===Nr?t:t._s.get(r.nodeId);if(!o)return we(`store "${r.nodeId}" not found`,"error");const{path:i}=r;ei(o)?i.unshift("state"):(i.length!==1||!o._customProperties.has(i[0])||i[0]in o.$state)&&i.unshift("$state"),En=!1,r.set(o,i,r.state.value),En=!0}}),n.on.editComponentState(r=>{if(r.type.startsWith("🍍")){const s=r.type.replace(/^🍍\s*/,""),o=t._s.get(s);if(!o)return we(`store "${s}" not found`,"error");const{path:i}=r;if(i[0]!=="state")return we(`Invalid path for store "${s}":
${i}
Only state can be modified.`);i[0]="$state",En=!1,r.set(o,i,r.state.value),En=!0}})})}function _p(e,t){jr.includes(es(t.$id))||jr.push(es(t.$id)),Xo({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:jr,app:e,settings:{logStoreChanges:{label:"Notify about new/deleted stores",type:"boolean",defaultValue:!0}}},n=>{const r=typeof n.now=="function"?n.now.bind(n):Date.now;t.$onAction(({after:i,onError:l,name:a,args:u})=>{const c=kc++;n.addTimelineEvent({layerId:en,event:{time:r(),title:"🛫 "+a,subtitle:"start",data:{store:et(t.$id),action:et(a),args:u},groupId:c}}),i(d=>{jt=void 0,n.addTimelineEvent({layerId:en,event:{time:r(),title:"🛬 "+a,subtitle:"end",data:{store:et(t.$id),action:et(a),args:u,result:d},groupId:c}})}),l(d=>{jt=void 0,n.addTimelineEvent({layerId:en,event:{time:r(),logType:"error",title:"💥 "+a,subtitle:"end",data:{store:et(t.$id),action:et(a),args:u,error:d},groupId:c}})})},!0),t._customProperties.forEach(i=>{Ft(()=>wt(t[i]),(l,a)=>{n.notifyComponentUpdate(),n.sendInspectorState(Re),En&&n.addTimelineEvent({layerId:en,event:{time:r(),title:"Change",subtitle:i,data:{newValue:l,oldValue:a},groupId:jt}})},{deep:!0})}),t.$subscribe(({events:i,type:l},a)=>{if(n.notifyComponentUpdate(),n.sendInspectorState(Re),!En)return;const u={time:r(),title:gp(l),data:mp({store:et(t.$id)},pp(i)),groupId:jt};l===dt.patchFunction?u.subtitle="⤵️":l===dt.patchObject?u.subtitle="🧩":i&&!Array.isArray(i)&&(u.subtitle=i.type),i&&(u.data["rawEvent(s)"]={_custom:{display:"DebuggerEvent",type:"object",tooltip:"raw DebuggerEvent[]",value:i}}),n.addTimelineEvent({layerId:en,event:u})},{detached:!0,flush:"sync"});const s=t._hotUpdate;t._hotUpdate=ht(i=>{s(i),n.addTimelineEvent({layerId:en,event:{time:r(),title:"🔥 "+t.$id,subtitle:"HMR update",data:{store:et(t.$id),info:et("HMR update")}}}),n.notifyComponentUpdate(),n.sendInspectorTree(Re),n.sendInspectorState(Re)});const{$dispose:o}=t;t.$dispose=()=>{o(),n.notifyComponentUpdate(),n.sendInspectorTree(Re),n.sendInspectorState(Re),n.getSettings().logStoreChanges&&we(`Disposed "${t.$id}" store 🗑`)},n.notifyComponentUpdate(),n.sendInspectorTree(Re),n.sendInspectorState(Re),n.getSettings().logStoreChanges&&we(`"${t.$id}" store installed 🆕`)})}let kc=0,jt;function nl(e,t,n){const r=t.reduce((s,o)=>(s[o]=Z(e)[o],s),{});for(const s in r)e[s]=function(){const o=kc,i=n?new Proxy(e,{get(...a){return jt=o,Reflect.get(...a)},set(...a){return jt=o,Reflect.set(...a)}}):e;jt=o;const l=r[s].apply(i,arguments);return jt=void 0,l}}function yp({app:e,store:t,options:n}){if(!t.$id.startsWith("__hot:")){if(t._isOptionsAPI=!!n.state,!t._p._testing){nl(t,Object.keys(n.actions),t._isOptionsAPI);const r=t._hotUpdate;Z(t)._hotUpdate=function(s){r.apply(this,arguments),nl(t,Object.keys(s._hmrPayload.actions),!!t._isOptionsAPI)}}_p(e,t)}}function bp(){const e=Yl(!0),t=e.run(()=>ln({}));let n=[],r=[];const s=ht({install(o){_s(s),s._a=o,o.provide(Ec,s),o.config.globalProperties.$pinia=s,Xn&&vp(o,s),r.forEach(i=>n.push(i)),r=[]},use(o){return!this._a&&!Yh?r.push(o):n.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return typeof Proxy<"u"&&s.use(yp),s}const Lc=()=>{};function rl(e,t,n,r=Lc){e.push(t);const s=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),r())};return!n&&Jl()&&Fu(s),s}function mn(e,...t){e.slice().forEach(n=>{n(...t)})}const wp=e=>e(),sl=Symbol(),Ns=Symbol();function vo(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,r)=>e.set(r,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],s=e[n];mo(s)&&mo(r)&&e.hasOwnProperty(n)&&!me(r)&&!Dt(r)?e[n]=vo(s,r):e[n]=r}return e}const Sp=Symbol();function Ep(e){return!mo(e)||!e.hasOwnProperty(Sp)}const{assign:Xe}=Object;function xp(e){return!!(me(e)&&e.effect)}function Cp(e,t,n,r){const{state:s,actions:o,getters:i}=t,l=n.state.value[e];let a;function u(){l||(n.state.value[e]=s?s():{});const c=df(n.state.value[e]);return Xe(c,o,Object.keys(i||{}).reduce((d,f)=>(d[f]=ht(G(()=>{_s(n);const g=n._s.get(e);return i[f].call(g,g)})),d),{}))}return a=Ic(e,u,t,n,r,!0),a}function Ic(e,t,n={},r,s,o){let i;const l=Xe({actions:{}},n),a={deep:!0};let u,c,d=[],f=[],g;const v=r.state.value[e];!o&&!v&&(r.state.value[e]={});const x=ln({});let T;function R($){let L;u=c=!1,typeof $=="function"?($(r.state.value[e]),L={type:dt.patchFunction,storeId:e,events:g}):(vo(r.state.value[e],$),L={type:dt.patchObject,payload:$,storeId:e,events:g});const O=T=Symbol();Bo().then(()=>{T===O&&(u=!0)}),c=!0,mn(d,L,r.state.value[e])}const w=o?function(){const{state:L}=n,O=L?L():{};this.$patch(Q=>{Xe(Q,O)})}:Lc;function S(){i.stop(),d=[],f=[],r._s.delete(e)}const y=($,L="")=>{if(sl in $)return $[Ns]=L,$;const O=function(){_s(r);const Q=Array.from(arguments),M=[],ee=[];function ae(ge){M.push(ge)}function re(ge){ee.push(ge)}mn(f,{args:Q,name:O[Ns],store:j,after:ae,onError:re});let te;try{te=$.apply(this&&this.$id===e?this:j,Q)}catch(ge){throw mn(ee,ge),ge}return te instanceof Promise?te.then(ge=>(mn(M,ge),ge)).catch(ge=>(mn(ee,ge),Promise.reject(ge))):(mn(M,te),te)};return O[sl]=!0,O[Ns]=L,O},I=ht({actions:{},getters:{},state:[],hotState:x}),B={_p:r,$id:e,$onAction:rl.bind(null,f),$patch:R,$reset:w,$subscribe($,L={}){const O=rl(d,$,L.detached,()=>Q()),Q=i.run(()=>Ft(()=>r.state.value[e],M=>{(L.flush==="sync"?c:u)&&$({storeId:e,type:dt.direct,events:g},M)},Xe({},a,L)));return O},$dispose:S},j=un(Xn?Xe({_hmrPayload:I,_customProperties:ht(new Set)},B):B);r._s.set(e,j);const F=(r._a&&r._a.runWithContext||wp)(()=>r._e.run(()=>(i=Yl()).run(()=>t({action:y}))));for(const $ in F){const L=F[$];if(me(L)&&!xp(L)||Dt(L))o||(v&&Ep(L)&&(me(L)?L.value=v[$]:vo(L,v[$])),r.state.value[e][$]=L);else if(typeof L=="function"){const O=y(L,$);F[$]=O,l.actions[$]=L}}if(Xe(j,F),Xe(Z(j),F),Object.defineProperty(j,"$state",{get:()=>r.state.value[e],set:$=>{R(L=>{Xe(L,$)})}}),Xn){const $={writable:!0,configurable:!0,enumerable:!1};["_p","_hmrPayload","_getters","_customProperties"].forEach(L=>{Object.defineProperty(j,L,Xe({value:j[L]},$))})}return r._p.forEach($=>{if(Xn){const L=i.run(()=>$({store:j,app:r._a,pinia:r,options:l}));Object.keys(L||{}).forEach(O=>j._customProperties.add(O)),Xe(j,L)}else Xe(j,i.run(()=>$({store:j,app:r._a,pinia:r,options:l})))}),v&&o&&n.hydrate&&n.hydrate(j.$state,v),u=!0,c=!0,j}function $c(e,t,n){let r,s;const o=typeof t=="function";typeof e=="string"?(r=e,s=o?n:t):(s=e,r=e.id);function i(l,a){const u=Xf();return l=l||(u?Je(Ec,null):null),l&&_s(l),l=Sc,l._s.has(r)||(o?Ic(r,t,s,l):Cp(r,s,l)),l._s.get(r)}return i.$id=r,i}const js=()=>bp();/*!
  * vue-router v4.4.5
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const yt=typeof document<"u";function Mc(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Tp(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Mc(e.default)}const se=Object.assign;function Ds(e,t){const n={};for(const r in t){const s=t[r];n[r]=Ve(s)?s.map(e):e(s)}return n}const Zn=()=>{},Ve=Array.isArray,Nc=/#/g,Pp=/&/g,Rp=/\//g,Ap=/=/g,Op=/\?/g,jc=/\+/g,kp=/%5B/g,Lp=/%5D/g,Dc=/%5E/g,Ip=/%60/g,Fc=/%7B/g,$p=/%7C/g,qc=/%7D/g,Mp=/%20/g;function ti(e){return encodeURI(""+e).replace($p,"|").replace(kp,"[").replace(Lp,"]")}function Np(e){return ti(e).replace(Fc,"{").replace(qc,"}").replace(Dc,"^")}function _o(e){return ti(e).replace(jc,"%2B").replace(Mp,"+").replace(Nc,"%23").replace(Pp,"%26").replace(Ip,"`").replace(Fc,"{").replace(qc,"}").replace(Dc,"^")}function jp(e){return _o(e).replace(Ap,"%3D")}function Dp(e){return ti(e).replace(Nc,"%23").replace(Op,"%3F")}function Fp(e){return e==null?"":Dp(e).replace(Rp,"%2F")}function An(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const qp=/\/$/,Hp=e=>e.replace(qp,"");function Fs(e,t,n="/"){let r,s={},o="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(r=t.slice(0,a),o=t.slice(a+1,l>-1?l:t.length),s=e(o)),l>-1&&(r=r||t.slice(0,l),i=t.slice(l,t.length)),r=Kp(r??t,n),{fullPath:r+(o&&"?")+o+i,path:r,query:s,hash:An(i)}}function Bp(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function ol(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Vp(e,t,n){const r=t.matched.length-1,s=n.matched.length-1;return r>-1&&r===s&&Bt(t.matched[r],n.matched[s])&&Hc(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Bt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Hc(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Up(e[n],t[n]))return!1;return!0}function Up(e,t){return Ve(e)?il(e,t):Ve(t)?il(t,e):e===t}function il(e,t){return Ve(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function Kp(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),s=r[r.length-1];(s===".."||s===".")&&r.push("");let o=n.length-1,i,l;for(i=0;i<r.length;i++)if(l=r[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(i).join("/")}const Rt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var cr;(function(e){e.pop="pop",e.push="push"})(cr||(cr={}));var er;(function(e){e.back="back",e.forward="forward",e.unknown=""})(er||(er={}));function zp(e){if(!e)if(yt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Hp(e)}const Wp=/^[^#]+#/;function Gp(e,t){return e.replace(Wp,"#")+t}function Yp(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const ys=()=>({left:window.scrollX,top:window.scrollY});function Jp(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),s=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!s)return;t=Yp(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function ll(e,t){return(history.state?history.state.position-t:-1)+e}const yo=new Map;function Qp(e,t){yo.set(e,t)}function Xp(e){const t=yo.get(e);return yo.delete(e),t}let Zp=()=>location.protocol+"//"+location.host;function Bc(e,t){const{pathname:n,search:r,hash:s}=t,o=e.indexOf("#");if(o>-1){let l=s.includes(e.slice(o))?e.slice(o).length:1,a=s.slice(l);return a[0]!=="/"&&(a="/"+a),ol(a,"")}return ol(n,e)+r+s}function eg(e,t,n,r){let s=[],o=[],i=null;const l=({state:f})=>{const g=Bc(e,location),v=n.value,x=t.value;let T=0;if(f){if(n.value=g,t.value=f,i&&i===v){i=null;return}T=x?f.position-x.position:0}else r(g);s.forEach(R=>{R(n.value,v,{delta:T,type:cr.pop,direction:T?T>0?er.forward:er.back:er.unknown})})};function a(){i=n.value}function u(f){s.push(f);const g=()=>{const v=s.indexOf(f);v>-1&&s.splice(v,1)};return o.push(g),g}function c(){const{history:f}=window;f.state&&f.replaceState(se({},f.state,{scroll:ys()}),"")}function d(){for(const f of o)f();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:a,listen:u,destroy:d}}function al(e,t,n,r=!1,s=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:s?ys():null}}function tg(e){const{history:t,location:n}=window,r={value:Bc(e,n)},s={value:t.state};s.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(a,u,c){const d=e.indexOf("#"),f=d>-1?(n.host&&document.querySelector("base")?e:e.slice(d))+a:Zp()+e+a;try{t[c?"replaceState":"pushState"](u,"",f),s.value=u}catch(g){console.error(g),n[c?"replace":"assign"](f)}}function i(a,u){const c=se({},t.state,al(s.value.back,a,s.value.forward,!0),u,{position:s.value.position});o(a,c,!0),r.value=a}function l(a,u){const c=se({},s.value,t.state,{forward:a,scroll:ys()});o(c.current,c,!0);const d=se({},al(r.value,a,null),{position:c.position+1},u);o(a,d,!1),r.value=a}return{location:r,state:s,push:l,replace:i}}function ng(e){e=zp(e);const t=tg(e),n=eg(e,t.state,t.location,t.replace);function r(o,i=!0){i||n.pauseListeners(),history.go(o)}const s=se({location:"",base:e,go:r,createHref:Gp.bind(null,e)},t,n);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}function rg(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),ng(e)}function Vc(e){return typeof e=="string"||e&&typeof e=="object"}function Uc(e){return typeof e=="string"||typeof e=="symbol"}const Kc=Symbol("");var cl;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(cl||(cl={}));function On(e,t){return se(new Error,{type:e,[Kc]:!0},t)}function mt(e,t){return e instanceof Error&&Kc in e&&(t==null||!!(e.type&t))}const ul="[^/]+?",sg={sensitive:!1,strict:!1,start:!0,end:!0},og=/[.+*?^${}()[\]/\\]/g;function ig(e,t){const n=se({},sg,t),r=[];let s=n.start?"^":"";const o=[];for(const u of e){const c=u.length?[]:[90];n.strict&&!u.length&&(s+="/");for(let d=0;d<u.length;d++){const f=u[d];let g=40+(n.sensitive?.25:0);if(f.type===0)d||(s+="/"),s+=f.value.replace(og,"\\$&"),g+=40;else if(f.type===1){const{value:v,repeatable:x,optional:T,regexp:R}=f;o.push({name:v,repeatable:x,optional:T});const w=R||ul;if(w!==ul){g+=10;try{new RegExp(`(${w})`)}catch(y){throw new Error(`Invalid custom RegExp for param "${v}" (${w}): `+y.message)}}let S=x?`((?:${w})(?:/(?:${w}))*)`:`(${w})`;d||(S=T&&u.length<2?`(?:/${S})`:"/"+S),T&&(S+="?"),s+=S,g+=20,T&&(g+=-8),x&&(g+=-20),w===".*"&&(g+=-50)}c.push(g)}r.push(c)}if(n.strict&&n.end){const u=r.length-1;r[u][r[u].length-1]+=.7000000000000001}n.strict||(s+="/?"),n.end?s+="$":n.strict&&(s+="(?:/|$)");const i=new RegExp(s,n.sensitive?"":"i");function l(u){const c=u.match(i),d={};if(!c)return null;for(let f=1;f<c.length;f++){const g=c[f]||"",v=o[f-1];d[v.name]=g&&v.repeatable?g.split("/"):g}return d}function a(u){let c="",d=!1;for(const f of e){(!d||!c.endsWith("/"))&&(c+="/"),d=!1;for(const g of f)if(g.type===0)c+=g.value;else if(g.type===1){const{value:v,repeatable:x,optional:T}=g,R=v in u?u[v]:"";if(Ve(R)&&!x)throw new Error(`Provided param "${v}" is an array but it is not repeatable (* or + modifiers)`);const w=Ve(R)?R.join("/"):R;if(!w)if(T)f.length<2&&(c.endsWith("/")?c=c.slice(0,-1):d=!0);else throw new Error(`Missing required param "${v}"`);c+=w}}return c||"/"}return{re:i,score:r,keys:o,parse:l,stringify:a}}function lg(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function zc(e,t){let n=0;const r=e.score,s=t.score;for(;n<r.length&&n<s.length;){const o=lg(r[n],s[n]);if(o)return o;n++}if(Math.abs(s.length-r.length)===1){if(fl(r))return 1;if(fl(s))return-1}return s.length-r.length}function fl(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ag={type:0,value:""},cg=/[a-zA-Z0-9_]/;function ug(e){if(!e)return[[]];if(e==="/")return[[ag]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${n})/"${u}": ${g}`)}let n=0,r=n;const s=[];let o;function i(){o&&s.push(o),o=[]}let l=0,a,u="",c="";function d(){u&&(n===0?o.push({type:0,value:u}):n===1||n===2||n===3?(o.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:u,regexp:c,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),u="")}function f(){u+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:a==="/"?(u&&d(),i()):a===":"?(d(),n=1):f();break;case 4:f(),n=r;break;case 1:a==="("?n=2:cg.test(a)?f():(d(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+a:n=3:c+=a;break;case 3:d(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),d(),i(),s}function fg(e,t,n){const r=ig(ug(e.path),n),s=se(r,{record:e,parent:t,children:[],alias:[]});return t&&!s.record.aliasOf==!t.record.aliasOf&&t.children.push(s),s}function dg(e,t){const n=[],r=new Map;t=gl({strict:!1,end:!0,sensitive:!1},t);function s(d){return r.get(d)}function o(d,f,g){const v=!g,x=hl(d);x.aliasOf=g&&g.record;const T=gl(t,d),R=[x];if("alias"in d){const y=typeof d.alias=="string"?[d.alias]:d.alias;for(const I of y)R.push(hl(se({},x,{components:g?g.record.components:x.components,path:I,aliasOf:g?g.record:x})))}let w,S;for(const y of R){const{path:I}=y;if(f&&I[0]!=="/"){const B=f.record.path,j=B[B.length-1]==="/"?"":"/";y.path=f.record.path+(I&&j+I)}if(w=fg(y,f,T),g?g.alias.push(w):(S=S||w,S!==w&&S.alias.push(w),v&&d.name&&!pl(w)&&i(d.name)),Wc(w)&&a(w),x.children){const B=x.children;for(let j=0;j<B.length;j++)o(B[j],w,g&&g.children[j])}g=g||w}return S?()=>{i(S)}:Zn}function i(d){if(Uc(d)){const f=r.get(d);f&&(r.delete(d),n.splice(n.indexOf(f),1),f.children.forEach(i),f.alias.forEach(i))}else{const f=n.indexOf(d);f>-1&&(n.splice(f,1),d.record.name&&r.delete(d.record.name),d.children.forEach(i),d.alias.forEach(i))}}function l(){return n}function a(d){const f=gg(d,n);n.splice(f,0,d),d.record.name&&!pl(d)&&r.set(d.record.name,d)}function u(d,f){let g,v={},x,T;if("name"in d&&d.name){if(g=r.get(d.name),!g)throw On(1,{location:d});T=g.record.name,v=se(dl(f.params,g.keys.filter(S=>!S.optional).concat(g.parent?g.parent.keys.filter(S=>S.optional):[]).map(S=>S.name)),d.params&&dl(d.params,g.keys.map(S=>S.name))),x=g.stringify(v)}else if(d.path!=null)x=d.path,g=n.find(S=>S.re.test(x)),g&&(v=g.parse(x),T=g.record.name);else{if(g=f.name?r.get(f.name):n.find(S=>S.re.test(f.path)),!g)throw On(1,{location:d,currentLocation:f});T=g.record.name,v=se({},f.params,d.params),x=g.stringify(v)}const R=[];let w=g;for(;w;)R.unshift(w.record),w=w.parent;return{name:T,path:x,params:v,matched:R,meta:pg(R)}}e.forEach(d=>o(d));function c(){n.length=0,r.clear()}return{addRoute:o,resolve:u,removeRoute:i,clearRoutes:c,getRoutes:l,getRecordMatcher:s}}function dl(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function hl(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:hg(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function hg(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function pl(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function pg(e){return e.reduce((t,n)=>se(t,n.meta),{})}function gl(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function gg(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;zc(e,t[o])<0?r=o:n=o+1}const s=mg(e);return s&&(r=t.lastIndexOf(s,r-1)),r}function mg(e){let t=e;for(;t=t.parent;)if(Wc(t)&&zc(e,t)===0)return t}function Wc({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function vg(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const o=r[s].replace(jc," "),i=o.indexOf("="),l=An(i<0?o:o.slice(0,i)),a=i<0?null:An(o.slice(i+1));if(l in t){let u=t[l];Ve(u)||(u=t[l]=[u]),u.push(a)}else t[l]=a}return t}function ml(e){let t="";for(let n in e){const r=e[n];if(n=jp(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Ve(r)?r.map(o=>o&&_o(o)):[r&&_o(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function _g(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Ve(r)?r.map(s=>s==null?null:""+s):r==null?r:""+r)}return t}const yg=Symbol(""),vl=Symbol(""),bs=Symbol(""),ni=Symbol(""),bo=Symbol("");function Nn(){let e=[];function t(r){return e.push(r),()=>{const s=e.indexOf(r);s>-1&&e.splice(s,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Mt(e,t,n,r,s,o=i=>i()){const i=r&&(r.enterCallbacks[s]=r.enterCallbacks[s]||[]);return()=>new Promise((l,a)=>{const u=f=>{f===!1?a(On(4,{from:n,to:t})):f instanceof Error?a(f):Vc(f)?a(On(2,{from:t,to:f})):(i&&r.enterCallbacks[s]===i&&typeof f=="function"&&i.push(f),l())},c=o(()=>e.call(r&&r.instances[s],t,n,u));let d=Promise.resolve(c);e.length<3&&(d=d.then(u)),d.catch(f=>a(f))})}function qs(e,t,n,r,s=o=>o()){const o=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(Mc(a)){const c=(a.__vccOpts||a)[t];c&&o.push(Mt(c,n,r,i,l,s))}else{let u=a();o.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const d=Tp(c)?c.default:c;i.mods[l]=c,i.components[l]=d;const g=(d.__vccOpts||d)[t];return g&&Mt(g,n,r,i,l,s)()}))}}return o}function _l(e){const t=Je(bs),n=Je(ni),r=G(()=>{const a=wt(e.to);return t.resolve(a)}),s=G(()=>{const{matched:a}=r.value,{length:u}=a,c=a[u-1],d=n.matched;if(!c||!d.length)return-1;const f=d.findIndex(Bt.bind(null,c));if(f>-1)return f;const g=yl(a[u-2]);return u>1&&yl(c)===g&&d[d.length-1].path!==g?d.findIndex(Bt.bind(null,a[u-2])):f}),o=G(()=>s.value>-1&&Eg(n.params,r.value.params)),i=G(()=>s.value>-1&&s.value===n.matched.length-1&&Hc(n.params,r.value.params));function l(a={}){return Sg(a)?t[wt(e.replace)?"replace":"push"](wt(e.to)).catch(Zn):Promise.resolve()}if(yt){const a=fn();if(a){const u={route:r.value,isActive:o.value,isExactActive:i.value,error:null};a.__vrl_devtools=a.__vrl_devtools||[],a.__vrl_devtools.push(u),fd(()=>{u.route=r.value,u.isActive=o.value,u.isExactActive=i.value,u.error=Vc(wt(e.to))?null:'Invalid "to" value'},{flush:"post"})}}return{route:r,href:G(()=>r.value.href),isActive:o,isExactActive:i,navigate:l}}const bg=cs({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:_l,setup(e,{slots:t}){const n=un(_l(e)),{options:r}=Je(bs),s=G(()=>({[bl(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[bl(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&t.default(n);return e.custom?o:K("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:s.value},o)}}}),wg=bg;function Sg(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Eg(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(!Ve(s)||s.length!==r.length||r.some((o,i)=>o!==s[i]))return!1}return!0}function yl(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const bl=(e,t,n)=>e??t??n,xg=cs({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=Je(bo),s=G(()=>e.route||r.value),o=Je(vl,0),i=G(()=>{let u=wt(o);const{matched:c}=s.value;let d;for(;(d=c[u])&&!d.components;)u++;return u}),l=G(()=>s.value.matched[i.value]);Or(vl,G(()=>i.value+1)),Or(yg,l),Or(bo,s);const a=ln();return Ft(()=>[a.value,l.value,e.name],([u,c,d],[f,g,v])=>{c&&(c.instances[d]=u,g&&g!==c&&u&&u===f&&(c.leaveGuards.size||(c.leaveGuards=g.leaveGuards),c.updateGuards.size||(c.updateGuards=g.updateGuards))),u&&c&&(!g||!Bt(c,g)||!f)&&(c.enterCallbacks[d]||[]).forEach(x=>x(u))},{flush:"post"}),()=>{const u=s.value,c=e.name,d=l.value,f=d&&d.components[c];if(!f)return wl(n.default,{Component:f,route:u});const g=d.props[c],v=g?g===!0?u.params:typeof g=="function"?g(u):g:null,T=K(f,se({},v,t,{onVnodeUnmounted:R=>{R.component.isUnmounted&&(d.instances[c]=null)},ref:a}));if(yt&&T.ref){const R={depth:i.value,name:d.name,path:d.path,meta:d.meta};(Ve(T.ref)?T.ref.map(S=>S.i):[T.ref.i]).forEach(S=>{S.__vrv_devtools=R})}return wl(n.default,{Component:T,route:u})||T}}});function wl(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Cg=xg;function jn(e,t){const n=se({},e,{matched:e.matched.map(r=>Ng(r,["instances","children","aliasOf"]))});return{_custom:{type:null,readOnly:!0,display:e.fullPath,tooltip:t,value:n}}}function Cr(e){return{_custom:{display:e}}}let Tg=0;function Pg(e,t,n){if(t.__hasDevtools)return;t.__hasDevtools=!0;const r=Tg++;Xo({id:"org.vuejs.router"+(r?"."+r:""),label:"Vue Router",packageName:"vue-router",homepage:"https://router.vuejs.org",logo:"https://router.vuejs.org/logo.png",componentStateTypes:["Routing"],app:e},s=>{typeof s.now!="function"&&console.warn("[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),s.on.inspectComponent((c,d)=>{c.instanceData&&c.instanceData.state.push({type:"Routing",key:"$route",editable:!1,value:jn(t.currentRoute.value,"Current Route")})}),s.on.visitComponentTree(({treeNode:c,componentInstance:d})=>{if(d.__vrv_devtools){const f=d.__vrv_devtools;c.tags.push({label:(f.name?`${f.name.toString()}: `:"")+f.path,textColor:0,tooltip:"This component is rendered by &lt;router-view&gt;",backgroundColor:Gc})}Ve(d.__vrl_devtools)&&(d.__devtoolsApi=s,d.__vrl_devtools.forEach(f=>{let g=f.route.path,v=Qc,x="",T=0;f.error?(g=f.error,v=Lg,T=Ig):f.isExactActive?(v=Jc,x="This is exactly active"):f.isActive&&(v=Yc,x="This link is active"),c.tags.push({label:g,textColor:T,tooltip:x,backgroundColor:v})}))}),Ft(t.currentRoute,()=>{a(),s.notifyComponentUpdate(),s.sendInspectorTree(l),s.sendInspectorState(l)});const o="router:navigations:"+r;s.addTimelineLayer({id:o,label:`Router${r?" "+r:""} Navigations`,color:4237508}),t.onError((c,d)=>{s.addTimelineEvent({layerId:o,event:{title:"Error during Navigation",subtitle:d.fullPath,logType:"error",time:s.now(),data:{error:c},groupId:d.meta.__navigationId}})});let i=0;t.beforeEach((c,d)=>{const f={guard:Cr("beforeEach"),from:jn(d,"Current Location during this navigation"),to:jn(c,"Target location")};Object.defineProperty(c.meta,"__navigationId",{value:i++}),s.addTimelineEvent({layerId:o,event:{time:s.now(),title:"Start of navigation",subtitle:c.fullPath,data:f,groupId:c.meta.__navigationId}})}),t.afterEach((c,d,f)=>{const g={guard:Cr("afterEach")};f?(g.failure={_custom:{type:Error,readOnly:!0,display:f?f.message:"",tooltip:"Navigation Failure",value:f}},g.status=Cr("❌")):g.status=Cr("✅"),g.from=jn(d,"Current Location during this navigation"),g.to=jn(c,"Target location"),s.addTimelineEvent({layerId:o,event:{title:"End of navigation",subtitle:c.fullPath,time:s.now(),data:g,logType:f?"warning":"default",groupId:c.meta.__navigationId}})});const l="router-inspector:"+r;s.addInspector({id:l,label:"Routes"+(r?" "+r:""),icon:"book",treeFilterPlaceholder:"Search routes"});function a(){if(!u)return;const c=u;let d=n.getRoutes().filter(f=>!f.parent||!f.parent.record.components);d.forEach(eu),c.filter&&(d=d.filter(f=>wo(f,c.filter.toLowerCase()))),d.forEach(f=>Zc(f,t.currentRoute.value)),c.rootNodes=d.map(Xc)}let u;s.on.getInspectorTree(c=>{u=c,c.app===e&&c.inspectorId===l&&a()}),s.on.getInspectorState(c=>{if(c.app===e&&c.inspectorId===l){const f=n.getRoutes().find(g=>g.record.__vd_id===c.nodeId);f&&(c.state={options:Ag(f)})}}),s.sendInspectorTree(l),s.sendInspectorState(l)})}function Rg(e){return e.optional?e.repeatable?"*":"?":e.repeatable?"+":""}function Ag(e){const{record:t}=e,n=[{editable:!1,key:"path",value:t.path}];return t.name!=null&&n.push({editable:!1,key:"name",value:t.name}),n.push({editable:!1,key:"regexp",value:e.re}),e.keys.length&&n.push({editable:!1,key:"keys",value:{_custom:{type:null,readOnly:!0,display:e.keys.map(r=>`${r.name}${Rg(r)}`).join(" "),tooltip:"Param keys",value:e.keys}}}),t.redirect!=null&&n.push({editable:!1,key:"redirect",value:t.redirect}),e.alias.length&&n.push({editable:!1,key:"aliases",value:e.alias.map(r=>r.record.path)}),Object.keys(e.record.meta).length&&n.push({editable:!1,key:"meta",value:e.record.meta}),n.push({key:"score",editable:!1,value:{_custom:{type:null,readOnly:!0,display:e.score.map(r=>r.join(", ")).join(" | "),tooltip:"Score used to sort routes",value:e.score}}}),n}const Gc=15485081,Yc=2450411,Jc=8702998,Og=2282478,Qc=16486972,kg=6710886,Lg=16704226,Ig=12131356;function Xc(e){const t=[],{record:n}=e;n.name!=null&&t.push({label:String(n.name),textColor:0,backgroundColor:Og}),n.aliasOf&&t.push({label:"alias",textColor:0,backgroundColor:Qc}),e.__vd_match&&t.push({label:"matches",textColor:0,backgroundColor:Gc}),e.__vd_exactActive&&t.push({label:"exact",textColor:0,backgroundColor:Jc}),e.__vd_active&&t.push({label:"active",textColor:0,backgroundColor:Yc}),n.redirect&&t.push({label:typeof n.redirect=="string"?`redirect: ${n.redirect}`:"redirects",textColor:16777215,backgroundColor:kg});let r=n.__vd_id;return r==null&&(r=String($g++),n.__vd_id=r),{id:r,label:n.path,tags:t,children:e.children.map(Xc)}}let $g=0;const Mg=/^\/(.*)\/([a-z]*)$/;function Zc(e,t){const n=t.matched.length&&Bt(t.matched[t.matched.length-1],e.record);e.__vd_exactActive=e.__vd_active=n,n||(e.__vd_active=t.matched.some(r=>Bt(r,e.record))),e.children.forEach(r=>Zc(r,t))}function eu(e){e.__vd_match=!1,e.children.forEach(eu)}function wo(e,t){const n=String(e.re).match(Mg);if(e.__vd_match=!1,!n||n.length<3)return!1;if(new RegExp(n[1].replace(/\$$/,""),n[2]).test(t))return e.children.forEach(i=>wo(i,t)),e.record.path!=="/"||t==="/"?(e.__vd_match=e.re.test(t),!0):!1;const s=e.record.path.toLowerCase(),o=An(s);return!t.startsWith("/")&&(o.includes(t)||s.includes(t))||o.startsWith(t)||s.startsWith(t)||e.record.name&&String(e.record.name).includes(t)?!0:e.children.some(i=>wo(i,t))}function Ng(e,t){const n={};for(const r in e)t.includes(r)||(n[r]=e[r]);return n}function jg(e){const t=dg(e.routes,e),n=e.parseQuery||vg,r=e.stringifyQuery||ml,s=e.history,o=Nn(),i=Nn(),l=Nn(),a=cf(Rt);let u=Rt;yt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=Ds.bind(null,b=>""+b),d=Ds.bind(null,Fp),f=Ds.bind(null,An);function g(b,H){let D,V;return Uc(b)?(D=t.getRecordMatcher(b),V=H):V=b,t.addRoute(V,D)}function v(b){const H=t.getRecordMatcher(b);H&&t.removeRoute(H)}function x(){return t.getRoutes().map(b=>b.record)}function T(b){return!!t.getRecordMatcher(b)}function R(b,H){if(H=se({},H||a.value),typeof b=="string"){const p=Fs(n,b,H.path),m=t.resolve({path:p.path},H),E=s.createHref(p.fullPath);return se(p,m,{params:f(m.params),hash:An(p.hash),redirectedFrom:void 0,href:E})}let D;if(b.path!=null)D=se({},b,{path:Fs(n,b.path,H.path).path});else{const p=se({},b.params);for(const m in p)p[m]==null&&delete p[m];D=se({},b,{params:d(p)}),H.params=d(H.params)}const V=t.resolve(D,H),ie=b.hash||"";V.params=c(f(V.params));const pe=Bp(r,se({},b,{hash:Np(ie),path:V.path})),h=s.createHref(pe);return se({fullPath:pe,hash:ie,query:r===ml?_g(b.query):b.query||{}},V,{redirectedFrom:void 0,href:h})}function w(b){return typeof b=="string"?Fs(n,b,a.value.path):se({},b)}function S(b,H){if(u!==b)return On(8,{from:H,to:b})}function y(b){return j(b)}function I(b){return y(se(w(b),{replace:!0}))}function B(b){const H=b.matched[b.matched.length-1];if(H&&H.redirect){const{redirect:D}=H;let V=typeof D=="function"?D(b):D;return typeof V=="string"&&(V=V.includes("?")||V.includes("#")?V=w(V):{path:V},V.params={}),se({query:b.query,hash:b.hash,params:V.path!=null?{}:b.params},V)}}function j(b,H){const D=u=R(b),V=a.value,ie=b.state,pe=b.force,h=b.replace===!0,p=B(D);if(p)return j(se(w(p),{state:typeof p=="object"?se({},ie,p.state):ie,force:pe,replace:h}),H||D);const m=D;m.redirectedFrom=H;let E;return!pe&&Vp(r,V,D)&&(E=On(16,{to:m,from:V}),ot(V,V,!0,!1)),(E?Promise.resolve(E):$(m,V)).catch(_=>mt(_)?mt(_,2)?_:Ct(_):te(_,m,V)).then(_=>{if(_){if(mt(_,2))return j(se({replace:h},w(_.to),{state:typeof _.to=="object"?se({},ie,_.to.state):ie,force:pe}),H||m)}else _=O(m,V,!0,h,ie);return L(m,V,_),_})}function X(b,H){const D=S(b,H);return D?Promise.reject(D):Promise.resolve()}function F(b){const H=hn.values().next().value;return H&&typeof H.runWithContext=="function"?H.runWithContext(b):b()}function $(b,H){let D;const[V,ie,pe]=Dg(b,H);D=qs(V.reverse(),"beforeRouteLeave",b,H);for(const p of V)p.leaveGuards.forEach(m=>{D.push(Mt(m,b,H))});const h=X.bind(null,b,H);return D.push(h),Ue(D).then(()=>{D=[];for(const p of o.list())D.push(Mt(p,b,H));return D.push(h),Ue(D)}).then(()=>{D=qs(ie,"beforeRouteUpdate",b,H);for(const p of ie)p.updateGuards.forEach(m=>{D.push(Mt(m,b,H))});return D.push(h),Ue(D)}).then(()=>{D=[];for(const p of pe)if(p.beforeEnter)if(Ve(p.beforeEnter))for(const m of p.beforeEnter)D.push(Mt(m,b,H));else D.push(Mt(p.beforeEnter,b,H));return D.push(h),Ue(D)}).then(()=>(b.matched.forEach(p=>p.enterCallbacks={}),D=qs(pe,"beforeRouteEnter",b,H,F),D.push(h),Ue(D))).then(()=>{D=[];for(const p of i.list())D.push(Mt(p,b,H));return D.push(h),Ue(D)}).catch(p=>mt(p,8)?p:Promise.reject(p))}function L(b,H,D){l.list().forEach(V=>F(()=>V(b,H,D)))}function O(b,H,D,V,ie){const pe=S(b,H);if(pe)return pe;const h=H===Rt,p=yt?history.state:{};D&&(V||h?s.replace(b.fullPath,se({scroll:h&&p&&p.scroll},ie)):s.push(b.fullPath,ie)),a.value=b,ot(b,H,D,h),Ct()}let Q;function M(){Q||(Q=s.listen((b,H,D)=>{if(!gr.listening)return;const V=R(b),ie=B(V);if(ie){j(se(ie,{replace:!0}),V).catch(Zn);return}u=V;const pe=a.value;yt&&Qp(ll(pe.fullPath,D.delta),ys()),$(V,pe).catch(h=>mt(h,12)?h:mt(h,2)?(j(h.to,V).then(p=>{mt(p,20)&&!D.delta&&D.type===cr.pop&&s.go(-1,!1)}).catch(Zn),Promise.reject()):(D.delta&&s.go(-D.delta,!1),te(h,V,pe))).then(h=>{h=h||O(V,pe,!1),h&&(D.delta&&!mt(h,8)?s.go(-D.delta,!1):D.type===cr.pop&&mt(h,20)&&s.go(-1,!1)),L(V,pe,h)}).catch(Zn)}))}let ee=Nn(),ae=Nn(),re;function te(b,H,D){Ct(b);const V=ae.list();return V.length?V.forEach(ie=>ie(b,H,D)):console.error(b),Promise.reject(b)}function ge(){return re&&a.value!==Rt?Promise.resolve():new Promise((b,H)=>{ee.add([b,H])})}function Ct(b){return re||(re=!b,M(),ee.list().forEach(([H,D])=>b?D(b):H()),ee.reset()),b}function ot(b,H,D,V){const{scrollBehavior:ie}=e;if(!yt||!ie)return Promise.resolve();const pe=!D&&Xp(ll(b.fullPath,0))||(V||!D)&&history.state&&history.state.scroll||null;return Bo().then(()=>ie(b,H,pe)).then(h=>h&&Jp(h)).catch(h=>te(h,b,H))}const Ne=b=>s.go(b);let dn;const hn=new Set,gr={currentRoute:a,listening:!0,addRoute:g,removeRoute:v,clearRoutes:t.clearRoutes,hasRoute:T,getRoutes:x,resolve:R,options:e,push:y,replace:I,go:Ne,back:()=>Ne(-1),forward:()=>Ne(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:ae.add,isReady:ge,install(b){const H=this;b.component("RouterLink",wg),b.component("RouterView",Cg),b.config.globalProperties.$router=H,Object.defineProperty(b.config.globalProperties,"$route",{enumerable:!0,get:()=>wt(a)}),yt&&!dn&&a.value===Rt&&(dn=!0,y(s.location).catch(ie=>{}));const D={};for(const ie in Rt)Object.defineProperty(D,ie,{get:()=>a.value[ie],enumerable:!0});b.provide(bs,H),b.provide(ni,fa(D)),b.provide(bo,a);const V=b.unmount;hn.add(b),b.unmount=function(){hn.delete(b),hn.size<1&&(u=Rt,Q&&Q(),Q=null,a.value=Rt,dn=!1,re=!1),V()},yt&&Pg(b,H,t)}};function Ue(b){return b.reduce((H,D)=>H.then(()=>F(D)),Promise.resolve())}return gr}function Dg(e,t){const n=[],r=[],s=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(u=>Bt(u,l))?r.push(l):n.push(l));const a=e.matched[i];a&&(t.matched.find(u=>Bt(u,a))||s.push(a))}return[n,r,s]}function dv(){return Je(bs)}function hv(e){return Je(ni)}const Fg=[{path:"/",name:"Layouts",component:()=>Ge(()=>import("./main-DBHZUh_w.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11])),meta:{requireAuth:!0,keepAlive:!0},children:[{path:"/",component:()=>Ge(()=>import("./index-DbZtx4KH.js"),__vite__mapDeps([12,9,5,10,7,13]))}]},{path:"/chat",component:()=>Ge(()=>import("./chat-DFFgbBvu.js"),__vite__mapDeps([14,1,2,4,5,15])),meta:{requireAuth:!1,keepAlive:!1}},{path:"/login",component:()=>Ge(()=>import("./login-DSMOSE6r.js"),__vite__mapDeps([16,6,5,4,17,10,7,18]))},{path:"/:catchAll(.*)*",component:()=>Ge(()=>import("./404-D6Pun2F2.js"),[])}];function qg(e){return qh(e)===!0?"__q_date|"+e.getTime():Hh(e)===!0?"__q_expr|"+e.source:typeof e=="number"?"__q_numb|"+e:typeof e=="boolean"?"__q_bool|"+(e?"1":"0"):typeof e=="string"?"__q_strn|"+e:typeof e=="function"?"__q_strn|"+e.toString():e===Object(e)?"__q_objt|"+JSON.stringify(e):e}function Hg(e){if(e.length<9)return e;const n=e.substring(0,8),r=e.substring(9);switch(n){case"__q_date":const s=Number(r);return new Date(Number.isNaN(s)===!0?r:s);case"__q_expr":return new RegExp(r);case"__q_numb":return Number(r);case"__q_bool":return r==="1";case"__q_strn":return""+r;case"__q_objt":return JSON.parse(r);default:return e}}function Bg(){const e=()=>null;return{has:()=>!1,hasItem:()=>!1,getLength:()=>0,getItem:e,getIndex:e,getKey:e,getAll:()=>{},getAllKeys:()=>[],set:ut,setItem:ut,remove:ut,removeItem:ut,clear:ut,isEmpty:()=>!0}}function Vg(e){const t=window[e+"Storage"],n=i=>{const l=t.getItem(i);return l?Hg(l):null},r=i=>t.getItem(i)!==null,s=(i,l)=>{t.setItem(i,qg(l))},o=i=>{t.removeItem(i)};return{has:r,hasItem:r,getLength:()=>t.length,getItem:n,getIndex:i=>i<t.length?n(t.key(i)):null,getKey:i=>i<t.length?t.key(i):null,getAll:()=>{let i;const l={},a=t.length;for(let u=0;u<a;u++)i=t.key(u),l[i]=n(i);return l},getAllKeys:()=>{const i=[],l=t.length;for(let a=0;a<l;a++)i.push(t.key(a));return i},set:s,setItem:s,remove:o,removeItem:o,clear:()=>{t.clear()},isEmpty:()=>t.length===0}}const tu=Ce.has.webStorage===!1?Bg():Vg("local"),je={install({$q:e}){e.localStorage=tu}};Object.assign(je,tu);const Tr="_userToken",Hs="_userInfo",Bs="_userMenus",Vs="_userRouters",Ug=$c("users",{state:()=>({userToken:je.getItem(Tr)||"",userInfo:JSON.parse(je.getItem(Hs)||"{}"),routers:JSON.parse(je.getItem(Vs)||"[]"),menus:JSON.parse(je.getItem(Bs)||"[]"),isRoutesAdded:!1}),getters:{isAuthenticated(){return!!this.userToken}},actions:{setUserToken(e){this.userToken=e,je.set(Tr,e)},clearUserToken(){this.userToken="",je.remove(Tr)},setUserInfo(e){this.userInfo=e,je.set(Hs,JSON.stringify(e))},setRouters(e){this.routers=e,je.set(Vs,JSON.stringify(e))},setMenus(e){this.menus=e,je.set(Bs,JSON.stringify(e))},setRoutesAdded(e){this.isRoutesAdded=e},clearAllUserData(){this.userToken="",this.userInfo={},this.routers=[],this.menus=[],je.remove(Tr),je.remove(Hs),je.remove(Vs),je.remove(Bs)},hasRoute(e){return e=e.split("?")[0],this.routers.includes(e)}}}),Kg=$c("tabs",{state:()=>{const e=Math.random().toString();return{tabs:new Map([["/",{key:"/"+e,label:"控制台",route:"/"}]])}},getters:{},actions:{addTab(e){if(this.tabs.has(e.route)||!e.label)return;const t=Math.random().toString();e.key=e.route+t,e.label=e.label.split("-")[0],this.tabs.set(e.route,e)},removeTab(e,t){if(e.currentRoute.value.fullPath===t){const n=this.getParentTab(t);e.push(n),this.tabs.delete(e.currentRoute.value.fullPath)}else this.tabs.delete(t)},getParentTab(e){const t=Array.from(this.tabs.keys()),n=t.indexOf(e);return n>0?t[n-1]:"/"},initTabs(){this.tabs=new Map([["/",{key:"/",label:"控制台",route:"/"}]])}}}),Us=function(){const t=jg({scrollBehavior:()=>({left:0,top:0}),routes:Fg,history:rg("/admin/")});return t.beforeEach(async(n,r,s)=>{const o=Ug(),i=Kg();if(o.userToken&&n.path==="/login"){s("/");return}if(n.meta.requireAuth&&!o.userToken){s("/login");return}o.isRoutesAdded?(i.addTab({key:n.path,label:n.name,route:n.path}),s()):(await nu(t,o.menus),o.setRoutesAdded(!0),s({...n,replace:!0}))}),t},zg=Object.assign({"../pages/404.vue":()=>Ge(()=>import("./404-D6Pun2F2.js"),[]),"../pages/chat.vue":()=>Ge(()=>import("./chat-DFFgbBvu.js"),__vite__mapDeps([14,1,2,4,5,15])),"../pages/index.vue":()=>Ge(()=>import("./index-DbZtx4KH.js"),__vite__mapDeps([12,9,5,10,7,13])),"../pages/login.vue":()=>Ge(()=>import("./login-DSMOSE6r.js"),__vite__mapDeps([16,6,5,4,17,10,7,18])),"../pages/table.vue":()=>Ge(()=>import("./table-9vg9nFPe.js"),__vite__mapDeps([19,17,6,5,4,3,2,7,8,9,10,13,20]))}),nu=(e,t)=>new Promise(n=>{t.forEach(r=>{if(r.route!=""&&r.data.template!=""){const s={path:r.route,name:r.name+"-"+r.id,component:zg["../pages/"+r.data.vueFile],meta:{requireAuth:!0,keepAlive:!0}};e.addRoute("Layouts",s)}r.children&&r.children.length>0&&nu(e,r.children)}),n()});async function Wg(e,t){const n=e(Gh);n.use(Uh,t);const r=typeof js=="function"?await js({}):js;n.use(r);const s=ht(typeof Us=="function"?await Us({store:r}):Us);return r.use(({store:o})=>{o.router=s}),{app:n,store:r,router:s}}const So={xs:18,sm:24,md:32,lg:38,xl:46},ri={size:String};function si(e,t=So){return G(()=>e.size!==void 0?{fontSize:e.size in t?`${t[e.size]}px`:e.size}:null)}function Gg(e,t){return e!==void 0&&e()||t}function pv(e,t){if(e!==void 0){const n=e();if(n!=null)return n.slice()}return t}function Un(e,t){return e!==void 0?t.concat(e()):t}function Yg(e,t){return e===void 0?t:t!==void 0?t.concat(e()):e()}function gv(e,t,n,r,s,o){t.key=r+s;const i=K(e,t,n);return s===!0?Sa(i,o()):i}const Sl="0 0 24 24",El=e=>e,Ks=e=>`ionicons ${e}`,ru={"mdi-":e=>`mdi ${e}`,"icon-":El,"bt-":e=>`bt ${e}`,"eva-":e=>`eva ${e}`,"ion-md":Ks,"ion-ios":Ks,"ion-logo":Ks,"iconfont ":El,"ti-":e=>`themify-icon ${e}`,"bi-":e=>`bootstrap-icons ${e}`},su={o_:"-outlined",r_:"-round",s_:"-sharp"},ou={sym_o_:"-outlined",sym_r_:"-rounded",sym_s_:"-sharp"},Jg=new RegExp("^("+Object.keys(ru).join("|")+")"),Qg=new RegExp("^("+Object.keys(su).join("|")+")"),xl=new RegExp("^("+Object.keys(ou).join("|")+")"),Xg=/^[Mm]\s?[-+]?\.?\d/,Zg=/^img:/,em=/^svguse:/,tm=/^ion-/,nm=/^(fa-(classic|sharp|solid|regular|light|brands|duotone|thin)|[lf]a[srlbdk]?) /,ts=pr({name:"QIcon",props:{...ri,tag:{type:String,default:"i"},name:String,color:String,left:Boolean,right:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=fn(),r=si(e),s=G(()=>"q-icon"+(e.left===!0?" on-left":"")+(e.right===!0?" on-right":"")+(e.color!==void 0?` text-${e.color}`:"")),o=G(()=>{let i,l=e.name;if(l==="none"||!l)return{none:!0};if(n.iconMapFn!==null){const c=n.iconMapFn(l);if(c!==void 0)if(c.icon!==void 0){if(l=c.icon,l==="none"||!l)return{none:!0}}else return{cls:c.cls,content:c.content!==void 0?c.content:" "}}if(Xg.test(l)===!0){const[c,d=Sl]=l.split("|");return{svg:!0,viewBox:d,nodes:c.split("&&").map(f=>{const[g,v,x]=f.split("@@");return K("path",{style:v,d:g,transform:x})})}}if(Zg.test(l)===!0)return{img:!0,src:l.substring(4)};if(em.test(l)===!0){const[c,d=Sl]=l.split("|");return{svguse:!0,src:c.substring(7),viewBox:d}}let a=" ";const u=l.match(Jg);if(u!==null)i=ru[u[1]](l);else if(nm.test(l)===!0)i=l;else if(tm.test(l)===!0)i=`ionicons ion-${n.platform.is.ios===!0?"ios":"md"}${l.substring(3)}`;else if(xl.test(l)===!0){i="notranslate material-symbols";const c=l.match(xl);c!==null&&(l=l.substring(6),i+=ou[c[1]]),a=l}else{i="notranslate material-icons";const c=l.match(Qg);c!==null&&(l=l.substring(2),i+=su[c[1]]),a=l}return{cls:i,content:a}});return()=>{const i={class:s.value,style:r.value,"aria-hidden":"true",role:"presentation"};return o.value.none===!0?K(e.tag,i,Gg(t.default)):o.value.img===!0?K(e.tag,i,Un(t.default,[K("img",{src:o.value.src})])):o.value.svg===!0?K(e.tag,i,Un(t.default,[K("svg",{viewBox:o.value.viewBox||"0 0 24 24"},o.value.nodes)])):o.value.svguse===!0?K(e.tag,i,Un(t.default,[K("svg",{viewBox:o.value.viewBox},[K("use",{"xlink:href":o.value.src})])])):(o.value.cls!==void 0&&(i.class+=" "+o.value.cls),K(e.tag,i,Un(t.default,[o.value.content])))}}}),rm=pr({name:"QAvatar",props:{...ri,fontSize:String,color:String,textColor:String,icon:String,square:Boolean,rounded:Boolean},setup(e,{slots:t}){const n=si(e),r=G(()=>"q-avatar"+(e.color?` bg-${e.color}`:"")+(e.textColor?` text-${e.textColor} q-chip--colored`:"")+(e.square===!0?" q-avatar--square":e.rounded===!0?" rounded-borders":"")),s=G(()=>e.fontSize?{fontSize:e.fontSize}:null);return()=>{const o=e.icon!==void 0?[K(ts,{name:e.icon})]:void 0;return K("div",{class:r.value,style:n.value},[K("div",{class:"q-avatar__content row flex-center overflow-hidden",style:s.value},Yg(t.default,o))])}}}),sm={size:{type:[String,Number],default:"1em"},color:String};function om(e){return{cSize:G(()=>e.size in So?`${So[e.size]}px`:e.size),classes:G(()=>"q-spinner"+(e.color?` text-${e.color}`:""))}}const oi=pr({name:"QSpinner",props:{...sm,thickness:{type:Number,default:5}},setup(e){const{cSize:t,classes:n}=om(e);return()=>K("svg",{class:n.value+" q-spinner-mat",width:t.value,height:t.value,viewBox:"25 25 50 50"},[K("circle",{class:"path",cx:"50",cy:"50",r:"20",fill:"none",stroke:"currentColor","stroke-width":e.thickness,"stroke-miterlimit":"10"})])}});function mv(e){return e===window?window.innerHeight:e.getBoundingClientRect().height}function Eo(e,t){const n=e.style;for(const r in t)n[r]=t[r]}function im(e){if(e==null)return;if(typeof e=="string")try{return document.querySelector(e)||void 0}catch{return}const t=wt(e);if(t)return t.$el||t}function vv(e,t){if(e==null||e.contains(t)===!0)return!0;for(let n=e.nextElementSibling;n!==null;n=n.nextElementSibling)if(n.contains(t))return!0;return!1}function lm(e,t=250){let n=!1,r;return function(){return n===!1&&(n=!0,setTimeout(()=>{n=!1},t),r=e.apply(this,arguments)),r}}function Cl(e,t,n,r){n.modifiers.stop===!0&&gc(e);const s=n.modifiers.color;let o=n.modifiers.center;o=o===!0||r===!0;const i=document.createElement("span"),l=document.createElement("span"),a=bh(e),{left:u,top:c,width:d,height:f}=t.getBoundingClientRect(),g=Math.sqrt(d*d+f*f),v=g/2,x=`${(d-g)/2}px`,T=o?x:`${a.left-u-v}px`,R=`${(f-g)/2}px`,w=o?R:`${a.top-c-v}px`;l.className="q-ripple__inner",Eo(l,{height:`${g}px`,width:`${g}px`,transform:`translate3d(${T},${w},0) scale3d(.2,.2,1)`,opacity:0}),i.className=`q-ripple${s?" text-"+s:""}`,i.setAttribute("dir","ltr"),i.appendChild(l),t.appendChild(i);const S=()=>{i.remove(),clearTimeout(y)};n.abort.push(S);let y=setTimeout(()=>{l.classList.add("q-ripple__inner--enter"),l.style.transform=`translate3d(${x},${R},0) scale3d(1,1,1)`,l.style.opacity=.2,y=setTimeout(()=>{l.classList.remove("q-ripple__inner--enter"),l.classList.add("q-ripple__inner--leave"),l.style.opacity=0,y=setTimeout(()=>{i.remove(),n.abort.splice(n.abort.indexOf(S),1)},275)},250)},50)}function Tl(e,{modifiers:t,value:n,arg:r}){const s=Object.assign({},e.cfg.ripple,t,n);e.modifiers={early:s.early===!0,stop:s.stop===!0,center:s.center===!0,color:s.color||r,keyCodes:[].concat(s.keyCodes||13)}}const am=yh({name:"ripple",beforeMount(e,t){const n=t.instance.$.appContext.config.globalProperties.$q.config||{};if(n.ripple===!1)return;const r={cfg:n,enabled:t.value!==!1,modifiers:{},abort:[],start(s){r.enabled===!0&&s.qSkipRipple!==!0&&s.type===(r.modifiers.early===!0?"pointerdown":"click")&&Cl(s,e,r,s.qKeyEvent===!0)},keystart:lm(s=>{r.enabled===!0&&s.qSkipRipple!==!0&&po(s,r.modifiers.keyCodes)===!0&&s.type===`key${r.modifiers.early===!0?"down":"up"}`&&Cl(s,e,r,!0)},300)};Tl(r,t),e.__qripple=r,Sh(r,"main",[[e,"pointerdown","start","passive"],[e,"click","start","passive"],[e,"keydown","keystart","passive"],[e,"keyup","keystart","passive"]])},updated(e,t){if(t.oldValue!==t.value){const n=e.__qripple;n!==void 0&&(n.enabled=t.value!==!1,n.enabled===!0&&Object(t.value)===t.value&&Tl(n,t))}},beforeUnmount(e){const t=e.__qripple;t!==void 0&&(t.abort.forEach(n=>{n()}),Eh(t,"main"),delete e._qripple)}}),iu={left:"start",center:"center",right:"end",between:"between",around:"around",evenly:"evenly",stretch:"stretch"},cm=Object.keys(iu),um={align:{type:String,validator:e=>cm.includes(e)}};function fm(e){return G(()=>{const t=e.align===void 0?e.vertical===!0?"stretch":"left":e.align;return`${e.vertical===!0?"items":"justify"}-${iu[t]}`})}function _v(e){if(Object(e.$parent)===e.$parent)return e.$parent;let{parent:t}=e.$;for(;Object(t)===t;){if(Object(t.proxy)===t.proxy)return t.proxy;t=t.parent}}function lu(e,t){typeof t.type=="symbol"?Array.isArray(t.children)===!0&&t.children.forEach(n=>{lu(e,n)}):e.add(t)}function yv(e){const t=new Set;return e.forEach(n=>{lu(t,n)}),Array.from(t)}function dm(e){return e.appContext.config.globalProperties.$router!==void 0}function bv(e){return e.isUnmounted===!0||e.isDeactivated===!0}function Pl(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}function Rl(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function hm(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(Array.isArray(s)===!1||s.length!==r.length||r.some((o,i)=>o!==s[i]))return!1}return!0}function Al(e,t){return Array.isArray(t)===!0?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function pm(e,t){return Array.isArray(e)===!0?Al(e,t):Array.isArray(t)===!0?Al(t,e):e===t}function gm(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(pm(e[n],t[n])===!1)return!1;return!0}const au={to:[String,Object],replace:Boolean,href:String,target:String,disable:Boolean},wv={...au,exact:Boolean,activeClass:{type:String,default:"q-router-link--active"},exactActiveClass:{type:String,default:"q-router-link--exact-active"}};function mm({fallbackTag:e,useDisableForRouterLinkProps:t=!0}={}){const n=fn(),{props:r,proxy:s,emit:o}=n,i=dm(n),l=G(()=>r.disable!==!0&&r.href!==void 0),a=G(t===!0?()=>i===!0&&r.disable!==!0&&l.value!==!0&&r.to!==void 0&&r.to!==null&&r.to!=="":()=>i===!0&&l.value!==!0&&r.to!==void 0&&r.to!==null&&r.to!==""),u=G(()=>a.value===!0?w(r.to):null),c=G(()=>u.value!==null),d=G(()=>l.value===!0||c.value===!0),f=G(()=>r.type==="a"||d.value===!0?"a":r.tag||e||"div"),g=G(()=>l.value===!0?{href:r.href,target:r.target}:c.value===!0?{href:u.value.href,target:r.target}:{}),v=G(()=>{if(c.value===!1)return-1;const{matched:I}=u.value,{length:B}=I,j=I[B-1];if(j===void 0)return-1;const X=s.$route.matched;if(X.length===0)return-1;const F=X.findIndex(Rl.bind(null,j));if(F!==-1)return F;const $=Pl(I[B-2]);return B>1&&Pl(j)===$&&X[X.length-1].path!==$?X.findIndex(Rl.bind(null,I[B-2])):F}),x=G(()=>c.value===!0&&v.value!==-1&&hm(s.$route.params,u.value.params)),T=G(()=>x.value===!0&&v.value===s.$route.matched.length-1&&gm(s.$route.params,u.value.params)),R=G(()=>c.value===!0?T.value===!0?` ${r.exactActiveClass} ${r.activeClass}`:r.exact===!0?"":x.value===!0?` ${r.activeClass}`:"":"");function w(I){try{return s.$router.resolve(I)}catch{}return null}function S(I,{returnRouterError:B,to:j=r.to,replace:X=r.replace}={}){if(r.disable===!0)return I.preventDefault(),Promise.resolve(!1);if(I.metaKey||I.altKey||I.ctrlKey||I.shiftKey||I.button!==void 0&&I.button!==0||r.target==="_blank")return Promise.resolve(!1);I.preventDefault();const F=s.$router[X===!0?"replace":"push"](j);return B===!0?F:F.then(()=>{}).catch(()=>{})}function y(I){if(c.value===!0){const B=j=>S(I,j);o("click",I,B),I.defaultPrevented!==!0&&B()}else o("click",I)}return{hasRouterLink:c,hasHrefLink:l,hasLink:d,linkTag:f,resolvedLink:u,linkIsActive:x,linkIsExactActive:T,linkClass:R,linkAttrs:g,getLink:w,navigateToRouterLink:S,navigateOnClick:y}}const Ol={none:0,xs:4,sm:8,md:16,lg:24,xl:32},vm={xs:8,sm:10,md:14,lg:20,xl:24},_m=["button","submit","reset"],ym=/[^\s]\/[^\s]/,bm=["flat","outline","push","unelevated"];function cu(e,t){return e.flat===!0?"flat":e.outline===!0?"outline":e.push===!0?"push":e.unelevated===!0?"unelevated":t}function Sv(e){const t=cu(e);return t!==void 0?{[t]:!0}:{}}const wm={...ri,...au,type:{type:String,default:"button"},label:[Number,String],icon:String,iconRight:String,...bm.reduce((e,t)=>(e[t]=Boolean)&&e,{}),square:Boolean,rounded:Boolean,glossy:Boolean,size:String,fab:Boolean,fabMini:Boolean,padding:String,color:String,textColor:String,noCaps:Boolean,noWrap:Boolean,dense:Boolean,tabindex:[Number,String],ripple:{type:[Boolean,Object],default:!0},align:{...um.align,default:"center"},stack:Boolean,stretch:Boolean,loading:{type:Boolean,default:null},disable:Boolean},Sm={...wm,round:Boolean};function Em(e){const t=si(e,vm),n=fm(e),{hasRouterLink:r,hasLink:s,linkTag:o,linkAttrs:i,navigateOnClick:l}=mm({fallbackTag:"button"}),a=G(()=>{const T=e.fab===!1&&e.fabMini===!1?t.value:{};return e.padding!==void 0?Object.assign({},T,{padding:e.padding.split(/\s+/).map(R=>R in Ol?Ol[R]+"px":R).join(" "),minWidth:"0",minHeight:"0"}):T}),u=G(()=>e.rounded===!0||e.fab===!0||e.fabMini===!0),c=G(()=>e.disable!==!0&&e.loading!==!0),d=G(()=>c.value===!0?e.tabindex||0:-1),f=G(()=>cu(e,"standard")),g=G(()=>{const T={tabindex:d.value};return s.value===!0?Object.assign(T,i.value):_m.includes(e.type)===!0&&(T.type=e.type),o.value==="a"?(e.disable===!0?T["aria-disabled"]="true":T.href===void 0&&(T.role="button"),r.value!==!0&&ym.test(e.type)===!0&&(T.type=e.type)):e.disable===!0&&(T.disabled="",T["aria-disabled"]="true"),e.loading===!0&&e.percentage!==void 0&&Object.assign(T,{role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":e.percentage}),T}),v=G(()=>{let T;e.color!==void 0?e.flat===!0||e.outline===!0?T=`text-${e.textColor||e.color}`:T=`bg-${e.color} text-${e.textColor||"white"}`:e.textColor&&(T=`text-${e.textColor}`);const R=e.round===!0?"round":`rectangle${u.value===!0?" q-btn--rounded":e.square===!0?" q-btn--square":""}`;return`q-btn--${f.value} q-btn--${R}`+(T!==void 0?" "+T:"")+(c.value===!0?" q-btn--actionable q-focusable q-hoverable":e.disable===!0?" disabled":"")+(e.fab===!0?" q-btn--fab":e.fabMini===!0?" q-btn--fab-mini":"")+(e.noCaps===!0?" q-btn--no-uppercase":"")+(e.dense===!0?" q-btn--dense":"")+(e.stretch===!0?" no-border-radius self-stretch":"")+(e.glossy===!0?" glossy":"")+(e.square?" q-btn--square":"")}),x=G(()=>n.value+(e.stack===!0?" column":" row")+(e.noWrap===!0?" no-wrap text-no-wrap":"")+(e.loading===!0?" q-btn__content--hidden":""));return{classes:v,style:a,innerClasses:x,attributes:g,hasLink:s,linkTag:o,navigateOnClick:l,isActionable:c}}const{passiveCapture:ze}=De;let vn=null,_n=null,yn=null;const xm=pr({name:"QBtn",props:{...Sm,percentage:Number,darkPercentage:Boolean,onTouchstart:[Function,Array]},emits:["click","keydown","mousedown","keyup"],setup(e,{slots:t,emit:n}){const{proxy:r}=fn(),{classes:s,style:o,innerClasses:i,attributes:l,hasLink:a,linkTag:u,navigateOnClick:c,isActionable:d}=Em(e),f=ln(null),g=ln(null);let v=null,x,T=null;const R=G(()=>e.label!==void 0&&e.label!==null&&e.label!==""),w=G(()=>e.disable===!0||e.ripple===!1?!1:{keyCodes:a.value===!0?[13,32]:[13],...e.ripple===!0?{}:e.ripple}),S=G(()=>({center:e.round})),y=G(()=>{const M=Math.max(0,Math.min(100,e.percentage));return M>0?{transition:"transform 0.6s",transform:`translateX(${M-100}%)`}:{}}),I=G(()=>{if(e.loading===!0)return{onMousedown:Q,onTouchstart:Q,onClick:Q,onKeydown:Q,onKeyup:Q};if(d.value===!0){const M={onClick:j,onKeydown:X,onMousedown:$};if(r.$q.platform.has.touch===!0){const ee=e.onTouchstart!==void 0?"":"Passive";M[`onTouchstart${ee}`]=F}return M}return{onClick:Zt}}),B=G(()=>({ref:f,class:"q-btn q-btn-item non-selectable no-outline "+s.value,style:o.value,...l.value,...I.value}));function j(M){if(f.value!==null){if(M!==void 0){if(M.defaultPrevented===!0)return;const ee=document.activeElement;if(e.type==="submit"&&ee!==document.body&&f.value.contains(ee)===!1&&ee.contains(f.value)===!1){f.value.focus();const ae=()=>{document.removeEventListener("keydown",Zt,!0),document.removeEventListener("keyup",ae,ze),f.value!==null&&f.value.removeEventListener("blur",ae,ze)};document.addEventListener("keydown",Zt,!0),document.addEventListener("keyup",ae,ze),f.value.addEventListener("blur",ae,ze)}}c(M)}}function X(M){f.value!==null&&(n("keydown",M),po(M,[13,32])===!0&&_n!==f.value&&(_n!==null&&O(),M.defaultPrevented!==!0&&(f.value.focus(),_n=f.value,f.value.classList.add("q-btn--active"),document.addEventListener("keyup",L,!0),f.value.addEventListener("blur",L,ze)),Zt(M)))}function F(M){f.value!==null&&(n("touchstart",M),M.defaultPrevented!==!0&&(vn!==f.value&&(vn!==null&&O(),vn=f.value,v=M.target,v.addEventListener("touchcancel",L,ze),v.addEventListener("touchend",L,ze)),x=!0,T!==null&&clearTimeout(T),T=setTimeout(()=>{T=null,x=!1},200)))}function $(M){f.value!==null&&(M.qSkipRipple=x===!0,n("mousedown",M),M.defaultPrevented!==!0&&yn!==f.value&&(yn!==null&&O(),yn=f.value,f.value.classList.add("q-btn--active"),document.addEventListener("mouseup",L,ze)))}function L(M){if(f.value!==null&&!(M!==void 0&&M.type==="blur"&&document.activeElement===f.value)){if(M!==void 0&&M.type==="keyup"){if(_n===f.value&&po(M,[13,32])===!0){const ee=new MouseEvent("click",M);ee.qKeyEvent=!0,M.defaultPrevented===!0&&ho(ee),M.cancelBubble===!0&&gc(ee),f.value.dispatchEvent(ee),Zt(M),M.qKeyEvent=!0}n("keyup",M)}O()}}function O(M){const ee=g.value;M!==!0&&(vn===f.value||yn===f.value)&&ee!==null&&ee!==document.activeElement&&(ee.setAttribute("tabindex",-1),ee.focus()),vn===f.value&&(v!==null&&(v.removeEventListener("touchcancel",L,ze),v.removeEventListener("touchend",L,ze)),vn=v=null),yn===f.value&&(document.removeEventListener("mouseup",L,ze),yn=null),_n===f.value&&(document.removeEventListener("keyup",L,!0),f.value!==null&&f.value.removeEventListener("blur",L,ze),_n=null),f.value!==null&&f.value.classList.remove("q-btn--active")}function Q(M){Zt(M),M.qSkipRipple=!0}return hs(()=>{O(!0)}),Object.assign(r,{click:M=>{d.value===!0&&j(M)}}),()=>{let M=[];e.icon!==void 0&&M.push(K(ts,{name:e.icon,left:e.stack!==!0&&R.value===!0,role:"img"})),R.value===!0&&M.push(K("span",{class:"block"},[e.label])),M=Un(t.default,M),e.iconRight!==void 0&&e.round===!1&&M.push(K(ts,{name:e.iconRight,right:e.stack!==!0&&R.value===!0,role:"img"}));const ee=[K("span",{class:"q-focus-helper",ref:g})];return e.loading===!0&&e.percentage!==void 0&&ee.push(K("span",{class:"q-btn__progress absolute-full overflow-hidden"+(e.darkPercentage===!0?" q-btn__progress--dark":"")},[K("span",{class:"q-btn__progress-indicator fit block",style:y.value})])),ee.push(K("span",{class:"q-btn__content text-center col items-center q-anchor--skip "+i.value},M)),e.loading!==null&&ee.push(K(ic,{name:"q-transition--fade"},()=>e.loading===!0?[K("span",{key:"loading",class:"absolute-full flex flex-center"},t.loading!==void 0?t.loading():[K(oi)])]:null)),Sa(K(u.value,B.value,ee),[[am,w.value,void 0,S.value]])}}}),Pn=[],tr=[];let Cm=1,Ot=document.body;function uu(e,t){const n=document.createElement("div");if(n.id=t!==void 0?`q-portal--${t}--${Cm++}`:e,Zr.globalNodes!==void 0){const r=Zr.globalNodes.class;r!==void 0&&(n.className=r)}return Ot.appendChild(n),Pn.push(n),tr.push(t),n}function Tm(e){const t=Pn.indexOf(e);Pn.splice(t,1),tr.splice(t,1),e.remove()}function Pm(e){if(e===Ot)return;if(Ot=e,Ot===document.body||tr.reduce((n,r)=>r==="dialog"?n+1:n,0)<2){Pn.forEach(n=>{n.contains(Ot)===!1&&Ot.appendChild(n)});return}const t=tr.lastIndexOf("dialog");for(let n=0;n<Pn.length;n++){const r=Pn[n];(n===t||tr[n]!=="dialog")&&r.contains(Ot)===!1&&Ot.appendChild(r)}}let Rm=0;const Dr={},Fr={},tt={},fu={},Am=/^\s*$/,du=[],Om=[void 0,null,!0,!1,""],ii=["top-left","top-right","bottom-left","bottom-right","top","bottom","left","right","center"],km=["top-left","top-right","bottom-left","bottom-right"],Sn={positive:{icon:e=>e.iconSet.type.positive,color:"positive"},negative:{icon:e=>e.iconSet.type.negative,color:"negative"},warning:{icon:e=>e.iconSet.type.warning,color:"warning",textColor:"dark"},info:{icon:e=>e.iconSet.type.info,color:"info"},ongoing:{group:!1,timeout:0,spinner:!0,color:"grey-8"}};function hu(e,t,n){if(!e)return Dn("parameter required");let r;const s={textColor:"white"};if(e.ignoreDefaults!==!0&&Object.assign(s,Dr),cn(e)===!1&&(s.type&&Object.assign(s,Sn[s.type]),e={message:e}),Object.assign(s,Sn[e.type||s.type],e),typeof s.icon=="function"&&(s.icon=s.icon(t)),s.spinner?(s.spinner===!0&&(s.spinner=oi),s.spinner=ht(s.spinner)):s.spinner=!1,s.meta={hasMedia:!!(s.spinner!==!1||s.icon||s.avatar),hasText:kl(s.message)||kl(s.caption)},s.position){if(ii.includes(s.position)===!1)return Dn("wrong position",e)}else s.position="bottom";if(Om.includes(s.timeout)===!0)s.timeout=5e3;else{const a=Number(s.timeout);if(isNaN(a)||a<0)return Dn("wrong timeout",e);s.timeout=Number.isFinite(a)?a:0}s.timeout===0?s.progress=!1:s.progress===!0&&(s.meta.progressClass="q-notification__progress"+(s.progressClass?` ${s.progressClass}`:""),s.meta.progressStyle={animationDuration:`${s.timeout+1e3}ms`});const o=(Array.isArray(e.actions)===!0?e.actions:[]).concat(e.ignoreDefaults!==!0&&Array.isArray(Dr.actions)===!0?Dr.actions:[]).concat(Sn[e.type]!==void 0&&Array.isArray(Sn[e.type].actions)===!0?Sn[e.type].actions:[]),{closeBtn:i}=s;if(i&&o.push({label:typeof i=="string"?i:t.lang.label.close}),s.actions=o.map(({handler:a,noDismiss:u,...c})=>({flat:!0,...c,onClick:typeof a=="function"?()=>{a(),u!==!0&&l()}:()=>{l()}})),s.multiLine===void 0&&(s.multiLine=s.actions.length>1),Object.assign(s.meta,{class:`q-notification row items-stretch q-notification--${s.multiLine===!0?"multi-line":"standard"}`+(s.color!==void 0?` bg-${s.color}`:"")+(s.textColor!==void 0?` text-${s.textColor}`:"")+(s.classes!==void 0?` ${s.classes}`:""),wrapperClass:"q-notification__wrapper col relative-position border-radius-inherit "+(s.multiLine===!0?"column no-wrap justify-center":"row items-center"),contentClass:"q-notification__content row items-center"+(s.multiLine===!0?"":" col"),leftClass:s.meta.hasText===!0?"additional":"single",attrs:{role:"alert",...s.attrs}}),s.group===!1?(s.group=void 0,s.meta.group=void 0):((s.group===void 0||s.group===!0)&&(s.group=[s.message,s.caption,s.multiline].concat(s.actions.map(a=>`${a.label}*${a.icon}`)).join("|")),s.meta.group=s.group+"|"+s.position),s.actions.length===0?s.actions=void 0:s.meta.actionsClass="q-notification__actions row items-center "+(s.multiLine===!0?"justify-end":"col-auto")+(s.meta.hasMedia===!0?" q-notification__actions--with-media":""),n!==void 0){n.notif.meta.timer&&(clearTimeout(n.notif.meta.timer),n.notif.meta.timer=void 0),s.meta.uid=n.notif.meta.uid;const a=tt[s.position].value.indexOf(n.notif);tt[s.position].value[a]=s}else{const a=Fr[s.meta.group];if(a===void 0){if(s.meta.uid=Rm++,s.meta.badge=1,["left","right","center"].indexOf(s.position)!==-1)tt[s.position].value.splice(Math.floor(tt[s.position].value.length/2),0,s);else{const u=s.position.indexOf("top")!==-1?"unshift":"push";tt[s.position].value[u](s)}s.group!==void 0&&(Fr[s.meta.group]=s)}else{if(a.meta.timer&&(clearTimeout(a.meta.timer),a.meta.timer=void 0),s.badgePosition!==void 0){if(km.includes(s.badgePosition)===!1)return Dn("wrong badgePosition",e)}else s.badgePosition=`top-${s.position.indexOf("left")!==-1?"right":"left"}`;s.meta.uid=a.meta.uid,s.meta.badge=a.meta.badge+1,s.meta.badgeClass=`q-notification__badge q-notification__badge--${s.badgePosition}`+(s.badgeColor!==void 0?` bg-${s.badgeColor}`:"")+(s.badgeTextColor!==void 0?` text-${s.badgeTextColor}`:"")+(s.badgeClass?` ${s.badgeClass}`:"");const u=tt[s.position].value.indexOf(a);tt[s.position].value[u]=Fr[s.meta.group]=s}}const l=()=>{Lm(s),r=void 0};if(s.timeout>0&&(s.meta.timer=setTimeout(()=>{s.meta.timer=void 0,l()},s.timeout+1e3)),s.group!==void 0)return a=>{a!==void 0?Dn("trying to update a grouped one which is forbidden",e):l()};if(r={dismiss:l,config:e,notif:s},n!==void 0){Object.assign(n,r);return}return a=>{if(r!==void 0)if(a===void 0)r.dismiss();else{const u=Object.assign({},r.config,a,{group:!1,position:s.position});hu(u,t,r)}}}function Lm(e){e.meta.timer&&(clearTimeout(e.meta.timer),e.meta.timer=void 0);const t=tt[e.position].value.indexOf(e);if(t!==-1){e.group!==void 0&&delete Fr[e.meta.group];const n=du[""+e.meta.uid];if(n){const{width:r,height:s}=getComputedStyle(n);n.style.left=`${n.offsetLeft}px`,n.style.width=r,n.style.height=s}tt[e.position].value.splice(t,1),typeof e.onDismiss=="function"&&e.onDismiss()}}function kl(e){return e!=null&&Am.test(e)!==!0}function Dn(e,t){return console.error(`Notify: ${e}`,t),!1}function Im(){return pr({name:"QNotifications",devtools:{hide:!0},setup(){return()=>K("div",{class:"q-notifications"},ii.map(e=>K(rh,{key:e,class:fu[e],tag:"div",name:`q-notification--${e}`},()=>tt[e].value.map(t=>{const n=t.meta,r=[];if(n.hasMedia===!0&&(t.spinner!==!1?r.push(K(t.spinner,{class:"q-notification__spinner q-notification__spinner--"+n.leftClass,color:t.spinnerColor,size:t.spinnerSize})):t.icon?r.push(K(ts,{class:"q-notification__icon q-notification__icon--"+n.leftClass,name:t.icon,color:t.iconColor,size:t.iconSize,role:"img"})):t.avatar&&r.push(K(rm,{class:"q-notification__avatar q-notification__avatar--"+n.leftClass},()=>K("img",{src:t.avatar,"aria-hidden":"true"})))),n.hasText===!0){let o;const i={class:"q-notification__message col"};if(t.html===!0)i.innerHTML=t.caption?`<div>${t.message}</div><div class="q-notification__caption">${t.caption}</div>`:t.message;else{const l=[t.message];o=t.caption?[K("div",l),K("div",{class:"q-notification__caption"},[t.caption])]:l}r.push(K("div",i,o))}const s=[K("div",{class:n.contentClass},r)];return t.progress===!0&&s.push(K("div",{key:`${n.uid}|p|${n.badge}`,class:n.progressClass,style:n.progressStyle})),t.actions!==void 0&&s.push(K("div",{class:n.actionsClass},t.actions.map(o=>K(xm,o)))),n.badge>1&&s.push(K("div",{key:`${n.uid}|${n.badge}`,class:t.meta.badgeClass,style:t.badgeStyle},[n.badge])),K("div",{ref:o=>{du[""+n.uid]=o},key:n.uid,class:n.class,...n.attrs},[K("div",{class:n.wrapperClass},s)])}))))}})}const $m={setDefaults(e){cn(e)===!0&&Object.assign(Dr,e)},registerType(e,t){cn(t)===!0&&(Sn[e]=t)},install({$q:e,parentApp:t}){if(e.notify=this.create=n=>hu(n,e),e.notify.setDefaults=this.setDefaults,e.notify.registerType=this.registerType,e.config.notify!==void 0&&this.setDefaults(e.config.notify),this.__installed!==!0){ii.forEach(r=>{tt[r]=ln([]);const s=["left","center","right"].includes(r)===!0?"center":r.indexOf("top")!==-1?"top":"bottom",o=r.indexOf("left")!==-1?"start":r.indexOf("right")!==-1?"end":"center",i=["left","right"].includes(r)?`items-${r==="left"?"start":"end"} justify-center`:r==="center"?"flex-center":`items-${o}`;fu[r]=`q-notifications__list q-notifications__list--${s} fixed column no-wrap ${i}`});const n=uu("q-notify");bc(Im(),t).mount(n)}}},Ev=[Element,String],Mm=[null,document,document.body,document.scrollingElement,document.documentElement];function xv(e,t){let n=im(t);if(n===void 0){if(e==null)return window;n=e.closest(".scroll,.scroll-y,.overflow-auto")}return Mm.includes(n)?window:n}function Cv(e){return(e===window?document.body:e).scrollHeight}function pu(e){return e===window?window.pageYOffset||window.scrollY||document.body.scrollTop||0:e.scrollTop}function gu(e){return e===window?window.pageXOffset||window.scrollX||document.body.scrollLeft||0:e.scrollLeft}function mu(e,t,n=0){const r=arguments[3]===void 0?performance.now():arguments[3],s=pu(e);if(n<=0){s!==t&&xo(e,t);return}requestAnimationFrame(o=>{const i=o-r,l=s+(t-s)/Math.max(i,n)*i;xo(e,l),l!==t&&mu(e,t,n-i,o)})}function vu(e,t,n=0){const r=arguments[3]===void 0?performance.now():arguments[3],s=gu(e);if(n<=0){s!==t&&Co(e,t);return}requestAnimationFrame(o=>{const i=o-r,l=s+(t-s)/Math.max(i,n)*i;Co(e,l),l!==t&&vu(e,t,n-i,o)})}function xo(e,t){if(e===window){window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,t);return}e.scrollTop=t}function Co(e,t){if(e===window){window.scrollTo(t,window.pageYOffset||window.scrollY||document.body.scrollTop||0);return}e.scrollLeft=t}function Tv(e,t,n){if(n){mu(e,t,n);return}xo(e,t)}function Pv(e,t,n){if(n){vu(e,t,n);return}Co(e,t)}let Pr;function Rv(){if(Pr!==void 0)return Pr;const e=document.createElement("p"),t=document.createElement("div");Eo(e,{width:"100%",height:"200px"}),Eo(t,{position:"absolute",top:"0px",left:"0px",visibility:"hidden",width:"200px",height:"150px",overflow:"hidden"}),t.appendChild(e),document.body.appendChild(t);const n=e.offsetWidth;t.style.overflow="scroll";let r=e.offsetWidth;return n===r&&(r=t.clientWidth),t.remove(),Pr=n-r,Pr}function Nm(e,t=!0){return!e||e.nodeType!==Node.ELEMENT_NODE?!1:t?e.scrollHeight>e.clientHeight&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-y"])):e.scrollWidth>e.clientWidth&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-x"]))}let Fn=0,zs,Ws,Kn,Gs=!1,Ll,Il,$l,Jt=null;function jm(e){Dm(e)&&Zt(e)}function Dm(e){if(e.target===document.body||e.target.classList.contains("q-layout__backdrop"))return!0;const t=wh(e),n=e.shiftKey&&!e.deltaX,r=!n&&Math.abs(e.deltaX)<=Math.abs(e.deltaY),s=n||r?e.deltaY:e.deltaX;for(let o=0;o<t.length;o++){const i=t[o];if(Nm(i,r))return r?s<0&&i.scrollTop===0?!0:s>0&&i.scrollTop+i.clientHeight===i.scrollHeight:s<0&&i.scrollLeft===0?!0:s>0&&i.scrollLeft+i.clientWidth===i.scrollWidth}return!0}function Ml(e){e.target===document&&(document.scrollingElement.scrollTop=document.scrollingElement.scrollTop)}function Rr(e){Gs!==!0&&(Gs=!0,requestAnimationFrame(()=>{Gs=!1;const{height:t}=e.target,{clientHeight:n,scrollTop:r}=document.scrollingElement;(Kn===void 0||t!==window.innerHeight)&&(Kn=n-t,document.scrollingElement.scrollTop=r),r>Kn&&(document.scrollingElement.scrollTop-=Math.ceil((r-Kn)/8))}))}function Nl(e){const t=document.body,n=window.visualViewport!==void 0;if(e==="add"){const{overflowY:r,overflowX:s}=window.getComputedStyle(t);zs=gu(window),Ws=pu(window),Ll=t.style.left,Il=t.style.top,$l=window.location.href,t.style.left=`-${zs}px`,t.style.top=`-${Ws}px`,s!=="hidden"&&(s==="scroll"||t.scrollWidth>window.innerWidth)&&t.classList.add("q-body--force-scrollbar-x"),r!=="hidden"&&(r==="scroll"||t.scrollHeight>window.innerHeight)&&t.classList.add("q-body--force-scrollbar-y"),t.classList.add("q-body--prevent-scroll"),document.qScrollPrevented=!0,Ce.is.ios===!0&&(n===!0?(window.scrollTo(0,0),window.visualViewport.addEventListener("resize",Rr,De.passiveCapture),window.visualViewport.addEventListener("scroll",Rr,De.passiveCapture),window.scrollTo(0,0)):window.addEventListener("scroll",Ml,De.passiveCapture))}Ce.is.desktop===!0&&Ce.is.mac===!0&&window[`${e}EventListener`]("wheel",jm,De.notPassive),e==="remove"&&(Ce.is.ios===!0&&(n===!0?(window.visualViewport.removeEventListener("resize",Rr,De.passiveCapture),window.visualViewport.removeEventListener("scroll",Rr,De.passiveCapture)):window.removeEventListener("scroll",Ml,De.passiveCapture)),t.classList.remove("q-body--prevent-scroll"),t.classList.remove("q-body--force-scrollbar-x"),t.classList.remove("q-body--force-scrollbar-y"),document.qScrollPrevented=!1,t.style.left=Ll,t.style.top=Il,window.location.href===$l&&window.scrollTo(zs,Ws),Kn=void 0)}function jl(e){let t="add";if(e===!0){if(Fn++,Jt!==null){clearTimeout(Jt),Jt=null;return}if(Fn>1)return}else{if(Fn===0||(Fn--,Fn>0))return;if(t="remove",Ce.is.ios===!0&&Ce.is.nativeMobile===!0){Jt!==null&&clearTimeout(Jt),Jt=setTimeout(()=>{Nl(t),Jt=null},100);return}}Nl(t)}let bn,Ys,Dl=0,Qt=null,ye={},tn={};const _u={group:"__default_quasar_group__",delay:0,message:!1,html:!1,spinnerSize:80,spinnerColor:"",messageColor:"",backgroundColor:"",boxClass:"",spinner:oi,customClass:""},yu={..._u};function Fm(e){if(e&&e.group!==void 0&&tn[e.group]!==void 0)return Object.assign(tn[e.group],e);const t=cn(e)===!0&&e.ignoreDefaults===!0?{..._u,...e}:{...yu,...e};return tn[t.group]=t,t}const Ze=kn({isActive:!1},{show(e){ye=Fm(e);const{group:t}=ye;return Ze.isActive=!0,bn!==void 0?(ye.uid=Dl,Ys.$forceUpdate()):(ye.uid=++Dl,Qt!==null&&clearTimeout(Qt),Qt=setTimeout(()=>{Qt=null;const n=uu("q-loading");bn=bc({name:"QLoading",setup(){ds(()=>{jl(!0)});function r(){Ze.isActive!==!0&&bn!==void 0&&(jl(!1),bn.unmount(n),Tm(n),bn=void 0,Ys=void 0)}function s(){if(Ze.isActive!==!0)return null;const o=[K(ye.spinner,{class:"q-loading__spinner",color:ye.spinnerColor,size:ye.spinnerSize})];return ye.message&&o.push(K("div",{class:"q-loading__message"+(ye.messageColor?` text-${ye.messageColor}`:""),[ye.html===!0?"innerHTML":"textContent"]:ye.message})),K("div",{class:"q-loading fullscreen flex flex-center z-max "+ye.customClass.trim(),key:ye.uid},[K("div",{class:"q-loading__backdrop"+(ye.backgroundColor?` bg-${ye.backgroundColor}`:"")}),K("div",{class:"q-loading__box column items-center "+ye.boxClass},o)])}return()=>K(ic,{name:"q-transition--fade",appear:!0,onAfterLeave:r},s)}},Ze.__parentApp),Ys=bn.mount(n)},ye.delay)),n=>{if(n===void 0||Object(n)!==n){Ze.hide(t);return}Ze.show({...n,group:t})}},hide(e){if(Ze.isActive===!0){if(e===void 0)tn={};else{if(tn[e]===void 0)return;{delete tn[e];const t=Object.keys(tn);if(t.length!==0){const n=t[t.length-1];Ze.show({group:n});return}}}Qt!==null&&(clearTimeout(Qt),Qt=null),Ze.isActive=!1}},setDefaults(e){cn(e)===!0&&Object.assign(yu,e)},install({$q:e,parentApp:t}){e.loading=this,Ze.__parentApp=t,e.config.loading!==void 0&&this.setDefaults(e.config.loading)}}),qn={};function qm(e){Object.assign(Ee,{request:e,exit:e,toggle:e})}function bu(){return document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||document.msFullscreenElement||null}function wu(){const e=Ee.activeEl=Ee.isActive===!1?null:bu();Pm(e===null||e===document.documentElement?document.body:e)}function Hm(){Ee.isActive=Ee.isActive===!1,wu()}function Fl(e,t){try{const n=e[t]();return n===void 0?Promise.resolve():n}catch(n){return Promise.reject(n)}}const Ee=kn({isActive:!1,activeEl:null},{isCapable:!1,install({$q:e}){e.fullscreen=this}});qn.request=["requestFullscreen","msRequestFullscreen","mozRequestFullScreen","webkitRequestFullscreen"].find(e=>document.documentElement[e]!==void 0),Ee.isCapable=qn.request!==void 0,Ee.isCapable===!1?qm(()=>Promise.reject("Not capable")):(Object.assign(Ee,{request(e){const t=e||document.documentElement,{activeEl:n}=Ee;return t===n?Promise.resolve():(n!==null&&t.contains(n)===!0?Ee.exit():Promise.resolve()).finally(()=>Fl(t,qn.request))},exit(){return Ee.isActive===!0?Fl(document,qn.exit):Promise.resolve()},toggle(e){return Ee.isActive===!0?Ee.exit():Ee.request(e)}}),qn.exit=["exitFullscreen","msExitFullscreen","mozCancelFullScreen","webkitExitFullscreen"].find(e=>document[e]),Ee.isActive=!!bu(),Ee.isActive===!0&&wu(),["onfullscreenchange","onmsfullscreenchange","onwebkitfullscreenchange"].forEach(e=>{document[e]=Hm}));const Bm={config:{brand:{primary:"#1976D2",secondary:"#26A69A",accent:"#9C27B0",dark:"#1D1D1D",positive:"#21BA45",negative:"#C10015",info:"#31CCEC",warning:"#F2C037"}},plugins:{Notify:$m,Loading:Ze,AppFullscreen:Ee}},Vm="/admin/";async function Um({app:e,router:t,store:n},r){let s=!1;const o=a=>{try{return t.resolve(a).href}catch{}return Object(a)===a?null:a},i=a=>{if(s=!0,typeof a=="string"&&/^https?:\/\//.test(a)){window.location.href=a;return}const u=o(a);u!==null&&(window.location.href=u,window.location.reload())},l=window.location.href.replace(window.location.origin,"");for(let a=0;s===!1&&a<r.length;a++)try{await r[a]({app:e,router:t,store:n,ssrContext:null,redirect:i,urlPath:l,publicPath:Vm})}catch(u){if(u&&u.url){i(u.url);return}console.error("[Quasar] boot error:",u);return}s!==!0&&(e.use(t),e.mount("#q-app"))}Wg(hc,Bm).then(e=>{const[t,n]=Promise.allSettled!==void 0?["allSettled",r=>r.map(s=>{if(s.status==="rejected"){console.error("[Quasar] boot error:",s.reason);return}return s.value.default})]:["all",r=>r.map(s=>s.default)];return Promise[t]([Ge(()=>import("./i18n-DH-o0fqO.js"),[]),Ge(()=>import("./axios-JqZ6Qsjg.js").then(r=>r.d),[])]).then(r=>{const s=n(r).filter(o=>typeof o=="function");Um(e,s)})});export{ri as $,Du as A,Ed as B,rm as C,Gm as D,Qm as E,$e as F,ev as G,ko as H,Ug as I,dv as J,nu as K,Zm as L,ts as M,pv as N,ut as O,xh as P,xm as Q,jf as R,Mf as S,dr as T,$f as U,hs as V,Ev as W,xv as X,De as Y,Un as Z,zh as _,Ia as a,uu as a$,si as a0,gv as a1,Yg as a2,am as a3,Zt as a4,Df as a5,zo as a6,ho as a7,Ms as a8,po as a9,hv as aA,Or as aB,iv as aC,$c as aD,je as aE,Kg as aF,fa as aG,wv as aH,Xm as aI,Vf as aJ,sm as aK,om as aL,Cv as aM,pu as aN,Tv as aO,mv as aP,Pv as aQ,Ym as aR,Wm as aS,gu as aT,an as aU,Rv as aV,un as aW,lv as aX,mm as aY,_v as aZ,Km as a_,gc as aa,Rh as ab,dm as ac,Nh as ad,uv as ae,qh as af,cn as ag,tv as ah,vs as ai,Z as aj,ic as ak,bh as al,Sa as am,bv as an,yh as ao,Ce as ap,nv as aq,Sh as ar,rv as as,Eh as at,yv as au,zm as av,lm as aw,wm as ax,Sv as ay,Oo as az,me as b,Tm as b0,jl as b1,vv as b2,fo as b3,av as b4,oi as b5,Dh as b6,$t as b7,Qi as b8,um as b9,fm as ba,Ze as bb,$m as bc,G as c,cs as d,Yl as e,Xo as f,fn as g,K as h,Je as i,Me as j,fv as k,Wr as l,Jm as m,ec as n,ds as o,pr as p,cv as q,ln as r,cf as s,Gg as t,sv as u,ov as v,Ft as w,Bo as x,Gr as y,Pf as z};
