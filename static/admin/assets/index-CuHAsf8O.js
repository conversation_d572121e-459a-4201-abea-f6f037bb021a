import{a as e}from"./axios-JqZ6Qsjg.js";const n=()=>e.get("/captcha/create",{headers:{"X-Skip-Loading":"true"}}),o=t=>e.post("/login",t),a=(t,s)=>e.post("/configure",{route:t,query:s}),u=t=>e.post("/lang/tabs",t),p=t=>e.post("/options",t),i=t=>e.post("/update/translate",t),c=()=>e.post("/init"),d=()=>e.post("/index"),q=t=>e.post("/update/password",t),g=t=>e.post("/update",t),f=t=>e.post("/notify/info",t),h=()=>e.post("/audio",{},{headers:{"X-Skip-Loading":"true"}});export{n as a,o as b,p as c,u as d,i as e,a as f,c as g,f as h,h as i,q as j,g as k,d as r};
