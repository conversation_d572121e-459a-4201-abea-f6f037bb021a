import{p as y,i as k,q as z,aB as E,c as s,h as l,t as Q,u as F,v as A,g as B,W as U,w as L,o as _,V as j,O as K,X as D,Y as I,aN as X,aT as Y,r as C,aU as Z,aV as H,aW as w,a as G,Z as N,au as J}from"./index-H1mtFlU6.js";import{Q as M}from"./QResizeObserver-DJfGZaa6.js";const oe=y({name:"QPageContainer",setup(e,{slots:o}){const{proxy:{$q:t}}=B(),a=k(F,z);if(a===z)return console.error("QPageContainer needs to be child of QLayout"),z;E(A,!0);const r=s(()=>{const d={};return a.header.space===!0&&(d.paddingTop=`${a.header.size}px`),a.right.space===!0&&(d[`padding${t.lang.rtl===!0?"Left":"Right"}`]=`${a.right.size}px`),a.footer.space===!0&&(d.paddingBottom=`${a.footer.size}px`),a.left.space===!0&&(d[`padding${t.lang.rtl===!0?"Right":"Left"}`]=`${a.left.size}px`),d});return()=>l("div",{class:"q-page-container",style:r.value},Q(o.default))}}),{passive:W}=I,ee=["both","horizontal","vertical"],te=y({name:"QScrollObserver",props:{axis:{type:String,validator:e=>ee.includes(e),default:"vertical"},debounce:[String,Number],scrollTarget:U},emits:["scroll"],setup(e,{emit:o}){const t={position:{top:0,left:0},direction:"down",directionChanged:!1,delta:{top:0,left:0},inflectionPoint:{top:0,left:0}};let a=null,r,d;L(()=>e.scrollTarget,()=>{x(),h()});function m(){a!==null&&a();const g=Math.max(0,X(r)),v=Y(r),i={top:g-t.position.top,left:v-t.position.left};if(e.axis==="vertical"&&i.top===0||e.axis==="horizontal"&&i.left===0)return;const q=Math.abs(i.top)>=Math.abs(i.left)?i.top<0?"up":"down":i.left<0?"left":"right";t.position={top:g,left:v},t.directionChanged=t.direction!==q,t.delta=i,t.directionChanged===!0&&(t.direction=q,t.inflectionPoint=t.position),o("scroll",{...t})}function h(){r=D(d,e.scrollTarget),r.addEventListener("scroll",c,W),c(!0)}function x(){r!==void 0&&(r.removeEventListener("scroll",c,W),r=void 0)}function c(g){if(g===!0||e.debounce===0||e.debounce==="0")m();else if(a===null){const[v,i]=e.debounce?[setTimeout(m,e.debounce),clearTimeout]:[requestAnimationFrame(m),cancelAnimationFrame];a=()=>{i(v),a=null}}}const{proxy:u}=B();return L(()=>u.$q.lang.rtl,m),_(()=>{d=u.$el.parentNode,h()}),j(()=>{a!==null&&a(),x()}),Object.assign(u,{trigger:c,getPosition:()=>t}),K}}),ie=y({name:"QLayout",props:{container:Boolean,view:{type:String,default:"hhh lpr fff",validator:e=>/^(h|l)h(h|r) lpr (f|l)f(f|r)$/.test(e.toLowerCase())},onScroll:Function,onScrollHeight:Function,onResize:Function},setup(e,{slots:o,emit:t}){const{proxy:{$q:a}}=B(),r=C(null),d=C(a.screen.height),m=C(e.container===!0?0:a.screen.width),h=C({position:0,direction:"down",inflectionPoint:0}),x=C(0),c=C(Z.value===!0?0:H()),u=s(()=>"q-layout q-layout--"+(e.container===!0?"containerized":"standard")),g=s(()=>e.container===!1?{minHeight:a.screen.height+"px"}:null),v=s(()=>c.value!==0?{[a.lang.rtl===!0?"left":"right"]:`${c.value}px`}:null),i=s(()=>c.value!==0?{[a.lang.rtl===!0?"right":"left"]:0,[a.lang.rtl===!0?"left":"right"]:`-${c.value}px`,width:`calc(100% + ${c.value}px)`}:null);function q(n){if(e.container===!0||document.qScrollPrevented!==!0){const f={position:n.position.top,direction:n.direction,directionChanged:n.directionChanged,inflectionPoint:n.inflectionPoint.top,delta:n.delta.top};h.value=f,e.onScroll!==void 0&&t("scroll",f)}}function O(n){const{height:f,width:S}=n;let b=!1;d.value!==f&&(b=!0,d.value=f,e.onScrollHeight!==void 0&&t("scrollHeight",f),P()),m.value!==S&&(b=!0,m.value=S),b===!0&&e.onResize!==void 0&&t("resize",n)}function V({height:n}){x.value!==n&&(x.value=n,P())}function P(){if(e.container===!0){const n=d.value>x.value?H():0;c.value!==n&&(c.value=n)}}let T=null;const p={instances:{},view:s(()=>e.view),isContainer:s(()=>e.container),rootRef:r,height:d,containerHeight:x,scrollbarWidth:c,totalWidth:s(()=>m.value+c.value),rows:s(()=>{const n=e.view.toLowerCase().split(" ");return{top:n[0].split(""),middle:n[1].split(""),bottom:n[2].split("")}}),header:w({size:0,offset:0,space:!1}),right:w({size:300,offset:0,space:!1}),footer:w({size:0,offset:0,space:!1}),left:w({size:300,offset:0,space:!1}),scroll:h,animate(){T!==null?clearTimeout(T):document.body.classList.add("q-body--layout-animate"),T=setTimeout(()=>{T=null,document.body.classList.remove("q-body--layout-animate")},155)},update(n,f,S){p[n][f]=S}};if(E(F,p),H()>0){let n=function(){b=null,$.classList.remove("hide-scrollbar")},f=function(){if(b===null){if($.scrollHeight>a.screen.height)return;$.classList.add("hide-scrollbar")}else clearTimeout(b);b=setTimeout(n,300)},S=function(R){b!==null&&R==="remove"&&(clearTimeout(b),n()),window[`${R}EventListener`]("resize",f)},b=null;const $=document.body;L(()=>e.container!==!0?"add":"remove",S),e.container!==!0&&S("add"),G(()=>{S("remove")})}return()=>{const n=N(o.default,[l(te,{onScroll:q}),l(M,{onResize:O})]),f=l("div",{class:u.value,style:g.value,ref:e.container===!0?void 0:r,tabindex:-1},n);return e.container===!0?l("div",{class:"q-layout-container overflow-hidden",ref:r},[l(M,{onResize:V}),l("div",{class:"absolute-full",style:v.value},[l("div",{class:"scroll",style:i.value},[f])])]):f}}}),se=y({name:"QToolbarTitle",props:{shrink:Boolean},setup(e,{slots:o}){const t=s(()=>"q-toolbar__title ellipsis"+(e.shrink===!0?" col-shrink":""));return()=>l("div",{class:t.value},Q(o.default))}}),ae=["top","middle","bottom"],re=y({name:"QBadge",props:{color:String,textColor:String,floating:Boolean,transparent:Boolean,multiLine:Boolean,outline:Boolean,rounded:Boolean,label:[Number,String],align:{type:String,validator:e=>ae.includes(e)}},setup(e,{slots:o}){const t=s(()=>e.align!==void 0?{verticalAlign:e.align}:null),a=s(()=>{const r=e.outline===!0&&e.color||e.textColor;return`q-badge flex inline items-center no-wrap q-badge--${e.multiLine===!0?"multi":"single"}-line`+(e.outline===!0?" q-badge--outline":e.color!==void 0?` bg-${e.color}`:"")+(r!==void 0?` text-${r}`:"")+(e.floating===!0?" q-badge--floating":"")+(e.rounded===!0?" q-badge--rounded":"")+(e.transparent===!0?" q-badge--transparent":"")});return()=>l("div",{class:a.value,style:t.value,role:"status","aria-label":e.label},N(o.default,e.label!==void 0?[e.label]:[]))}}),ce=y({name:"QToolbar",props:{inset:Boolean},setup(e,{slots:o}){const t=s(()=>"q-toolbar row no-wrap items-center"+(e.inset===!0?" q-toolbar--inset":""));return()=>l("div",{class:t.value,role:"toolbar"},Q(o.default))}}),ue=y({name:"QChatMessage",props:{sent:Boolean,label:String,bgColor:String,textColor:String,name:String,avatar:String,text:Array,stamp:String,size:String,labelHtml:Boolean,nameHtml:Boolean,textHtml:Boolean,stampHtml:Boolean},setup(e,{slots:o}){const t=s(()=>e.sent===!0?"sent":"received"),a=s(()=>`q-message-text-content q-message-text-content--${t.value}`+(e.textColor!==void 0?` text-${e.textColor}`:"")),r=s(()=>`q-message-text q-message-text--${t.value}`+(e.bgColor!==void 0?` text-${e.bgColor}`:"")),d=s(()=>"q-message-container row items-end no-wrap"+(e.sent===!0?" reverse":"")),m=s(()=>e.size!==void 0?`col-${e.size}`:""),h=s(()=>({msg:e.textHtml===!0?"innerHTML":"textContent",stamp:e.stampHtml===!0?"innerHTML":"textContent",name:e.nameHtml===!0?"innerHTML":"textContent",label:e.labelHtml===!0?"innerHTML":"textContent"}));function x(u){return o.stamp!==void 0?[u,l("div",{class:"q-message-stamp"},o.stamp())]:e.stamp?[u,l("div",{class:"q-message-stamp",[h.value.stamp]:e.stamp})]:[u]}function c(u,g){const v=g===!0?u.length>1?i=>i:i=>l("div",[i]):i=>l("div",{[h.value.msg]:i});return u.map((i,q)=>l("div",{key:q,class:r.value},[l("div",{class:a.value},x(v(i)))]))}return()=>{const u=[];o.avatar!==void 0?u.push(o.avatar()):e.avatar!==void 0&&u.push(l("img",{class:`q-message-avatar q-message-avatar--${t.value}`,src:e.avatar,"aria-hidden":"true"}));const g=[];o.name!==void 0?g.push(l("div",{class:`q-message-name q-message-name--${t.value}`},o.name())):e.name!==void 0&&g.push(l("div",{class:`q-message-name q-message-name--${t.value}`,[h.value.name]:e.name})),o.default!==void 0?g.push(c(J(o.default()),!0)):e.text!==void 0&&g.push(c(e.text)),u.push(l("div",{class:m.value},g));const v=[];return o.label!==void 0?v.push(l("div",{class:"q-message-label"},o.label())):e.label!==void 0&&v.push(l("div",{class:"q-message-label",[h.value.label]:e.label})),v.push(l("div",{class:d.value},u)),l("div",{class:`q-message q-message-${t.value}`},v)}}});export{ie as Q,ce as a,se as b,re as c,ue as d,oe as e,te as f};
