package indexs

import (
	"zfeng/core/context"
	"zfeng/service"
)

// HelpersRows 帮助中心数据
type HelpersRows struct {
	Helpers []map[string]interface{} `json:"helpers"`
}

// Helpers 帮助中心
func Helpers(c *context.CustomCtx, bodyParams *context.NoRequestBody) error {
	data := &HelpersRows{
		Helpers: make([]map[string]interface{}, 0),
	}

	adminService := service.NewAdminUserService()
	adminID, err := adminService.GetAdminUserByDomainWithCache(c.Rds, c.OriginHost)
	if err != nil {
		return c.<PERSON>rror<PERSON><PERSON>(err.<PERSON>rror())
	}
	merchantID, _ := adminService.GetMerchantIDWithCache(c.Rds, adminID)

	// 帮助中心
	adminSettingService := service.NewAdminSettingService()
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "siteHelpCenter").ToInterface(&data.Helpers)
	translateService := service.NewTranslateService()
	for _, helper := range data.Helpers {
		helper["name"] = translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, helper["name"].(string))
		helper["content"] = translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, helper["content"].(string))
	}

	return c.SuccessJson(data)
}
