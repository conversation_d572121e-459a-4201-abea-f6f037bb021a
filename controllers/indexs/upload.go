package indexs

import (
	"os"
	"zfeng/core/context"
	"zfeng/utils"
)

const (
	uploadDir     = "/uploads"
	staticFileDir = "/static"
)

// Upload handles file upload
func Upload(c *context.CustomCtx, _ *context.NoRequestBody) error {
	file, err := c.FormFile("file")
	if err != nil {
		return c.<PERSON><PERSON>("Failed to get uploaded file")
	}

	currentDir, _ := os.Getwd()
	filename := "/" + utils.GenerateRandomFileName(file.Filename)
	fullPath := currentDir + staticFileDir + uploadDir

	if err := os.MkdirAll(fullPath, os.ModePerm); err != nil {
		return c.<PERSON><PERSON><PERSON><PERSON><PERSON>("Failed to create upload directory")
	}

	filePath := fullPath + filename
	if err := c.SaveFile(file, filePath); err != nil {
		return c.<PERSON><PERSON><PERSON><PERSON><PERSON>("Failed to save file")
	}

	return c.<PERSON>(uploadDir + filename)
}
