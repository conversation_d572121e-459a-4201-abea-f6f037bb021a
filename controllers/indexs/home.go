package indexs

import (
	"zfeng/core/context"
	"zfeng/core/databases"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// Home 首页信息
func Home(ctx *context.CustomCtx, params *context.NoRequestBody) error {
	if ctx.Claims == nil {
		ctx.Claims = service.NewUserService().GetTokenClaims(ctx)
	}
	helpers := make([]map[string]interface{}, 0)
	// 帮助中心
	adminSettingService := service.NewAdminSettingService()
	translateService := service.NewTranslateService()
	_ = adminSettingService.GetAdminSettingByFieldWithCache(ctx.Rds, ctx.Claims.MerchantID, "siteHelpCenter").ToInterface(&helpers)
	for _, helper := range helpers {
		helper["name"] = translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, helper["name"].(string))
		helper["content"] = translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, helper["content"].(string))
	}

	scrollAnnouncement := make([]*models.Article, 0)
	databases.Db.Model(&models.Article{}).
		Where("admin_id = ?", ctx.Claims.MerchantID).
		Where("type = ?", models.ArticleTypeScroll).
		Order("sort").
		Limit(10).
		Find(&scrollAnnouncement)
	for _, row := range scrollAnnouncement {
		row.Title = translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, row.Title)
		row.Content = translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, row.Content)
	}

	// 备选防范 热门产品
	popularProductList := make([]*models.ProductInfo, 0)
	productService := service.NewProductService()
	db := productService.GetProductInfoDB(ctx.Claims.MerchantID, 0, ctx.Claims.UserID)
	result := db.Order("sort ASC").Limit(4).Find(&popularProductList)
	if result.Error != nil {
		return ctx.ErrorJson(translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, "abnormalOperation"))
	}
	translate := service.NewTranslateService()
	walletAssetsService := service.NewWalletAssetsService()

	// 推荐产品
	recommendProductList := make([]*models.ProductInfo, 0)
	result = db.Order("sort ASC").Limit(20).Find(&recommendProductList)
	if result.Error != nil {
		return ctx.ErrorJson(translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, "abnormalOperation"))
	}

	for _, v := range popularProductList {
		v.AssetsInfo = walletAssetsService.GetAssetsInfo(ctx.Rds, translate, ctx.Lang, v.AssetsID, ctx.Claims.MerchantID, ctx.Claims.UserID)
		v.SymbolAssetsInfo = walletAssetsService.GetAssetsInfo(ctx.Rds, translate, ctx.Lang, v.Data.SymbolAssetsID, ctx.Claims.MerchantID, ctx.Claims.UserID)
		v.Ticker, _ = productService.GetTickers(ctx.Rds, v.Type, v.Symbol)
		if v.IsTranslate == model.BoolTrue {
			v.Name = translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, v.Name)
		}

		// 获取货币数据
		v.Ticker, _ = productService.GetTickers(ctx.Rds, v.Type, v.Symbol)
	}

	for _, v := range recommendProductList {
		v.AssetsInfo = walletAssetsService.GetAssetsInfo(ctx.Rds, translate, ctx.Lang, v.AssetsID, ctx.Claims.MerchantID, ctx.Claims.UserID)
		v.SymbolAssetsInfo = walletAssetsService.GetAssetsInfo(ctx.Rds, translate, ctx.Lang, v.Data.SymbolAssetsID, ctx.Claims.MerchantID, ctx.Claims.UserID)
		v.Ticker, _ = productService.GetTickers(ctx.Rds, v.Type, v.Symbol)
		if v.IsTranslate == model.BoolTrue {
			v.Name = translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, v.Name)
		}

		// 获取货币数据
		v.Ticker, _ = productService.GetTickers(ctx.Rds, v.Type, v.Symbol)
	}

	settingService := service.NewAdminSettingService()
	banner := make([]string, 0)
	_ = settingService.GetAdminSettingByFieldWithCache(ctx.Rds, ctx.Claims.MerchantID, "siteBanner").ToInterface(&banner)
	return ctx.SuccessJson(&homeData{
		Helpers:              helpers,
		Banner:               banner,
		ScrollAnnouncement:   scrollAnnouncement,
		PopularProductList:   popularProductList,
		RecommendProductList: recommendProductList,
	})
}

type homeData struct {
	Helpers              []map[string]interface{} `json:"helpers"`
	ScrollAnnouncement   []*models.Article        `json:"scrollAnnouncement"`   // 滚动公告
	Banner               []string                 `json:"banner"`               // 轮播图
	PopularProductList   []*models.ProductInfo    `json:"popularProductList"`   // 推荐热门列表
	RecommendProductList []*models.ProductInfo    `json:"recommendProductList"` // 推荐产品列表
}
