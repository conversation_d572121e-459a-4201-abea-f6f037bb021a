package indexs

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/core/views/vues"
	"zfeng/middleware"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"

	"github.com/dchest/captcha"
)

// LoginParams 登录参数
type LoginParams struct {
	Username   string `json:"username" validate:"omitempty,min=2,max=50"`
	Email      string `json:"email" validate:"omitempty,email,max=50"`
	Telephone  string `json:"telephone" validate:"omitempty,max=15"`
	Password   string `json:"password" validate:"required"`
	Type       int8   `json:"type" validate:"required"`
	CaptchaKey string `json:"captchaKey"`
	Captcha    string `json:"captcha"`
}

// LoginData 登录数据
type LoginData struct {
	Token      string                      `json:"token"`
	ChatURL    string                      `json:"chatUrl"`
	UserInfo   models.UserInfo             `json:"userInfo"`
	NoticeList []*models.NotifyDisplayData `json:"noticeList"`
	NoticeNums int64                       `json:"noticeNums"`
}

// Login 登录
func Login(c *context.CustomCtx, bodyParams *LoginParams) error {
	adminService := service.NewAdminUserService()
	adminID, err := adminService.GetAdminUserByDomainWithCache(c.Rds, c.OriginHost)
	if err != nil {
		return c.ErrorJson(err.Error())
	}
	merchantID, _ := adminService.GetMerchantIDWithCache(c.Rds, adminID)

	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, merchantID)
	db := model.NewModel()
	translateService := service.NewTranslateService()
	adminSettingService := service.NewAdminSettingService()
	loginTemplate := make([][]*vues.Input, 0)

	// 用户信息
	userInfo := &models.User{}

	// 邮箱登录
	switch bodyParams.Type {
	case models.UserRegisterTypeEmail:
		adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "emailLoginTemplate").ToInterface(&loginTemplate)
		db.Where("admin_id IN ?", subAdminIDs).Where("email = ?", bodyParams.Email).First(userInfo)
	// 手机登录
	case models.UserRegisterTypeTelephone:
		adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "phoneLoginTemplate").ToInterface(&loginTemplate)
		db.Where("admin_id IN ?", subAdminIDs).Where("telephone = ?", bodyParams.Telephone).First(userInfo)
	case models.UserRegisterTypeUsername:
		adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "usernameLoginTemplate").ToInterface(&loginTemplate)
		db.Where("admin_id IN ?", subAdminIDs).Where("username = ?", bodyParams.Username).First(userInfo)
	default:
		return c.ErrorJson("Errors")
	}

	// 验证验证码
	if !adminSettingService.GetVueInputsDisplayValue(loginTemplate, "captcha") {
		if bodyParams.CaptchaKey == "" || bodyParams.Captcha == "" {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "captchaError"))
		}
		if !captcha.VerifyString(bodyParams.CaptchaKey, bodyParams.Captcha) {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "captchaError"))
		}
	}

	// 验证密码是否正确
	if userInfo.ID == 0 || userInfo.Password != utils.EncryptPassword(bodyParams.Password) {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "accountOrPasswordError"))
	}

	// 验证用户是否冻结
	if userInfo.Status == models.UserStatusFrozen {
		userStatusCheckBox, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "siteUserStatus").ToCheckbox()
		if _, ok := userStatusCheckBox["freezeLogin"]; ok && userStatusCheckBox["freezeLogin"] {
			tipsField, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "freezeTips").ToString()
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, tipsField))
		}
	}

	// 生成前端Token
	formatTimePicker := &views.FormatTimePicker{}
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "merchantTokenExpire").ToInterface(formatTimePicker)
	tokenExpire := formatTimePicker.GetSecond()
	deviceNums, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "merchantDeviceNums").ToInt()
	token, err := middleware.GenerateFrontendToken(c.Ctx, userInfo.AdminID, merchantID, userInfo.ID, time.Duration(tokenExpire)*time.Second, deviceNums)
	if err != nil {
		return c.ErrorJson(err.Error())
	}

	// 当前用户信息
	userService := service.NewUserService()
	userInfoData := userInfo.ToUserInfo()
	userInfoData.AuthStatus = userService.GetUserAuthStatus(c.Rds, merchantID, userInfo.ID, models.UserAuthModeIdentity)
	userInfoData.AuthAddressStatus = userService.GetUserAuthStatus(c.Rds, merchantID, userInfo.ID, models.UserAuthModeAddress)
	userInfoData.LevelInfo = userService.GetUserLevelInfo(c.Rds, merchantID, userInfo.ID, c.Lang)

	// 获取客服链接
	chatURL, _ := adminService.GetChatURLWithCache(c.Rds, userInfo.AdminID)
	// 如果客服链接为空，则使用默认客服链接
	if chatURL == "" {
		chatURL, _ = adminService.GetChatURLWithCache(c.Rds, merchantID)
	}

	data := &LoginData{
		Token:      token,
		ChatURL:    chatURL,
		UserInfo:   userInfoData,
		NoticeList: make([]*models.NotifyDisplayData, 0),
		NoticeNums: 0,
	}

	// 返回用户通知列表
	data.NoticeList = make([]*models.NotifyDisplayData, 0)
	db.Model(&models.Notify{}).Where("user_id = ?", userInfo.ID).
		Where("mode = ?", models.NotifyModeFrontend).
		Where("type = ?", models.NotifyTypeSystem).
		Where("status = ?", models.NotifyStatusUnread).
		Count(&data.NoticeNums).Order("id DESC").Find(&data.NoticeList)

	// 修改用户最后登录时间，最后登录IP
	db.Model(&models.User{}).Where("id = ?", userInfo.ID).Updates(&models.User{
		LastLoginAt: time.Now(),
		LastLoginIP: utils.GetClientIP(c.Ctx),
	})

	// 用户访问记录 - 登录
	db.Create(&models.Access{
		UserID:    userInfo.ID,
		AdminID:   userInfo.AdminID,
		Name:      "用户登录",
		Type:      models.AccessTypeLogin,
		IP:        utils.GetClientIP(c.Ctx),
		Method:    models.AccessMethodPost,
		Route:     "/login",
		UserAgent: string(c.Context().UserAgent()),
		Referer:   string(c.Context().Referer()),
		Data: models.AccessData{
			Headers: utils.StructToString(c.Ctx.GetRespHeaders()),
		},
	})

	return c.SuccessJson(data)
}
