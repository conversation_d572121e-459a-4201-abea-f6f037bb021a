package indexs

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// ArticleIndexParams 文章列表请求参数
type ArticleIndexParams struct {
	Pagination *model.Pagination `json:"pagination"`
}

// ArticleIndexRows 文章列表行数据
type ArticleIndexRows struct {
	models.Article
}

// ArticleIndex 文章列表
func ArticleIndex(c *context.CustomCtx, bodyParams *ArticleIndexParams) error {
	adminService := service.NewAdminUserService()
	merchantID, err := adminService.GetAdminUserByDomainWithCache(c.Rds, c.OriginHost)
	if err != nil {
		return c.Error<PERSON>son(err.Error())
	}

	db := model.NewModel()
	articleIndexRows := make([]*ArticleIndexRows, 0)
	data := &model.IndexData{Count: 0}

	db.Model(&models.Article{}).Where("admin_id = ? AND type = ?", merchantID, models.ArticleTypeNews).
		Count(&data.Count).
		Scopes(bodyParams.Pagination.Scopes()).
		Find(&articleIndexRows)
	translateService := service.NewTranslateService()
	for _, row := range articleIndexRows {
		row.Title = translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, row.Title)
		row.Content = translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, row.Content)
	}
	data.Items = articleIndexRows
	return c.SuccessJson(data)
}
