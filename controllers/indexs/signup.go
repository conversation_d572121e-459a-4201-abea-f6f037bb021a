package indexs

import (
	"strings"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/core/views/vues"
	"zfeng/middleware"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"

	"github.com/dchest/captcha"
	"gorm.io/gorm"
)

// SignupParams 注册参数
type SignupParams struct {
	Type        int8   `json:"type" validate:"required"`
	Username    string `json:"username"`
	Password    string `json:"password" validate:"required,min=6,max=20"`
	SecurityKey string `json:"securityKey" validate:"omitempty,min=6,max=20"`
	Email       string `json:"email" validate:"omitempty,email,max=50"`
	Nickname    string `json:"nickname" validate:"omitempty,min=2,max=20"`
	Sex         int8   `json:"sex"`
	CountryID   uint   `json:"countryId"`
	Telephone   string `json:"telephone" validate:"omitempty,max=15"`
	InviteCode  string `json:"inviteCode" validate:"omitempty,max=6"`
	CaptchaKey  string `json:"captchaKey"`
	Captcha     string `json:"captcha"`
}

// Signup 注册
func Signup(c *context.CustomCtx, bodyParams *SignupParams) error {
	bodyParams.Email = strings.TrimSpace(bodyParams.Email)
	bodyParams.Telephone = strings.TrimSpace(bodyParams.Telephone)
	bodyParams.Nickname = strings.TrimSpace(bodyParams.Nickname)
	bodyParams.Password = strings.TrimSpace(bodyParams.Password)
	bodyParams.SecurityKey = strings.TrimSpace(bodyParams.SecurityKey)

	adminService := service.NewAdminUserService()
	db := model.NewModel()
	adminID, err := adminService.GetAdminUserByDomainWithCache(c.Rds, c.OriginHost)
	if err != nil {
		return c.ErrorJson(err.Error())
	}
	merchantID, _ := adminService.GetMerchantIDWithCache(c.Rds, adminID)

	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(c.Rds, merchantID)
	translateService := service.NewTranslateService()
	adminSettingService := service.NewAdminSettingService()

	// 注册模版
	signupTemplate := make([][]*vues.Input, 0)
	userInfo := &models.User{}
	newUserInfo := &models.User{}

	switch bodyParams.Type {
	case models.UserRegisterTypeEmail:
		adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "emailRegisterTemplate").ToInterface(&signupTemplate)
		newUserInfo.Email = bodyParams.Email
		newUserInfo.Username = bodyParams.Email
		newUserInfo.Nickname = bodyParams.Email
		db.Where("admin_id IN ?", subAdminIDs).Where("email = ?", bodyParams.Email).Find(userInfo)
	case models.UserRegisterTypeTelephone:
		adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "phoneRegisterTemplate").ToInterface(&signupTemplate)
		newUserInfo.Telephone = bodyParams.Telephone
		newUserInfo.Username = bodyParams.Telephone
		newUserInfo.Nickname = bodyParams.Telephone
		db.Where("admin_id IN ?", subAdminIDs).Where("telephone = ?", bodyParams.Telephone).Find(userInfo)
	case models.UserRegisterTypeUsername:
		adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "usernameRegisterTemplate").ToInterface(&signupTemplate)
		newUserInfo.Username = bodyParams.Username
		newUserInfo.Nickname = bodyParams.Username
		db.Where("admin_id IN ?", subAdminIDs).Where("username = ?", bodyParams.Username).Find(userInfo)
	default:
		return c.ErrorJson("Errors")
	}

	// 验证验证码
	if !adminSettingService.GetVueInputsDisplayValue(signupTemplate, "captcha") {
		if bodyParams.CaptchaKey == "" || bodyParams.Captcha == "" {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "captchaError"))
		}
		if !captcha.VerifyString(bodyParams.CaptchaKey, bodyParams.Captcha) {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "captchaError"))
		}
	}

	// 验证用户名是否存在
	if userInfo.ID > 0 {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "usernameExist"))
	}

	// 邀请码
	newUserInfo.AdminID = merchantID

	// 是否需要邀请码
	inviteUserInfo := &models.User{}
	if !adminSettingService.GetVueInputsDisplayValue(signupTemplate, "inviteCode") || bodyParams.InviteCode != "" {
		db.Where("invite_code = ?", bodyParams.InviteCode).Where("admin_id IN ?", subAdminIDs).Find(inviteUserInfo)
		if inviteUserInfo.ID == 0 {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "inviteCodeError"))
		}
		newUserInfo.AdminID = inviteUserInfo.AdminID
		newUserInfo.ParentID = inviteUserInfo.ID
	}

	// 是否需要提现密码
	if !adminSettingService.GetVueInputsDisplayValue(signupTemplate, "securityKey") && bodyParams.SecurityKey == "" {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "securityKey", "notEmpty"))
	}

	// 是否需要用户名
	if !adminSettingService.GetVueInputsDisplayValue(signupTemplate, "username") && bodyParams.Username == "" {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "username", "notEmpty"))
	}

	// 是否需要邮箱
	if !adminSettingService.GetVueInputsDisplayValue(signupTemplate, "email") && bodyParams.Email == "" {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "email", "notEmpty"))
	}

	// 是否需要昵称
	if !adminSettingService.GetVueInputsDisplayValue(signupTemplate, "nickname") && bodyParams.Nickname == "" {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "nickname", "notEmpty"))
	}

	// 是否需要性别
	if !adminSettingService.GetVueInputsDisplayValue(signupTemplate, "sex") && bodyParams.Sex == 0 {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "sex", "notEmpty"))
	}

	// 是否需要电话号码
	if !adminSettingService.GetVueInputsDisplayValue(signupTemplate, "telephone") {
		if bodyParams.Telephone == "" {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "telephone", "notEmpty"))
		}

		countryInfo := &models.Country{}
		if bodyParams.CountryID == 0 {
			db.Where("admin_id = ?", merchantID).Order("sort ASC").Find(countryInfo)
		} else {
			db.Where("id = ?", bodyParams.CountryID).Where("admin_id = ?", merchantID).Find(countryInfo)
		}
		if countryInfo.ID == 0 {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "telephone", "notEmpty"))
		}
	}

	// 创建当前用户
	newUserInfo.CountryID = bodyParams.CountryID
	if bodyParams.Nickname != "" {
		newUserInfo.Nickname = bodyParams.Nickname
	}
	newUserInfo.Sex = bodyParams.Sex
	newUserInfo.Birthday = time.Now().Add(-20 * 365 * 24 * time.Hour)
	newUserInfo.Password = utils.EncryptPassword(bodyParams.Password)
	if bodyParams.SecurityKey == "" {
		newUserInfo.SecurityKey = utils.EncryptPassword(bodyParams.Password)
	} else {
		newUserInfo.SecurityKey = utils.EncryptPassword(bodyParams.SecurityKey)
	}
	newUserInfo.InviteCode = utils.GenerateInviteCode(6)
	newUserInfo.LastLoginAt = time.Now()
	newUserInfo.LastLoginIP = utils.GetClientIP(c.Ctx)
	newUserInfo.SignupIP = utils.GetClientIP(c.Ctx)
	newUserInfo.Avatar, _ = adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "userAvatar").ToString()

	// 添加当前用户, 并且如果上级用户是存在的, 则添加上级用户的邀请记录
	err = db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(newUserInfo).Error; err != nil {
			return err
		}

		// 是否冻结余额
		merchantAdminId, _ := adminService.GetMerchantIDWithCache(c.Rds, userInfo.AdminID)
		freezeBalanceMaps, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantAdminId, models.AdminSettingFreezeBalanceField).ToCheckbox()

		// 赠送新手福利
		inviteReward := &models.AdminSettingInviteRegisterReward{}
		adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "walletRecommendReward").ToInterface(&inviteReward)
		walletService := service.NewWalletService()
		if inviteReward.Register > 0 {
			err = walletService.IncreaseBalance(tx, c.Rds, c.Lang, models.BillTypeRegisterReward, 0, newUserInfo, inviteReward.Register)
			if err != nil {
				return err
			}

			// 冻结注册金额
			if freezeBalanceMaps[models.AdminSettingFreezeBalanceSignupFreeze] {
				result := tx.Model(&models.User{}).Where("id = ?", newUserInfo.ID).Update("frozen_amount", gorm.Expr("frozen_amount + ?", inviteReward.Register))
				if result.Error != nil {
					return result.Error
				}
			}
		}

		// 赠送邀请奖励
		if inviteUserInfo.ID > 0 && inviteReward.Invite > 0 {
			err = walletService.IncreaseBalance(tx, c.Rds, c.Lang, models.BillTypeInviteReward, newUserInfo.ID, inviteUserInfo, inviteReward.Invite)
			if err != nil {
				return err
			}

			// 冻结邀请金额
			if freezeBalanceMaps[models.AdminSettingFreezeBalanceInviteFreeze] {
				result := tx.Model(&models.User{}).Where("id = ?", inviteUserInfo.ID).Update("frozen_amount", gorm.Expr("frozen_amount + ?", inviteReward.Register))
				if result.Error != nil {
					return result.Error
				}
			}
		}
		return nil
	})
	if err != nil {
		return c.ErrorJson(err.Error())
	}

	// 生成前端Token
	formatTimePicker := &views.FormatTimePicker{}
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "merchantTokenExpire").ToInterface(formatTimePicker)
	tokenExpire := formatTimePicker.GetSecond()
	deviceNums, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "merchantDeviceNums").ToInt()
	token, err := middleware.GenerateFrontendToken(c.Ctx, newUserInfo.AdminID, merchantID, newUserInfo.ID, time.Duration(tokenExpire)*time.Second, deviceNums)
	if err != nil {
		return c.ErrorJson(err.Error())
	}

	// 获取客服链接
	chatURL, _ := adminService.GetChatURLWithCache(c.Rds, newUserInfo.AdminID)
	// 如果客服链接为空，则使用默认客服链接
	if chatURL == "" {
		chatURL, _ = adminService.GetChatURLWithCache(c.Rds, merchantID)
	}

	return c.SuccessJson(&LoginData{
		Token:    token,
		ChatURL:  chatURL,
		UserInfo: newUserInfo.ToUserInfo(),
	})
}
