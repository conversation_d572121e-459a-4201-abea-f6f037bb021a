package indexs

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// ArticleDetailsParams 文章参数
type ArticleDetailsParams struct {
	Symbol string `form:"symbol" json:"symbol"`
}

// ArticleDetails 文章详情
func ArticleDetails(c *context.CustomCtx, bodyParams *ArticleDetailsParams) error {
	adminService := service.NewAdminUserService()
	adminID, err := adminService.GetAdminUserByDomainWithCache(c.Rds, c.OriginHost)
	if err != nil {
		return c.ErrorJson(err.Error())
	}

	db := model.NewModel()
	translateService := service.NewTranslateService()
	articleInfo := models.Article{}

	merchantID, _ := adminService.GetMerchantIDWithCache(c.Rds, adminID)
	db.Where("admin_id = ? AND symbol = ?", merchantID, bodyParams.Symbol).Find(&articleInfo)
	if articleInfo.ID == 0 {
		return c.ErrorJson("article not found")
	}

	articleInfo.Title = translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, articleInfo.Title)
	articleInfo.Content = translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, articleInfo.Content)

	// 更新阅读量
	db.Model(&articleInfo).Where("id = ?", articleInfo.ID).Update("nums", articleInfo.Nums+1)

	return c.SuccessJson(articleInfo)
}
