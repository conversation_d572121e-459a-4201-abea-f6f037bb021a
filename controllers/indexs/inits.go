package indexs

import (
	"fmt"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/core/views/vues"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"

	"github.com/gomodule/redigo/redis"
)

// InitsData 商户初始化数据
type InitsData struct {
	Name       string                               `json:"name"`       //	站点名称
	Logo       string                               `json:"logo"`       //	站点Logo
	Icon       string                               `json:"icon"`       //	站点图标
	LoginImage string                               `json:"loginImage"` //	登录背景图
	Lang       string                               `json:"lang"`       //	语言
	ChatURL    string                               `json:"chatUrl"`    //	客服链接
	Notice     string                               `json:"notice"`     //	站点公告
	Introduce  string                               `json:"introduce"`  //	站点介绍
	Template   string                               `json:"template"`   //	站点模版
	StatisCode string                               `json:"statisCode"` //	统计代码
	ThemeColor string                               `json:"themeColor"` //	站点主题色
	Countrys   []*models.CountryDisplayData         `json:"countrys"`   //	国家列表
	Translte   map[string]string                    `json:"translate"`  //	翻译数据
	Langs      []*models.LangDisplayData            `json:"langs"`      //	语言列表
	Footer     map[string]*FooterData               `json:"footer"`     //	底部信息
	Menus      map[string][]*models.MenuDisplayData `json:"menus"`      //	菜单列表
	Settings   map[string]interface{}               `json:"settings"`   //	设置数据
	Channels   []*models.ChannelDisplayData         `json:"channels"`   //	渠道列表
	NoticeList []*models.NotifyDisplayData          `json:"noticeList"` //	用户通知列表
	NoticeNums int64                                `json:"noticeNums"` //	用户通知未读数
	MinScore   int64                                `json:"minScore"`   //	最低积分
}

// Inits 商户初始化配置
func Inits(c *context.CustomCtx, bodyParams *context.NoRequestBody) error {
	//	通过域名获取商户信息
	db := model.NewModel()
	adminService := service.NewAdminUserService()
	adminID, err := adminService.GetAdminUserByDomainWithCache(c.Rds, c.OriginHost)
	if err != nil {
		return c.ErrorJson(err.Error())
	}
	merchantID, _ := adminService.GetMerchantIDWithCache(c.Rds, adminID)

	data := &InitsData{}
	adminSettingService := service.NewAdminSettingService()
	walletAssetsService := service.NewWalletAssetsService()
	countryService := service.NewSystemCountryService()
	langService := service.NewSystemLangService()
	translateService := service.NewTranslateService()
	channelService := service.NewSystemChannelService()
	menuService := service.NewSystemMenuService()
	data.Name, _ = adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "siteName").ToString()
	data.Logo, _ = adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "siteLogo").ToString()
	data.ThemeColor, _ = adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, models.AdminSettingThemeColor).ToString()
	data.Icon, _ = adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "siteIcon").ToString()
	data.LoginImage, _ = adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "loginImage").ToString()
	data.Template, _ = adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "merchantTemplate").ToString()
	data.StatisCode, _ = adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "statisCode").ToString()

	//	返回语言列表
	data.Langs, _ = langService.GetAdminLanguagesWithCache(c.Rds, merchantID)
	for _, v := range data.Langs {
		if v.Symbol == c.Lang {
			data.Lang = v.Symbol
			c.Lang = v.Symbol
		}
	}
	if (c.Lang == "" || data.Lang == "") && len(data.Langs) > 0 {
		data.Lang = data.Langs[0].Symbol
		c.Lang = data.Langs[0].Symbol
	}

	//	返回国家列表
	data.Countrys, _ = countryService.GetAdminCountriesWithCache(c.Rds, merchantID)

	// 返回菜单列表
	data.Menus = make(map[string][]*models.MenuDisplayData)
	data.Menus["mobileNavs"], _ = menuService.GetFrontendMenusByTypeWithCache(c.Rds, c.Lang, merchantID, models.MenuTypeMobileNavigationBar)
	data.Menus["desktopNavs"], _ = menuService.GetFrontendMenusByTypeWithCache(c.Rds, c.Lang, merchantID, models.MenuTypeDesktopNavigationBar)
	data.Menus["commonUserMenu"], _ = menuService.GetFrontendMenusByTypeWithCache(c.Rds, c.Lang, merchantID, models.MenuTypeCommonUserMenu)
	data.Menus["commonUserMoreMenu"], _ = menuService.GetFrontendMenusByTypeWithCache(c.Rds, c.Lang, merchantID, models.MenuTypeCommonUserMoreMenu)
	data.Menus["commonWalletMenu"], _ = menuService.GetFrontendMenusByTypeWithCache(c.Rds, c.Lang, merchantID, models.MenuTypeCommonWalletMenu)

	// 返回渠道列表
	data.Channels, _ = channelService.GetChannelOptions(merchantID)

	// 翻译数据
	data.Translte, _ = translateService.GetFrontendTranslatesWithCache(c.Rds, merchantID, c.Lang)

	// 底部信息
	data.Footer = map[string]*FooterData{
		"community": {
			Label: translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "community"),
			Value: make([]map[string]interface{}, 0),
		},
		"product": {
			Label: translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "product"),
			Value: make([]map[string]interface{}, 0),
		},
		"service": {
			Label: translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "service"),
			Value: make([]map[string]interface{}, 0),
		},
		"support": {
			Label: translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "support"),
			Value: make([]map[string]interface{}, 0),
		},
		"aboutUs": {
			Label: translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, "aboutUs"),
			Value: make([]map[string]interface{}, 0),
		},
	}
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "siteFooterProduct").ToInterface(&data.Footer["product"].Value)
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "siteFooterCommunity").ToInterface(&data.Footer["community"].Value)
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "siteFooterService").ToInterface(&data.Footer["service"].Value)
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "siteFooterSupport").ToInterface(&data.Footer["support"].Value)
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "siteFooterAboutUs").ToInterface(&data.Footer["aboutUs"].Value)
	for _, v := range data.Footer {
		v.TranslateName(c.Rds, translateService, merchantID, c.Lang)
	}

	// 模版设置
	data.Settings = make(map[string]interface{})

	// 站点公告
	data.Notice, _ = adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "siteNotice").ToString()
	data.Notice = translateService.GetTranslatesEmptyByFieldsWithCache(c.Rds, merchantID, c.Lang, data.Notice)

	// 站点介绍
	data.Introduce, _ = adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "siteIntroduce").ToString()
	data.Introduce = translateService.GetTranslatesEmptyByFieldsWithCache(c.Rds, merchantID, c.Lang, data.Introduce)

	// 最低积分
	minScore, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "siteMinCreditScore").ToInt()
	data.MinScore = int64(minScore)

	// 用户名登录模版
	usernameLoginInuts := make([][]*vues.Input, 0)
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "usernameLoginTemplate").ToInterface(&usernameLoginInuts)
	data.Settings["usernameLogin"] = usernameLoginInuts

	// 邮箱登录模版
	emailLoginInputs := make([][]*vues.Input, 0)
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "emailLoginTemplate").ToInterface(&emailLoginInputs)
	data.Settings["emailLogin"] = emailLoginInputs

	// 手机登录模版
	phoneLoginInputs := make([][]*vues.Input, 0)
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "phoneLoginTemplate").ToInterface(&phoneLoginInputs)
	data.Settings["phoneLogin"] = phoneLoginInputs

	// 用户名注册模版
	usernameRegisterInputs := make([][]*vues.Input, 0)
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "usernameRegisterTemplate").ToInterface(&usernameRegisterInputs)
	data.Settings["usernameRegister"] = usernameRegisterInputs

	// 邮箱注册模版
	emailRegisterInputs := make([][]*vues.Input, 0)
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "emailRegisterTemplate").ToInterface(&emailRegisterInputs)
	data.Settings["emailRegister"] = emailRegisterInputs

	// 手机注册模版
	phoneRegisterInputs := make([][]*vues.Input, 0)
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "phoneRegisterTemplate").ToInterface(&phoneRegisterInputs)
	data.Settings["phoneRegister"] = phoneRegisterInputs

	// 用户设置
	userSettingInputs := make([][]*vues.Input, 0)
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "userSettingTemplate").ToInterface(&userSettingInputs)
	data.Settings["userSetting"] = userSettingInputs

	// 身份认证模版
	userAuthInputs := make([][]*vues.Input, 0)
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "userAuthTemplate").ToInterface(&userAuthInputs)
	data.Settings["userAuth"] = userAuthInputs

	// 提现账户银行卡模版
	withdrawAccountBankInputs := make([][]*vues.Input, 0)
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "withdrawAccountBankTemplate").ToInterface(&withdrawAccountBankInputs)
	data.Settings["withdrawAccountBank"] = withdrawAccountBankInputs

	// 提现账户资产模版
	withdrawAccountAssetInputs := make([][]*vues.Input, 0)
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "withdrawAccountAssetTemplate").ToInterface(&withdrawAccountAssetInputs)
	data.Settings["withdrawAccountAsset"] = withdrawAccountAssetInputs

	// 基础设置
	data.Settings["basics"], _ = adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "basicSettings").ToCheckbox()

	// 用户状态配置
	data.Settings["status"], _ = adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "siteUserStatus").ToCheckbox()

	// 钱包账单options
	data.Settings["billOptions"], _ = adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "walletBillOptions").ToOptions()
	for _, v := range data.Settings["billOptions"].([]*views.SelectOption) {
		v.Label = translateService.GetTranslatesByFieldsWithCache(c.Rds, merchantID, c.Lang, fmt.Sprintf(models.WalletBillTypePrefix+"%d", int(v.Value.(float64))))
	}

	// 钱包资产options
	data.Settings["walletAssets"] = walletAssetsService.GetMerchantWalletAssets(merchantID)
	data.Settings["topicSettings"], _ = adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, "topicSettings").ToCheckbox()

	// 获取购买方式
	buyLevelMannerStr, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, merchantID, models.AdminSettingBuyLevelOptions2).ToString()
	data.Settings["buyLevelManner"] = buyLevelMannerStr

	// 客服链接
	userService := service.NewUserService()
	claims := userService.GetTokenClaims(c)
	if claims != nil {
		data.ChatURL, _ = adminService.GetChatURLWithCache(c.Rds, claims.AdminID)

		// 返回用户通知列表
		data.NoticeList = make([]*models.NotifyDisplayData, 0)
		db.Model(&models.Notify{}).Where("user_id = ?", claims.UserID).
			Where("mode = ?", models.NotifyModeFrontend).
			Where("type = ?", models.NotifyTypeSystem).
			Where("status = ?", models.NotifyStatusUnread).
			Count(&data.NoticeNums).Order("id DESC").Find(&data.NoticeList)

		// 用户访问记录 - 一天只记录一次
		if string(c.Context().Referer()) != "" {
			var todayAccountCount int64
			db.Model(&models.Access{}).Where("user_id = ?", claims.UserID).Where("type = ?", models.AccessTypeRefresh).Where("DATE_FORMAT(created_at, '%Y-%m-%d') = ?", time.Now().Format("2006-01-02")).Count(&todayAccountCount)
			if todayAccountCount == 0 {
				db.Create(&models.Access{
					UserID:    claims.UserID,
					AdminID:   claims.AdminID,
					Name:      "初始化数据",
					Type:      models.AccessTypeRefresh,
					IP:        utils.GetClientIP(c.Ctx),
					Method:    models.AccessMethodPost,
					Route:     "/inits",
					UserAgent: string(c.Context().UserAgent()),
					Referer:   string(c.Context().Referer()),
					Data: models.AccessData{
						Headers: utils.StructToString(c.Ctx.GetRespHeaders()),
					},
				})
			}
		}

		// 如果用户有设置公告, 那么使用用户设置的
		userSettingInfo := &models.Setting{}
		db.Model(&models.Setting{}).Where("user_id = ?", claims.UserID).Where("field = ?", models.UserSettingsNotice).Find(userSettingInfo)
		if userSettingInfo.ID > 0 && userSettingInfo.Value != "" {
			data.Notice = userSettingInfo.Value
		}
	}

	// 如果客服链接为空，则使用默认客服链接
	if data.ChatURL == "" {
		data.ChatURL, _ = adminService.GetChatURLWithCache(c.Rds, merchantID)
	}

	// 返回商户初始化数据
	return c.SuccessJson(data)
}

// FooterData 底部信息
type FooterData struct {
	Label string                   `json:"label"`
	Value []map[string]interface{} `json:"value"`
}

// TranslateName 翻译名称
func (f *FooterData) TranslateName(rdsConn redis.Conn, translateService *service.TranslateService, merchantID uint, lang string) {
	for _, v := range f.Value {
		if v["name"] != nil {
			v["name"] = translateService.GetTranslatesByFieldsWithCache(rdsConn, merchantID, lang, v["name"].(string))
		}
	}
}
