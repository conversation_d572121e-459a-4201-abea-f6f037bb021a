package products

import (
	"zfeng/core/context"
	"zfeng/models"
	"zfeng/service"
)

// CategoryIndexParams 产品分类列表参数
type CategoryIndexParams struct {
	IDs []int `json:"ids" validate:"required"`
}

// CategoryIndex 产品分类列表
func CategoryIndex(ctx *context.CustomCtx, params *CategoryIndexParams) error {
	if ctx.Claims == nil {
		ctx.Claims = service.NewUserService().GetTokenClaims(ctx)
	}

	translateService := service.NewTranslateService()
	data := make([]*models.CategoryDisplayData, 0)
	categoryService := service.NewProductCategoryService()
	for _, v := range params.IDs {
		if v == 0 {
			data = append(data, &models.CategoryDisplayData{
				ID:       0,
				Name:     translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, "optional"),
				Children: []*models.CategoryDisplayData{},
			})
		}
		val := categoryService.ProductCategoryChildren(ctx.Rds, ctx.Lang, 0, ctx.Claims.MerchantID)
		if val != nil {
			data = append(data, val...)
		}
	}

	return ctx.SuccessJson(data)
}
