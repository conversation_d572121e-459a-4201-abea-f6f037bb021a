package products

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// OrderRevokeParams 合约撤单｜平仓｜爆仓参数
type OrderRevokeParams struct {
	ID uint `json:"id" validate:"required"` //	订单ID
}

// OrderRevoke 撤销订单
func OrderRevoke(ctx *context.CustomCtx, params *OrderRevokeParams) error {
	translateService := service.NewTranslateService()
	db := model.NewModel()
	orderInfo := models.Order{}
	if db.Where("user_id = ?", ctx.Claims.UserID).
		Where("status NOT IN ?", []int8{models.ProductOrderStatusCancelled, models.ProductOrderStatusCompleted}).
		Where("id = ?", params.ID).
		Find(&orderInfo).RowsAffected == 0 {
		return ctx.ErrorJson(translateService.GetTranslatesByFieldsWithCache(ctx.Rds, 0, ctx.Lang, "abnormalOperation"))
	}

	err := service.NewProductOrderService().ProductOrderRevoke(ctx.Rds, ctx.Lang, &orderInfo)
	if err != nil {
		return ctx.ErrorJson(translateService.GetTranslatesByFieldsWithCache(ctx.Rds, 0, ctx.Lang, "abnormalOperation"))
	}
	return ctx.SuccessOk()
}
