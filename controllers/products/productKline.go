package products

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/module/exchange/interfaces"
	"zfeng/service"
	"zfeng/utils"
)

// ProductKlineParams 产品K线图参数
type ProductKlineParams struct {
	ID       int    `json:"id" validate:"required"`  // 产品ID
	Bar      string `json:"bar" validate:"required"` // 时间粒度
	FromTime int64  `json:"fromTime"`                // 从什么时间
	ToTime   int64  `json:"toTime"`                  // 到什么时间
	Limit    int    `json:"limit"`                   // 获取数量
}

// ProductKline 产品K线图
func ProductKline(ctx *context.CustomCtx, params *ProductKlineParams) error {
	translateService := service.NewTranslateService()
	if ctx.Claims == nil {
		ctx.Claims = service.NewUserService().GetTokenClaims(ctx)
	}
	db := model.NewModel()
	productInfo := &models.Product{}
	result := db.Where("admin_id = ?", ctx.Claims.MerchantID).
		Where("id = ?", params.ID).
		Where("status = ?", models.ProductStatusEnabled).
		Find(productInfo)
	if result.RowsAffected == 0 {
		return ctx.ErrorJson(translateService.GetTranslateByFieldWithCacheToArgs(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, "abnormalOperation"))
	}

	// 解析时区
	location := utils.GetTimeZoneByLocation(ctx.TimeZone)
	// 获取该时区的当前时间
	nowTime := time.Now().In(location)
	// 计算一年前的时间
	oneYearAgo := nowTime.AddDate(-1, 0, 0)
	if params.FromTime != 0 || params.ToTime != 0 {
		nowTime = time.Unix(params.ToTime, 0)
		oneYearAgo = time.Unix(params.FromTime, 0)
	}

	if params.Limit == 0 {
		params.Limit = 100
	}
	data, err := service.NewProductService().GetKline(ctx.Rds, productInfo.Type, productInfo.Symbol, params.Bar, nowTime, oneYearAgo, int64(params.Limit), time.Second)
	if err != nil {
		data = make([]*interfaces.KlineAttrs, 0)
	}

	return ctx.SuccessJson(data)
}
