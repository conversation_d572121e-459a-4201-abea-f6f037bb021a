package products

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/module/exchange/interfaces"
	"zfeng/service"
)

// ProductTradesParams 产品交易参数
type ProductTradesParams struct {
	ID    int    `json:"id" validate:"required"` //	产品ID
	Limit string `json:"limit"`                  //	获取数量
}

// ProductTrades 产品订单交易量
func ProductTrades(ctx *context.CustomCtx, params *ProductTradesParams) error {
	if ctx.Claims == nil {
		ctx.Claims = service.NewUserService().GetTokenClaims(ctx)
	}
	translateService := service.NewTranslateService()
	productInfo := &models.Product{}
	db := model.NewModel()
	result := db.Where("id = ?", params.ID).
		Where("status = ?", models.ProductStatusEnabled).
		Find(productInfo)
	if result.RowsAffected == 0 {
		return ctx.ErrorJson(translateService.GetTranslateByFieldWithCacheToArgs(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, "abnormalOperation"))
	}

	data := make([]*interfaces.Trades, 0)
	if params.Limit == "" || params.Limit == "0" {
		params.Limit = "1"
	}
	data, _ = service.NewProductService().GetTrades(ctx.Rds, productInfo.Type, productInfo.Symbol, params.Limit)
	return ctx.SuccessJson(data)
}
