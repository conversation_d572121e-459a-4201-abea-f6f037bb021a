package products

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// OrderSettingParams 产品订单设置参数
type OrderSettingParams struct {
	ID        int     `json:"id" validate:"required"` // 订单Id
	Index     int     `json:"index" validate:"gt=0"`  // 倍数
	TakePrice float64 `json:"takePrice"`              // 止盈价格
	StopPrice float64 `json:"stopPrice"`              // 止损价格
}

// OrderSetting 产品订单设置
func OrderSetting(ctx *context.CustomCtx, params *OrderSettingParams) error {
	translateService := service.NewTranslateService()
	orderInfo := &models.Order{}
	db := model.NewModel()
	if result := db.Where("id = ?", params.ID).
		Where("user_id = ?", ctx.Claims.UserID).
		Where("type = ?", models.ProductOrderTypeContract).
		Where("status in ?", []int8{models.ProductOrderStatusRunning, models.ProductOrderStatusWaiting}).
		Find(orderInfo); result.RowsAffected == 0 {
		return ctx.ErrorJson(translateService.GetTranslatesByFieldsWithCache(ctx.Rds, 0, ctx.Lang, "abnormalOperation"))
	}

	orderInfo.Data.TakePrice = params.TakePrice
	orderInfo.Data.StopPrice = params.StopPrice
	orderInfo.Data.Index = params.Index

	if result := db.Model(&models.Order{}).
		Where("id = ?", orderInfo.ID).
		Update("data", orderInfo.Data); result.Error != nil {
		return ctx.ErrorJson(translateService.GetTranslatesByFieldsWithCache(ctx.Rds, 0, ctx.Lang, "abnormalOperation"))
	}
	return ctx.SuccessOk()
}
