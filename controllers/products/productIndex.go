package products

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// ProductIndexParams  产品列表参数
type ProductIndexParams struct {
	ProductName string            `json:"productName"` // 产品名
	CategoryID  uint              `json:"categoryId"`  // 获取分类产品
	IsLike      bool              `json:"isLike"`      // 是否自选货币
	Pagination  *model.Pagination `json:"pagination"`
}

// ProductIndex 产品列表
func ProductIndex(ctx *context.CustomCtx, params *ProductIndexParams) error {
	if ctx.Claims == nil {
		ctx.Claims = service.NewUserService().GetTokenClaims(ctx)
	}

	productService := service.NewProductService()
	data := &model.IndexData{Count: 0}
	db := productService.GetProductInfoDB(ctx.Claims.MerchantID, params.CategoryID, ctx.Claims.UserID)

	productList := make([]*models.ProductInfo, 0)
	switch {
	case params.ProductName != "":
		db.Where("p.name LIKE ?", "%"+params.ProductName+"%")
	case params.IsLike:
		db.Where("co.id > ?", 0)
	}

	// 获取产品数据
	if result := db.Order("p.sort").Count(&data.Count).Scopes(params.Pagination.Scopes()).Find(&productList); result.Error != nil {
		return result.Error
	}
	translateService := service.NewTranslateService()
	for _, v := range productList {
		if v.IsTranslate == model.BoolTrue {
			v.Name = translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, v.Name)
		}

		// 获取货币数据
		v.Ticker, _ = productService.GetTickers(ctx.Rds, v.Type, v.Symbol)
	}

	data.Items = productList
	return ctx.SuccessJson(data)
}
