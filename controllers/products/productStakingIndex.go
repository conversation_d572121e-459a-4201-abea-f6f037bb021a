package products

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// ProductStakingIndexParams  质押产品列表参数
type ProductStakingIndexParams struct {
	ID         uint              `json:"id"`
	Pagination *model.Pagination `json:"pagination"`
}

// ProductStakingIndex 产质押品列表
func ProductStakingIndex(ctx *context.CustomCtx, params *ProductStakingIndexParams) error {
	if ctx.Claims == nil {
		ctx.Claims = service.NewUserService().GetTokenClaims(ctx)
	}
	data := &model.IndexData{Items: []*models.ProductInfo{}, Count: 0}
	db := model.NewModel()
	categoryIds := make([]uint, 0)
	db.Model(&models.Category{}).Where("admin_id = ?", ctx.Claims.MerchantID).Where("type = ?", models.CategoryTypeStaking).Where("status = ?", models.CategoryStatusEnabled).Pluck("id", &categoryIds)
	productModel := db.Model(&models.Product{}).Where("category_id IN ?", categoryIds).Where("type = ?", models.ProductTypeStaking)
	if params.ID != 0 {
		db.Where("id = ?", params.ID)
	}

	// 获取产品数据
	if result := productModel.Count(&data.Count).Scopes(params.Pagination.Scopes()).Order("sort DESC").Find(&data.Items); result.Error != nil {
		return result.Error
	}

	translateService := service.NewTranslateService()
	walletAssetsService := service.NewWalletAssetsService()
	for _, v := range data.Items.([]*models.ProductInfo) {
		if v.IsTranslate == model.BoolTrue {
			v.Name = translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, v.Name)
		}
		v.AssetsInfo = walletAssetsService.GetAssetsInfo(ctx.Rds, translateService, ctx.Lang, v.AssetsID, ctx.Claims.MerchantID, ctx.Claims.UserID)
		v.SymbolAssetsInfo = walletAssetsService.GetAssetsInfo(ctx.Rds, translateService, ctx.Lang, v.Data.SymbolAssetsID, ctx.Claims.MerchantID, ctx.Claims.UserID)

	}

	return ctx.SuccessJson(data)
}
