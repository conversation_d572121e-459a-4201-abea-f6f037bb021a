package products

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/module/exchange/interfaces"
	"zfeng/service"
)

// ProductKlineListParams 产品K线图列表参数
type ProductKlineListParams struct {
	IDs   []int  `json:"ids" validate:"required"` // 产品ID
	Bar   string `json:"bar" validate:"required"` // 时间粒度
	Limit int    `json:"limit"`                   // 获取数量
}

// ProductKlineListData 产品K线图列表数据
type ProductKlineListData struct {
	Symbol string                   `json:"symbol"` //	产品标识
	Data   []*interfaces.KlineAttrs `json:"data"`   // kline数据
}

// ProductKlineList 产品K线图列表
func ProductKlineList(ctx *context.CustomCtx, params *ProductKlineListParams) error {
	if ctx.Claims == nil {
		ctx.Claims = service.NewUserService().GetTokenClaims(ctx)
	}

	db := model.NewModel()
	productList := make([]*models.Product, 0)
	db.Where("admin_id = ?", ctx.Claims.MerchantID).
		Where("id IN ?", params.IDs).
		Where("status = ?", models.ProductStatusEnabled).
		Find(&productList)

	productService := service.NewProductService()
	data := make([]*ProductKlineListData, 0)
	for _, v := range productList {
		// 获取当前的整点时间搓
		nowTime := time.Now()
		// 计算一年前的时间1
		oneYearAgo := nowTime.AddDate(-2, 0, 0)
		klineData := &ProductKlineListData{Symbol: v.Symbol}
		if params.Limit == 0 {
			params.Limit = 100
		}

		if v.Data.Bars != nil {
			for _, bar := range v.Data.Bars {
				if bar.Label == params.Bar {
					params.Bar = bar.Val
				}
			}
		}

		var err error
		klineData.Data, err = productService.GetKline(ctx.Rds, v.Type, v.Symbol, params.Bar, nowTime, oneYearAgo, int64(params.Limit), 30)
		if err != nil {
			klineData.Data = make([]*interfaces.KlineAttrs, 0)
			continue
		}

		data = append(data, klineData)
	}
	return ctx.SuccessJson(data)
}
