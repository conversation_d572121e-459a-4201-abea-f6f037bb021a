package products

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// CollectCreateParams 添加收藏参数
type CollectCreateParams struct {
	ID int `json:"id" validate:"required"` //	产品ID
}

// CollectCreate 收藏产品
func CollectCreate(ctx *context.CustomCtx, params *CollectCreateParams) error {
	db := model.NewModel()
	translateService := service.NewTranslateService()
	productInfo := &models.Product{}
	if result := db.Where("id = ?", params.ID).
		Where("admin_id = ?", ctx.Claims.MerchantID).
		Where("status = ?", models.ProductStatusEnabled).
		Find(productInfo); result.RowsAffected == 0 {
		return ctx.ErrorJson(translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, "abnormalOperation"))
	}

	collectInfo := &models.Collect{}
	result := db.Where("admin_id = ?", ctx.Claims.MerchantID).
		Where("product_id = ?", productInfo.ID).
		Where("user_id = ?", ctx.Claims.UserID).
		Find(collectInfo)
	if result.Error != nil {
		return ctx.ErrorJson(translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, "abnormalOperation"))
	}

	if collectInfo.ID > 0 {
		if collectInfo.Status == models.CollectStatusEnabled {
			collectInfo.Status = models.CollectStatusDisable
		} else {
			collectInfo.Status = models.CollectStatusEnabled
		}

		db.Model(&models.Collect{}).
			Where("id = ?", collectInfo.ID).
			Update("status", collectInfo.Status)
		return ctx.SuccessOk()
	}

	db.Create(&models.Collect{
		AdminId:   ctx.Claims.MerchantID,
		ProductId: productInfo.ID,
		UserId:    ctx.Claims.UserID,
		Status:    models.CollectStatusEnabled,
	})

	return ctx.SuccessOk()
}
