package products

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// OrderIndexParams 产品订单列表参数
type OrderIndexParams struct {
	ProductId  uint             `json:"productId"`                //	产品Id
	Type       int              `json:"type" validate:"required"` //	类型
	Status     []int            `json:"status"`                   //	状态
	Pagination model.Pagination `json:"pagination"`               // 分页数据
}

// OrderIndexData 产品订单列表返回数据
type OrderIndexData struct {
	models.Order
	ProductInfo models.ProductInfo `json:"productInfo" gorm:"foreignKey:ProductID"` //	产品信息
}

// OrderIndex 产品订单列表
func OrderIndex(ctx *context.CustomCtx, params *OrderIndexParams) error {
	data := &model.IndexData{}
	data.Items = make([]*OrderIndexData, 0)
	db := model.NewModel()
	query := db.Equal("product_id", params.ProductId).In("status", params.Status).Model(&models.Order{})
	query.Model(&models.Order{}).
		Preload("ProductInfo").
		Where("admin_id = ?", ctx.Claims.AdminID).
		Where("user_id = ?", ctx.Claims.UserID).
		Where("type = ?", params.Type).
		Count(&data.Count).
		Scopes(params.Pagination.Scopes()).
		Find(&data.Items)
	productService := service.NewProductService()
	walletAssetsService := service.NewWalletAssetsService()
	translate := service.NewTranslateService()
	for _, v := range data.Items.([]*OrderIndexData) {
		if v.Data.Rate == 0 {
			v.Data.Rate = v.Data.StakingStrategy.Rate
		}
		if v.ProductInfo.IsTranslate == model.BoolTrue {
			v.ProductInfo.Name = translate.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, v.ProductInfo.Name)
		}
		v.ProductInfo.AssetsInfo = walletAssetsService.GetAssetsInfo(ctx.Rds, translate, ctx.Lang, v.ProductInfo.AssetsID, ctx.Claims.MerchantID, ctx.Claims.UserID)
		v.ProductInfo.SymbolAssetsInfo = walletAssetsService.GetAssetsInfo(ctx.Rds, translate, ctx.Lang, v.ProductInfo.Data.SymbolAssetsID, ctx.Claims.MerchantID, ctx.Claims.UserID)
		v.ProductInfo.Ticker, _ = productService.GetTickers(ctx.Rds, v.ProductInfo.Type, v.ProductInfo.Symbol)
		v.Fee = v.ProductInfo.Fee
	}

	return ctx.SuccessJson(data)
}
