package products

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/module/exchange/interfaces"
	"zfeng/service"
)

// ProductBooksParams 产品深度参数
type ProductBooksParams struct {
	ID   int    `json:"id" validate:"required"` //	产品ID
	Size string `json:"size"`                   //	大小
}

// ProductBooks 产品深度
func ProductBooks(ctx *context.CustomCtx, params *ProductBooksParams) error {
	if ctx.Claims == nil {
		ctx.Claims = service.NewUserService().GetTokenClaims(ctx)
	}
	translateService := service.NewTranslateService()
	productInfo := &models.Product{}
	db := model.NewModel()
	result := db.Where("id = ?", params.ID).
		Where("status = ?", models.ProductStatusEnabled).
		Find(productInfo)
	if result.RowsAffected == 0 {
		return ctx.ErrorJson(translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, "abnormalOperation"))
	}

	data := &interfaces.Books{
		Asks: make([][]string, 0),
		Bids: make([][]string, 0),
	}

	if params.Size == "" || params.Size == "0" {
		params.Size = "50"
	}
	data, _ = service.NewProductService().GetBooks(ctx.Rds, productInfo.Type, productInfo.Symbol, params.Size)
	return ctx.SuccessJson(data)
}
