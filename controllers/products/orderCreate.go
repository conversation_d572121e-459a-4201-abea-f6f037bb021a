package products

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"
)

// OrderCreateParams 产品订单创建参数
type OrderCreateParams struct {
	ID        uint    `json:"id" validate:"required"`                 //	产品ID
	Side      int8    `json:"side" validate:"required,oneof=1 2 3"`   //	方向 1买 2卖 3双赢
	Type      int8    `json:"type" validate:"required,oneof=1 2 3 4"` //	类型 1币币 2合约 3期货 4质押
	Mode      int8    `json:"mode" validate:"required,oneof=1 2"`     //	模式 1限价 2市价
	Index     int     `json:"index"`                                  //	合约=倍数 期货=索引 质押=索引
	Price     float64 `json:"price"`                                  //	合约=限价价格 期权=限价价格
	TakePrice float64 `json:"takePrice"`                              //	合约=止盈价格 期权=止盈价格
	StopPrice float64 `json:"stopPrice"`                              //	合约=止损价格 期权=止损价格
	Money     float64 `json:"money" validate:"required,gte=0"`        // 	合约=购买金额 期货=购买金额 质押=购买金额
	Password  string  `json:"password"`
}

// OrderCreate 创建产品订单
func OrderCreate(ctx *context.CustomCtx, bodyParams *OrderCreateParams) error {
	adminSettingService := service.NewAdminSettingService()
	translateService := service.NewTranslateService()
	basicSettings, _ := adminSettingService.GetAdminSettingByFieldWithCache(ctx.Rds, ctx.Claims.MerchantID, models.AdminSettingBasic).ToCheckbox()
	if _, ok := basicSettings[models.AdminSettingBasicShowBuyOrderPassword]; !ok {
		return ctx.ErrorJson(translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, "formatError"))
	}
	userInfo := &models.User{}
	model.NewModel().Model(userInfo).Where("id = ?", ctx.Claims.UserID).Find(userInfo)
	if basicSettings[models.AdminSettingBasicShowBuyOrderPassword] {
		if bodyParams.Password == "" || userInfo.SecurityKey != utils.EncryptPassword(bodyParams.Password) {
			return ctx.ErrorJson(translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, "securityPasswordError"))
		}
	}
	if userInfo.Status == models.UserStatusFrozen {
		userStatusCheckBox, _ := adminSettingService.GetAdminSettingByFieldWithCache(ctx.Rds, ctx.Claims.MerchantID, "siteUserStatus").ToCheckbox()
		if _, ok := userStatusCheckBox["freezeOrder"]; ok && userStatusCheckBox["freezeOrder"] {
			tipsField, _ := adminSettingService.GetAdminSettingByFieldWithCache(ctx.Rds, ctx.Claims.MerchantID, "freezeTips").ToString()
			return ctx.ErrorJson(translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, tipsField))
		}
	}
	err := service.NewProductOrderService().ProductOrderCreate(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, &service.OrderCreateParams{
		ID:        bodyParams.ID,
		UserID:    ctx.Claims.UserID,
		Side:      bodyParams.Side,
		Type:      bodyParams.Type,
		Mode:      bodyParams.Mode,
		Index:     bodyParams.Index,
		Price:     bodyParams.Price,
		TakePrice: bodyParams.TakePrice,
		StopPrice: bodyParams.StopPrice,
		Money:     bodyParams.Money,
	})
	if err != nil {
		return ctx.ErrorJson(err.Error())
	}
	return ctx.SuccessOk()
}
