package products

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// OrderCloseParams 产品订单手动平仓参数
type OrderCloseParams struct {
	ID uint `json:"id" validate:"required"` // 订单Id
}

// OrderClose 手动平仓
func OrderClose(ctx *context.CustomCtx, params *OrderCloseParams) error {
	translateService := service.NewTranslateService()
	orderInfo := models.OrderInfo{}
	db := model.NewModel()
	result := db.Preload("ProductInfo").
		Where("id = ?", params.ID).
		Where("user_id = ?", ctx.Claims.UserID).
		Where("type = ?", models.ProductOrderTypeContract).
		Where("status = ?", models.ProductOrderStatusRunning).
		Find(&orderInfo)
	if result.RowsAffected <= 0 {
		return ctx.ErrorJson(translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, "abnormalOperation"))
	}

	if err := service.NewProductOrderService().ProductOrderClose(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, &orderInfo); err != nil {
		return ctx.ErrorJson(translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, err.Error()))
	}
	return ctx.SuccessOk()
}
