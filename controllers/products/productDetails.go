package products

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/databases"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// ProductDetailParams 产品详情参数
type ProductDetailParams struct {
	ID   int  `json:"id"`                                         //	产品ID
	Type int8 `json:"type" validate:"required,oneof=11 21 31 41"` // 分类模式 11数字货币 21外汇 31期货 41质押
}

// ProductDetailData 产品详情参数
type ProductDetailData struct {
	IsStop      bool                  `json:"isStop"`      // 是否停止交易
	FuturesRate []*models.FuturesRate `json:"futuresRate"` // 期权收益率
	models.ProductInfo
}

// ProductDetails 产品详情
func ProductDetails(ctx *context.CustomCtx, params *ProductDetailParams) error {
	if ctx.Claims == nil {
		ctx.Claims = service.NewUserService().GetTokenClaims(ctx)
	}

	// 获取产品详情
	productInfo := &ProductDetailData{}
	productService := service.NewProductService()
	db := productService.GetProductInfoDB(ctx.Claims.MerchantID, 0, ctx.Claims.UserID)
	settingService := service.NewAdminSettingService()
	if params.ID == 0 {
		preferredProducts := models.PreferredProducts{}
		_ = settingService.GetAdminSettingByFieldWithCache(ctx.Rds, ctx.Claims.MerchantID, "preferredProducts").ToInterface(&preferredProducts)
		switch params.Type {
		case models.CategoryTypeSpot:
			db.Where("p.symbol = ?", preferredProducts.Spot)
		case models.CategoryTypeForex:
			db.Where("p.symbol = ?", preferredProducts.Forex)
		case models.CategoryTypeFutures:
			db.Where("p.symbol = ?", preferredProducts.Futures)
		case models.CategoryTypeStaking:
			db.Where("p.symbol = ?", preferredProducts.Staking)
		}
		db.Where("p.category_id IN (?)", databases.Db.Model(&models.Category{}).Select("id").Where("type = ?", params.Type))
	} else {
		db.Where("p.id = ?", params.ID)
	}
	db.Find(&productInfo.ProductInfo)
	if db.RowsAffected == 0 {
		return ctx.SuccessJson(productInfo)
	}

	switch productInfo.CategoryType {
	case models.CategoryTypeFutures:
		// 获取期货收益率
		err := settingService.GetAdminSettingByFieldWithCache(ctx.Rds, ctx.Claims.MerchantID, "futuresRate").ToInterface(&productInfo.FuturesRate)
		if err != nil {
			return ctx.ErrorJson("abnormalOperation")
		}
	}

	// 产品行情信息
	productInfo.Ticker, _ = service.NewProductService().GetTickers(ctx.Rds, productInfo.Type, productInfo.Symbol)

	//	翻译多语言
	translate := service.NewTranslateService()
	if productInfo.IsTranslate == model.BoolTrue {
		productInfo.Name = translate.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, productInfo.Name)
	}
	productInfo.Desc = translate.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, productInfo.Desc)
	walletAssetsService := service.NewWalletAssetsService()
	productInfo.AssetsInfo = walletAssetsService.GetAssetsInfo(ctx.Rds, translate, ctx.Lang, productInfo.AssetsID, ctx.Claims.MerchantID, ctx.Claims.UserID)
	productInfo.SymbolAssetsInfo = walletAssetsService.GetAssetsInfo(ctx.Rds, translate, ctx.Lang, productInfo.Data.SymbolAssetsID, ctx.Claims.MerchantID, ctx.Claims.UserID)

	// 是否停止交易
	weekday := time.Now().Weekday().String()
	switch weekday {
	case "Saturday":
		productInfo.IsStop = true
	case "Sunday":
		productInfo.IsStop = true
	}

	return ctx.SuccessJson(productInfo)
}
