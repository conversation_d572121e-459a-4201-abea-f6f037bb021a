package products

import (
	"sort"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// ProductRankingParams  产品列表参数
type ProductRankingParams struct {
	CategoryId uint `json:"categoryId"` // 获取分类排行产品
}

// ProductRankingData 产品排行列表返回参数

// RankingData 排行数据
type RankingData struct {
	Increase []*models.ProductInfo `json:"increase"`
	UpToDate []*models.ProductInfo `json:"upToDate"`
	Popular  []*models.ProductInfo `json:"popular"`
}

// ProductRanking 产品排行
func ProductRanking(ctx *context.CustomCtx, params *ProductRankingParams) error {
	if ctx.Claims == nil {
		ctx.Claims = service.NewUserService().GetTokenClaims(ctx)
	}

	data := &model.IndexData{Count: 0}
	productListTmp := make([]*models.ProductInfo, 0)
	productService := service.NewProductService()
	productService.GetProductInfoDB(ctx.Claims.MerchantID, params.CategoryId, ctx.Claims.UserID).Distinct().Find(&productListTmp)
	translateService := service.NewTranslateService()
	productList := make([]*models.ProductInfo, 0)
	distinctProductList := make(map[string]bool)
	for i, v := range productListTmp {
		if !distinctProductList[v.Name] {
			distinctProductList[v.Name] = true
			if v.IsTranslate == model.BoolTrue {
				v.Name = translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, v.Name)
			}
			v.Ticker, _ = productService.GetTickers(ctx.Rds, v.Type, v.Symbol)
			if v.Ticker.Last > 0 {
				productList = append(productList, productListTmp[i])
			}
		}
	}
	if len(productList) == 0 {
		return ctx.ErrorJson("abnormalOperation")
	}

	rankingData := RankingData{}
	// 根据金额计算涨幅榜单
	sort.Slice(productList, func(i, j int) bool {
		increaseRate := (productList[i].Ticker.Last - productList[i].Ticker.Open24h) / productList[i].Ticker.Open24h
		increaseRate1 := (productList[j].Ticker.Last - productList[j].Ticker.Open24h) / productList[j].Ticker.Open24h
		return increaseRate > increaseRate1
	})
	data.Count = 3
	if len(productList) > int(data.Count) {
		rankingData.Increase = productList[:data.Count]
	}

	productService.GetProductInfoDB(ctx.Claims.MerchantID, params.CategoryId, ctx.Claims.UserID).Order("p.created_at DESC").Limit(int(data.Count)).Find(&rankingData.UpToDate)
	productService.GetProductInfoDB(ctx.Claims.MerchantID, params.CategoryId, ctx.Claims.UserID).Order("p.sort ASC").Limit(int(data.Count)).Find(&rankingData.Popular)

	for i := 0; i < int(data.Count); i++ {
		rankingData.UpToDate[i].Ticker, _ = productService.GetTickers(ctx.Rds, rankingData.UpToDate[i].Type, rankingData.UpToDate[i].Symbol)
		if rankingData.UpToDate[i].IsTranslate == model.BoolTrue {
			rankingData.UpToDate[i].Name = translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, rankingData.UpToDate[i].Name)
		}
		rankingData.Popular[i].Ticker, _ = productService.GetTickers(ctx.Rds, rankingData.Popular[i].Type, rankingData.Popular[i].Symbol)
		if rankingData.Popular[i].IsTranslate == model.BoolTrue {
			rankingData.Popular[i].Name = translateService.GetTranslatesByFieldsWithCache(ctx.Rds, ctx.Claims.MerchantID, ctx.Lang, rankingData.Popular[i].Name)
		}
	}
	data.Items = rankingData
	return ctx.SuccessJson(data)
}
