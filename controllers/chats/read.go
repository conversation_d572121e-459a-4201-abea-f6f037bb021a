package chats

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/inits/instance"
	"zfeng/models"
	"zfeng/module/socket"
)

// ReadParams 阅读消息参数
type ReadParams struct {
	IDs []uint `json:"ids" validate:"required"`
}

// Read 阅读消息
func Read(c *context.CustomCtx, bodyParams *ReadParams) error {
	db := model.NewModel()

	// 用户消息
	messageList := make([]*models.ChatsMessages, 0)
	db.Model(&models.ChatsMessages{}).Where("id IN ?", bodyParams.IDs).Where("receiver_id = ?", c.Claims.UserID).Find(&messageList)

	// 更新会话最后消息接收者ID
	db.Model(&models.ChatsMessages{}).Where("id IN ?", bodyParams.IDs).Where("receiver_id = ?", c.Claims.UserID).Update("status", models.ChatsMessagesStatusRead)

	// 发送消息给管理员
	for _, messageInfo := range messageList {
		instance.ChatsSocket.RedisUserPublish(c.Rds, socket.ConnTypeAdmin, socket.MessageOperateRead, messageInfo.SenderID, messageInfo.ID)
	}
	return c.SuccessOk()
}
