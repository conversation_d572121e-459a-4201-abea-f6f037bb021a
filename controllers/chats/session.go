package chats

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"

	"github.com/google/uuid"
)

// SessionRows 会话返回数据
type SessionRows struct {
	Session   models.ChatsSessions `json:"session"`
	AdminInfo SessionAdminInfo     `json:"adminInfo"`
}

// SessionAdminInfo 坐席用户信息
type SessionAdminInfo struct {
	ID       uint   `json:"id"`
	Avatar   string `json:"avatar"`
	Username string `json:"username"`
}

// 获取聊天会话
func Session(c *context.CustomCtx, bodyParams *context.NoRequestBody) error {
	db := model.NewModel()
	sessionInfo := models.ChatsSessions{}
	result := db.Model(&models.ChatsSessions{}).Where("user_id = ?", c.Claims.UserID).Find(&sessionInfo)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	// 坐席用户
	adminInfo := models.AdminUser{}
	db.Model(&models.AdminUser{}).Where("id = ?", c.Claims.AdminID).Find(&adminInfo)
	// 如果没有会话, 则创建一个
	if sessionInfo.ID == 0 {
		userInfo := &models.User{}
		db.Model(&models.User{}).Where("id = ?", c.Claims.UserID).Find(userInfo)
		sessionInfo.AdminID = c.Claims.AdminID
		sessionInfo.UserID = c.Claims.UserID
		sessionInfo.Name = userInfo.Username
		sessionInfo.SessionID = uuid.New().String()
		sessionInfo.Type = models.ChatsSessionsTypeUser
		sessionInfo.Data = models.ChatsSessionsData{}
		result := db.Model(&models.ChatsSessions{}).Create(&sessionInfo)
		if result.Error != nil {
			return c.ErrorJson(result.Error.Error())
		}
	} else {
		sessionInfo.Status = models.ChatsSessionsStatusActive
		result := db.Model(&models.ChatsSessions{}).Where("id = ?", sessionInfo.ID).Updates(sessionInfo)
		if result.Error != nil {
			return c.ErrorJson(result.Error.Error())
		}
	}

	return c.SuccessJson(SessionRows{
		Session: sessionInfo,
		AdminInfo: SessionAdminInfo{
			ID:       adminInfo.ID,
			Avatar:   adminInfo.Avatar,
			Username: adminInfo.Nickname,
		},
	})
}
