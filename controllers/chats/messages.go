package chats

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// MessagesParams 消息参数
type MessagesParams struct {
	SessionID  string            `json:"sessionId" validate:"required"`
	Pagination *model.Pagination `json:"pagination"`
}

// Messages 消息
func Messages(c *context.CustomCtx, bodyParams *MessagesParams) error {
	db := model.NewModel()

	// 当前会话信息
	sessionInfo := models.ChatsSessions{}
	result := db.Model(&models.ChatsSessions{}).Where("type = ?", models.ChatsSessionsTypeUser).Where("session_id = ?", bodyParams.SessionID).Where("user_id = ?", c.Claims.UserID).First(&sessionInfo)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	data := &model.IndexData{Items: make([]*models.ChatsMessages, 0), Count: 0}
	db.Model(&models.ChatsMessages{}).Where("session_id = ?", bodyParams.SessionID).
		Count(&data.Count).Scopes(bodyParams.Pagination.Scopes()).
		Find(&data.Items)

	if data.Count == 0 {
		// 如果没有消息, 那么添加问候语
		translateService := service.NewTranslateService()
		firstGreeting := translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "chatsFirstGreeting")
		messages := data.Items.([]*models.ChatsMessages)
		messages = append(messages, &models.ChatsMessages{
			AdminID:    c.Claims.AdminID,
			SessionID:  sessionInfo.SessionID,
			SenderID:   sessionInfo.AdminID,
			ReceiverID: c.Claims.UserID,
			SenderType: models.ChatsMessagesSenderTypeAdmin,
			Message:    firstGreeting,
			Type:       models.ChatsMessagesTypeText,
			Status:     models.ChatsMessagesStatusUnread,
			BaseModel:  model.BaseModel{CreatedAt: time.Now()},
		})
		data.Items = messages
	}

	return c.SuccessJson(data)
}
