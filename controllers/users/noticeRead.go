package users

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// NoticeReadParams 消息阅读
type NoticeReadParams struct {
	ID uint `json:"id" validate:"required"`
}

// NoticeRead 消息阅读
func NoticeRead(c *context.CustomCtx, bodyParams *NoticeReadParams) error {
	db := model.NewModel()
	notify := &models.Notify{}
	db.Model(&models.Notify{}).Where("user_id = ?", c.Claims.UserID).Where("mode = ?", models.NotifyModeFrontend).Where("id = ?", bodyParams.ID).Find(notify)

	// 如果消息存在, 那么修改当前状态
	if notify.ID > 0 {
		notify.Status = models.NotifyStatusRead
		db.Save(notify)
	}

	return c.SuccessJson(notify)
}
