package users

import (
	"strconv"
	"strings"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// InviteRows 邀请信息
type InviteRows struct {
	InviteReward float64             `json:"inviteReward"` //	邀请奖励
	InviteCount  int64               `json:"inviteCount"`  //	邀请人数
	RewardAmount float64             `json:"rewardAmount"` //	奖励金额
	SignupAmount float64             `json:"signupAmount"` //	注册奖励
	InviteRule   string              `json:"inviteRule"`   //	邀请规则
	InviteRecord []*InviteRecordRows `json:"inviteRecord"` //	邀请记录
}

// InviteRecordRows 邀请记录
type InviteRecordRows struct {
	ID        uint      `json:"id"`        //	邀请ID
	Nickname  string    `json:"nickname"`  //	用户名
	CreatedAt time.Time `json:"createdAt"` //	创建时间
}

// Invite 邀请信息
func Invite(c *context.CustomCtx, bodyParams *context.NoRequestBody) error {
	db := model.NewModel()
	data := &InviteRows{InviteRecord: make([]*InviteRecordRows, 0)}

	adminSettingService := service.NewAdminSettingService()
	translateService := service.NewTranslateService()
	inviteReward := &models.AdminSettingInviteRegisterReward{}
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, "walletRecommendReward").ToInterface(&inviteReward)
	data.RewardAmount = inviteReward.Invite
	data.SignupAmount = inviteReward.Register

	// 人数和总奖励
	db.Model(&models.User{}).Where("parent_id = ?", c.Claims.UserID).Count(&data.InviteCount)
	db.Model(&models.WalletBill{}).Where("user_id = ?", c.Claims.UserID).Where("type = ?", models.BillTypeInviteReward).Select("IFNULL(sum(money), 0)").Scan(&data.InviteReward)

	// 邀请规则
	data.InviteRule, _ = translateService.GetTranslateByFieldWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "inviteRuleContent")
	data.InviteRule = strings.ReplaceAll(data.InviteRule, "{inviteReward}", strconv.FormatFloat(data.RewardAmount, 'f', -1, 64))
	data.InviteRule = strings.ReplaceAll(data.InviteRule, "{signupReward}", strconv.FormatFloat(data.SignupAmount, 'f', -1, 64))

	// 邀请记录
	db.Model(&models.User{}).Where("parent_id = ?", c.Claims.UserID).Order("created_at DESC").Limit(20).Find(&data.InviteRecord)

	return c.SuccessJson(data)
}
