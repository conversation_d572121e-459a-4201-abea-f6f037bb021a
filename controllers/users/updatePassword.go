package users

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"
)

// UpdatePasswordParams 更新密码参数
type UpdatePasswordParams struct {
	Type        int8   `json:"type" validate:"required"`
	OldPassword string `json:"oldPassword" validate:"required"`
	NewPassword string `json:"newPassword" validate:"required,min=6,max=20"`
}

// UpdatePassword 更新密码
func UpdatePassword(c *context.CustomCtx, bodyParams *UpdatePasswordParams) error {
	db := model.NewModel()
	userInfo := &models.User{}
	translateService := service.NewTranslateService()
	db.Where("id = ?", c.Claims.UserID).First(userInfo)

	switch bodyParams.Type {
	case 1:
		if userInfo.Password != utils.EncryptPassword(bodyParams.OldPassword) {
			return c.<PERSON>rror<PERSON>son(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "password", "notCorrect"))
		}
		// 更新密码
		db.Model(&models.User{}).Where("id = ?", userInfo.ID).Update("password", utils.EncryptPassword(bodyParams.NewPassword))
	case 2:
		if userInfo.SecurityKey != utils.EncryptPassword(bodyParams.OldPassword) {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "securityKey", "notCorrect"))
		}
		// 更新安全密钥
		db.Model(&models.User{}).Where("id = ?", userInfo.ID).Update("security_key", utils.EncryptPassword(bodyParams.NewPassword))
	}

	return c.SuccessOk()
}
