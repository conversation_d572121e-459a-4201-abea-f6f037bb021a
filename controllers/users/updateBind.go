package users

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"

	"gorm.io/gorm"
)

// 更新绑定
type UpdateBindParams struct {
	Type      int8   `json:"type" validate:"required"`
	Value     string `json:"value" validate:"required,max=50"`
	CountryID uint   `json:"countryId"`
	Password  string `json:"password" validate:"required"`
}

// UpdateBind 更新绑定
func UpdateBind(c *context.CustomCtx, bodyParams *UpdateBindParams) error {
	db := model.NewModel()
	userInfo := &models.User{}
	translateService := service.NewTranslateService()
	db.Where("id = ?", c.Claims.UserID).First(userInfo)

	if userInfo.SecurityKey != utils.EncryptPassword(bodyParams.Password) {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.Merchant<PERSON>, c.<PERSON>, "securityKey", "notCorrect"))
	}

	var result *gorm.DB
	switch bodyParams.Type {
	case 1:
		if !utils.IsEmail(bodyParams.Value) {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "formatError", "email"))
		}

		// 绑定邮箱, 如果邮箱跟用户名一样，那么一起修改
		if userInfo.Username == userInfo.Email {
			userInfo.Email = bodyParams.Value
			userInfo.Username = bodyParams.Value
			result = db.Model(&models.User{}).Where("id = ?", userInfo.ID).Updates(userInfo)
		} else {
			result = db.Model(&models.User{}).Where("id = ?", userInfo.ID).Update("email", bodyParams.Value)
		}
		if result.Error != nil {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "formatError", "email"))
		}
	case 2:
		if !utils.IsTelephone(bodyParams.Value) {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "formatError", "telephone"))
		}

		// 如果有上传国家ID，则验证国家是否存在
		countryInfo := &models.Country{}
		db.Where("id = ?", bodyParams.CountryID).Where("admin_id = ?", c.Claims.MerchantID).Find(countryInfo)
		if countryInfo.ID == 0 {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "country", "notEmpty"))
		}

		// 绑定手机, 如果手机跟用户名一样，那么一起修改
		if userInfo.Username == userInfo.Telephone {
			userInfo.Telephone = bodyParams.Value
			userInfo.Username = bodyParams.Value
			result = db.Model(&models.User{}).Where("id = ?", userInfo.ID).Updates(userInfo)
		} else {
			result = db.Model(&models.User{}).Where("id = ?", userInfo.ID).Update("telephone", bodyParams.Value)
		}
		if result.Error != nil {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "formatError", "telephone"))
		}
	}

	return c.SuccessOk()
}
