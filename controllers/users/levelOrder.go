package users

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"

	"gorm.io/gorm"
)

// LevelOrderParams 升级等级参数
type LevelOrderParams struct {
	LevelID  uint   `json:"levelId"`
	Password string `json:"password"`
}

// LevelOrder 升级等级
func LevelOrder(c *context.CustomCtx, bodyParams *LevelOrderParams) error {
	db := model.NewModel()
	translateService := service.NewTranslateService()
	userInfo := &models.User{}
	db.Model(&models.User{}).Where("id = ?", c.Claims.UserID).First(userInfo)

	adminSettingService := service.NewAdminSettingService()

	// 判断是否需要提现密码
	basicSettings, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, models.AdminSettingBasic).ToCheckbox()
	if _, ok := basicSettings[models.AdminSettingBasicShowBuyLevelPassword]; !ok {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "formatError"))
	}
	if basicSettings[models.AdminSettingBasicShowBuyLevelPassword] {
		if bodyParams.Password == "" || userInfo.SecurityKey != utils.EncryptPassword(bodyParams.Password) {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "securityPasswordError"))
		}
	}
	if userInfo.Status == models.UserStatusFrozen {
		userStatusCheckBox, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, "siteUserStatus").ToCheckbox()
		if _, ok := userStatusCheckBox["freezeOrder"]; ok && userStatusCheckBox["freezeOrder"] {
			tipsField, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, "freezeTips").ToString()
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, tipsField))
		}
	}

	levelInfo := models.Level{}
	result := db.Model(&models.Level{}).Where("id = ?", bodyParams.LevelID).First(&levelInfo)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	// 检查用户是否已经升级 - 仅对全额购买和补差价购买进行检查
	userLevelInfo := &models.UserLevel{}
	result = db.Model(&models.UserLevel{}).Where("user_id = ?", userInfo.ID).Find(userLevelInfo)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	// 获取购买方式
	buyLevelMannerStr, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, models.AdminSettingBuyLevelOptions2).ToString()
	switch buyLevelMannerStr {
	case models.UserLevelBuyTypeFullAmount:
	case models.UserLevelBuyTypeDifference:
		// 补差价购买逻辑
		if userLevelInfo.ID > 0 && userLevelInfo.Status == models.UserLevelStatusEnabled {
			// 获取当前等级的价格
			currentLevelPrice := 0.0
			if len(userLevelInfo.Data.Levels) > 0 {
				// 获取当前等级价格
				for _, level := range userLevelInfo.Data.Levels {
					if level.Money > currentLevelPrice {
						currentLevelPrice = level.Money
					}
				}
			}

			// 如果新等级价格高于当前等级，计算差价
			if levelInfo.Money > currentLevelPrice {
				levelInfo.Money = levelInfo.Money - currentLevelPrice
			} else {
				// 如果新等级价格低于或等于当前等级，可以免费升级
				levelInfo.Money = 0
			}
		}

	case models.UserLevelBuyTypeClassification:
		// 按等级分类购买逻辑
		// 检查用户是否已经拥有该等级
		hasLevel := false
		if userLevelInfo.ID > 0 {
			for _, level := range userLevelInfo.Data.Levels {
				if level.ID == levelInfo.ID {
					hasLevel = true
					break
				}
			}
		}

		if hasLevel {
			translate, _ := translateService.GetTranslateByFieldWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "alreadyHasLevel")
			return c.ErrorJson(translate)
		}

	default:
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "formatError"))
	}

	walletService := service.NewWalletService()

	// 只有在全额购买和补差价购买模式下才检查等级高低
	if buyLevelMannerStr != models.UserLevelBuyTypeClassification {
		if userLevelInfo.ID > 0 {
			// 获取用户当前最高等级
			highestSymbol := int8(-1)
			if len(userLevelInfo.Data.Levels) > 0 {
				for _, level := range userLevelInfo.Data.Levels {
					if level.Symbol > highestSymbol {
						highestSymbol = level.Symbol
					}
				}
			}

			if highestSymbol >= levelInfo.Symbol {
				translate, _ := translateService.GetTranslateByFieldWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "formatError")
				return c.ErrorJson(translate)
			}
		}
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		// 花费余额
		err := walletService.SpendBalance(tx, c.Rds, c.Lang, models.BillTypeMembershipPurchase, levelInfo.ID, userInfo, levelInfo.Money)
		if err != nil {
			return err
		}

		// 更新用户等级
		return models.UpdateUserLevel(tx, userInfo, userLevelInfo, levelInfo, buyLevelMannerStr)
	})
	if err != nil {
		return c.ErrorJson(err.Error())
	}

	return c.SuccessOk()
}
