package users

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// Info 用户信息
func Info(c *context.CustomCtx, _ *context.NoRequestBody) error {
	db := model.NewModel()
	userInfo := &models.User{}
	result := db.Where("id = ?", c.Claims.UserID).First(userInfo)
	if result.Error != nil {
		return c.Error<PERSON>son(result.Error.Error())
	}

	userService := service.NewUserService()
	userInfoData := userInfo.ToUserInfo()
	userInfoData.LevelInfo = userService.GetUserLevelInfo(c.Rds, c.Claims.MerchantID, c.Claims.UserID, c.Lang)
	userInfoData.AuthStatus = userService.GetUserAuthStatus(c.Rds, c.Claims.MerchantID, c.Claims.UserID, models.UserAuthModeIdentity)
	userInfoData.AuthAddressStatus = userService.GetUserAuthStatus(c.Rds, c.Claims.MerchantID, c.Claims.UserID, models.UserAuthModeAddress)
	return c.SuccessJson(userInfoData)
}
