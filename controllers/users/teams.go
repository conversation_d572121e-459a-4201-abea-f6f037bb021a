package users

import (
	"fmt"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// TeamsRows 团队信息
type TeamsRows struct {
	TotalCount   int64               `json:"totalCount"`   //	总人数
	TotalAmount  float64             `json:"totalAmount"`  //	总收益
	RewardRecord []*RewardRecordRows `json:"rewardRecord"` //	收益记录
}

// RewardRecordRows 收益记录
type RewardRecordRows struct {
	UID       uint      `json:"uid" gorm:"column:source_id"` //	来源ID
	CreatedAt time.Time `json:"createdAt"`                   //	奖励时间
	Name      string    `json:"name" gorm:"-"`               //	奖励名称
	Type      int8      `json:"type"`                        //	奖励类型
	Money     float64   `json:"money"`                       //	奖励金额
}

func Teams(c *context.CustomCtx, bodyParams *context.NoRequestBody) error {
	db := model.NewModel()
	data := &TeamsRows{}

	// 团队总人数
	db.Model(&models.User{}).Where("parent_id = ?", c.Claims.UserID).Count(&data.TotalCount)

	// 收益类型
	incomeTypes := []int8{
		models.BillTypeInviteReward, models.BillTypeDistributionReward,
	}
	// 团队总收益
	db.Model(&models.WalletBill{}).Where("user_id = ?", c.Claims.UserID).Where("type IN (?)", incomeTypes).Where("assets_id = 0").Select("IFNULL(SUM(money), 0)").Scan(&data.TotalAmount)

	// 收益记录
	db.Model(&models.WalletBill{}).Where("user_id = ?", c.Claims.UserID).Where("type IN (?)", incomeTypes).Where("assets_id = 0").Order("created_at DESC").Limit(50).Find(&data.RewardRecord)

	translateService := service.NewTranslateService()
	for _, v := range data.RewardRecord {
		v.Name = translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, fmt.Sprintf(models.WalletBillTypePrefix+"%d", v.Type))
	}

	return c.SuccessJson(data)
}
