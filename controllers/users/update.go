package users

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// UpdateParams 更新用户参数
type UpdateParams struct {
	Avatar   string                   `json:"avatar"`
	Nickname string                   `json:"nickname" validate:"omitempty,min=3,max=20"`
	Sex      int8                     `json:"sex"`
	Birthday model.GormDateTimeParams `json:"birthday"`
	Desc     string                   `json:"desc" validate:"omitempty,max=200"`
}

// Update 更新用户
func Update(c *context.CustomCtx, bodyParams *UpdateParams) error {
	db := model.NewModel()
	bodyParams.Birthday = model.FormatDateTimeToRFC3339(bodyParams.Birthday, c.TimeZone)
	db.Model(&models.User{}).Where("id = ?", c.Claims.UserID).Updates(bodyParams)

	return c.SuccessOk()
}
