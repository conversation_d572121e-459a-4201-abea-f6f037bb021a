package users

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// AuthInfoParams 获取用户认证信息参数
type AuthInfoParams struct {
	Mode int8 `json:"mode" validate:"required"`
}

// AuthInfo 获取用户认证信息
func AuthInfo(c *context.CustomCtx, bodyParams *AuthInfoParams) error {
	db := model.NewModel()
	authInfo := &models.UserAuth{}
	db.Model(&models.UserAuth{}).Where("user_id = ?", c.Claims.UserID).Where("mode = ?", bodyParams.Mode).Find(authInfo)

	return c.Success<PERSON>son(authInfo)
}
