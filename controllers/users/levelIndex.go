package users

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// LevelIndex 等级列表
func LevelIndex(c *context.CustomCtx, bodyParams *context.NoRequestBody) error {
	db := model.NewModel()
	data := make([]*models.Level, 0)

	db.Model(&models.Level{}).Where("status = ?", models.LevelStatusEnabled).Where("admin_id = ?", c.Claims.MerchantID).Find(&data)

	translateService := service.NewTranslateService()
	for _, v := range data {
		v.Name = translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, v.Name)
		v.Desc = translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, v.Desc)
	}

	return c.<PERSON><PERSON>(data)
}
