package users

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
)

// NoticeIndexParams 消息列表
type NoticeIndexParams struct {
	Pagination *model.Pagination `json:"pagination"`
}

// NoticeIndex 消息列表
func NoticeIndex(c *context.CustomCtx, bodyParams *NoticeIndexParams) error {
	db := model.NewModel()
	data := &model.IndexData{Items: make([]*models.Notify, 0), Count: 0}

	db.Model(&models.Notify{}).Where("user_id = ?", c.Claims.UserID).
		Where("mode = ?", models.NotifyModeFrontend).
		Count(&data.Count).Scopes(bodyParams.Pagination.Scopes()).
		Find(&data.Items)

	return c.<PERSON><PERSON><PERSON>(data)
}
