package users

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/core/views/vues"
	"zfeng/models"
	"zfeng/service"
)

// AuthSubmitParams 提交用户认证参数
type AuthSubmitParams struct {
	Mode     int8   `json:"mode" validate:"required"`
	Type     int8   `json:"type" validate:"required"`
	RealName string `json:"realName"`
	IDNumber string `json:"idNumber"`
	Photo1   string `json:"photo1"`
	Photo2   string `json:"photo2"`
	Photo3   string `json:"photo3"`
	Address  string `json:"address"`
}

// AuthSubmit 提交用户认证
func AuthSubmit(c *context.CustomCtx, bodyParams *AuthSubmitParams) error {
	db := model.NewModel()
	authInfo := &models.UserAuth{}
	result := db.Model(&models.UserAuth{}).Where("user_id = ?", c.Claims.UserID).Where("mode = ?", bodyParams.Mode).Find(authInfo)
	if result.Error != nil {
		return c.Error<PERSON>son(result.Error.Error())
	}

	adminSettingService := service.NewAdminSettingService()
	translateService := service.NewTranslateService()
	userAuthTemplate := make([][]*vues.Input, 0)
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, "userAuthTemplate").ToInterface(&userAuthTemplate)
	if !adminSettingService.GetVueInputsDisplayValue(userAuthTemplate, "realName") && bodyParams.RealName == "" {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "idName", "notEmpty"))
	}
	if !adminSettingService.GetVueInputsDisplayValue(userAuthTemplate, "idNumber") && bodyParams.IDNumber == "" {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "idNumber", "notEmpty"))
	}
	if !adminSettingService.GetVueInputsDisplayValue(userAuthTemplate, "address") && bodyParams.Address == "" {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "idAddress", "notEmpty"))
	}
	if !adminSettingService.GetVueInputsDisplayValue(userAuthTemplate, "photo1") && bodyParams.Photo1 == "" {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "idPhoto1", "notEmpty"))
	}
	if !adminSettingService.GetVueInputsDisplayValue(userAuthTemplate, "photo2") && bodyParams.Photo2 == "" {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "idPhoto2", "notEmpty"))
	}
	if !adminSettingService.GetVueInputsDisplayValue(userAuthTemplate, "photo3") && bodyParams.Photo3 == "" {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "idPhoto3", "notEmpty"))
	}

	authInfo.AdminID = c.Claims.AdminID
	authInfo.UserID = c.Claims.UserID
	authInfo.RealName = bodyParams.RealName
	authInfo.IDNumber = bodyParams.IDNumber
	authInfo.Photo1 = bodyParams.Photo1
	authInfo.Photo2 = bodyParams.Photo2
	authInfo.Photo3 = bodyParams.Photo3
	authInfo.Address = bodyParams.Address
	authInfo.Type = bodyParams.Type
	authInfo.Status = models.UserAuthStatusPending

	if authInfo.ID == 0 {
		result = db.Create(authInfo)
	} else {
		result = db.Save(authInfo)
	}
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}
	return c.SuccessOk()
}
