package controllers

import (
	"zfeng/controllers/chats"
	"zfeng/controllers/indexs"
	"zfeng/controllers/notifys"
	"zfeng/controllers/products"
	"zfeng/controllers/users"
	"zfeng/controllers/wallets"
	"zfeng/core/context"
	"zfeng/inits/instance"
	"zfeng/inits/instance/markets"
	"zfeng/middleware"
	"zfeng/module/socket"

	"github.com/gofiber/fiber/v2"
	"github.com/gomodule/redigo/redis"
)

// SetupRoutes 设置路由
func SetupRoutes(app *fiber.App) {
	app.Get("/", context.NewHandler(indexs.NewIndex)).Name("首页")
	app.Post("/captcha/create", indexs.NewCaptcha).Name("生成验证码")
	app.Get("/captcha/:captchaId", indexs.ShowCaptcha).Name("显示验证码")
	app.Get("/inits", context.NewHandler[context.NoRequestBody](indexs.Inits)).Name("初始化数据")
	app.Post("/login", context.NewHandler[indexs.LoginParams](indexs.Login)).Name("登录")
	app.Post("/signup", context.NewHandler[indexs.SignupParams](indexs.Signup)).Name("注册")
	app.Post("/helpers", context.NewHandler[context.NoRequestBody](indexs.Helpers)).Name("帮助中心")
	app.Post("/home", context.NewHandler[context.NoRequestBody](indexs.Home)).Name("首页信息")
	app.Post("/article/index", context.NewHandler[indexs.ArticleIndexParams](indexs.ArticleIndex)).Name("文章列表")
	app.Post("/article/detail", context.NewHandler[indexs.ArticleDetailsParams](indexs.ArticleDetails)).Name("文章详情")
	app.Get("/chats/ws", instance.NewChatsSocketConn(socket.ConnTypeUser)).Name("前台聊天")
	app.Get("/product/ws", markets.NewProductSocketConn(socket.ConnTypeProduct)).Name("前台产品")
	app.Post("/home", context.NewHandler[context.NoRequestBody](indexs.Home)).Name("首页信息")

	app.Post("/product/klines", context.NewHandler[products.ProductKlineListParams](products.ProductKlineList)).Name("产品K线图列表")
	app.Post("/product/kline", context.NewHandler[products.ProductKlineParams](products.ProductKline)).Name("产品K线图")
	app.Post("/product/trades", context.NewHandler[products.ProductTradesParams](products.ProductTrades)).Name("产品订单交易量")
	app.Post("/product/books", context.NewHandler[products.ProductBooksParams](products.ProductBooks)).Name("产品深度列表")
	app.Post("/product/details", context.NewHandler[products.ProductDetailParams](products.ProductDetails)).Name("产品详情")
	app.Post("/product/index", context.NewHandler[products.ProductIndexParams](products.ProductIndex)).Name("产品列表")
	app.Post("/product/staking/index", context.NewHandler[products.ProductStakingIndexParams](products.ProductStakingIndex)).Name("质押产品列表")
	app.Post("/product/ranking/index", context.NewHandler[products.ProductRankingParams](products.ProductRanking)).Name("产品排行列表")
	app.Post("/product/category/index", context.NewHandler[products.CategoryIndexParams](products.CategoryIndex)).Name("产品分类列表")

	// 异步通知
	app.Post("/notifys/crypto", context.NewHandler[notifys.CryptoParams](notifys.Crypto)).Name("异步通知加密货币")
	app.Post("/notifys/deposit/order", context.NewHandler[notifys.DepositOrderParams](notifys.DepositOrder)).Name("异步回调充值订单")

	// 添加 /auth 路由组，使用 JWT 中间件
	authRuoter := app.Group("/v1", middleware.InitJWT(middleware.FrontendPrivateKey, successHandler))
	authRuoter.Post("/upload", context.NewHandler(indexs.Upload)).Name("上传文件")

	// 聊天
	authRuoter.Post("/chats/session", context.NewHandler[context.NoRequestBody](chats.Session)).Name("聊天会话")
	authRuoter.Post("/chats/messages", context.NewHandler[chats.MessagesParams](chats.Messages)).Name("聊天消息")
	authRuoter.Post("/chats/send", context.NewHandler[chats.SendParams](chats.Send)).Name("发送消息")
	authRuoter.Post("/chats/read", context.NewHandler[chats.ReadParams](chats.Read)).Name("阅读消息")

	authRuoter.Post("/users/info", context.NewHandler[context.NoRequestBody](users.Info)).Name("用户信息")
	authRuoter.Post("/users/update", context.NewHandler[users.UpdateParams](users.Update)).Name("更新用户")
	authRuoter.Post("/users/update/password", context.NewHandler[users.UpdatePasswordParams](users.UpdatePassword)).Name("更新密码")
	authRuoter.Post("/users/update/bind", context.NewHandler[users.UpdateBindParams](users.UpdateBind)).Name("更新绑定")

	// 用户等级
	authRuoter.Post("/users/level/index", context.NewHandler[context.NoRequestBody](users.LevelIndex)).Name("等级列表")
	authRuoter.Post("/users/level/order", context.NewHandler[users.LevelOrderParams](users.LevelOrder)).Name("升级等级")

	// 用户认证
	authRuoter.Post("/users/auth/info", context.NewHandler[users.AuthInfoParams](users.AuthInfo)).Name("用户认证信息")
	authRuoter.Post("/users/auth/submit", context.NewHandler[users.AuthSubmitParams](users.AuthSubmit)).Name("提交用户认证")

	// 邀请
	authRuoter.Post("/users/invite", context.NewHandler[context.NoRequestBody](users.Invite)).Name("邀请信息")

	// 团队
	authRuoter.Post("/users/teams", context.NewHandler[context.NoRequestBody](users.Teams)).Name("团队信息")

	// 消息
	authRuoter.Post("/users/notice/index", context.NewHandler[users.NoticeIndexParams](users.NoticeIndex)).Name("消息列表")
	authRuoter.Post("/users/notice/read", context.NewHandler[users.NoticeReadParams](users.NoticeRead)).Name("消息阅读")

	// 钱包
	authRuoter.Post("/wallets/index", context.NewHandler[context.NoRequestBody](wallets.Index)).Name("钱包首页")
	authRuoter.Post("/wallets/payment", context.NewHandler[wallets.PaymentParams](wallets.Payment)).Name("支付列表")
	authRuoter.Post("/wallets/assets", context.NewHandler[context.NoRequestBody](wallets.Assets)).Name("资产列表")
	authRuoter.Post("/wallets/order/index", context.NewHandler[wallets.OrderIndexBodyParams](wallets.OrderIndex)).Name("充提订单列表")
	authRuoter.Post("/wallets/bill/index", context.NewHandler[wallets.BillIndexParams](wallets.BillIndex)).Name("账单列表")
	authRuoter.Post("/wallets/transfer", context.NewHandler[wallets.TransferParams](wallets.Transfer)).Name("转账")
	authRuoter.Post("/wallets/swaps", context.NewHandler[wallets.SwapsParams](wallets.Swaps)).Name("兑换")
	authRuoter.Post("/wallets/swaps/preview", context.NewHandler[wallets.SwapsPreviewParams](wallets.SwapsPreview)).Name("兑换预览")
	authRuoter.Post("/wallets/withdraw", context.NewHandler[wallets.WithdrawParams](wallets.Withdraw)).Name("提现")
	authRuoter.Post("/wallets/deposit", context.NewHandler[wallets.DepositParams](wallets.Deposit)).Name("充值")
	authRuoter.Post("/wallets/deposit/info", context.NewHandler[wallets.DepositInfoParams](wallets.DepositInfo)).Name("充值信息")

	// 提现账户
	authRuoter.Post("/wallets/account/index", context.NewHandler[context.NoRequestBody](wallets.AccountIndex)).Name("提现账户列表")
	authRuoter.Post("/wallets/account/create", context.NewHandler[wallets.AccountCreateParams](wallets.AccountCreate)).Name("新增提现账户")
	authRuoter.Post("/wallets/account/update", context.NewHandler[wallets.AccountUpdateParams](wallets.AccountUpdate)).Name("更新提现账户")
	authRuoter.Post("/wallets/account/delete", context.NewHandler[wallets.AccountDeleteParams](wallets.AccountDelete)).Name("删除提现账户")

	// 产品管理
	authRuoter.Post("/product/collect/create", context.NewHandler[products.CollectCreateParams](products.CollectCreate)).Name("添加收藏产品")
	authRuoter.Post("/product/tactics/create", context.NewHandler[products.TacticsCreateParams](products.TacticsCreate)).Name("添加产品策略")

	authRuoter.Post("/product/order/create", context.NewHandler[products.OrderCreateParams](products.OrderCreate)).Name("创建产品订单")
	authRuoter.Post("/product/order/index", context.NewHandler[products.OrderIndexParams](products.OrderIndex)).Name("产品订单列表")
	authRuoter.Post("/product/order/setting", context.NewHandler[products.OrderSettingParams](products.OrderSetting)).Name("产品订单设置")
	authRuoter.Post("/product/order/revoke", context.NewHandler[products.OrderRevokeParams](products.OrderRevoke)).Name("产品订单撤销")
	authRuoter.Post("/product/order/close", context.NewHandler[products.OrderCloseParams](products.OrderClose)).Name("产品订单手动平仓")
}

// successHandler 处理 JWT 验证成功后的逻辑
func successHandler(rdsConn redis.Conn, claims *context.TokenClaims, c *fiber.Ctx) error {
	// 用户操作路由需要记录的数据
	// TODO...
	return nil
}
