package notifys

import (
	"gorm.io/gorm"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"
)

type DepositOrderParams struct {
	AppID     string  `json:"appId" validate:"required"`     // 应用ID
	UserID    uint    `json:"userId" validate:"required"`    // 用户ID
	Symbol    string  `json:"symbol" validate:"required"`    // 产品标识：trc20_usdt | erc20_usdt
	Timestamp int64   `json:"timestamp" validate:"required"` // 时间戳
	Nonce     string  `json:"nonce" validate:"required"`     // 随机字符串
	Sign      string  `json:"sign" validate:"required"`      // 签名
	Amount    float64 `json:"amount" validate:"required"`    //	金额
	Hash      string  `json:"hash" validate:"required"`      // 哈希
	Address   string  `json:"address" validate:"required"`   // 地址
}

// DepositOrder 充值订单回调
func DepositOrder(c *context.CustomCtx, bodyParams *DepositOrderParams) error {
	db := model.NewModel()

	// 检查hash 订单是否存在, 如果存在那么直接返回
	walletOrderInfo := models.WalletOrder{}
	result := db.Where("order_sn = ?", bodyParams.Hash).Find(&walletOrderInfo)
	if result.Error != nil {
		return result.Error
	}
	// 如果订单存在 那么直接返回
	if walletOrderInfo.ID == 0 {
		// 签名内容
		paramsMap := map[string]interface{}{
			"appId":     bodyParams.AppID,
			"userId":    bodyParams.UserID,
			"symbol":    bodyParams.Symbol,
			"timestamp": bodyParams.Timestamp,
			"nonce":     bodyParams.Nonce,
			"sign":      bodyParams.Sign,
			"hash":      bodyParams.Hash,
		}

		// 查询对应的支付
		paymentInfo := models.WalletPayment{}
		result = db.Where("conf->>'$.appKey' = ?", bodyParams.AppID).
			Where("conf->>'$.id' = ?", bodyParams.Symbol).
			Where("type = ?", models.PaymentTypeThirdParty).
			Where("mode = ?", models.PaymentModeDeposit).
			Where("status = ?", models.PaymentStatusEnabled).
			Find(&paymentInfo)
		if result.Error != nil || paymentInfo.ID == 0 {
			return c.ErrorJson("未找到钱包支付")
		}

		// 校验签名
		if signBool, err := utils.VerifySignature(paymentInfo.Conf.AppKey, paymentInfo.Conf.AppSecret, paramsMap); !signBool || err != nil {
			return c.ErrorJson("无效的签名")
		}

		// 用户信息
		userInfo := &models.User{}
		result = db.Model(&models.User{}).Where("id = ?", bodyParams.UserID).Find(userInfo)
		if result.Error != nil || userInfo.ID == 0 {
			return c.ErrorJson("未找到用户")
		}

		// 创建订单
		paymentInfo.Data.BankCardNo = bodyParams.Address
		order := &models.WalletOrder{
			AdminID:  userInfo.AdminID,
			UserID:   userInfo.ID,
			AssetsID: 0,
			SourceID: paymentInfo.ID,
			Money:    bodyParams.Amount,
			OrderSN:  bodyParams.Hash,
			Type:     models.WalletOrderTypeDeposit,
			Status:   models.WalletOrderStatusCompleted,
			Data: models.WalletOrderData{
				WalletPayment: paymentInfo,
			},
		}

		walletService := service.NewWalletService()
		err := db.Transaction(func(tx *gorm.DB) error {
			err := tx.Create(order).Error
			if err != nil {
				return err
			}

			actualAmount := order.Money - order.Fee
			err = walletService.IncreaseBalance(tx, c.Rds, c.Lang, models.BillTypeDeposit, order.ID, userInfo, actualAmount)
			if err != nil {
				return err
			}

			return nil
		})
		if err != nil {
			return c.ErrorJson("创建订单失败")
		}
	}

	return c.SuccessOk()
}
