package notifys

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/utils"
)

// HmacSha256 加密
// @param app string 应用ID
// @sign string 签名
// @timestamp int64 时间戳
// @nonce string 随机字符串
// @bodyParams 其他请求参数....
// @orderId uint 订单ID
type CryptoParams struct {
	Data    map[string]interface{} `json:"data"`
	Code    int64                  `json:"code"`
	Message string                 `json:"message"`
}

// Crypto 加密货币回调
func Crypto(c *context.CustomCtx, cryptoParams *CryptoParams) error {
	db := model.NewModel()
	bodyParams := cryptoParams.Data
	appId, ok := bodyParams["appId"].(string)
	if !ok {
		return c.ErrorJson("appId cannot be empty")
	}

	// 查询应用
	channelInfo := models.Channel{}
	if err := db.Where("app_id = ?", appId).First(&channelInfo).Error; err != nil {
		return c.ErrorJson("appId not found")
	}

	// 验证签名
	if signBool, err := utils.VerifySignature(channelInfo.AppID, channelInfo.SecretKey, bodyParams); !signBool {
		return c.ErrorJson(err.Error())
	}

	// TODO...

	return c.SuccessOk()
}
