package wallets

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"
)

// AccountDeleteParams 删除账户参数
type AccountDeleteParams struct {
	ID       uint   `json:"id" validate:"required"`
	Password string `json:"password"`
}

// AccountDelete 删除账户
func AccountDelete(c *context.CustomCtx, bodyParams *AccountDeleteParams) error {
	db := model.NewModel()
	translateService := service.NewTranslateService()
	adminSettingService := service.NewAdminSettingService()
	userInfo := &models.User{}
	db.Model(&models.User{}).Where("id = ?", c.Claims.UserID).First(userInfo)

	// 判断是否需要提现密码
	basicSettings, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, models.AdminSettingBasic).ToCheckbox()
	if _, ok := basicSettings[models.AdminSettingBasicShowWithdrawAccountPassword]; !ok {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "formatError"))
	}
	if basicSettings[models.AdminSettingBasicShowWithdrawAccountPassword] {
		if bodyParams.Password == "" || userInfo.SecurityKey != utils.EncryptPassword(bodyParams.Password) {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "securityPasswordError"))
		}
	}

	db.Model(&models.WalletAccount{}).
		Where("user_id = ?", c.Claims.UserID).
		Where("id = ?", bodyParams.ID).
		Delete(&models.WalletAccount{})

	return c.SuccessOk()
}
