package wallets

import (
	"fmt"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"

	"gorm.io/gorm"
)

const (
	WalletsRouter         = "/wallets"                 //	钱包中心
	WalletsDepositPayment = "/wallets/deposit/payment" //	充值支付
)

// 充值参数
type DepositParams struct {
	PaymentID uint    `json:"paymentId" validate:"required"` //	支付ID
	Proof     string  `json:"proof"`                         //	凭证
	Amount    float64 `json:"amount" validate:"required"`    //	金额
	Password  string  `json:"password"`                      //	密码
}

// DepositData 充值数据
type DepositData struct {
	ID   int64  `json:"id"`   //	订单ID
	Mode int64  `json:"mode"` //	充值模式			1:站内充值充值模式 -1正常模式
	Url  string `json:"url"`  //	跳转路由, 默认跳转到钱包页面
}

// 充值
func Deposit(c *context.CustomCtx, bodyParams *DepositParams) error {
	db := model.NewModel()
	// 支付信息
	paymentInfo := models.WalletPayment{}
	result := db.Where("id = ?", bodyParams.PaymentID).
		Where("admin_id = ?", c.Claims.MerchantID).
		Where("status = ?", models.PaymentStatusEnabled).
		First(&paymentInfo)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	// 如果不是第三方充值
	var orderId int64 = -1
	var depositMode int64 = 1
	if paymentInfo.Type != models.PaymentTypeThirdParty {
		depositMode = -1
		// 用户信息
		userInfo := models.User{}
		result = db.Where("id =?", c.Claims.UserID).First(&userInfo)
		if result.Error != nil {
			return c.ErrorJson(result.Error.Error())
		}

		adminSettingService := service.NewAdminSettingService()
		translateService := service.NewTranslateService()

		// 验证密码
		basicSettings, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, models.AdminSettingBasic).ToCheckbox()
		if _, ok := basicSettings[models.AdminSettingBasicShowDepositPassword]; !ok {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "formatError"))
		}
		if basicSettings[models.AdminSettingBasicShowDepositPassword] {
			if bodyParams.Password == "" || userInfo.SecurityKey != utils.EncryptPassword(bodyParams.Password) {
				return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "securityPasswordError"))
			}
		}

		// 判断是否需要凭证
		if paymentInfo.IsProof == model.BoolTrue && bodyParams.Proof == "" {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "depositProof", "notEmpty"))
		}

		// 判断充值金额范围
		if bodyParams.Amount < paymentInfo.MinAmount || bodyParams.Amount > paymentInfo.MaxAmount {
			return c.ErrorJson(fmt.Sprintf(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "rangeLimit"), paymentInfo.MinAmount, paymentInfo.MaxAmount))
		}

		// 充值时间限制
		now := time.Now().In(utils.GetTimeZoneByLocation(c.TimeZone)).Format("15:04:05")
		if now < paymentInfo.StartTime || now > paymentInfo.EndTime {
			return c.ErrorJson(translateService.GetTranslateByFieldWithCacheToArgs(c.Rds, c.Claims.MerchantID, c.Lang, "rangeLimitString", paymentInfo.StartTime, paymentInfo.EndTime))
		}

		// 创建充值记录
		orderInfo := &models.WalletOrder{
			AdminID:  c.Claims.AdminID,
			UserID:   c.Claims.UserID,
			AssetsID: paymentInfo.AssetsID,
			SourceID: paymentInfo.ID,
			OrderSN:  utils.GenerateOrderSN(),
			Money:    bodyParams.Amount,
			Proof:    bodyParams.Proof,
			Data: models.WalletOrderData{
				WalletPayment: paymentInfo,
			},
		}
		err := db.Transaction(func(tx *gorm.DB) error {
			orderInfo.Type = models.WalletOrderTypeDeposit
			if paymentInfo.AssetsID > 0 {
				orderInfo.Type = models.WalletOrderTypeAssetDeposit
			}

			result = tx.Create(orderInfo)
			if result.Error != nil {
				return result.Error
			}
			return nil
		})
		if err != nil {
			return c.ErrorJson(err.Error())
		}

		// 赋值订单ID
		orderId = int64(orderInfo.ID)
	}

	// 跳转路由
	returnUrl := WalletsRouter
	if paymentInfo.Type == models.PaymentTypeThirdParty {
		returnUrl = WalletsDepositPayment
		orderId = int64(paymentInfo.ID)
	}

	return c.SuccessJson(&DepositData{
		ID:   orderId,
		Mode: depositMode,
		Url:  returnUrl,
	})
}
