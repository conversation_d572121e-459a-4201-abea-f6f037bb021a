package wallets

import (
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"

	"gorm.io/gorm"
)

// OrderIndexBodyParams 订单列表请求参数
type OrderIndexBodyParams struct {
	Pagination *model.Pagination `json:"pagination"`
}

// OrderIndexRows 订单列表行数据
type OrderIndexRows struct {
	ID         uint                           `json:"id"`
	OrderSn    string                         `json:"orderSn"`
	AssetsID   uint                           `json:"assetsId"`
	Type       int8                           `json:"type"`
	Money      float64                        `json:"money"`
	Fee        float64                        `json:"fee"`
	Status     int8                           `json:"status"`
	Reason     string                         `json:"reason"`
	AssetsInfo models.WalletAssetsDisplayData `json:"assetsInfo" gorm:"foreignKey:ID;references:AssetsID"`
	CreatedAt  time.Time                      `json:"createdAt"`
	Data       models.WalletOrderData         `json:"data"`
}

// OrderIndex 订单列表
func OrderIndex(c *context.CustomCtx, bodyParams *OrderIndexBodyParams) error {
	db := model.NewModel()
	data := &model.IndexData{Items: make([]*OrderIndexRows, 0), Count: 0}

	db.Model(&models.WalletOrder{}).Preload("AssetsInfo", func(db *gorm.DB) *gorm.DB {
		return db.Model(&models.WalletAssets{})
	}).Where("user_id = ?", c.Claims.UserID).
		Count(&data.Count).Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	for _, v := range data.Items.([]*OrderIndexRows) {
		if v.AssetsID == 0 {
			assetsInfo := models.WalletAssets{}
			db.Model(&models.WalletAssets{}).Where("currency = ?", v.Data.Currency).Where("admin_id = ?", v.Data.AdminID).Find(&assetsInfo)
			v.AssetsInfo = models.WalletAssetsDisplayData{
				ID:       assetsInfo.ID,
				Name:     assetsInfo.Name,
				Currency: assetsInfo.Currency,
				Icon:     assetsInfo.Icon,
				Rate:     assetsInfo.Rate,
				Decimals: assetsInfo.Decimals,
				Network:  assetsInfo.Network,
			}
			if assetsInfo.ID == 0 {
				v.AssetsInfo.Decimals = 2
			}
		}
	}
	return c.SuccessJson(data)
}
