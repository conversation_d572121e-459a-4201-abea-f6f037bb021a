package wallets

import (
	"fmt"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// DepositInfoParams 充值信息参数
type DepositInfoParams struct {
	ID   int64 `json:"id" validate:"required"`   //	订单ID		   如果模式1, 那么id是支付ID, 如果不是。那么是订单ID
	Mode uint  `json:"mode" validate:"required"` //	充值模式		1:站内充值充值模式 -1正常模式
}

type DepositInfoData struct {
	Name      string    `json:"name"`      //	名称
	Symbol    string    `json:"symbol"`    //	符号
	Icon      string    `json:"icon"`      //	图标
	Value     string    `json:"value"`     //	内容
	Url       string    `json:"url"`       //	链接
	Type      int8      `json:"type"`      // 	类型
	Money     float64   `json:"money"`     //	金额
	Status    int8      `json:"status"`    //	状态
	Desc      string    `json:"desc"`      //	描述
	Interval  int64     `json:"interval"`  //	间隔(s)
	CreatedAt time.Time `json:"createdAt"` //	创建时间
}

// DepositInfo 充值信息
func DepositInfo(c *context.CustomCtx, bodyParams *DepositInfoParams) error {
	db := model.NewModel()
	data := &DepositInfoData{}

	switch bodyParams.Mode {
	case 1:
		// 自带充值通道
		paymentInfo := models.WalletPayment{}
		result := db.Where("id = ?", bodyParams.ID).
			Where("admin_id = ?", c.Claims.MerchantID).
			Where("status = ?", models.PaymentStatusEnabled).
			First(&paymentInfo)
		if result.Error != nil {
			return c.ErrorJson(result.Error.Error())
		}

		// 返回数据
		data = &DepositInfoData{
			Value:     "TRSBzaApA3XdbBKDdbCe5qWKE7jyUnCieM",
			Name:      paymentInfo.Name,
			Symbol:    paymentInfo.Currency,
			Icon:      paymentInfo.Icon,
			Type:      paymentInfo.Conf.PaymentType,
			Status:    models.WalletOrderStatusPending,
			Desc:      paymentInfo.Desc,
			Interval:  0,
			CreatedAt: time.Now(),
		}
	default:
		// 订单信息
		walletOrderInfo := models.WalletOrder{}
		result := db.Where("id = ?", bodyParams.ID).
			Where("admin_id = ?", c.Claims.MerchantID).
			First(&walletOrderInfo)
		if result.Error != nil {
			return c.ErrorJson(result.Error.Error())
		}

		// 支付信息
		paymentInfo := models.WalletPayment{}
		result = db.Where("id = ?", walletOrderInfo.SourceID).
			Where("admin_id = ?", c.Claims.MerchantID).
			First(&paymentInfo)
		if result.Error != nil {
			return c.ErrorJson(result.Error.Error())
		}

		// 获取支付提示翻译
		translateService := service.NewTranslateService()
		paymentDesc := translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, fmt.Sprintf("walletPaymentDesc%v", paymentInfo.ID))
		data = &DepositInfoData{
			Name:      paymentInfo.Name,
			Symbol:    paymentInfo.Currency,
			Icon:      paymentInfo.Icon,
			Money:     walletOrderInfo.Money,
			Type:      paymentInfo.Conf.PaymentType,
			Status:    walletOrderInfo.Status,
			Desc:      paymentDesc,
			Interval:  20 * 60,
			CreatedAt: walletOrderInfo.CreatedAt,
		}

		if paymentInfo.Conf.PaymentType == models.PaymentTypeCrypto {
			data.Value = "TRSBzaApA3XdbBKDdbCe5qWKE7jyUnCieM"
		}
	}

	// 跳转的路由
	data.Url = WalletsRouter
	return c.SuccessJson(data)
}
