package wallets

import (
	"errors"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

type IndexData struct {
	TotalAssets      float64                        `json:"totalAssets"`      // 总资产
	AvailableBalance float64                        `json:"availableBalance"` // 可用余额
	TodayProfit      float64                        `json:"todayProfit"`      // 今日盈亏
	TodayProfitRate  float64                        `json:"todayProfitRate"`  // 今日盈亏率
	AssetsList       []*WalletAssets                `json:"assetsList"`       // 用户资产列表
	DailyStats       []*models.WalletBillDailyStats `json:"dailyStats"`       // 每日统计
}

type WalletAssets struct {
	models.WalletAssets
	UserAssets models.UserAssets `json:"userAssets" gorm:"foreignKey:AssetsID"`
}

// Index 钱包首页
func Index(c *context.CustomCtx, bodyParams *context.NoRequestBody) error {
	db := model.NewModel()

	translateService := service.NewTranslateService()
	// 当前用户信息
	userInfo := &models.User{}
	result := db.Model(&models.User{}).Where("id = ?", c.Claims.UserID).Find(userInfo)
	if result.RowsAffected <= 0 {
		return errors.New(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "userNotExist"))
	}

	data := &IndexData{AssetsList: make([]*WalletAssets, 0), DailyStats: make([]*models.WalletBillDailyStats, 0)}

	db.Model(&models.WalletAssets{}).
		Preload("UserAssets", func(db *gorm.DB) *gorm.DB {
			return db.Where("user_id = ?", c.Claims.UserID)
		}).Where("admin_id = ?", c.Claims.MerchantID).
		Where("status = ?", models.WalletAssetsStatusEnabled).
		Find(&data.AssetsList)

	// 计算总资产
	data.TotalAssets = userInfo.AvailableAmount
	data.AvailableBalance = userInfo.AvailableAmount
	for _, asset := range data.AssetsList {
		data.TotalAssets += asset.UserAssets.AvailableAmount * asset.Rate
	}

	// 获取每日统计
	walletBillService := service.NewWalletBillService()
	data.DailyStats = walletBillService.GetDailyStats(c.Claims.UserID, data.TotalAssets)

	// 计算今日盈亏 - 从订单数据计算
	todayStart := time.Now().Truncate(24 * time.Hour)
	todayEnd := todayStart.Add(24 * time.Hour)

	// 查询今日完成的合约、期货、质押订单
	orders := make([]*models.Order, 0)
	db.Model(&models.Order{}).
		Where("user_id = ?", c.Claims.UserID).
		Where("admin_id = ?", c.Claims.AdminID).
		Where("status = ?", models.ProductOrderStatusCompleted).
		Where("updated_at BETWEEN ? AND ?", todayStart, todayEnd).
		Where("type IN ?", []int8{
			models.ProductOrderTypeContract,
			models.ProductOrderTypeFutures,
			models.ProductOrderTypeStaking,
		}).
		Find(&orders)
	var orderProfit float64
	for _, order := range orders {
		if order.Type == models.ProductOrderTypeContract {
			profit := order.Data.Amount - order.Money
			orderProfit += profit
		} else if order.Type == models.ProductOrderTypeFutures {
			if order.Data.Amount < 0 {
				orderProfit += order.Data.Amount
			} else if order.Data.Amount > 0 {
				orderProfit += order.Data.Amount - order.Money
			}
		} else {
			if order.Data.Amount < 0 {
				orderProfit += order.Data.Amount
			} else if order.Data.Amount > 0 {
				orderProfit += order.Data.Amount - order.Money
			}
		}
	}
	// 更新今日盈亏
	data.TodayProfit = orderProfit

	// 计算今日盈亏率
	if data.TotalAssets > 0 {
		data.TodayProfitRate = (data.TodayProfit / data.TotalAssets) * 100
	}

	return c.SuccessJson(data)
}
