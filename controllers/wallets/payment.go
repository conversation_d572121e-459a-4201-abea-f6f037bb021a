package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// PaymentParams 支付参数
type PaymentParams struct {
	Types []int8 `json:"types"` // 支付类型
	Modes []int8 `json:"modes"` // 支付模式
}

// PaymentRows 支付列表
type PaymentRows struct {
	models.WalletPayment
	AssetsInfo models.WalletAssets `json:"assetsInfo" gorm:"foreignKey:AssetsID"`
	UserAssets models.UserAssets   `json:"userAssets" gorm:"foreignKey:AssetsID;references:AssetsID"`
}

// Payment 支付列表
func Payment(c *context.CustomCtx, bodyParams *PaymentParams) error {
	db := model.NewModel()

	data := make([]*PaymentRows, 0)
	model := db.Model(&models.WalletPayment{}).Preload("AssetsInfo").Preload("UserAssets", func(db *gorm.DB) *gorm.DB {
		return db.Where("user_id = ?", c.Claims.UserID).Where("status = ?", models.UserAssetStatusEnabled)
	}).Where("admin_id = ?", c.Claims.MerchantID).Where("status = ?", models.PaymentStatusEnabled)
	if len(bodyParams.Modes) > 0 {
		model = model.Where("mode IN (?)", bodyParams.Modes)
	}
	if len(bodyParams.Types) > 0 {
		model = model.Where("type IN (?)", bodyParams.Types)
	}
	model.Order("sort ASC").Find(&data)

	translateService := service.NewTranslateService()
	for _, v := range data {
		v.Desc = translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, fmt.Sprintf("walletPaymentDesc%v", v.ID))
		if v.Desc == models.TranslateEmptyValue {
			v.Desc = ""
		}

		// 如果余额的支付列表, 是数字货币存在的, 那么使用当前的汇率
		if v.AssetsID == 0 {
			assetsInfo := models.WalletAssets{}
			db.Model(&models.WalletAssets{}).Where("currency = ?", v.Currency).Where("admin_id = ?", v.AdminID).Find(&assetsInfo)
			v.AssetsInfo = assetsInfo
		}
	}

	return c.SuccessJson(data)
}
