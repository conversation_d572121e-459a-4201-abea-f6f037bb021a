package wallets

import (
	"fmt"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"

	"gorm.io/gorm"
)

// WithdrawParams 提现参数
type WithdrawParams struct {
	AccountID   uint    `json:"accountId"`                  //	提现账户ID
	PaymentID   uint    `json:"paymentId"`                  //	支付方式ID
	Amount      float64 `json:"amount" validate:"required"` //	金额
	Password    string  `json:"password"`                   //	密码
	BankName    string  `json:"bankName"`                   //	公链名称｜银行名称
	BankCardNo  string  `json:"bankCardNo"`                 //	银行卡号｜收款地址
	RealName    string  `json:"realName"`                   //	真实姓名｜Token名称
	BankAddress string  `json:"bankAddress"`                //	银行地址｜公链协议
	BankCode    string  `json:"bankCode"`                   //	银行编码｜公链Token
}

// Withdraw 提现
func Withdraw(c *context.CustomCtx, bodyParams *WithdrawParams) error {
	db := model.NewModel()
	translateService := service.NewTranslateService()
	adminSettingService := service.NewAdminSettingService()

	// 用户信息
	userInfo := models.User{}
	result := db.Where("id = ?", c.Claims.UserID).First(&userInfo)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	// 账户信息
	accountInfo := &models.WalletAccount{}
	var err error
	if bodyParams.AccountID > 0 {
		result = db.Where("id = ?", bodyParams.AccountID).Where("user_id = ?", c.Claims.UserID).Find(accountInfo)
		if accountInfo.ID == 0 {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "formatError"))
		}
	} else {
		// 获取默认支付方式
		paymentInfo := models.WalletPayment{}
		db.Where("id =?", bodyParams.PaymentID).Where("admin_id =?", c.Claims.MerchantID).Where("status =?", models.PaymentStatusEnabled).Find(&paymentInfo)
		if paymentInfo.ID == 0 {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "formatError"))
		}

		// 创建新的提现账户
		accountInfo, err = CreateAccount(c, &AccountCreateParams{
			PaymentID:   bodyParams.PaymentID,
			BankName:    bodyParams.BankName,
			BankCardNo:  bodyParams.BankCardNo,
			RealName:    bodyParams.RealName,
			BankAddress: bodyParams.BankAddress,
			BankCode:    bodyParams.BankCode,
		})
		if err != nil {
			return c.ErrorJson(err.Error())
		}
	}
	tempLateOrderMap, err := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, "siteUserStatus").ToCheckbox()
	if err != nil {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, 0, c.Lang, "abnormalOperation"))
	}
	siteMinCreditScore, err := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, "siteMinCreditScore").ToInt()
	if err != nil {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, 0, c.Lang, "abnormalOperation"))
	}

	if tempLateOrderMap["creditWithdraw"] && userInfo.Score < siteMinCreditScore {
		tooLowScoreTips, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, "tooLowScoreTips").ToString()
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, tooLowScoreTips))
	}

	// 获取提现审核数
	walletWithdrawPendingNums, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, "walletWithdrawPendingNums").ToInt()
	var widthdrawOrderPendingNums int64
	db.Model(&models.WalletOrder{}).Where("user_id =?", c.Claims.UserID).Where("type IN ?", []int8{models.WalletOrderTypeWithdrawal, models.WalletOrderTypeAssetWithdrawal}).Where("status = ?", models.WalletOrderStatusPending).Count(&widthdrawOrderPendingNums)
	if walletWithdrawPendingNums > 0 && widthdrawOrderPendingNums >= int64(walletWithdrawPendingNums) {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "uncompletedOrders"))
	}

	// 获取提现订单数
	walletWithdrawNums := &models.AdminSettingWalletWithdrawNums{}
	adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, "walletWithdrawNums").ToInterface(walletWithdrawNums)
	var withdrawOrderNums int64
	staTime := time.Now().AddDate(0, 0, 0-int(walletWithdrawNums.Day))
	db.Model(&models.WalletOrder{}).Where("user_id = ?", c.Claims.UserID).Where("type IN ?", []int8{models.WalletOrderTypeWithdrawal, models.WalletOrderTypeAssetWithdrawal}).Where("status = ?", models.WalletOrderStatusCompleted).Where("created_at >= ?", staTime).Count(&withdrawOrderNums)
	if withdrawOrderNums >= int64(walletWithdrawNums.Nums) {
		translateDay := translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "day")
		translateNums := translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "quantity")
		numberLimitNums := fmt.Sprintf("%d %s %d%s", walletWithdrawNums.Day, translateDay, walletWithdrawNums.Nums, translateNums)
		return c.ErrorJson(numberLimitNums)
	}

	// 验证密码
	basicSettings, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, models.AdminSettingBasic).ToCheckbox()
	if _, ok := basicSettings[models.AdminSettingBasicShowWithdrawPassword]; !ok {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "formatError"))
	}
	if basicSettings[models.AdminSettingBasicShowWithdrawPassword] {
		if bodyParams.Password == "" || userInfo.SecurityKey != utils.EncryptPassword(bodyParams.Password) {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "securityPasswordError"))
		}
	}

	// 支付信息
	paymentInfo := models.WalletPayment{}
	result = db.Where("id = ?", accountInfo.PaymentID).Where("admin_id = ?", c.Claims.MerchantID).Where("status = ?", models.PaymentStatusEnabled).First(&paymentInfo)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}
	paymentInfo.Data = accountInfo.Data

	// 判断提现金额范围
	if bodyParams.Amount < paymentInfo.MinAmount || bodyParams.Amount > paymentInfo.MaxAmount {
		return c.ErrorJson(fmt.Sprintf(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "rangeLimit"), paymentInfo.MinAmount, paymentInfo.MaxAmount))
	}

	// 判断提现时间
	now := time.Now().In(utils.GetTimeZoneByLocation(c.TimeZone)).Format("15:04:05")
	if now < paymentInfo.StartTime || now > paymentInfo.EndTime {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "rangeLimitString", paymentInfo.StartTime, paymentInfo.EndTime))
	}

	// 创建提现记录
	walletService := service.NewWalletService()
	err = db.Transaction(func(tx *gorm.DB) error {
		orderType := models.WalletOrderTypeWithdrawal
		if paymentInfo.AssetsID > 0 {
			orderType = models.WalletOrderTypeAssetWithdrawal
		}
		orderInfo := &models.WalletOrder{
			AdminID:  c.Claims.AdminID,
			UserID:   c.Claims.UserID,
			AssetsID: paymentInfo.AssetsID,
			Type:     orderType,
			OrderSN:  utils.GenerateOrderSN(),
			SourceID: accountInfo.ID,
			Money:    bodyParams.Amount,
			Fee:      paymentInfo.Fee * bodyParams.Amount,
			Data: models.WalletOrderData{
				WalletPayment: paymentInfo,
			},
		}

		result = tx.Create(orderInfo)
		if result.Error != nil {
			return result.Error
		}

		if paymentInfo.AssetsID > 0 {
			// 扣除资产余额
			assetsInfo := models.WalletAssets{}
			result = tx.Where("id = ?", paymentInfo.AssetsID).First(&assetsInfo)
			if result.Error != nil {
				return result.Error
			}

			err = walletService.SpendAssets(tx, c.Rds, c.Lang, models.BillTypeWithdrawal, orderInfo.ID, &userInfo, &assetsInfo, bodyParams.Amount)
			if err != nil {
				return err
			}
		} else {
			// 扣除账户余额
			err = walletService.SpendBalance(tx, c.Rds, c.Lang, models.BillTypeWithdrawal, orderInfo.ID, &userInfo, bodyParams.Amount)
			if err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		return c.ErrorJson(err.Error())
	}

	return c.SuccessOk()
}
