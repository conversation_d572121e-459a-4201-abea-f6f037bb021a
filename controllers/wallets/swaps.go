package wallets

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"

	"gorm.io/gorm"
)

// SwapsParams 兑换资产参数
type SwapsParams struct {
	AssetsID   uint    `json:"assetsId"`                        //	兑换资产
	ToAssetsID uint    `json:"toAssetsId"`                      //	获得资产
	Amount     float64 `json:"amount" validate:"required,gt=0"` //	金额
	Password   string  `json:"password"`                        //	密码
}

// Swaps	兑换资产
func Swaps(c *context.CustomCtx, bodyParams *SwapsParams) error {
	translateService := service.NewTranslateService()
	adminSettingService := service.NewAdminSettingService()

	// 如果资产ID相同, 则返回错误
	if bodyParams.AssetsID == bodyParams.ToAssetsID {
		translate, _ := translateService.GetTranslateByFieldWithCache(c.Rds, c.Claims.MerchantID, c.<PERSON>, "formatError")
		return c.ErrorJson(translate)
	}

	db := model.NewModel()

	userInfo := models.User{}
	result := db.Where("id = ?", c.Claims.UserID).First(&userInfo)
	if result.Error != nil {
		return c.ErrorJson(result.Error.Error())
	}

	// 验证提现密码是否正确
	basicSettings, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, models.AdminSettingBasic).ToCheckbox()
	if _, ok := basicSettings[models.AdminSettingBasicShowSwapsPassword]; !ok {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "formatError"))
	}
	if basicSettings[models.AdminSettingBasicShowSwapsPassword] {
		if bodyParams.Password == "" || userInfo.SecurityKey != utils.EncryptPassword(bodyParams.Password) {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "securityPasswordError"))
		}
	}

	// 获取兑换资产信息
	assetsInfo := models.WalletAssets{}
	if bodyParams.AssetsID > 0 {
		result = db.Where("id = ?", bodyParams.AssetsID).Where("admin_id = ?", c.Claims.MerchantID).First(&assetsInfo)
		if result.Error != nil {
			return c.ErrorJson(result.Error.Error())
		}
	}

	// 获取获得资产信息
	toAssetsInfo := models.WalletAssets{}
	if bodyParams.ToAssetsID > 0 {
		result = db.Where("id = ?", bodyParams.ToAssetsID).Where("admin_id = ?", c.Claims.MerchantID).First(&toAssetsInfo)
		if result.Error != nil {
			return c.ErrorJson(result.Error.Error())
		}
	}

	walletService := service.NewWalletService()
	err := db.Transaction(func(tx *gorm.DB) error {
		switch {
		case bodyParams.AssetsID == 0:
			// 如果使用余额兑换资产
			err := walletService.SpendBalance(tx, c.Rds, c.Lang, models.BillTypeSwapsSend, userInfo.ID, &userInfo, bodyParams.Amount)
			if err != nil {
				return err
			}

			// 添加用户资产
			receiveAmount := bodyParams.Amount / toAssetsInfo.Rate
			err = walletService.IncreaseAssets(tx, c.Rds, c.Lang, models.BillTypeSwapsReceive, userInfo.ID, &userInfo, &toAssetsInfo, receiveAmount)
			if err != nil {
				return err
			}
		case bodyParams.ToAssetsID == 0:
			// 如果使用资产兑换余额
			err := walletService.SpendAssets(tx, c.Rds, c.Lang, models.BillTypeSwapsSend, userInfo.ID, &userInfo, &assetsInfo, bodyParams.Amount)
			if err != nil {
				return err
			}

			// 添加用户余额
			receiveAmount := bodyParams.Amount * assetsInfo.Rate
			err = walletService.IncreaseBalance(tx, c.Rds, c.Lang, models.BillTypeSwapsReceive, userInfo.ID, &userInfo, receiveAmount)
			if err != nil {
				return err
			}
		default:
			// 如果使用资产兑换资产
			err := walletService.SpendAssets(tx, c.Rds, c.Lang, models.BillTypeSwapsSend, userInfo.ID, &userInfo, &assetsInfo, bodyParams.Amount)
			if err != nil {
				return err
			}

			// 添加用户资产 - 先兑换成余额 - 余额兑换成资产
			receiveAmount := bodyParams.Amount * assetsInfo.Rate / toAssetsInfo.Rate
			err = walletService.IncreaseAssets(tx, c.Rds, c.Lang, models.BillTypeSwapsReceive, userInfo.ID, &userInfo, &toAssetsInfo, receiveAmount)
			if err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		return c.ErrorJson(err.Error())
	}

	return c.SuccessOk()
}
