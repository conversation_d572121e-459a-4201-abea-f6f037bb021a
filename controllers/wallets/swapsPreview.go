package wallets

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// SwapsPreviewParams 闪兑预览参数
type SwapsPreviewParams struct {
	AssetsID   uint `json:"assetsId"`   //	兑换资产
	ToAssetsID uint `json:"toAssetsId"` //	获得资产
}

// SwapsPreviewData 闪兑预览数据
type SwapsPreviewData struct {
	Rate float64 `json:"rate"` //	汇率
	Fee  float64 `json:"fee"`  //	手续费
}

// SwapsPreview 闪兑预览
func SwapsPreview(c *context.CustomCtx, bodyParams *SwapsPreviewParams) error {
	data := &SwapsPreviewData{}
	adminService := service.NewAdminUserService()
	db := model.NewModel()
	adminSubIDs, err := adminService.GetSubAdminIDsWithCache(c.Rds, c.Claims.AdminID)
	if err != nil {
		return c.ErrorJson(err.Error())
	}
	if bodyParams.AssetsID == bodyParams.ToAssetsID {
		return c.SuccessJson(data)
	}

	// 获取兑换资产信息
	assetsInfo := models.WalletAssets{}
	if bodyParams.AssetsID > 0 {
		result := db.Where("id = ?", bodyParams.AssetsID).Where("admin_id IN ?", adminSubIDs).First(&assetsInfo)
		if result.Error != nil {
			return c.ErrorJson(result.Error.Error())
		}
	}

	// 获取获得资产信息
	toAssetsInfo := models.WalletAssets{}
	if bodyParams.ToAssetsID > 0 {
		result := db.Where("id = ?", bodyParams.ToAssetsID).Where("admin_id IN ?", adminSubIDs).First(&toAssetsInfo)
		if result.Error != nil {
			return c.ErrorJson(result.Error.Error())
		}
	}

	switch {
	case bodyParams.AssetsID == 0:
		data.Rate = toAssetsInfo.Rate
	case bodyParams.ToAssetsID == 0:
		data.Rate = assetsInfo.Rate
	default:
		data.Rate = assetsInfo.Rate / toAssetsInfo.Rate
	}

	return c.SuccessJson(data)
}
