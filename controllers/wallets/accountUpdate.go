package wallets

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"
)

// AccountUpdateParams 更新账户参数
type AccountUpdateParams struct {
	ID          uint   `json:"id" validate:"required"`
	BankName    string `json:"bankName" validate:"required"`
	BankCardNo  string `json:"bankCardNo" validate:"required"`
	RealName    string `json:"realName" validate:"required"`
	BankAddress string `json:"bankAddress" validate:"required"`
	BankCode    string `json:"bankCode" validate:"required"`
	Password    string `json:"password"`
}

// AccountUpdate 更新账户
func AccountUpdate(c *context.CustomCtx, bodyParams *AccountUpdateParams) error {
	db := model.NewModel()
	translateService := service.NewTranslateService()
	adminSettingService := service.NewAdminSettingService()

	userInfo := &models.User{}
	db.Model(&models.User{}).Where("id = ?", c.Claims.UserID).First(userInfo)

	// 判断是否需要提现密码
	basicSettings, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, models.AdminSettingBasic).ToCheckbox()
	if _, ok := basicSettings[models.AdminSettingBasicShowWithdrawAccountPassword]; !ok {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "formatError"))
	}
	if basicSettings[models.AdminSettingBasicShowWithdrawAccountPassword] {
		if bodyParams.Password == "" || userInfo.SecurityKey != utils.EncryptPassword(bodyParams.Password) {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "securityPasswordError"))
		}
	}

	db.Model(&models.WalletAccount{}).
		Where("user_id = ?", c.Claims.UserID).
		Where("id = ?", bodyParams.ID).
		Update("data", models.WalletPaymentData{
			BankName:    bodyParams.BankName,
			BankCode:    bodyParams.BankCode,
			BankAddress: bodyParams.BankAddress,
			RealName:    bodyParams.RealName,
			BankCardNo:  bodyParams.BankCardNo,
		})

	return c.SuccessOk()
}
