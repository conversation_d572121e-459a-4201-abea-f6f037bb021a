package wallets

import (
	"fmt"
	"time"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// BillIndexParams 账单列表参数
type BillIndexParams struct {
	AssetsID   uint                   `json:"assetsId"`
	Type       int8                   `json:"type"`
	CreatedAt  *model.RangeDatePicker `json:"createdAt"`
	Pagination *model.Pagination      `json:"pagination"`
}

// BillIndexRow 账单列表行
type BillIndexRow struct {
	ID         int64                          `json:"id"`
	AssetsID   uint                           `json:"assetsId"`
	Name       string                         `json:"name"`
	Type       int8                           `json:"type"`
	Money      float64                        `json:"money"`
	Balance    float64                        `json:"balance"`
	CreatedAt  time.Time                      `json:"createdAt"`
	AssetsInfo models.WalletAssetsDisplayData `json:"assetsInfo" gorm:"foreignKey:ID;references:AssetsID"`
}

// BillIndex 账单列表
func BillIndex(c *context.CustomCtx, bodyParams *BillIndexParams) error {
	db := model.NewModel()
	data := &model.IndexData{Items: make([]*BillIndexRow, 0), Count: 0}

	db.Equal("assets_id", bodyParams.AssetsID).Equal("type", bodyParams.Type).
		BetweenTime("created_at", bodyParams.CreatedAt, c.TimeZone).
		Model(&models.WalletBill{}).Where("user_id = ?", c.Claims.UserID).
		Preload("AssetsInfo", func(db *gorm.DB) *gorm.DB {
			return db.Model(&models.WalletAssets{})
		}).
		Count(&data.Count).Scopes(bodyParams.Pagination.Scopes()).Find(&data.Items)

	translateService := service.NewTranslateService()
	for _, v := range data.Items.([]*BillIndexRow) {
		v.Balance = v.Balance + v.Money
		if v.AssetsInfo.ID == 0 {
			v.AssetsInfo.Decimals = 2
		}
		v.Name = translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, fmt.Sprintf(models.WalletBillTypePrefix+"%d", v.Type))
	}

	return c.SuccessJson(data)
}
