package wallets

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"

	"gorm.io/gorm"
)

// AssetsRows 资产列表
type AssetsRows struct {
	models.WalletAssets
	UserAssets models.UserAssets `json:"userAssets" gorm:"foreignKey:ID;references:AssetsID"`
}

// Assets 资产列表
func Assets(c *context.CustomCtx, bodyParams *context.NoRequestBody) error {
	db := model.NewModel()
	data := make([]AssetsRows, 0)

	db.Model(&models.WalletAssets{}).
		Preload("UserAssets", func(db *gorm.DB) *gorm.DB {
			return db.Where("user_id = ?", c.Claims.UserID)
		}).
		Where("admin_id = ?", c.Claims.MerchantID).
		Where("status = ?", models.WalletAssetsStatusEnabled).Find(&data)
	return c.<PERSON><PERSON>(data)
}
