package wallets

import (
	"errors"
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
)

// AccountCreateParams 创建账户参数
type AccountCreateParams struct {
	PaymentID   uint   `json:"paymentId" validate:"required"` // 支付ID
	BankName    string `json:"bankName" validate:"required"`
	BankCardNo  string `json:"bankCardNo" validate:"required"`
	RealName    string `json:"realName" validate:"required"`
	BankAddress string `json:"bankAddress" validate:"required"`
	BankCode    string `json:"bankCode" validate:"required"`
}

// AccountCreate 创建账户
func AccountCreate(c *context.CustomCtx, bodyParams *AccountCreateParams) error {
	_, err := CreateAccount(c, bodyParams)
	if err != nil {
		return c.ErrorJson(err.Error())
	}
	return c.SuccessOk()
}

// CreateAccount 创建提现账户
func CreateAccount(c *context.CustomCtx, bodyParams *AccountCreateParams) (*models.WalletAccount, error) {
	db := model.NewModel()
	translateService := service.NewTranslateService()
	adminSettingService := service.NewAdminSettingService()
	paymentInfo := models.WalletPayment{}

	if err := db.Where("id = ?", bodyParams.PaymentID).Where("admin_id = ?", c.Claims.MerchantID).Where("status = ?", models.PaymentStatusEnabled).First(&paymentInfo).Error; err != nil {
		return nil, errors.New(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, "formatError"))
	}

	// 获取用户账户数量
	var accountNums int64
	db.Model(&models.WalletAccount{}).Where("user_id = ?", c.Claims.UserID).Where("payment_id = ?", bodyParams.PaymentID).Count(&accountNums)

	// 获取最大账户数量
	maxAccountNums, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, "walletAccountNums").ToInt()
	if accountNums >= int64(maxAccountNums) {
		msg := fmt.Sprintf(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "numberLimit"), maxAccountNums)
		return nil, errors.New(msg)
	}

	// 账户类型
	accountType := models.AccountTypeBalance
	if int8(paymentInfo.AssetsID) > 0 {
		accountType = models.AccountTypeAsset
	}

	// 修改参数设置
	switch paymentInfo.Type {
	case models.PaymentTypeCrypto:
		bodyParams.BankName = paymentInfo.Data.BankName
		bodyParams.BankAddress = paymentInfo.Data.BankAddress
		bodyParams.BankCode = paymentInfo.Data.BankCode
		bodyParams.RealName = paymentInfo.Data.RealName
	}

	walletAccount := &models.WalletAccount{
		AdminID:   c.Claims.AdminID,
		UserID:    c.Claims.UserID,
		PaymentID: bodyParams.PaymentID,
		Name:      paymentInfo.Name + "[" + bodyParams.BankCardNo + "]",
		Type:      accountType,
		Data: models.WalletPaymentData{
			BankName:    bodyParams.BankName,
			BankCode:    bodyParams.BankCode,
			BankAddress: bodyParams.BankAddress,
			RealName:    bodyParams.RealName,
			BankCardNo:  bodyParams.BankCardNo,
		},
	}

	result := db.Create(walletAccount)
	if result.Error != nil {
		return nil, result.Error
	}
	return walletAccount, nil
}
