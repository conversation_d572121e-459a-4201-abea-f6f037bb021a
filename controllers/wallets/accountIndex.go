package wallets

import (
	"fmt"
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// AccountIndexRows 账户列表
type AccountIndexRows struct {
	models.WalletAccount
	PaymentInfo AccountIndexPaymentInfo `json:"paymentInfo" gorm:"foreignKey:PaymentID"`
}

// AccountIndexPaymentInfo 账户支付信息
type AccountIndexPaymentInfo struct {
	models.WalletPayment
	UserAssets models.UserAssets   `json:"userAssets" gorm:"foreignKey:AssetsID;references:AssetsID"`
	AssetsInfo models.WalletAssets `json:"assetsInfo" gorm:"foreignKey:AssetsID"`
}

// AccountIndex 账户列表
func AccountIndex(c *context.CustomCtx, _ *context.NoRequestBody) error {
	var data []*AccountIndexRows

	model.NewModel().Model(&models.WalletAccount{}).
		Preload("PaymentInfo", func(db *gorm.DB) *gorm.DB {
			return db.Model(&models.WalletPayment{})
		}).
		Preload("PaymentInfo.UserAssets", func(db *gorm.DB) *gorm.DB {
			return db.Where("user_id = ?", c.Claims.UserID)
		}).
		Preload("PaymentInfo.AssetsInfo").
		Where("user_id = ? AND status = ?", c.Claims.UserID, models.AccountStatusEnabled).
		Order("id DESC").
		Find(&data)

	translateService := service.NewTranslateService()
	for _, v := range data {
		v.PaymentInfo.Desc = translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, fmt.Sprintf("walletPaymentDesc%v", v.PaymentInfo.ID))
		if v.PaymentInfo.Desc == models.TranslateEmptyValue {
			v.PaymentInfo.Desc = ""
		}
	}

	return c.SuccessJson(data)
}
