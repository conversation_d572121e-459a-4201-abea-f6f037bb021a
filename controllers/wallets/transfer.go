package wallets

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"
	"zfeng/utils"

	"gorm.io/gorm"
)

// TransferParams 转账参数
type TransferParams struct {
	AssetsID    uint    `json:"assetsId"`                        // 资产ID
	AccountType int8    `json:"accountType"`                     // 账户类型
	Username    string  `json:"username" validate:"required"`    // 账户名称 邮箱｜手机｜UID｜用户名
	Amount      float64 `json:"amount" validate:"required,gt=0"` // 金额
	Memo        string  `json:"memo"`                            // 备注
	Password    string  `json:"password"`                        // 密码
}

// Transfer 转账
func Transfer(c *context.CustomCtx, bodyParams *TransferParams) error {
	adminServie := service.NewAdminUserService()
	translateService := service.NewTranslateService()
	adminSettingService := service.NewAdminSettingService()
	adminSubIDs, _ := adminServie.GetSubAdminIDsWithCache(c.Rds, c.Claims.MerchantID)
	receiveInfo := &models.User{}

	db := model.NewModel()
	switch bodyParams.AccountType {
	case models.UserRegisterTypeEmail:
		// 邮箱
		db.Where("admin_id IN ?", adminSubIDs).Where("email = ?", bodyParams.Username).Find(receiveInfo)
	case models.UserRegisterTypeTelephone:
		// 手机号码
		db.Where("admin_id IN ?", adminSubIDs).Where("telephone = ?", bodyParams.Username).Find(receiveInfo)
	case models.UserRegisterTypeUsername:
		// 用户名
		db.Where("admin_id IN ?", adminSubIDs).Where("username = ?", bodyParams.Username).Find(receiveInfo)
	default:
		db.Where("admin_id IN ?", adminSubIDs).Where("id = ?", bodyParams.Username).Find(receiveInfo)
	}

	userInfo := &models.User{}
	db.Where("id = ?", c.Claims.UserID).Find(userInfo)

	// 用户不存在
	if receiveInfo.ID == 0 || userInfo.ID == receiveInfo.ID {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "userNotExist"))
	}

	tempLateOrderMap, err := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, "siteUserStatus").ToCheckbox()
	if err != nil {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, 0, c.Lang, "abnormalOperation"))
	}
	siteMinCreditScore, err := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, "siteMinCreditScore").ToInt()
	if err != nil {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, 0, c.Lang, "abnormalOperation"))
	}

	if tempLateOrderMap["creditTransfer"] && userInfo.Score < siteMinCreditScore {
		tooLowScoreTips, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, "tooLowScoreTips").ToString()
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, tooLowScoreTips))
	}

	// 密码错误
	basicSettings, _ := adminSettingService.GetAdminSettingByFieldWithCache(c.Rds, c.Claims.MerchantID, models.AdminSettingBasic).ToCheckbox()
	if _, ok := basicSettings[models.AdminSettingBasicShowTransferPassword]; !ok {
		return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "formatError"))
	}
	if basicSettings[models.AdminSettingBasicShowTransferPassword] {
		if bodyParams.Password == "" || userInfo.SecurityKey != utils.EncryptPassword(bodyParams.Password) {
			return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "securityPasswordError"))
		}
	}

	walletService := service.NewWalletService()
	err = db.Transaction(func(tx *gorm.DB) error {
		if bodyParams.AssetsID > 0 {
			// 判断资产是否存在
			assetsInfo := &models.WalletAssets{}
			db.Where("id = ?", bodyParams.AssetsID).Find(assetsInfo)
			if assetsInfo.ID == 0 {
				return c.ErrorJson(translateService.GetTranslatesByFieldsWithCache(c.Rds, c.Claims.MerchantID, c.Lang, "assetsNotExist"))
			}

			// 扣款
			err := walletService.SpendAssets(tx, c.Rds, c.Lang, models.BillTypeTransferSend, receiveInfo.ID, userInfo, assetsInfo, bodyParams.Amount)
			if err != nil {
				return err
			}

			// 加款
			err = walletService.IncreaseAssets(tx, c.Rds, c.Lang, models.BillTypeTransferReceive, userInfo.ID, receiveInfo, assetsInfo, bodyParams.Amount)
			if err != nil {
				return err
			}

		} else {
			// 扣款
			err := walletService.SpendBalance(tx, c.Rds, c.Lang, models.BillTypeTransferSend, receiveInfo.ID, userInfo, bodyParams.Amount)
			if err != nil {
				return err
			}
			// 加款
			err = walletService.IncreaseBalance(tx, c.Rds, c.Lang, models.BillTypeTransferReceive, userInfo.ID, receiveInfo, bodyParams.Amount)
			if err != nil {
				return err
			}
		}

		// 创建转账记录
		transferInfo := &models.Transfer{}
		transferInfo.SenderID = userInfo.ID
		transferInfo.ReceiverID = receiveInfo.ID
		transferInfo.Amount = bodyParams.Amount
		transferInfo.Type = models.TransferTypeInternal
		transferInfo.Status = models.TransferStatusCompleted
		transferInfo.AdminID = userInfo.AdminID
		transferInfo.AssetsID = bodyParams.AssetsID
		result := db.Create(transferInfo)
		return result.Error
	})

	if err != nil {
		return c.ErrorJson(err.Error())
	}

	return c.SuccessOk()
}
