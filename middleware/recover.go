package middleware

import (
	"fmt"
	"runtime/debug"
	"strings"
	"zfeng/core/config"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"go.uber.org/zap"
)

// InitRecover 初始化异常中间件
func InitRecover() fiber.Handler {
	return recover.New(recover.Config{
		EnableStackTrace: config.Debug,
		StackTraceHandler: func(c *fiber.Ctx, e interface{}) {
			stack := string(debug.Stack())

			// 获取引发异常的文件和行号
			file, line := extractFileAndLine(stack)

			zap.L().WithOptions(zap.WithCaller(false)).Error(fmt.Sprintf("%s:%d   Recover", file, line),
				zap.Any("error", e),
			)
		},
	})
}

// extractFileAndLine 从堆栈信息中提取文件名和行号
func extractFileAndLine(stack string) (string, int) {
	lines := strings.Split(stack, "\n")
	for i, line := range lines {
		if strings.Contains(line, "panic.go") && i+2 < len(lines) {
			parts := strings.Split(lines[i+2], ":")
			if len(parts) >= 2 {
				file := strings.TrimSpace(parts[0])
				var lineNum int
				fmt.Sscanf(parts[1], "%d", &lineNum)
				return file, lineNum
			}
		}
	}
	return "unknown", 0
}
