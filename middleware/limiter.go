package middleware

import (
	"time"
	"zfeng/core/context"
	"zfeng/utils"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/limiter"
)

const (
	// MaxRequests 定义每个时间窗口内的最大请求次数
	MaxRequests = 100
	// ExpirationTime 定义时间窗口的长度
	ExpirationTime = 1 * time.Minute
)

// InitLimiter 初始化速率限制器中间件
func InitLimiter() fiber.Handler {
	return limiter.New(limiter.Config{
		Max:        MaxRequests,
		Expiration: ExpirationTime,
		KeyGenerator: func(c *fiber.Ctx) string {
			return utils.GetClientIP(c)
		},
		LimitReached: func(c *fiber.Ctx) error {
			return c.Status(fiber.StatusTooManyRequests).JSON(
				context.ErrorResponse("Rate limit exceeded", fiber.StatusTooManyRequests),
			)
		},
	})
}
