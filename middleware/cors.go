package middleware

import (
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
)

// InitCORS 初始化跨域中间件
func InitCORS() fiber.Handler {
	return cors.New(cors.Config{
		AllowOrigins: "*", // 允许所有来源，可以根据需要限制特定域名
		AllowMethods: "GET,POST,PUT,DELETE,OPTIONS",
		AllowHeaders: "Origin,Content-Type,Accept,Authorization,Time-Zone,X-Skip-Loading,X-Forwarded-Host",
		MaxAge:       300, // 预检请求结果缓存时间（秒）
	})
}
