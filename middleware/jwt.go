package middleware

import (
	"crypto/md5"
	"crypto/rsa"
	"fmt"
	"os"
	"time"
	"zfeng/core/cache"
	"zfeng/core/context"
	"zfeng/utils"

	jwtware "github.com/gofiber/contrib/jwt"
	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt/v5"
	"github.com/gomodule/redigo/redis"
)

const (
	// UserTokensPrefix 缓存用户Tokens
	UserTokensPrefix = "userTokens"

	// FrontendKeysFile 前台密钥文件路径
	FrontendKeysFile = "./frontendKeys.pem"

	// AdminKeysFile 后台密钥文件路径
	AdminKeysFile = "./adminKeys.pem"
)

var (
	// AdminPrivateKey 后台私钥
	AdminPrivateKey *rsa.PrivateKey
	// AdminPublicKey 后台公钥
	AdminPublicKey *rsa.PublicKey

	// FrontendPrivateKey 前台私钥
	FrontendPrivateKey *rsa.PrivateKey
	// FrontendPublicKey 前台公钥
	FrontendPublicKey *rsa.PublicKey
)

func init() {
	// 如果是子进程, 那么延迟100毫秒, 去获取文件
	if fiber.IsChild() {
		time.Sleep(100 * time.Millisecond)
	}

	var err error

	// 初始化后台密钥
	AdminPrivateKey, AdminPublicKey, err = initializeKeys(AdminKeysFile)
	if err != nil {
		panic(fmt.Errorf("failed to initialize admin keys: %v", err))
	}

	// 初始化前台密钥
	FrontendPrivateKey, FrontendPublicKey, err = initializeKeys(FrontendKeysFile)
	if err != nil {
		panic(fmt.Errorf("failed to initialize frontend keys: %v", err))
	}
}

// InitJWT 初始化 JWT 中间件
func InitJWT(privateKey *rsa.PrivateKey, successHandler func(rdsConn redis.Conn, claims *context.TokenClaims, c *fiber.Ctx) error) fiber.Handler {
	if privateKey == nil {
		panic("没有设置 RS256 Key")
	}

	return jwtware.New(jwtware.Config{
		ContextKey: "token",
		SigningKey: jwtware.SigningKey{
			JWTAlg: jwtware.RS256,
			Key:    privateKey.Public(),
		},
		Filter: func(c *fiber.Ctx) bool {
			return false
		},
		SuccessHandler: func(c *fiber.Ctx) error {
			// 获取 Redis 连接
			rdsConn := cache.Rds.Get()
			defer rdsConn.Close()

			claims, err := context.GetLocalsToken(c)
			if err != nil {
				return c.Status(fiber.StatusUnauthorized).JSON(context.ErrorResponse(err.Error(), -1))
			}

			// 验证 token 唯一性
			if err := ValidateTokenUniqueness(claims, rdsConn, c); err != nil {
				return c.Status(fiber.StatusUnauthorized).JSON(context.ErrorResponse(err.Error(), -1))
			}

			// 执行验证成功之后Handler
			err = successHandler(rdsConn, claims, c)
			if err != nil {
				return c.Status(fiber.StatusUnauthorized).JSON(context.ErrorResponse(err.Error(), -1))
			}
			return c.Next()
		},
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error": "无效的令牌",
			})
		},
		TokenLookup: "header:Authorization",
		AuthScheme:  "Bearer",
	})
}

// GenerateAdminToken 生成后台 JWT 令牌
func GenerateAdminToken(ctx *fiber.Ctx, adminID, merchantID uint, expirationTime time.Duration, maxTokens int) (string, error) {
	return GenerateToken(ctx, adminID, merchantID, 0, AdminPrivateKey, expirationTime, maxTokens)
}

// GenerateFrontendToken 生成前台 JWT 令牌
func GenerateFrontendToken(ctx *fiber.Ctx, adminID, merchantID, userID uint, expirationTime time.Duration, maxTokens int) (string, error) {
	return GenerateToken(ctx, adminID, merchantID, userID, FrontendPrivateKey, expirationTime, maxTokens)
}

// GenerateToken 生成新的 JWT 令牌
func GenerateToken(ctx *fiber.Ctx, adminID, merchantID, userID uint, privateKey *rsa.PrivateKey, expirationTime time.Duration, maxTokens int) (string, error) {
	// 获取IP地址和User-Agent
	ip := utils.GetClientIP(ctx)
	ua := ctx.Get("User-Agent")

	// 生成唯一ID并进行MD5加密
	uniqueIDRaw := fmt.Sprintf("%s:%s:%d:%d", ip, ua, adminID, userID)
	uniqueID := fmt.Sprintf("%x", md5.Sum([]byte(uniqueIDRaw)))

	claims := context.TokenClaims{
		AdminID:    adminID,
		MerchantID: merchantID,
		UserID:     userID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(expirationTime)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			ID:        uniqueID,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodRS256, &claims)
	signedToken, err := token.SignedString(privateKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	rdsConn := cache.Rds.Get()
	defer rdsConn.Close()

	if err := CacheToken(rdsConn, adminID, userID, signedToken, expirationTime, maxTokens); err != nil {
		return "", fmt.Errorf("failed to cache token: %w", err)
	}

	return signedToken, nil
}

// ValidateTokenUniqueness 验证Token设备唯一性
func ValidateTokenUniqueness(claims *context.TokenClaims, rdsConn redis.Conn, ctx *fiber.Ctx) error {
	tokenString := utils.GetToken(ctx)

	// 获取IP地址和User-Agent
	// ip := utils.GetClientIP(ctx)
	// ua := ctx.Get("User-Agent")

	// // 生成唯一ID并进行MD5加密
	// uniqueIDRaw := fmt.Sprintf("%s:%s:%d:%d", ip, ua, claims.AdminID, claims.UserID)
	// uniqueID := fmt.Sprintf("%x", md5.Sum([]byte(uniqueIDRaw)))

	// // 检查生成的唯一ID是否与令牌中的ID匹配
	// if claims.ID != uniqueID {
	// 	return fmt.Errorf("token does not match the current device")
	// }

	// 检查令牌是否存在于缓存中
	exists, err := IsTokenCached(rdsConn, claims.AdminID, claims.UserID, tokenString)
	if err != nil {
		return fmt.Errorf("failed to check token cache: %w", err)
	}

	if !exists {
		return fmt.Errorf("token not found in cache")
	}

	return nil
}

// ValidateToken 验证JWT令牌
func ValidateToken(tokenString string, publicKey *rsa.PublicKey) (*context.TokenClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &context.TokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return publicKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	claims, ok := token.Claims.(*context.TokenClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}

	if time.Now().After(claims.ExpiresAt.Time) {
		return nil, fmt.Errorf("token has expired")
	}

	return claims, nil
}

// CacheToken 缓存用户Token到Redis
func CacheToken(rdsConn redis.Conn, adminID, userID uint, token string, expirationTime time.Duration, maxTokens int) error {
	cacheKey := fmt.Sprintf("%s:%d:%d", UserTokensPrefix, adminID, userID)

	// 使用Lua脚本来原子性地执行所有操作
	script := `
		local listLength = redis.call('RPUSH', KEYS[1], ARGV[1])
        redis.call('EXPIRE', KEYS[1], ARGV[2])
        while listLength > tonumber(ARGV[3]) do
            redis.call('LPOP', KEYS[1])
            listLength = listLength - 1
        end
        return listLength
	`

	_, err := redis.Int(rdsConn.Do("EVAL", script, 1, cacheKey, token, int(expirationTime.Seconds()), maxTokens))
	if err != nil {
		return fmt.Errorf("failed to cache token: %w", err)
	}

	return nil
}

// IsTokenCached 检查Token是否在缓存中
func IsTokenCached(rdsConn redis.Conn, adminID, userID uint, token string) (bool, error) {
	cacheKey := fmt.Sprintf("%s:%d:%d", UserTokensPrefix, adminID, userID)
	exists, err := redis.Bool(rdsConn.Do("EXISTS", cacheKey))
	if err != nil {
		return false, fmt.Errorf("failed to check if key exists: %w", err)
	}

	if !exists {
		return false, nil
	}

	// 链表第一条数据是 0
	index, err := redis.Int(rdsConn.Do("LPOS", cacheKey, token))
	return index > -1, err
}

// RemoveToken 从缓存中移除指定的Token
func RemoveToken(rdsConn redis.Conn, adminID, userID uint, token string) error {
	key := fmt.Sprintf("%s:%d:%d", UserTokensPrefix, adminID, userID)
	count, err := redis.Int(rdsConn.Do("LREM", key, 0, token))
	if err != nil {
		return fmt.Errorf("failed to remove token: %w", err)
	}

	if count == 0 {
		return fmt.Errorf("token not found in cache")
	}

	return nil
}

// ClearAdminTokenCache 清除管理Token缓存
func ClearAdminTokenCache(rdsConn redis.Conn, adminID uint) error {
	cacheKey := fmt.Sprintf("%s:%d", UserTokensPrefix, adminID)
	_, err := rdsConn.Do("DEL", cacheKey)
	if err != nil {
		return fmt.Errorf("failed to clear admin token cache: %w", err)
	}
	return nil
}

func initializeKeys(keyFile string) (*rsa.PrivateKey, *rsa.PublicKey, error) {
	if utils.FileExists(keyFile) {
		keyData, err := os.ReadFile(keyFile)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to read key file: %w", err)
		}

		privateKey, err := utils.PEMToPrivateKey(keyData)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to parse private key: %w", err)
		}

		publicKey := &privateKey.PublicKey
		return privateKey, publicKey, nil
	}

	privateKey, publicKey, err := utils.GenerateRSAKeyPair(2048)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to generate RSA key pair: %w", err)
	}

	privatePEM := utils.PrivateKeyToPEM(privateKey)

	if err := os.WriteFile(keyFile, privatePEM, 0600); err != nil {
		return nil, nil, fmt.Errorf("failed to write private key file: %w", err)
	}

	return privateKey, publicKey, nil
}
