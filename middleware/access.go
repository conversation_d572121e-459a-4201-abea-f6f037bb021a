package middleware

import (
	"io"
	"os"
	"zfeng/core/config"
	"zfeng/utils"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"gopkg.in/natefinch/lumberjack.v2"
)

// AccessLogFilename 定义访问日志文件名称
const AccessLogFilename = "./logs/access.log"

// InitLogger 初始化日志中间件
func InitLogger() fiber.Handler {
	output := getLogOutput()

	return logger.New(logger.Config{
		Output:        output,
		TimeFormat:    "2006-01-02T15:04:05.000Z0700",
		DisableColors: !config.Debug,
		Format:        "${time} | ${path} | ${method} | ${status} | ${ua} | ${latency} | ${IP4} | ${error}\n",
		CustomTags:    getCustomTags(),
	})
}

// getLogOutput 根据配置返回适当的日志输出
func getLogOutput() io.Writer {
	if config.Debug {
		return os.Stdout
	}
	return &lumberjack.Logger{
		Filename:   AccessLogFilename,
		MaxSize:    100, // 单个文件最大 100M
		MaxBackups: 30,  // 最多保留 30 个备份
		MaxAge:     7,   // 最多保留 7 天
		Compress:   true,
	}
}

// getCustomTags 返回自定义标签配置
func getCustomTags() map[string]logger.LogFunc {
	return map[string]logger.LogFunc{
		"IP4": func(output logger.Buffer, c *fiber.Ctx, data *logger.Data, extraParam string) (int, error) {
			return output.WriteString(utils.GetClientIP(c))
		},
	}
}
