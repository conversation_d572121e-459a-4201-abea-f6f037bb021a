package utils

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"regexp"
	"strings"

	"github.com/goccy/go-json"
	"github.com/gofiber/fiber/v2"
)

// EncryptPassword 使用双重MD5加密用户密码
func EncryptPassword(password string) string {
	if password == "" {
		return ""
	}

	// 第一次MD5加密
	firstHash := md5.Sum([]byte(password))
	firstHashStr := hex.EncodeToString(firstHash[:])

	// 第二次MD5加密
	secondHash := md5.Sum([]byte(firstHashStr))
	return hex.EncodeToString(secondHash[:])
}

// StructToString 将结构体转换为字符串
func StructToString(v interface{}) string {
	jsonBytes, err := json.Marshal(v)
	if err != nil {
		return fmt.Sprintf("Error: %v", err)
	}
	return string(jsonBytes)
}

// StructToBytes 将结构体转换为字节数组
func StructToBytes(v interface{}) []byte {
	jsonBytes, _ := json.Marshal(v)
	return jsonBytes
}

// GetClientIP 获取客户端IP，考虑代理IP的情况
func GetClientIP(c *fiber.Ctx) string {
	// 首先检查X-Forwarded-For头
	if ip := c.Get("X-Forwarded-For"); ip != "" {
		// X-Forwarded-For可能包含多个IP，我们取第一个
		return strings.TrimSpace(strings.Split(ip, ",")[0])
	}

	// 如果没有X-Forwarded-For，检查X-Real-IP
	if ip := c.Get("X-Real-IP"); ip != "" {
		return ip
	}

	// 如果以上都没有，使用RemoteIP
	return c.IP()
}

// GetToken 从请求中检索令牌。
// It first checks the Authorization header, then falls back to the 'token' query parameter.
func GetToken(ctx *fiber.Ctx) string {
	// Check Authorization header first
	authHeader := ctx.Get("Authorization")
	if authHeader != "" {
		// The header format should be "Bearer <token>"
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) == 2 && strings.ToLower(parts[0]) == "bearer" {
			return parts[1]
		}
	}

	// If not found in header, check query parameter
	return ctx.Query("token")
}

// FilterHTML 过滤HTML代码
func FilterHTML(input string) string {
	re := regexp.MustCompile(`<[^>]*>`)
	return re.ReplaceAllString(input, "")
}
