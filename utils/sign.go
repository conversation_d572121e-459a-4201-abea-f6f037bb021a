package utils

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"
)

// GenerateSignature 生成签名
func GenerateSignature(appId, appSecret string, bodyParams map[string]interface{}, timestamp int64, nonce string) (string, error) {
	// 1. 对参数进行排序
	keys := make([]string, 0, len(bodyParams))
	for k := range bodyParams {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var signStr strings.Builder
	signStr.WriteString("app_key=")
	signStr.WriteString(appId)
	signStr.WriteString("&nonce=")
	signStr.WriteString(nonce)
	signStr.WriteString("&timestamp=")
	signStr.WriteString(strconv.FormatInt(timestamp, 10))

	for _, k := range keys {
		signStr.WriteByte('&')
		signStr.WriteString(k)
		signStr.WriteByte('=')
		// 自动转换各种类型为字符串
		switch v := bodyParams[k].(type) {
		case string:
			signStr.WriteString(v)
		case int, int64:
			signStr.WriteString(fmt.Sprintf("%d", v))
		case float64:
			signStr.WriteString(strconv.FormatFloat(v, 'f', -1, 64))
		case bool:
			signStr.WriteString(strconv.FormatBool(v))
		default:
			signStr.WriteString(fmt.Sprintf("%v", v))
		}
	}

	// 2. 使用HMAC-SHA256加密
	mac := hmac.New(sha256.New, []byte(appSecret))
	if _, err := mac.Write([]byte(signStr.String())); err != nil {
		return "", err
	}
	return hex.EncodeToString(mac.Sum(nil)), nil
}

// VerifySignature 验证签名
func VerifySignature(appId, appSecret string, bodyParams map[string]interface{}) (bool, error) {
	// 检查签名是否存在
	sign, ok := bodyParams["sign"].(string)
	if !ok {
		return false, errors.New("SignatureCannotBeEmpty")
	}
	delete(bodyParams, "sign")

	// 检查时间戳有效性（5分钟内有效）
	timestamp, ok := bodyParams["timestamp"].(int64)
	if !ok {
		return false, errors.New("TimestampCannotBeEmpty")
	}
	if time.Now().Unix()-timestamp > 300 {
		return false, errors.New("SignatureExpired")
	}
	delete(bodyParams, "timestamp")

	// 检测nonce是否存在
	nonce, ok := bodyParams["nonce"].(string)
	if !ok {
		return false, errors.New("NonceCannotBeEmpty")
	}
	delete(bodyParams, "nonce")

	newSign, err := GenerateSignature(appId, appSecret, bodyParams, timestamp, nonce)
	if err != nil {
		return false, err
	}
	return hmac.Equal([]byte(newSign), []byte(sign)), nil
}

// SignRandomNonce 生成随机字符串
func SignRandomNonce() string {
	b := make([]byte, 16)
	_, _ = rand.Read(b)
	return hex.EncodeToString(b)
}
