package utils

import "time"

// IsMarketClosedForTrading checks if the general Forex/Futures markets are closed.
// This is a simplified check for weekend closure.
// Markets typically close Friday 5 PM EST (22:00 UTC) and reopen Sunday 5 PM EST (22:00 UTC).
func IsMarketClosedForTrading(now time.Time) bool {
	utcNow := now.UTC()
	weekday := utcNow.Weekday()
	hour := utcNow.Hour()

	// Market is closed from Friday 22:00 UTC to Sunday 22:00 UTC
	if weekday == time.Friday && hour >= 22 {
		return true // Friday night, closed
	}
	if weekday == time.Saturday {
		return true // All of Saturday, closed
	}
	if weekday == time.Sunday && hour < 22 {
		return true // Sunday, until market reopens
	}

	return false
}
