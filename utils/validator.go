package utils

import "regexp"

// IsEmail 验证是否为有效的邮箱地址
func IsEmail(email string) bool {
	if email == "" {
		return false
	}
	// 基本的邮箱格式验证正则表达式
	emailRegex := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	match, _ := regexp.MatchString(emailRegex, email)
	return match
}

// IsTelephone 验证是否为有效的手机号码
func IsTelephone(telephone string) bool {
	if telephone == "" {
		return false
	}
	// 手机号码格式验证正则表达式 - 仅验证是否为纯数字
	telephoneRegex := `^\d+$`
	match, _ := regexp.MatchString(telephoneRegex, telephone)
	return match
}
