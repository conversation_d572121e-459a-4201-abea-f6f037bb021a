package utils

import "time"

// CalculatePeriod 计算当前期数
func CalculatePeriod(orderCreatedTime time.Time, currentTime time.Time, periodCount int, unit string) int {

	// 根据不同的单位进行相应的时间差计算
	var periodDuration time.Duration
	switch unit {
	case "year":
		yearsPassed := currentTime.Year() - orderCreatedTime.Year()
		// 如果当前日期早于订单创建日期，需减去一年
		if currentTime.YearDay() < orderCreatedTime.YearDay() {
			yearsPassed--
		}
		return yearsPassed/periodCount + 1
	case "month":
		monthsPassed := (currentTime.Year()-orderCreatedTime.Year())*12 + int(currentTime.Month()-orderCreatedTime.Month())
		// 如果当前日期早于订单创建日期，需减去一个月
		if currentTime.Day() < orderCreatedTime.Day() {
			monthsPassed--
		}
		return monthsPassed/periodCount + 1
	case "day":
		periodDuration = time.Hour * 24
	case "hour":
		periodDuration = time.Hour
	case "minute":
		periodDuration = time.Minute
	case "second":
		periodDuration = time.Second
	}

	// 其他以天、小时、分钟、秒为单位的周期数计算
	durationPassed := currentTime.Sub(orderCreatedTime)
	return int(durationPassed / (periodDuration * time.Duration(periodCount)))
}
