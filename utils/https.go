package utils

import (
	"bytes"
	"golang.org/x/net/proxy"
	"io"
	"net/http"
	"net/url"
	"time"
)

type Client struct {
	client    *http.Client    //	client对象
	transport *http.Transport //	代理对象
	headers   http.Header     // 请求头信息
	params    url.Values      // 请求头信息
	error     error           // 错误信息
}

// ClientProxyInfo 代理信息
type ClientProxyInfo struct {
	Host string //	主机
	User string // 用户
	Pass string // 密码
}

// NewClient 创建连接
func NewClient() *Client {
	return &Client{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// Set 设置头信息
func (c *Client) Set(k, v string) *Client {
	if c.headers == nil {
		c.headers = make(http.Header)
	}
	c.headers.Set(k, v)
	return c
}

// AddParam 设置头Get参数
func (c *Client) AddParam(k, v string) *Client {
	if c.params == nil {
		c.params = make(url.Values)
	}
	c.params.Set(k, v)
	return c
}

// Get 请求
func (c *Client) Get(path string) ([]byte, error) {
	if c.params != nil {
		path += "?" + c.params.Encode()
	}
	return c.Request("GET", path, "")
}

// Post 请求
func (c *Client) Post(path, paramType, params string) ([]byte, error) {
	if c.params != nil {
		path += "?" + c.params.Encode()
	}
	c.Set("Content-Type", paramType)
	return c.Request("POST", path, params)
}

// Request 请求信息
func (c *Client) Request(method string, url string, params string) ([]byte, error) {
	if c.error != nil {
		return nil, c.error
	}

	var bufferParams io.Reader
	if len(params) > 0 {
		bufferParams = bytes.NewBufferString(params)
	}

	// 创建请求链接
	req, err := http.NewRequest(method, url, bufferParams)
	if err != nil {
		return nil, err
	}

	// 设置请求头信息
	if c.headers != nil {
		req.Header = c.headers
	}

	// 请求信息
	if c.transport != nil {
		c.client.Transport = c.transport
	}
	resp, err := c.client.Do(req)
	if err != nil {
		return nil, err
	}
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 关闭连接
	err = resp.Body.Close()
	if err != nil {
		return nil, err
	}
	// 如果有设置代理, 那么关闭闲置连接
	if c.transport != nil {
		c.transport.CloseIdleConnections()
	}
	return bodyBytes, nil
}

// SetTimeout 设置超时时间
func (c *Client) SetTimeout(timeout time.Duration) *Client {
	c.client.Timeout = timeout
	return c
}

// SetTransport 设置代理
func (c *Client) SetTransport(transport *http.Transport) *Client {
	c.transport = transport
	return c
}

// SetSocket5 设置Socket5
func (c *Client) SetSocket5(proxyInfo *ClientProxyInfo) *Client {
	if proxyInfo != nil {
		dialer, err := proxy.SOCKS5("tcp", proxyInfo.Host, &proxy.Auth{User: proxyInfo.User, Password: proxyInfo.Pass}, proxy.Direct)
		if err != nil {
			c.error = err
		}

		// 转成上下文模式, 可以设置超时等模式
		if contextDialer, ok := dialer.(proxy.ContextDialer); ok {
			c.transport = &http.Transport{
				DialContext: contextDialer.DialContext,
			}
		}
	}
	return c
}
