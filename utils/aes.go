package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"io"
)

// GenerateAESKey 生成指定长度的AES密钥
func GenerateAESKey(bits int) ([]byte, error) {
	if bits%64 != 0 || bits < 128 || bits > 256 {
		return nil, errors.New("invalid AES key size, must be 128/192/256")
	}
	key := make([]byte, bits/8)
	_, err := rand.Read(key)
	return key, err
}

type Aes struct {
}

// NewAes 初始化AES加密器
func NewAes() *Aes {
	return &Aes{}
}

// Encrypt AES加密 key 必须是16、24或32字节长度，对应AES-128、AES-192或AES-256
func (a *Aes) Encrypt(plaintext, key []byte) (string, error) {
	// 创建加密块
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	// 创建随机数
	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	// 加密数据
	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)

	// 返回Base64编码的加密数据
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// Decrypt AES解密
func (a *Aes) Decrypt(encryptedText string, key []byte) (string, error) {
	// 解码Base64
	ciphertext, err := base64.StdEncoding.DecodeString(encryptedText)
	if err != nil {
		return "", err
	}

	// 创建加密块
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	// 提取nonce
	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return "", errors.New("insufficient encrypted data length")
	}

	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}
