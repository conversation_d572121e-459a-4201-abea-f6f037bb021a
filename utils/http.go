package utils

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"net/url"
)

// HttpClient 是一个HTTP客户端
type HttpClient struct {
	client  *http.Client      // HTTP客户端
	body    []byte            // 响应体
	err     error             // 错误信息
	headers map[string]string // 头信息
	params  url.Values        // 查询参数
	proxy   string            // 代理地址
	baseURL string            // 基础URL
}

// NewHttpClient 创建一个新的HttpClient
func NewHttpClient(baseURL string) *HttpClient {
	return &HttpClient{
		client:  &http.Client{},
		headers: make(map[string]string), // Initialize headers map
		baseURL: baseURL,                 // Set base URL
		params:  url.Values{},            // Initialize params map,
	}
}

// SetHeader 设置请求头
func (h *HttpClient) SetHeader(key, value string) *HttpClient {
	h.headers[key] = value
	return h
}

// SetGetParams 设置查询参数
func (h *HttpClient) SetGetParams(params map[string]string) *HttpClient {
	for key, value := range params {
		h.params.Set(key, value)
	}
	return h
}

// SetProxy 设置代理地址
func (h *HttpClient) SetProxy(proxy string) *HttpClient {
	h.proxy = proxy
	if proxy != "" {
		proxyURL, err := url.Parse(proxy)
		if err == nil {
			h.client.Transport = &http.Transport{Proxy: func(req *http.Request) (*url.URL, error) {
				return proxyURL, nil
			}}
		}
	}
	return h
}

// Get 发送GET请求
func (h *HttpClient) Get(endpoint string) *HttpClient {
	url := h.baseURL + endpoint
	if len(h.params) > 0 {
		url += "?" + h.params.Encode()
	}
	// Create a new request with baseURL and endpoint, and set the headers
	req, err := http.NewRequest("GET", url, nil) // Use baseURL
	if err != nil {
		h.err = err
		return h
	}

	// Set headers
	for key, value := range h.headers {
		req.Header.Set(key, value)
	}

	resp, err := h.client.Do(req)
	if err != nil {
		h.err = err
		return h
	}
	defer resp.Body.Close()

	h.body, h.err = io.ReadAll(resp.Body)
	return h
}

// Post 发送POST请求
func (h *HttpClient) Post(endpoint string, data []byte) *HttpClient {
	req, err := http.NewRequest("POST", h.baseURL+endpoint, bytes.NewBuffer(data)) // Use baseURL
	if err != nil {
		h.err = err
		return h
	}
	req.Header.Set("Content-Type", "application/json")

	// Set headers
	for key, value := range h.headers {
		req.Header.Set(key, value)
	}

	resp, err := h.client.Do(req)
	if err != nil {
		h.err = err
		return h
	}
	defer resp.Body.Close()

	h.body, h.err = io.ReadAll(resp.Body)
	return h
}

// GetBody 获取响应体
func (h *HttpClient) GetBody() []byte {
	return h.body
}

// ToString 将响应体转换为字符串
func (h *HttpClient) ToString() string {
	return string(h.body)
}

// ToStruct 将响应体转换为结构体
func (h *HttpClient) ToStruct(val interface{}) error {
	err := json.Unmarshal(h.body, val)
	if err != nil {
		return err
	}
	return nil
}
