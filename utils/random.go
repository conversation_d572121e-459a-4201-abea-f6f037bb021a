package utils

import (
	"fmt"
	"math"
	"math/rand"
	"path/filepath"
	"time"

	"github.com/google/uuid"
)

const (
	letterBytes   = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	letterIdxBits = 6                    // 6 bits to represent a letter index
	letterIdxMask = 1<<letterIdxBits - 1 // All 1-bits, as many as letterIdxBits
	letterIdxMax  = 63 / letterIdxBits   // # of letter indices fitting in 63 bits
)

// GenerateRandomString 生成指定长度的随机字符串
func GenerateRandomString(n int) string {
	b := make([]byte, n)

	src := rand.NewSource(int64(uuid.New().ID()))
	// A src.Int63() generates 63 random bits, enough for letterIdxMax characters!
	for i, cache, remain := n-1, src.Int63(), letterIdxMax; i >= 0; {
		if remain == 0 {
			cache, remain = src.Int63(), letterIdxMax
		}
		if idx := int(cache & letterIdxMask); idx < len(letterBytes) {
			b[i] = letterBytes[idx]
			i--
		}
		cache >>= letterIdxBits
		remain--
	}

	return string(b)
}

// GenerateNumericString 生成指定长度的数字字符串
func GenerateNumericString(length int) string {
	if length <= 0 {
		return ""
	}

	const digits = "0123456789"
	b := make([]byte, length)

	// 第一个字符单独处理以避免前导零
	src := rand.NewSource(int64(uuid.New().ID()))
	b[0] = digits[src.Int63()%9+1] // 1-9

	// 处理剩余字符
	for i := 1; i < length; i++ {
		src = rand.NewSource(int64(uuid.New().ID()))
		b[i] = digits[src.Int63()%10] // 0-9
	}

	return string(b)
}

// GenerateInviteCode 生成指定长度的邀请码
func GenerateInviteCode(length int) string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	code := make([]byte, length)
	for i := range code {
		src := rand.NewSource(int64(uuid.New().ID()))
		code[i] = charset[src.Int63()%int64(len(charset))]
	}
	return string(code)
}

// GenerateRandomFileName 生成随机文件名
func GenerateRandomFileName(originalName string) string {
	// 获取文件扩展名
	ext := filepath.Ext(originalName)

	// 生成随机字符串作为文件名
	randomPart := GenerateRandomString(16)

	// 添加时间戳以确保唯一性
	timestamp := time.Now().UnixNano() / int64(time.Nanosecond)

	// 组合新的文件名
	newFileName := fmt.Sprintf("%s_%d%s", randomPart, timestamp, ext)

	return newFileName
}

// GenerateRandomFloat64 生成指定范围内的随机float64数字，保留指定位小数
func GenerateRandomFloat64(min, max float64, decimalPlaces int) float64 {
	// 确保min小于max
	if min > max {
		min, max = max, min
	}

	// 生成随机float64
	random := min + rand.Float64()*(max-min)

	// 计算四舍五入的倍数
	roundMultiplier := math.Pow10(decimalPlaces)

	// 保留指定位小数
	return math.Round(random*roundMultiplier) / roundMultiplier
}

// GenerateRandomInt 随机整数
func GenerateRandomInt(min, max int) int {
	if min == max {
		return min
	}
	rand.NewSource(time.Now().UnixNano())
	return rand.Intn(max-min) + min
}

// GenerateOrderSN 生成订单号
func GenerateOrderSN() string {
	// 获取当前时间
	now := time.Now()

	// 格式化时间为yyyyMMddHHmmss
	timeStr := now.Format("20060102150405")

	// 生成6位随机数
	randomNum := fmt.Sprintf("%06d", rand.Intn(1000000))

	// 组合订单号
	orderSN := fmt.Sprintf("%s%s", timeStr, randomNum)

	return orderSN
}
