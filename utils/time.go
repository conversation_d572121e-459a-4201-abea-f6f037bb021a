package utils

import (
	"strconv"
	"time"
)

// GetTimeZoneByLocation 获取时区
func GetTimeZoneByLocation(timeZone string) *time.Location {
	if len(timeZone) != 6 || (timeZone[0] != '+' && timeZone[0] != '-') {
		return time.UTC
	}

	// 提取小时和分钟
	sign := 1
	if timeZone[0] == '-' {
		sign = -1
	}
	hours, err := strconv.Atoi(timeZone[1:3])
	if err != nil {
		return time.UTC
	}
	minutes, err := strconv.Atoi(timeZone[4:6])
	if err != nil {
		return time.UTC
	}

	// 计算总偏移秒数
	totalOffset := sign * (hours*3600 + minutes*60)
	return time.FixedZone(timeZone, totalOffset)
}

// TimeZoneConversion 时区转换
func TimeZoneConversion(inputTimeStr string, layout string, timeZone string) time.Time {
	parsedTime, err := time.ParseInLocation(layout, inputTimeStr, time.Local)
	if err != nil {
		return time.Now()
	}

	targetLocation := GetTimeZoneByLocation(timeZone)
	return parsedTime.In(targetLocation)
}
