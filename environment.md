### linux 项目编译方法
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o src/service main.go

### unbantu22 运行环境 配置
    安装redis 服务
        1. apt install redis-server     # 安装 redis 服务
    安装 mysql8.0 服务
        1. apt install mysql-server     # 安装 mysql8.0 服务

        ### 修改root 密码
        1. mysql                                                                                        # 进入 mysql
        2. use mysql;                                                                                   # 进入 mysql 数据库
        3. alter user 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'Aa123098..';         # 修改 root 密码
        4. flush privileges;                                                                            # 刷新权限
    安装 nginx 服务
        1. apt install nginx                                                                           # 安装 nginx 服务

        ### 添加配置文件流程
        1. cd /etc/nginx/conf.d/                           # 进入 nginx 配置文件目录
        2. vim 域名.conf                                    # 添加配置文件 必须以 .conf 结尾 nginx 才能认到
        3. nginx -t                                        # 测试配置文件是否正确
        4. nginx -s reload                                 # 重新加载配置文件 - 生效

        ### 安装申请 Let’s Encrypt 证书 如果没有使用其他的证书, 可以使用免费的证书
        1. apt install certbot                                                                         # 安装 certbot
        2. certbot certonly --webroot -w /var/www/src/public -d example.com -d www.example.com         # 申请证书, 多域名
        3. 证书生成完毕后，我们可以在 /etc/letsencrypt/live/ 目录下看到对应域名的文件夹，里面存放了指向证书的一些快捷方式。 这时候我们的第一生成证书已经完成了，接下来就是配置我们的web服务器，启用HTTPS。

        ### 默认的 nginx 配置文件 域名.conf 修改到这个配置文件里面, 可以多个和在一起配置

    安装 nvm 管理 nodejs 版本
        1. curl https://raw.githubusercontent.com/creationix/nvm/master/install.sh | bash           # 安装 nvm
        2. source ~/.bashrc                                                                          # 加载 nvm 环境变量
        3. nvm install 18                                                                           # 安装 nodejs 18 版本
        4. npm install -g yarn                                                                      # 安装 yarn 包管理工具

### 反响代理配置
server {
   listen 80;
   server_name [域名, 多个用空格隔开];
   index index.html;

   location / {
      proxy_pass http://127.0.0.1:4010;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "upgrade";
      proxy_read_timeout 3600;
   }
}

### 替换 ssr 文件域名
grep -rIlZ "http://************:4010" /www/wwwroot/ssr | xargs -0 sudo sed -i 's#http://************:4010#https://baidu.com:4010#g'




export https_proxy=http://127.0.0.1:60766 http_proxy=http://127.0.0.1:60766 all_proxy=socks5://127.0.0.1:60766
export https_proxy=http://************:7890 http_proxy=http://************:7890 all_proxy=socks5://************:7890


进程奔溃自动重启

#!/bin/bash

while true
do
    node index.js
    echo "进程已退出, 2 秒后重新启动..."
    sleep 2
done