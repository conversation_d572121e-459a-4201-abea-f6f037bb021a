package instance

import (
	"encoding/json"
	"reflect"
	"zfeng/core/cache"
	"zfeng/core/context"
	"zfeng/middleware"
	"zfeng/module/socket"
	"zfeng/module/socket/handler"

	"github.com/gofiber/fiber/v2"
)

// ChatsSocket 聊天socket
var ChatsSocket = socket.NewSocket("chatsSocket").
	SetEventMessage(OnChatsMessageFunc).
	SetEventOpen(OnChatsOpenFunc).
	SetEventClose(OnChatsCloseFunc)

// NewChatsSocketConn 创建socket 连接
func NewChatsSocketConn(connType int8) fiber.Handler {
	return handler.NewSocketConn(connType, ChatsSocket)
}

// OnChatsMessageFunc 消息事件
func OnChatsMessageFunc(socketInstance *socket.Socket, connType int8, uuidStr string, msg []byte) error {
	connMessage := &socket.ClientMessage{}
	err := json.Unmarshal(msg, connMessage)
	if err != nil {
		return err
	}

	rdsConn := cache.Rds.Get()
	defer rdsConn.Close()

	switch connMessage.Op {
	case socket.MessageOperateBindUser:
		var claims *context.TokenClaims
		var err error

		// 验证Token
		switch connType {
		case socket.ConnTypeAdmin:
			if connMessage.Data == nil || reflect.TypeOf(connMessage.Data).Kind() != reflect.String {
				return nil
			}
			// 绑定管理用户
			claims, err = middleware.ValidateToken(connMessage.Data.(string), middleware.AdminPublicKey)
			if claims != nil && claims.AdminID > 0 {
				socketInstance.ConnMaps.SetConnUserId(socket.ConnTypeAdmin, uuidStr, claims.MerchantID, claims.AdminID)

				// 设置当前连接管理ID
				socketInstance.ConnMaps.SetConnUserId(connType, uuidStr, claims.MerchantID, claims.AdminID)

				// 绑定用户ID 后台绑定的是管理ID
				socketInstance.ConnMaps.RedisSetConnInfo(rdsConn, connType, claims.AdminID, &socket.UserConnInfo{
					UUID: uuidStr, Key: socketInstance.GetSocketKey(), UserId: claims.UserID, AdminId: claims.AdminID, MerchantId: claims.MerchantID,
				})
			}

		case socket.ConnTypeUser:
			if connMessage.Data == nil || reflect.TypeOf(connMessage.Data).Kind() != reflect.String {
				return nil
			}
			// 绑定用户
			claims, err = middleware.ValidateToken(connMessage.Data.(string), middleware.FrontendPublicKey)
			if claims != nil && claims.UserID > 0 {
				socketInstance.ConnMaps.SetConnUserId(socket.ConnTypeUser, uuidStr, claims.AdminID, claims.UserID)

				// 设置当前连接管理ID
				socketInstance.ConnMaps.SetConnUserId(connType, uuidStr, claims.AdminID, claims.UserID)

				// 绑定用户ID 后台绑定的是管理ID
				socketInstance.ConnMaps.RedisSetConnInfo(rdsConn, connType, claims.UserID, &socket.UserConnInfo{
					UUID: uuidStr, Key: socketInstance.GetSocketKey(), UserId: claims.UserID, AdminId: claims.AdminID, MerchantId: claims.MerchantID,
				})
			}
		}
		if err != nil {
			return err
		}

		if claims != nil && (claims.AdminID > 0 || claims.UserID > 0) {
			// 发布绑定用户成功消息
			socketInstance.RedisPublish(connType, socket.MessageOperateBindUser, uuidStr, "ok")

			// 打开事件
			socketInstance.EventOpen(socketInstance, connType, uuidStr)
		}
	}

	return nil
}

// OnChatsOpenFunc 打开事件
func OnChatsOpenFunc(socketInstance *socket.Socket, connType int8, uuidStr string) {
	switch connType {
	case socket.ConnTypeUser:
		// 用户打开事件, 推送用户上线通知
		rdsConn := cache.Rds.Get()
		defer rdsConn.Close()

		connInfo := socketInstance.ConnMaps.GetConn(connType, uuidStr)
		// 发送订阅消息
		socketInstance.RedisUserPublish(rdsConn, socket.ConnTypeAdmin, socket.MessageOperateOnline, connInfo.AdminID, connInfo.UserID)
	}
}

// OnChatsCloseFunc 关闭事件
func OnChatsCloseFunc(socketInstance *socket.Socket, connInfo *socket.ConnInfo) {
	switch connInfo.Type {
	case socket.ConnTypeUser:
		// 用户关闭事件, 推送用户下线通知
		rdsConn := cache.Rds.Get()
		defer rdsConn.Close()
		socketInstance.RedisUserPublish(rdsConn, socket.ConnTypeAdmin, socket.MessageOperateOffline, connInfo.AdminID, connInfo.UserID)
	}
}
