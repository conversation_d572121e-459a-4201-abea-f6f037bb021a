package example

import (
	"zfeng/module/markets"
	"zfeng/module/markets/okex"
	"zfeng/module/socket/client"
	"zfeng/utils"
)

type Marekts struct {
	Okex client.SocketClientInterface
}

// NewMarkets 创建一个新的Markets
func NewMarkets() *Marekts {
	// 获取数据库需要订阅的 okex 产品
	subscribes := []*client.Subscribe{
		{
			Name: okex.SubscribeChannelTickers, //	通道名称
			Data: utils.StructToBytes(&okex.Subscribe{
				Op:   "subscribe", // 操作类型
				Args: []okex.SubscribeArgs{{Channel: okex.SubscribeChannelTickers, InstId: "BTC-USDT"}},
			}),
		},
	}

	return &Marekts{
		Okex: markets.NewMarkets(okex.Platform).NewWebsocket(subscribes, SubscribeMessageFunc),
	}
}

// SubscribeMessageFunc 订阅消息处理
func SubscribeMessageFunc(platform string, channel string, data interface{}) {
	switch platform {
	case okex.Platform:
		// okex 平台数据
		switch channel {
		case okex.SubscribeChannelTickers:
			// 最新行情
		case okex.SubscribeChannelBooks:
			// 行情深度
		case okex.SubscribeChannelTrades:
			// 最新成交
		default:
			return
		}
	default:
		return
	}

}
