package example

import (
	"zfeng/core/cache"
	"zfeng/module/socket"

	"github.com/goccy/go-json"
)

// MaketsSockets 市场socket
var MaketsSockets = socket.NewSocket("markets").SetEventMessage(OnMessageFunc)

// OnMessageFunc 消息处理
func OnMessageFunc(socketInstance *socket.Socket, connType int8, uuidStr string, msg []byte) error {
	connMessage := &socket.ClientMessage{}
	err := json.Unmarshal(msg, connMessage)
	if err != nil {
		return err
	}

	rdsConn := cache.Rds.Get()
	defer rdsConn.Close()

	switch connMessage.Op {
	case socket.MessageOperateSubscribe:
		// 订阅消息
		subscribe := &socket.SubscribeInfo{}
		dataBytes, _ := json.Marshal(connMessage.Data)
		err = json.Unmarshal(dataBytes, &subscribe)
		if err != nil {
			return err
		}
		if subscribe.Channel != "" && len(subscribe.Args) > 0 {
			socketInstance.Subscribe(rdsConn, uuidStr, subscribe.Channel, subscribe.Args)
			socketInstance.RedisPublish(connType, socket.MessageOperateSubscribe, uuidStr, &socket.ClientMessage{
				Op:   socket.MessageOperateSubscribe,
				Data: "ok",
			})
		}
	case socket.MessageOperateUnSubscribe:
		// 取消订阅通道
		subscribe := &socket.SubscribeInfo{}
		dataBytes, _ := json.Marshal(connMessage.Data)
		err = json.Unmarshal(dataBytes, &subscribe)
		if err != nil {
			return err
		}

		if subscribe.Channel != "" && len(subscribe.Args) > 0 {
			socketInstance.UnSubscribe(rdsConn, uuidStr, subscribe.Channel, subscribe.Args)
			socketInstance.RedisPublish(connType, socket.MessageOperateUnSubscribe, uuidStr, &socket.ClientMessage{
				Op:   socket.MessageOperateUnSubscribe,
				Data: "ok",
			})
		}
	}
	return nil
}
