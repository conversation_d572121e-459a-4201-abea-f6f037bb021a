package markets

import (
	"encoding/json"
	"zfeng/core/cache"
	"zfeng/middleware"
	"zfeng/module/exchange/interfaces"
	"zfeng/module/socket"
	"zfeng/module/socket/handler"
	"zfeng/service"

	"github.com/gofiber/fiber/v2"
)

// ProductSocket 产品socket
var ProductSocket = socket.NewSocket("productSocket").
	SetEventMessage(OnProductMessageFunc).
	SetEventOpen(OnProductOpenFunc).
	SetEventClose(OnProductCloseFunc)

// NewProductSocketConn 创建socket 连接
func NewProductSocketConn(connType int8) fiber.Handler {
	symbolList := service.NewProductService().GetProductSymbolList()
	initSubscribeChannelInfo := []*socket.RedisSubscribeChannel{
		{Channel: interfaces.SubscribeChannelTickers, Args: symbolList, ConsumerFunc: func(data []byte) {}},
		{Channel: interfaces.SubscribeChannelBooks, Args: symbolList, ConsumerFunc: func(data []byte) {}},
		{Channel: interfaces.SubscribeChannelTrades, Args: symbolList, ConsumerFunc: func(data []byte) {}},
	}
	ProductSocket.InitSubscribeChannel(initSubscribeChannelInfo...)
	return handler.NewSocketConn(connType, ProductSocket)
}

// OnProductMessageFunc 消息事件
func OnProductMessageFunc(socketInstance *socket.Socket, connType int8, uuidStr string, msg []byte) error {
	connMessage := &socket.ClientMessage{}
	err := json.Unmarshal(msg, connMessage)
	if err != nil {
		return err
	}

	rdsConn := cache.Rds.Get()
	defer rdsConn.Close()

	switch connMessage.Op {
	case socket.MessageOperateBindUser:
		v, ok := connMessage.Data.(string)
		// 绑定用户
		claims, err := middleware.ValidateToken(v, middleware.FrontendPublicKey)
		if ok && err == nil && claims != nil && claims.UserID > 0 {
			// 设置当前连接管理ID
			socketInstance.ConnMaps.SetConnUserId(connType, uuidStr, claims.AdminID, claims.UserID)

			// 绑定用户ID 后台绑定的是管理ID
			socketInstance.ConnMaps.RedisSetConnInfo(rdsConn, connType, claims.UserID, &socket.UserConnInfo{
				UUID: uuidStr, Key: socketInstance.GetSocketKey(), UserId: claims.UserID, AdminId: claims.AdminID, MerchantId: claims.MerchantID,
			})
			// 发布绑定用户成功消息
			socketInstance.RedisPublish(connType, socket.MessageOperateBindUser, uuidStr, "ok")

			// 打开事件
			socketInstance.EventOpen(socketInstance, connType, uuidStr)
		} else {
			// 发布绑定用户成功消息
			socketInstance.RedisPublish(connType, socket.MessageOperateBindUser, uuidStr, "err")
		}
	case socket.MessageOperateSubscribe:
		// 订阅通道
		subscribe := &socket.SubscribeInfo{}
		dataBytes, _ := json.Marshal(connMessage.Data)
		err = json.Unmarshal(dataBytes, &subscribe)
		if err != nil {
			return err
		}
		if subscribe.Channel != "" && len(subscribe.Args) > 0 {
			socketInstance.Subscribe(rdsConn, uuidStr, subscribe.Channel, subscribe.Args)
			socketInstance.RedisPublish(connType, socket.MessageOperateSubscribe, uuidStr, &socket.ClientMessage{
				Op:   socket.MessageOperateSubscribe,
				Data: "ok",
			})
		}

	case socket.MessageOperateUnSubscribe:
		// 取消订阅通道
		subscribe := &socket.SubscribeInfo{}
		dataBytes, _ := json.Marshal(connMessage.Data)
		err = json.Unmarshal(dataBytes, &subscribe)
		if err != nil {
			return err
		}

		if subscribe.Channel != "" && len(subscribe.Args) > 0 {
			socketInstance.UnSubscribe(rdsConn, uuidStr, subscribe.Channel, subscribe.Args)
			socketInstance.RedisPublish(connType, socket.MessageOperateUnSubscribe, uuidStr, &socket.ClientMessage{
				Op:   socket.MessageOperateUnSubscribe,
				Data: "ok",
			})
		}
	}

	return nil
}

// OnProductOpenFunc 打开事件
func OnProductOpenFunc(socketInstance *socket.Socket, connType int8, uuidStr string) {
}

// OnProductCloseFunc 关闭事件
func OnProductCloseFunc(socketInstance *socket.Socket, connInfo *socket.ConnInfo) {
}
