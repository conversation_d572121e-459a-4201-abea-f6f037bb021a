package markets

import (
	"encoding/json"
	"zfeng/module/exchange/customize"
	"zfeng/module/exchange/interfaces"
	"zfeng/module/exchange/okx"
	"zfeng/module/exchange/trading"
	"zfeng/module/socket"
	"zfeng/module/socket/client"
	"zfeng/service"
	"zfeng/utils"

	"github.com/gomodule/redigo/redis"
)

func InitExchange() {
	productService := service.NewProductService()
	// 初始化交易所数据
	okx.Exchange.SetWebSocketMessageFunc(MessageFunc).
		InitWebSocket(GetOkExchangeSubscribes(productService.GetProductOKEXSymbolList()))

	////初始化易汇产品数据
	//ieforex.Exchange.SetSymbolDecimalMap(productService.GetProductInvestingSymbolDecimalList()).
	//	SetWebSocketMessageFunc(MessageFunc).
	//	InitWebSocket([]*client.Subscribe{{Name: interfaces.SubscribeChannelTickers, Data: ""}})
	//初始化易汇产品数据
	trading.Exchange.SetWebSocketMessageFunc(MessageFunc).InitWebSocket(GetTradingExchangeSubscribes(productService.GetProductTradingSymbolList()))

	// 初始化议会产品
	customize.Exchange.SetWebSocketMessageFunc(MessageFunc).InitWebSocket(nil)
}

// GetOkExchangeSubscribes 获取订阅OkExchange信息
func GetOkExchangeSubscribes(symbolList []string) []*client.Subscribe {
	subscribeList := []*client.Subscribe{
		// 订阅产品行情信息
		{Name: interfaces.SubscribeChannelTickers},
		// 订阅产品深度信息
		{Name: interfaces.SubscribeChannelBooks},
		// 订阅产品深度信息
		{Name: interfaces.SubscribeChannelTrades},
	}

	// 组成订阅信息
	for _, subscribe := range subscribeList {
		newSubscribe := &okx.SubscribeParams{
			Op:   "subscribe",
			Args: make([]*okx.SubscribeArg, 0),
		}
		switch subscribe.Name {
		case interfaces.SubscribeChannelTickers:
			for _, s := range symbolList {
				newSubscribe.Args = append(newSubscribe.Args, &okx.SubscribeArg{
					Channel: subscribe.Name,
					InstID:  s,
				})
			}
			subscribe.Data, _ = json.Marshal(newSubscribe)
		case interfaces.SubscribeChannelBooks:
			for _, s := range symbolList {
				newSubscribe.Args = append(newSubscribe.Args, &okx.SubscribeArg{
					Channel: subscribe.Name,
					InstID:  s,
				})
			}
			subscribe.Data, _ = json.Marshal(newSubscribe)
		case interfaces.SubscribeChannelTrades:
			for _, s := range symbolList {
				newSubscribe.Args = append(newSubscribe.Args, &okx.SubscribeArg{
					Channel: subscribe.Name,
					InstID:  s,
				})
			}
			subscribe.Data = utils.JsonToBytes(newSubscribe)
		}
	}
	return subscribeList
}

// GetTradingExchangeSubscribes 获取订阅TradingExchange信息
func GetTradingExchangeSubscribes(symbolList []string) []*client.Subscribe {
	subscribeList := []*client.Subscribe{
		// 订阅产品行情信息
		{Name: interfaces.SubscribeChannelTickers, Data: utils.JsonToBytes(symbolList)},
	}
	return subscribeList
}

type SubscribeData struct {
	Channel string      `json:"channel"`
	Symbol  string      `json:"symbol"`
	Data    interface{} `json:"data"`
}

func MessageFunc(channel, symbol string, rdsConn redis.Conn, data interface{}) {
	channelList := ProductSocket.GetSubscribeChannelList(rdsConn, channel)
	for uuid, args := range channelList {
		if utils.ArrayStringIndexOf(args, symbol) > -1 {
			// 获取连接对象信息
			ProductSocket.RedisPublish(socket.ConnTypeProduct, socket.MessageOperateSubscribe, uuid, &SubscribeData{
				Channel: channel,
				Symbol:  symbol,
				Data:    data,
			})
		}
	}
}
