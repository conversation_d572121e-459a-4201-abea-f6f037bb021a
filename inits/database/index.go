package database

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"zfeng/core/cache"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/service"

	"gorm.io/gorm"
)

// InitModels 初始化数据 - 初始化商户数据, 载入sql文件
func InitDatabase() {
	// 初始化商户数据
	db := model.NewModel()
	var count int64
	db.Model(&models.AdminSetting{}).Where("admin_id = ?", models.DefaultMerchantID).Count(&count)
	if count == 0 {
		merchantService := service.NewMerchantService()
		err := db.Transaction(func(tx *gorm.DB) error {
			return merchantService.InitMerchantData(tx, models.DefaultMerchantID)
		})
		if err != nil {
			panic("Failed to initialize merchant data: " + err.Error())
		}
	}

	// 初始化数据库迁移数据
	sqlFiles, err := filepath.Glob("sql/*.sql")
	if err != nil {
		panic("Failed to read SQL files: " + err.Error())
	}

	for _, file := range sqlFiles {
		fileName := filepath.Base(file)

		migrateInfo := models.Migrate{}
		db.Model(&models.Migrate{}).Where("name = ?", fileName).Where("status = ?", models.MigrationStatusEnabled).Find(&migrateInfo)
		if migrateInfo.ID > 0 {
			continue
		}

		sqlContent, err := os.ReadFile(file)
		if err != nil {
			panic("Failed to read SQL file " + file + ": " + err.Error())
		}
		if len(sqlContent) == 0 {
			continue
		}

		err = db.Transaction(func(tx *gorm.DB) error {
			// 使用智能分割 SQL 语句
			statements := SplitSQLStatements(string(sqlContent))
			for _, stmt := range statements {
				if err := tx.Exec(stmt).Error; err != nil {
					return err
				}
			}
			return nil
		})
		if err != nil {
			panic("Failed to execute SQL from file " + file + ": " + err.Error())
		}

		migrateInfo.Name = fileName
		result := db.Create(&migrateInfo)
		if result.Error != nil {
			panic("Failed to save migrate info: " + result.Error.Error())
		}
	}

	// 清除启动初始化的缓存
	rdsConn := cache.Rds.Get()
	defer rdsConn.Close()

	// 删除翻译的中的缓存
	_, _ = rdsConn.Do("DEL", fmt.Sprintf(models.RedisLangTranslateKey))
}

// SplitSQLStatements 智能分割 SQL 语句
func SplitSQLStatements(content string) []string {
	var statements []string
	var currentStmt strings.Builder
	var inString bool

	for i := 0; i < len(content); i++ {
		ch := content[i]
		currentStmt.WriteByte(ch)

		switch ch {
		case '\'':
			// 处理字符串中的引号
			if i == 0 || content[i-1] != '\\' {
				inString = !inString
			}
		case ';':
			if !inString {
				// 找到一个完整的语句
				stmt := strings.TrimSpace(currentStmt.String())
				if stmt != "" {
					statements = append(statements, stmt)
				}
				currentStmt.Reset()
			}
		}
	}

	// 处理最后一个语句（如果没有分号结尾）
	lastStmt := strings.TrimSpace(currentStmt.String())
	if lastStmt != "" {
		statements = append(statements, lastStmt)
	}

	return statements
}
