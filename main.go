package main

import (
	"strings"
	"zfeng/admin"
	adminControllers "zfeng/admin/controllers"
	"zfeng/controllers"
	"zfeng/core/config"
	"zfeng/core/context"
	"zfeng/inits"
	"zfeng/middleware"
	"zfeng/service"

	"github.com/goccy/go-json"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
)

func main() {
	// 项目APP
	fiberApp := createFiberApp()

	// 启动监听
	if err := fiberApp.Listen(":" + config.Port); err != nil {
		zap.L().Fatal("运行错误", zap.Error(err))
	}
}

func createFiberApp() *fiber.App {
	// 主 Fiber 应用
	mainApp := fiber.New(fiber.Config{
		AppName:      config.Name,
		ServerHeader: config.Name,
		Prefork:      config.PreFork,
		JSONEncoder:  json.Marshal,
		JSONDecoder:  json.Unmarshal,
	})

	// 后台 Fiber 应用
	backendApp := admin.InitAdminApp()

	// 前台 Fiber 应用
	frontendApp := fiber.New()
	frontendApp.Use(middleware.InitFavicon())
	frontendApp.Use(middleware.InitRecover())
	frontendApp.Use(middleware.InitCORS())
	frontendApp.Use(func(c *fiber.Ctx) error {
		// 跳过静态文件的速率限制
		if strings.HasPrefix(c.Path(), "/"+config.StaticFilesPath) {
			return c.Next()
		}
		return middleware.InitLimiter()(c)
	})
	frontendApp.Use(middleware.InitLogger())

	// 前台路由
	controllers.SetupRoutes(frontendApp)

	// 将后台和前台应用挂载到主应用
	mainApp.Mount(adminControllers.AdminRoutePrefix, backendApp)
	mainApp.Mount("/", frontendApp)

	// 静态资源 - 移到这里，确保它不受前台应用中间件的影响
	mainApp.Static("/", config.StaticFilesPath)

	// 初始化程序
	if !fiber.IsChild() {
		inits.RunInits()
	}

	// 设置上下文翻译函数
	context.TranslateFunc = service.NewTranslateService().GetTranslateByFieldWithCacheToArgs
	return mainApp
}
