package ethers

import (
	"encoding/hex"
	"errors"
	"fmt"
	"math/big"
	"strings"
	"zfeng/module/blockchain/interfaces"
	"zfeng/utils"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/goccy/go-json"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

const (
	// BalanceDecimal 余额精度
	BalanceDecimal = 18
	// Erc20USDT 钱包USDT地址
	Erc20USDTDecimal = 6
)

// Ethers 以太坊区块链对象
type Ethers struct {
	RPCURL string
	RPCKEY string

	// 转账之前执行
	beforeTransfer func(fromAddr, toAddr, contractAddr string) error
}

// NewEthers 创建以太坊区块链对象
func NewEthers() *Ethers {
	return &Ethers{
		RPCURL: "https://mainnet.infura.io/v3/",
		RPCKEY: "********************************",

		beforeTransfer: func(fromAddr, toAddr, contractAddr string) error {
			return nil
		},
	}
}

// Transfer 转账
func (e *Ethers) Transfer(fromAddr string, toAddr string, amount *big.Int, fromAddrPrivateKeyHex string) (string, error) {
	// 地址格式验证
	if len(fromAddr) != 42 || len(toAddr) != 42 || !strings.HasPrefix(fromAddr, "0x") || !strings.HasPrefix(toAddr, "0x") {
		return "", fmt.Errorf("invalid Ethereum address format")
	}

	// 交易前执行
	err := e.beforeTransfer(fromAddr, toAddr, "")
	if err != nil {
		return "", err
	}

	// 获取nonce
	nonce, err := e.getTransactionCount(fromAddr)
	if err != nil {
		return "", err
	}

	// 获取gas价格
	gasPrice, err := e.GetGasPrice()
	if err != nil {
		return "", err
	}

	// 预估Gas 限制
	gasLimit, err := e.EstimateGas(fromAddr, toAddr, amount)
	if err != nil {
		return "", err
	}

	// 创建交易对象
	tx := types.NewTransaction(
		nonce,
		common.HexToAddress(toAddr),
		amount,
		gasLimit, // 标准ETH转账gas limit
		gasPrice,
		nil,
	)

	// 签名交易
	txData, err := e.signature(tx, fromAddrPrivateKeyHex)
	if err != nil {
		return "", err
	}

	// 发送交易
	txHash, err := e.sendRawTransaction(hexutil.Encode(txData))
	if err != nil {
		return "", err
	}
	return txHash, nil
}

// BeforeTransfer 转账之前执行
func (e *Ethers) BeforeTransfer(fun func(fromAddr, toAddr, contractAddr string) error) interfaces.Blockchain {
	e.beforeTransfer = fun
	return e
}

// TransferTrc20 转账TRC20
func (e *Ethers) TransferTrc20(fromAddr string, toAddr string, amount *big.Int, fromAddrPrivateKeyHex string, contractAddr string) (string, error) {
	// 地址格式验证
	if len(fromAddr) != 42 || len(toAddr) != 42 || !strings.HasPrefix(fromAddr, "0x") || !strings.HasPrefix(toAddr, "0x") {
		return "", fmt.Errorf("invalid Ethereum address format")
	}

	// 交易前执行
	err := e.beforeTransfer(fromAddr, toAddr, contractAddr)
	if err != nil {
		return "", err
	}

	// 获取nonce
	nonce, err := e.getTransactionCount(fromAddr)
	if err != nil {
		return "", err
	}

	// 获取gas价格
	gasPrice, err := e.GetGasPrice()
	if err != nil {
		return "", err
	}

	// 构造调用数据 (transfer方法签名 + 参数编码)
	toAddrPart := fmt.Sprintf("%064x", common.HexToAddress(toAddr).Bytes())
	amountHex := fmt.Sprintf("%064x", amount)
	data := fmt.Sprintf("0xa9059cbb%s%s", toAddrPart, amountHex)

	// 预估Gas 限制
	gasLimit, err := e.EstimateErc20Gas(fromAddr, contractAddr, data)
	if err != nil {
		return "", err
	}

	// 创建合约调用交易
	tx := types.NewTransaction(
		nonce,
		common.HexToAddress(contractAddr), // 代币合约地址
		big.NewInt(0),                     // ETH转账金额设为0
		gasLimit,                          // 合约交互需要更多gas
		gasPrice,
		hexutil.MustDecode(data), // 调用数据
	)

	// 签名交易
	txData, err := e.signature(tx, fromAddrPrivateKeyHex)
	if err != nil {
		return "", err
	}

	// 发送交易
	txHash, err := e.sendRawTransaction(hexutil.Encode(txData))
	if err != nil {
		return "", err
	}
	return txHash, nil
}

// GetTransactionsTrc20 获取钱包 ERC20 交易记录
func (e *Ethers) GetTransactionsTrc20(contractAddr string, params map[string]interface{}) ([]*interfaces.Transaction, string, error) {
	// 地址格式验证
	if !common.IsHexAddress(contractAddr) {
		return nil, "", fmt.Errorf("invalid Ethereum address format")
	}

	var fromBlock, toBlock string
	toBlock = "latest"
	if _, ok := params["fromBlock"]; ok {
		fromBlock = hexutil.EncodeBig(big.NewInt(params["fromBlock"].(int64)))
	} else {
		latestBlockNumber, err := e.GetBlockNumber()
		if err != nil {
			return nil, "", err
		}
		fromBlock = hexutil.EncodeBig(latestBlockNumber)
	}
	if _, ok := params["toBlock"]; ok {
		toBlock = hexutil.EncodeBig(big.NewInt(params["toBlock"].(int64)))
	}

	resps := Resps{}
	bodyParams := map[string]interface{}{
		"fromBlock": fromBlock,
		"toBlock":   toBlock,
		"address":   common.HexToAddress(contractAddr),
		"topics": []interface{}{
			"0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef",
		},
	}
	err := e.rpcCall(&resps, "eth_getLogs", bodyParams)
	if err != nil {
		return nil, "", err
	}

	// 解析日志到交易结构
	var transactions []*interfaces.Transaction
	transactionLogs := []types.Log{}
	resultBytes, _ := json.Marshal(resps.Result)
	json.Unmarshal(resultBytes, &transactionLogs)
	for _, transaction := range transactionLogs {
		if len(transaction.Topics) < 3 || len(transaction.Data) < 32 {
			continue // 跳过无效日志
		}

		// 精确截取数据段（最后32字节）
		dataSegment := transaction.Data
		if len(dataSegment) > 32 {
			dataSegment = dataSegment[len(dataSegment)-32:] // 取最后32字节
		}

		// 解析转账金额（32字节大整数）
		amount := new(big.Int).SetBytes(dataSegment)
		transactions = append([]*interfaces.Transaction{{
			Hash:           transaction.TxHash.Hex(),
			Contract:       contractAddr,
			From:           common.BytesToAddress(transaction.Topics[1].Bytes()).Hex(),
			To:             common.BytesToAddress(transaction.Topics[2].Bytes()).Hex(),
			Amount:         amount,
			Success:        true,
			BlockTimestamp: 0,
			BlockNumber:    int64(transaction.BlockNumber),
		}}, transactions...)
	}

	return transactions, "", nil
}

// MonitorTransactionsTrc20 监控钱包TRC20交易记录
func (e *Ethers) MonitorTransactionsTrc20(contractAddr string, cronSpec string, fun func(transaction *interfaces.Transaction)) error {
	c := cron.New(
		cron.WithChain(
			// 添加串行执行锁
			cron.DelayIfStillRunning(
				cron.DefaultLogger,
			),
		),
	)
	defer c.Start()

	// 获取当前区块高度
	blockNumberBig, err := e.GetBlockNumber()
	if err != nil {
		return err
	}
	blockNumber := blockNumberBig.Int64() + 1
	c.AddFunc(cronSpec, func() {
		for {
			transactions, _, err := e.GetTransactionsTrc20(contractAddr, map[string]interface{}{
				"fromBlock": blockNumber, "toBlock": blockNumber,
			})
			// 错误处理
			if err != nil {
				zap.L().Error("Etheres GetTransactionsTrc20 Failed", zap.Error(err))
				break
			}

			// 没有数据处理
			if len(transactions) == 0 {
				break
			}

			// 处理交易
			for _, transaction := range transactions {
				fun(transaction)
			}
			blockNumber = blockNumber + 1
			zap.L().Info("Etheres MonitorTransactionsTrc20 Success", zap.Int64("区块高度", blockNumber))
		}

	})
	return nil
}

// GetTransactionByHash 获取交易详情
func (e *Ethers) GetTransactionByHash(txHash string) (*interfaces.Transaction, error) {
	resps := Resps{}
	err := e.rpcCall(&resps, "eth_getTransactionByHash", txHash)
	if err != nil {
		return nil, err
	}

	// 交易详情不存在
	if resps.Result == nil {
		return nil, errors.New("Transaction not found")
	}

	// 解析交易基础信息
	txData, _ := json.Marshal(resps.Result)
	var txResult struct {
		Hash  string `json:"hash"`
		From  string `json:"from"`
		To    string `json:"to"`
		Input string `json:"input"`
	}
	json.Unmarshal(txData, &txResult)

	// 获取交易收据确认状态
	var receipt struct {
		Status      string `json:"status"`
		GasUsed     string `json:"gasUsed"`
		BlockNumber string `json:"blockNumber"`
	}
	e.rpcCall(&resps, "eth_getTransactionReceipt", txHash)
	receiptData, _ := json.Marshal(resps.Result)
	json.Unmarshal(receiptData, &receipt)

	toAddr, amount, err := e.ParseTransferInput(txResult.Input)
	if err != nil {
		return nil, err
	}

	return &interfaces.Transaction{
		Hash:           txResult.Hash,
		Contract:       txResult.To,
		From:           txResult.From,
		To:             toAddr,
		Amount:         amount,
		Success:        receipt.Status == "0x1",
		BlockTimestamp: 0,
		BlockNumber:    hexutil.MustDecodeBig(receipt.BlockNumber).Int64(),
	}, nil
}

// GetGasPrice 获取GasPrice
func (e *Ethers) GetGasPrice() (*big.Int, error) {
	resps := Resps{}
	err := e.rpcCall(&resps, "eth_gasPrice")
	if err != nil {
		return nil, err
	}

	gasPrice, err := hexutil.DecodeBig(resps.Result.(string))
	if err != nil {
		return nil, err
	}

	return gasPrice, nil
}

// EstimateGas ETH 转账 Gas 预估方法
func (e *Ethers) EstimateGas(fromAddr, toAddr string, amount *big.Int) (uint64, error) {
	resps := Resps{}
	params := map[string]interface{}{
		"from":  fromAddr,
		"to":    toAddr,
		"value": hexutil.EncodeBig(amount),
	}

	err := e.rpcCall(&resps, "eth_estimateGas", params)
	if err != nil {
		return 0, fmt.Errorf("ETH 转账 Gas 预估失败: %v", err)
	}

	gasPrice := hexutil.MustDecodeUint64(resps.Result.(string))
	return gasPrice + gasPrice/5, nil
}

// EstimateErc20Gas ERC20 转账 Gas 预估方法
func (e *Ethers) EstimateErc20Gas(fromAddr, contractAddr string, data string) (uint64, error) {
	resps := Resps{}

	params := map[string]interface{}{
		"from": fromAddr,
		"to":   contractAddr,
		"data": data,
	}

	err := e.rpcCall(&resps, "eth_estimateGas", params)
	if err != nil {
		return 0, fmt.Errorf("ERC20 转账 Gas 预估失败: %v", err) // 修正错误信息
	}

	gasPrice := hexutil.MustDecodeUint64(resps.Result.(string))
	return gasPrice * 3 / 2, nil
}

// GetBlockNumber 获取当前区块高度
func (e *Ethers) GetBlockNumber() (*big.Int, error) {
	resps := Resps{}
	err := e.rpcCall(&resps, "eth_blockNumber")
	if err != nil {
		return nil, err
	}
	if resps.Result == nil {
		return nil, errors.New("Block number not found")
	}
	return hexutil.DecodeBig(resps.Result.(string))
}

// GetBalance 获取钱包余额
func (e *Ethers) GetBalance(addr string) (*big.Int, error) {
	// 基础地址格式验证
	if len(addr) != 42 || addr[:2] != "0x" {
		return nil, fmt.Errorf("invalid Ethereum address format")
	}

	// 调用RPC接口获取余额
	resps := Resps{}
	err := e.rpcCall(&resps, "eth_getBalance", addr, "latest")
	if err != nil {
		return nil, err
	}

	// 解析余额
	if resps.Result == nil {
		return nil, errors.New("Balance not found")
	}

	balance := new(big.Int)
	balance.SetString(strings.TrimPrefix(resps.Result.(string), "0x"), 16)
	return balance, nil
}

// GetTrc20Balance 通过钱包地址获取钱包的TRC20余额 免费的Key 1s/ 5次请求
func (e *Ethers) GetTrc20Balance(addr string, contractAddrList ...string) (map[string]*big.Int, error) {
	// 基础地址格式验证
	if len(addr) != 42 || addr[:2] != "0x" {
		return nil, fmt.Errorf("invalid Ethereum address format")
	}

	balances := make(map[string]*big.Int)
	for _, tokenAddr := range contractAddrList {
		// 调用RPC接口获 TRC20 取余额
		resps := Resps{}
		// balanceOf方法签名 + 地址参数
		data := fmt.Sprintf("0x70a08231000000000000000000000000%s", addr[2:])
		err := e.rpcCall(&resps, "eth_call", map[string]string{"to": tokenAddr, "data": data}, "latest")
		if err != nil {
			return nil, err
		}

		balance := new(big.Int)
		balance.SetString(strings.TrimPrefix(resps.Result.(string), "0x"), 16)
		balances[tokenAddr] = balance
	}

	return balances, nil
}

// rpcCall 调用RPC接口
func (e *Ethers) rpcCall(result *Resps, method string, params ...interface{}) error {
	// 构建请求参数
	bodyParamsBytes, _ := json.Marshal(map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  method,
		"params":  params,
		"id":      1,
	})

	err := utils.NewHttpClient(e.RPCURL).Post(e.RPCKEY, bodyParamsBytes).ToStruct(&result)
	if err != nil || result.Error.Code != 0 {
		if err == nil {
			err = fmt.Errorf("RPC Call Failed: %s", result.Error.Message)
		}
		return err
	}
	return nil
}

// getTransactionCount 获取交易 nonce
func (e *Ethers) getTransactionCount(addr string) (uint64, error) {
	resps := Resps{}
	err := e.rpcCall(&resps, "eth_getTransactionCount", addr, "latest")
	if err != nil {
		return 0, err
	}
	return hexutil.DecodeUint64(resps.Result.(string))
}

// signature 交易签名
func (e *Ethers) signature(tx *types.Transaction, privateKeyHex string) ([]byte, error) {
	// 解析私钥
	privateKey, err := crypto.HexToECDSA(privateKeyHex)
	if err != nil {
		return nil, err
	}

	// 获取链ID（以太坊主网为1）
	chainID := big.NewInt(1)

	// 交易签名
	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
	if err != nil {
		return nil, err
	}

	return signedTx.MarshalBinary()
}

// sendRawTransaction 发送原始交易
func (e *Ethers) sendRawTransaction(txData string) (string, error) {
	resps := Resps{}
	err := e.rpcCall(&resps, "eth_sendRawTransaction", txData)
	if err != nil {
		return "", err
	}
	return resps.Result.(string), nil
}

// GenerateWalletAddress 生成钱包地址
func (e *Ethers) GenerateWalletAddress() (string, string, error) {
	// 使用以太坊的crypto包直接生成私钥
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		return "", "", fmt.Errorf("GenerateKey Failed: %w", err)
	}

	// 转换私钥为HEX格式
	privateKeyHex := hex.EncodeToString(crypto.FromECDSA(privateKey))

	// 使用以太坊的crypto包获取地址
	address := crypto.PubkeyToAddress(privateKey.PublicKey).Hex()

	return address, privateKeyHex, nil
}

// GetAccountResource 获取账户资源
func (e *Ethers) GetAccountResource(addr string) (*interfaces.AccountResource, error) {
	return &interfaces.AccountResource{}, nil
}

// SetRPCURL 设置RPC地址
func (e *Ethers) SetRPCURL(url string) interfaces.Blockchain {
	e.RPCURL = url
	return e
}

// SetRPCKEY 设置RPCKEY
func (e *Ethers) SetRPCKEY(key string) interfaces.Blockchain {
	e.RPCKEY = key
	return e
}

// ParseTransferInput 解析ERC20转账的input数据
func (e *Ethers) ParseTransferInput(input string) (toAddr string, amount *big.Int, err error) {
	// 验证input格式（方法签名+参数）
	if len(input) < 138 || !strings.HasPrefix(input, "0xa9059cbb") {
		return "", nil, fmt.Errorf("invalid ERC20 transfer input")
	}

	// 分割参数部分（每32字节一个参数）
	dataHex := input[10:] // 移除方法签名0xa9059cbb
	if len(dataHex) < 64 {
		return "", nil, fmt.Errorf("invalid parameter length")
	}

	// 解析收款地址（第二个参数前20字节）
	addressPart := dataHex[24:64] // 跳过前24字符（12字节填充）
	if !common.IsHexAddress("0x" + addressPart) {
		return "", nil, fmt.Errorf("invalid address encoding")
	}
	toAddr = common.HexToAddress("0x" + addressPart).Hex()

	// 解析转账金额（第二个参数）
	amountBytes, err := hex.DecodeString(dataHex[64:128])
	if err != nil {
		return "", nil, fmt.Errorf("invalid amount encoding")
	}
	amount = new(big.Int).SetBytes(amountBytes)
	return toAddr, amount, nil
}
