package blockchain

import "math/big"

// TransferAmountToFloat 交易金额按照精度 转成小数金额
func TransferAmountToFloat(amount *big.Int, decimals float64) *big.Float {
	amountFloat := new(big.Float).SetInt(amount)
	// 将decimals转换为10的N次方（示例：decimals=6 → 10^6=1_000_000）
	divisor := big.NewFloat(0).SetInt(
		big.NewInt(0).Exp(big.NewInt(10), big.NewInt(int64(decimals)), nil),
	)
	return amountFloat.Quo(amountFloat, divisor)
}

// TransferAmountToInt 交易金额 按照精度转成大数
func TransferAmountToInt(amount float64, decimals float64) *big.Int {
	// 将浮点数转换为精确的大数表示
	amountBig := big.NewFloat(amount)

	// 计算精度倍数（10^decimals）
	multiplier := new(big.Int).Exp(
		big.NewInt(10),
		big.NewInt(int64(decimals)), // 将decimals转换为整数指数
		nil,
	)

	// 转换为big.Float进行乘法运算
	multiplierFloat := new(big.Float).SetInt(multiplier)

	// 执行乘法运算
	resultFloat := new(big.Float).Mul(amountBig, multiplierFloat)

	// 转换为整数（自动四舍五入到最接近的整数）
	resultInt := new(big.Int)
	resultFloat.Int(resultInt)

	return resultInt
}
