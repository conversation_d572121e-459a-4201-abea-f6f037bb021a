package blockchain

import (
	"zfeng/module/blockchain/ethers"
	"zfeng/module/blockchain/interfaces"
	"zfeng/module/blockchain/tron"
)

type BlockchainType string

const (
	BlockchainTron     BlockchainType = "tron" //	波场
	BlockchainEthereum BlockchainType = "eth"  //	以太坊
)

// NewBlockchain 创建一个新的区块链对象
func NewBlockchain(blockchain BlockchainType) interfaces.Blockchain {
	switch blockchain {
	case BlockchainTron:
		return tron.NewTron()
	case BlockchainEthereum:
		return ethers.NewEthers()
	default:
		return nil
	}
}
