package interfaces

import "math/big"

// Transactions 交易所详情
type Transaction struct {
	Hash           string   `json:"hash"`           // 交易哈希（唯一标识）
	Contract       string   `json:"contract"`       // 合约地址
	From           string   `json:"from"`           // 发送方地址
	To             string   `json:"to"`             // 接收方地址
	Amount         *big.Int `json:"amount"`         // 交易金额
	Success        bool     `json:"success"`        // 交易是否成功
	BlockTimestamp int64    `json:"blockTimestamp"` // 交易时间戳
	BlockNumber    int64    `json:"blockNumber"`    // 区块高度
}

// AccountResource 账户资源
type AccountResource struct {
	Energy    int64 `json:"energy"`    // 能量
	Bandwidth int64 `json:"bandwidth"` // 带宽
}
