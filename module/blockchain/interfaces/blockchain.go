package interfaces

import (
	"math/big"
)

// Blockchain 区块链对象接口
type Blockchain interface {
	// SetRPCURL 设置RPC地址
	SetRPCURL(url string) Blockchain

	// SetRPCKEY 设置RPCKEY
	SetRPCKEY(key string) Blockchain

	// GenerateWalletAddress 生成钱包地址
	GenerateWalletAddress() (string, string, error)

	// GetBalance 获取钱包余额
	GetBalance(addr string) (*big.Int, error)

	// GetTrc20Balance 获取钱包Trc20余额
	GetTrc20Balance(addr string, contractAddrList ...string) (map[string]*big.Int, error)

	// Transfer 钱包转账
	Transfer(fromAddr string, toAddr string, amount *big.Int, fromAddrPrivateKeyHex string) (string, error)

	// BeforeTransfer 转账前执行
	BeforeTransfer(fun func(fromAddr, toAddr, contractAddr string) error) Blockchain

	// TransferTrc20 钱包Trc20 转账
	TransferTrc20(fromAddr string, toAddr string, amount *big.Int, fromAddrPrivateKeyHex string, contractAddr string) (string, error)

	// GetTransactionsTrc20 获取地址交易记录Trc20
	GetTransactionsTrc20(contractAddr string, params map[string]interface{}) ([]*Transaction, string, error)

	// MonitorTransactionsTrc20 监控地址交易记录Trc20
	MonitorTransactionsTrc20(contractAddr string, cronSpec string, fun func(transaction *Transaction)) error

	// GetTransactionByHash 获取交易详情
	GetTransactionByHash(txHash string) (*Transaction, error)

	// GetGasPrice 获取当前gas价格
	GetGasPrice() (*big.Int, error)

	// GetAccountResource 获取账户资源
	GetAccountResource(addr string) (*AccountResource, error)

	// GetBlockNumber 获取当前区块高度
	GetBlockNumber() (*big.Int, error)
}
