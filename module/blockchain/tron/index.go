package tron

import (
	"bytes"
	"crypto/ecdsa"
	"crypto/elliptic"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"math/big"
	"net/url"
	"slices"
	"strings"
	"zfeng/module/blockchain/interfaces"
	"zfeng/utils"

	"github.com/btcsuite/btcd/btcutil/base58"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"

	"golang.org/x/crypto/sha3"
)

const (
	VersionPrefix = "41" // 版本前缀

	// 转账一笔能量数量
	EnergyNum int64 = 65000

	// BalanceDecimal 余额精度
	BalanceDecimal = 6 // 余额精度
	// Trc20USDTDecimal Trc20 精度
	Trc20USDTDecimal = 6

	// ErrAccountNotFound 账户未激活错误
	ErrAccountNotFound = "account not activated"
)

// Tron 波场区块链对象
type Tron struct {
	RPCURL string
	RPCKEY string

	// 转账之前执行
	beforeTransfer func(fromAddr, toAddr, contractAddr string) error
}

// NewTron 创建波场区块链对象
func NewTron() *Tron {
	return &Tron{
		RPCURL: "https://api.trongrid.io",
		RPCKEY: "7ed35e2d-5607-4ca3-b132-27c373a514c6",

		beforeTransfer: func(fromAddr, toAddr, contractAddr string) error {
			return nil
		},
	}
}

// Transfer 转账
func (t *Tron) Transfer(fromAddr string, toAddr string, amount *big.Int, fromAddrPrivateKeyHex string) (string, error) {
	// 交易前执行
	err := t.beforeTransfer(fromAddr, toAddr, "")
	if err != nil {
		return "", err
	}

	// 创建原始交易
	createResp := struct {
		TxID       string                 `json:"txID"`
		RawDataHex string                 `json:"raw_data_hex"`
		RawData    map[string]interface{} `json:"raw_data"`
		Error      string                 `json:"Error"`
	}{}
	bodyParamsBytes, _ := json.Marshal(map[string]interface{}{
		"owner_address": fromAddr,
		"to_address":    toAddr,
		"amount":        amount.Int64(),
		"visible":       true,
	})
	err = utils.NewHttpClient(t.RPCURL).
		SetHeader("TRON-PRO-API-KEY", t.RPCKEY).
		SetHeader("Content-Type", "application/json").
		Post("/wallet/createtransaction", bodyParamsBytes).
		ToStruct(&createResp)
	if err != nil || createResp.Error != "" {
		if err != nil {
			createResp.Error = err.Error()
		}
		return "", fmt.Errorf("CreateTransactionFailed: %s", createResp.Error)
	}

	// dataHex 签名
	signature, err := t.signature(fromAddrPrivateKeyHex, createResp.RawDataHex)
	if err != nil {
		return "", err
	}

	// 广播交易交易
	return t.broadcasttransaction(createResp.TxID, createResp.RawData, signature)
}

// BeforeTransfer 转账前执行
func (t *Tron) BeforeTransfer(fun func(fromAddr, toAddr, contractAddr string) error) interfaces.Blockchain {
	t.beforeTransfer = fun
	return t
}

// TransferTrc20 Trc20 转账
func (t *Tron) TransferTrc20(fromAddr string, toAddr string, amount *big.Int, fromAddrPrivateKeyHex string, contractAddr string) (string, error) {
	// 交易前执行
	err := t.beforeTransfer(fromAddr, toAddr, contractAddr)
	if err != nil {
		return "", err
	}

	createResp := struct {
		Result struct {
			Result  bool   `json:"result"`
			Code    string `json:"code"`
			Message string `json:"message"`
		} `json:"result"`
		Transaction struct {
			Visible    bool                   `json:"visible"`
			TxID       string                 `json:"txID"`
			RawDataHex string                 `json:"raw_data_hex"`
			RawData    map[string]interface{} `json:"raw_data"`
		} `json:"transaction"`
	}{}

	// 调用合约
	toAddrHex, _ := Base58CheckDecode(toAddr, 64)
	bodyParamsBytes, _ := json.Marshal(map[string]interface{}{
		"owner_address":     fromAddr,
		"contract_address":  contractAddr,
		"function_selector": "transfer(address,uint256)",
		"parameter":         fmt.Sprintf("%v%064x", toAddrHex, amount.Int64()),
		"fee_limit":         100_000_000,
		"visible":           true,
	})
	err = utils.NewHttpClient(t.RPCURL).
		SetHeader("TRON-PRO-API-KEY", t.RPCKEY).
		SetHeader("Content-Type", "application/json").
		Post("/wallet/triggersmartcontract", bodyParamsBytes).
		ToStruct(&createResp)
	if err != nil || createResp.Result.Message != "" {
		if err != nil {
			createResp.Result.Message = err.Error()
		}
		return "", fmt.Errorf("CreateTransactionFialed: %v", createResp.Result.Message)
	}

	// 交易签名
	signature, err := t.signature(fromAddrPrivateKeyHex, createResp.Transaction.RawDataHex)
	if err != nil {
		return "", err
	}

	// 广播交易
	return t.broadcasttransaction(createResp.Transaction.TxID, createResp.Transaction.RawData, signature)
}

// broadcasttransaction 广播
func (t *Tron) broadcasttransaction(txID string, rawData map[string]interface{}, signature []byte) (string, error) {
	broadcastResp := struct {
		Result  bool   `json:"result"`
		Txid    string `json:"txid"`
		Code    string `json:"code"`
		Message string `json:"message"`
	}{}
	broadcastParams, _ := json.Marshal(map[string]interface{}{
		"txID":      txID,
		"visible":   true,
		"raw_data":  rawData,
		"signature": []string{hex.EncodeToString(signature)},
	})

	err := utils.NewHttpClient(t.RPCURL).
		SetHeader("TRON-PRO-API-KEY", t.RPCKEY).
		SetHeader("Content-Type", "application/json").
		Post("/wallet/broadcasttransaction", broadcastParams).ToStruct(&broadcastResp)
	if err != nil || !broadcastResp.Result {
		if err != nil {
			broadcastResp.Message = err.Error()
		}
		return "", fmt.Errorf("BroadcastFailed: %s", broadcastResp.Message)
	}
	return broadcastResp.Txid, nil
}

// signature 签名
func (t *Tron) signature(privateKeyHex string, rawDataHex string) ([]byte, error) {
	// 私钥转 私钥Key
	privateKey, err := crypto.HexToECDSA(privateKeyHex)
	if err != nil {
		return nil, err
	}

	// 交易签名
	txData, _ := hex.DecodeString(rawDataHex)
	hash := sha256.Sum256(txData)
	return crypto.Sign(hash[:], privateKey)
}

// GetBalance 获取钱包余额
func (t *Tron) GetBalance(addr string) (*big.Int, error) {
	accounts, err := t.GetAccounts(addr)
	if err != nil {
		return nil, err
	}
	if len(accounts.Data) == 0 {
		return new(big.Int).SetInt64(0), errors.New(ErrAccountNotFound)
	}
	return new(big.Int).SetInt64(accounts.Data[0].Balance), nil
}

// GetTrc20Balance 通过钱包地址获取钱包的TRC20余额
func (t *Tron) GetTrc20Balance(addr string, contractAddrList ...string) (map[string]*big.Int, error) {
	accounts, err := t.GetAccounts(addr)
	if err != nil {
		return nil, err
	}

	trc20List := map[string]*big.Int{}
	for _, v := range contractAddrList {
		trc20List[v] = new(big.Int).SetInt64(0)
	}
	for _, account := range accounts.Data {
		for _, token := range account.Trc20 {
			for tokenAddr, tokenBalance := range token {
				if slices.Contains(contractAddrList, tokenAddr) {
					trc20List[tokenAddr], _ = new(big.Int).SetString(tokenBalance, 10)
				}
			}
		}
	}
	return trc20List, nil
}

// GetTransactionsTrc20 通过钱包地址获取钱包的交易记
func (t *Tron) GetTransactionsTrc20(contractAddr string, params map[string]interface{}) ([]*interfaces.Transaction, string, error) {
	queryUrl := url.Values{}
	for k, v := range params {
		queryUrl.Add(k, fmt.Sprintf("%v", v))
	}

	transactions := &ContractsEvents{}
	err := utils.NewHttpClient(t.RPCURL).
		SetHeader("TRON-PRO-API-KEY", t.RPCKEY).
		SetHeader("Content-Type", "application/json").
		Get(fmt.Sprintf("/v1/contracts/%s/events?", contractAddr) + queryUrl.Encode()).
		ToStruct(transactions)
	if err != nil {
		return nil, "", err
	}

	transactionList := make([]*interfaces.Transaction, 0)
	for _, event := range transactions.Data {
		amount, _ := new(big.Int).SetString(event.Result.Value, 10)
		fromAddr, _ := hex.DecodeString(strings.TrimPrefix(event.Result.From, "0x"))
		toAddr, _ := hex.DecodeString(strings.TrimPrefix(event.Result.To, "0x"))
		transactionList = append(transactionList, &interfaces.Transaction{
			Hash:           event.TransactionId,
			Contract:       event.ContractAddress,
			From:           Base58CheckEncode(fromAddr),
			To:             Base58CheckEncode(toAddr),
			Amount:         amount,
			Success:        !event.Unconfirmed,
			BlockTimestamp: event.BlockTimestamp,
			BlockNumber:    event.BlockNumber,
		})
	}
	return transactionList, transactions.Meta.FingerPrint, nil
}

// MonitorTransactionsTrc20 监控Trc20交易
func (t *Tron) MonitorTransactionsTrc20(contractAddr string, cronSpec string, fun func(transaction *interfaces.Transaction)) error {
	c := cron.New(
		cron.WithChain(
			// 添加串行执行锁
			cron.DelayIfStillRunning(
				cron.DefaultLogger,
			),
		),
	)
	defer c.Start()

	// 获取最新的区块
	transactions, _, err := t.GetTransactionsTrc20(contractAddr, map[string]interface{}{})
	if err != nil {
		return err
	}

	// 最新的区块高度
	if len(transactions) == 0 {
		return errors.New("no transactions found")
	}
	blockNumber := transactions[0].BlockNumber + 1

	// 每5秒执行一次
	c.AddFunc(cronSpec, func() {
		for {
			transactions, _, err := t.GetTransactionsTrc20(contractAddr, map[string]interface{}{
				"limit": 200, "block_number": blockNumber,
			})
			if err != nil {
				zap.L().Error("Tron GetTransactionsTrc20 Failed", zap.Error(err))
				break
			}

			// 没有数据处理
			if len(transactions) == 0 {
				break
			}

			// 处理数据
			for _, transaction := range transactions {
				fun(transaction)
			}

			blockNumber = blockNumber + 1
			zap.L().Info("Tron MonitorTransactionsTrc20 Success", zap.Int64("区块高度", blockNumber))
		}
	})

	return nil
}

// GetTransactionByHash 通过交易哈希获取交易详情
func (t *Tron) GetTransactionByHash(txHash string) (*interfaces.Transaction, error) {
	transactions := &ContractsEvents{}
	err := utils.NewHttpClient(t.RPCURL).
		SetHeader("TRON-PRO-API-KEY", t.RPCKEY).
		SetHeader("Content-Type", "application/json").
		Get(fmt.Sprintf("/v1/transactions/%s/events?", txHash)).
		ToStruct(transactions)
	if err != nil {
		return nil, err
	}

	data := &interfaces.Transaction{}
	if len(transactions.Data) > 0 {
		event := transactions.Data[0]
		amount, _ := new(big.Int).SetString(event.Result.Value, 10)
		fromAddr, _ := hex.DecodeString(strings.TrimPrefix(event.Result.From, "0x"))
		toAddr, _ := hex.DecodeString(strings.TrimPrefix(event.Result.To, "0x"))
		data = &interfaces.Transaction{
			Hash:           event.TransactionId,
			Contract:       event.ContractAddress,
			From:           Base58CheckEncode(fromAddr),
			To:             Base58CheckEncode(toAddr),
			Amount:         amount,
			Success:        !event.Unconfirmed,
			BlockTimestamp: event.BlockTimestamp,
			BlockNumber:    event.BlockNumber,
		}
	}

	return data, nil
}

// GetAccounts 获取账户信息
func (t *Tron) GetAccounts(addr string) (*Accounts, error) {
	accounts := &Accounts{}
	err := utils.NewHttpClient(t.RPCURL).
		SetHeader("TRON-PRO-API-KEY", t.RPCKEY).
		SetHeader("Content-Type", "application/json").
		Get(fmt.Sprintf("/v1/accounts/%s", addr)).
		ToStruct(accounts)
	if err != nil {
		return nil, err
	}

	return accounts, nil
}

// GenerateWalletAddress 生成钱包地址
func (t *Tron) GenerateWalletAddress() (string, string, error) {
	// 使用 secp256k1
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		return "", "", fmt.Errorf("GenerateKey Failed: %w", err)
	}

	// 转换私钥为HEX格式
	privateKeyHex := hex.EncodeToString(crypto.FromECDSA(privateKey))

	// 获取公钥并去掉第一个字节
	publicKeyBytes := crypto.FromECDSAPub(&privateKey.PublicKey)[1:]

	// 使用 keccak256 哈希
	hash := sha3.NewLegacyKeccak256()
	hash.Write(publicKeyBytes)
	addrBytes := hash.Sum(nil)[12:]

	// 生成地址
	address := Base58CheckEncode(addrBytes)
	return address, privateKeyHex, nil
}

// GetGasPrice 获取GasPrice
func (t *Tron) GetGasPrice() (*big.Int, error) {
	return nil, nil
}

// GetAccountResource 获取账户资源
func (t *Tron) GetAccountResource(addr string) (*interfaces.AccountResource, error) {
	resps := struct {
		FreeNetUsed  int64 `json:"freeNetUsed"`
		FreeNetLimit int64 `json:"freeNetLimit"`
		EnergyUsed   int64
		EnergyLimit  int64
	}{}
	bodyParamsBytes, _ := json.Marshal(map[string]interface{}{
		"address": addr, "visible": true,
	})
	err := utils.NewHttpClient(t.RPCURL).
		SetHeader("TRON-PRO-API-KEY", t.RPCKEY).
		SetHeader("Content-Type", "application/json").
		Post("/wallet/getaccountresource", bodyParamsBytes).
		ToStruct(&resps)
	if err != nil {
		return nil, err
	}

	return &interfaces.AccountResource{
		Energy:    resps.EnergyLimit - resps.EnergyUsed,
		Bandwidth: resps.FreeNetLimit - resps.FreeNetUsed,
	}, nil
}

// GetBlockNumber 获取当前区块高度
func (t *Tron) GetBlockNumber() (*big.Int, error) {
	resps := struct {
		BlockId     string `json:"blockID"`
		BlockHeader struct {
			RawData struct {
				Number         int64  `json:"number"`
				TxTrieRoot     string `json:"txTrieRoot"`
				ParentHash     string `json:"parentHash"`
				WitnessAddress string `json:"witnessAddress"`
				Version        int64  `json:"version"`
				Timestamp      int64  `json:"timestamp"`
			} `json:"raw_data"`
			WitnessSignature string `json:"witnessSignature"`
		} `json:"block_header"`
	}{}

	err := utils.NewHttpClient(t.RPCURL).
		SetHeader("TRON-PRO-API-KEY", t.RPCKEY).
		SetHeader("Content-Type", "application/json").
		Get("/walletsolidity/getblock").
		ToStruct(&resps)
	if err != nil {
		return nil, err
	}

	return big.NewInt(resps.BlockHeader.RawData.Number), nil
}

// SetRPCURL 设置RPC地址
func (t *Tron) SetRPCURL(url string) interfaces.Blockchain {
	t.RPCURL = url
	return t
}

// SetRPCKEY 设置RPCKEY
func (t *Tron) SetRPCKEY(key string) interfaces.Blockchain {
	t.RPCKEY = key
	return t
}

// Base58CheckEncode 波场地址转换函数
func Base58CheckEncode(payload []byte) string {
	return base58.CheckEncode(payload, 0x41)
}

// Base58CheckDecode 波场地址解码函数
func Base58CheckDecode(addr string, desiredLength int) (string, error) {
	decoded, version, err := base58.CheckDecode(addr)
	if err != nil || version != 0x41 {
		return "", fmt.Errorf("invalid TRON address")
	}

	addrHex := hex.EncodeToString(append([]byte{0x41}, decoded...))
	if len(addrHex) < desiredLength {
		zeroPadding := desiredLength - len(addrHex)
		addrHex = strings.Repeat("0", zeroPadding) + addrHex
	}

	return addrHex, nil
}

// ToECDSA 辅助函数
func ToECDSA(privateKey []byte) (*ecdsa.PrivateKey, error) {
	return ecdsa.GenerateKey(elliptic.P256(), bytes.NewReader(privateKey))
}
