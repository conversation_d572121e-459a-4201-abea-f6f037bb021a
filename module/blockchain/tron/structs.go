package tron

// Accounts 账号信息
type Accounts struct {
	Data    []*AccountData `json:"data"`    //	账户信息
	Success bool           `json:"success"` //	请求是否成功
	Meta    struct {
		At          int    `json:"at"`          //	响应时间戳
		PageSize    int    `json:"page_size"`   //	分页大小
		FingerPrint string `json:"fingerprint"` //	指纹
		Link        struct {
			Next string `json:"next"` //	下一页URL
		} `json:"link"`
	}
}

// AccountData 账户信息
type AccountData struct {
	OwnerPermission struct {
		Keys []struct {
			Address string `json:"address"` //	授权地址
			Weight  int    `json:"weight"`  //	密钥权重
		} `json:"keys"` //	授权密钥列表
		Threshold     int    `json:"threshold"`       //	执行交易所需阈值权重
		PermssionName string `json:"permission_name"` //	权限名称
	} `json:"owner_permission"` //	账户所有权配置
	AccountResource struct {
		EnergyWindowOptimized      bool `json:"energy_window_optimized"`        //	是否优化能量窗口
		LatestConsumeTimeForEnergy int  `json:"latest_consume_time_for_energy"` //	最近能量消耗时间（毫秒时间戳）
		EnergyWindowSize           int  `json:"energy_window_size"`             //	能量计算窗口时长（8小时）
	} `json:"account_resource"` //	账户资源情况
	ActivePermission []struct {
		Operations string `json:"operations"` //	允许的操作类型（16进制编码）
		Keys       []struct {
			Address string `json:"address"` //	授权地址
			Weight  int    `json:"weight"`  //	密钥权重
		}
		Threshold     int    `json:"threshold"`       //	执行交易所需阈值权重
		ID            int    `json:"id"`              //	权限ID
		Type          string `json:"type"`            //	权限类型
		PermssionName string `json:"permission_name"` //	权限名称
	} `json:"active_permission"` //	活跃权限配置（多签设置）
	Address             string `json:"address"`              //	账户base58地址（原始字节）
	CreateTime          int    `json:"create_time"`          //	账户创建时间（对应2024-11-05 03:07:03 UTC）
	LatestOprationTime  int    `json:"latest_opration_time"` //	最近操作时间（对应2024-11-05 03:07:03 UTC）
	FreeAssetNetUsageV2 []struct {
		Value int    `json:"value"` //	免费带宽额度
		Key   string `json:"key"`   //	资产ID（TRC10）
	} `json:"free_asset_net_usage_v2"` //	各资产免费带宽额度
	FreeNetUsage int `json:"free_net_usage"` //	免费带宽额度
	AssetV2      []struct {
		Value int    `json:"value"` //	资产数量（实际值需除以10^6）
		Key   string `json:"key"`   //	资产ID（USDT-TRC10为TR7NHqje...）
	} `json:"asset_v2"` //	TRC10资产列表
	FrozenV2 []map[string]string `json:"frozen_v2"` //	冻结资产（第一项为TRX）
	Balance  int64               `json:"balance"`   //	TRX余额（单位：SUN，1TRX=1,000,000 SUN）
	Trc20    []map[string]string `json:"trc20"`     // TRC20代币余额
}

// ContractsEvents 合约事件
type ContractsEvents struct {
	Data    []*ContractEventData `json:"data"`    //	账户信息
	Success bool                 `json:"success"` //	请求是否成功
	Meta    struct {
		At          int    `json:"at"`          //	响应时间戳
		PageSize    int    `json:"page_size"`   //	分页大小
		FingerPrint string `json:"fingerprint"` //	指纹
		Link        struct {
			Next string `json:"next"` //	下一页URL
		} `json:"link"`
	}
}

// ContractEventData 合约事件数据
type ContractEventData struct {
	BlockNumber           int64  `json:"block_number"`            //	区块高度
	BlockTimestamp        int64  `json:"block_timestamp"`         //	区块时间戳（毫秒时间戳）
	CallerContractAddress string `json:"caller_contract_address"` //	调用合约地址
	ContractAddress       string `json:"contract_address"`        //	合约地址
	EventIndex            int64  `json:"event_index"`             //	事件索引
	EventName             string `json:"event_name"`              //	事件名称
	Result                struct {
		From  string `json:"from"`  //	发送者地址
		To    string `json:"to"`    //	接收者地址
		Value string `json:"value"` //	转账金额（单位：SUN）
	} `json:"result"` //	事件结果
	ResultType struct {
		From  string `json:"from"`  //	发送者地址类型
		To    string `json:"to"`    //	接收者地址类型
		Value string `json:"value"` //	转账金额类型
	}
	Event         string `json:"event"`          //	事件类型
	TransactionId string `json:"transaction_id"` //	交易ID
	Unconfirmed   bool   `json:"_unconfirmed"`   //	是否为未确认交易
}
