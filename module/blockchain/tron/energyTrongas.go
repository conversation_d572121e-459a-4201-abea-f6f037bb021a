package tron

import (
	"errors"
	"zfeng/utils"
)

type EneryResps struct {
	Code int64  `json:"code"` //	请求成功状态吗 10000
	Msg  string `json:"msg"`
	Data struct {
		OrderId         string   `json:"orderId"`
		Balance         int64    `json:"balance"`
		OrderMoney      float64  `json:"orderMoney"`
		Hash            []string `json:"hash"`
		SendAddressList []string `json:"sendaddressList"`
	} `json:"data"`
}

// RentingTrongasEnergy 购买Trongas 能量
func (t *Tron) RentingTrongasEnergy(addr string, num int64, appKey string) error {
	bodyParamsBytes := utils.StructToBytes(map[string]interface{}{
		"apiKey":         appKey,          //	apiKey
		"resType":        "ENERGY",        //	资源类型 ENERGY 能量 ｜ BANDWIDTH 带宽
		"payNums":        num * EnergyNum, //	资源数量
		"rentTime":       1,               // 单位小时1
		"resLock":        0,               //	租用锁定 0 不锁定 1锁定
		"receiveAddress": addr,            //	接收资源地址
		"orderType":      "",
		"orderNotes":     "",
	})

	resps := &EneryResps{}
	err := utils.NewHttpClient("https://trongas.io").
		SetHeader("Content-Type", "application/json").
		Post("/api/pay", bodyParamsBytes).ToStruct(&resps)
	if err != nil {
		return err
	}

	if resps.Code != 10000 {
		return errors.New(resps.Msg)
	}

	return nil
}
