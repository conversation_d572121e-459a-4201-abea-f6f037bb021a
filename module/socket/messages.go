package socket

import (
	"zfeng/core/cache"

	"github.com/gomodule/redigo/redis"
)

// RedisPublish 发布消息 - 给某一个 uuid 发送消息
func (_Socket *Socket) RedisPublish(connType int8, op string, receiver string, data interface{}) {
	_ = cache.RdsSubscribe.Publish(_Socket.key, &ConsumerMessage{
		Type: connType,
		Op:   op,
		UUID: receiver,
		Data: data,
	})
}

// RedisUserPublish 发布消息 给某个用户发送消息
func (_Socket *Socket) RedisUserPublish(rdsConn redis.Conn, connType int8, op string, userId uint, data interface{}) {
	connInfoList := _Socket.ConnMaps.RedisGetConnInfo(rdsConn, connType, userId)

	for _, connInfo := range connInfoList {
		_Socket.RedisPublish(connType, op, connInfo.UUID, data)
	}
}

// ConnWriteJson 因为多线程的不允许直接调用当前方法
func (_Socket *Socket) ConnWriteJson(connType int8, uuidStr string, data interface{}) error {
	connInfo := _Socket.ConnMaps.GetConn(connType, uuidStr)
	_Socket.sync.Lock()
	defer _Socket.sync.Unlock()

	if connInfo != nil && connInfo.Conn != nil {
		return connInfo.Conn.WriteJSON(data)
	}
	return nil
}

// ConnWriteMessage 连接对象写入消息
func (_Socket *Socket) ConnWriteMessage(connType int8, uuidStr string, messageType int, data []byte) error {
	connInfo := _Socket.ConnMaps.GetConn(connType, uuidStr)

	_Socket.sync.Lock()
	defer _Socket.sync.Unlock()

	if connInfo != nil && connInfo.Conn != nil {
		return connInfo.Conn.WriteMessage(messageType, data)
	}
	return nil
}
