package socket

import (
	"fmt"

	"github.com/goccy/go-json"
	"github.com/gomodule/redigo/redis"
)

const (
	RedisUserConnInfoList = "RedisUserConnInfoList"
)

// UserConnInfo 连接的用户信息
type UserConnInfo struct {
	UUID       string `json:"uuid"`       //	UUID
	Key        string `json:"key"`        //	socket 标识
	UserId     uint   `json:"userId"`     //	用户ID
	AdminId    uint   `json:"adminId"`    // 	管理ID
	MerchantId uint   `json:"merchantId"` // 	商户ID
	Type       string `json:"type"`       //	类型
	Device     string `json:"device"`     //	设备信息
	Origin     string `json:"origin"`     //	源域名
	IP         string `json:"ip"`         //	IP信息
}

// RedisSetConnInfo 设置｜更新 用户连接信息
func (_ConnMaps *ConnMaps) RedisSetConnInfo(rdsConn redis.Conn, connType int8, userId uint, currentInfo *UserConnInfo) {
	_ConnMaps.Lock()
	defer _ConnMaps.Unlock()

	userConnInfoList := _ConnMaps.RedisGetConnInfo(rdsConn, connType, userId)

	connIndexOf := -1
	for connIndex, connInfo := range userConnInfoList {
		if connInfo.UUID == currentInfo.UUID {
			connIndexOf = connIndex
		}
	}
	if connIndexOf == -1 {
		userConnInfoList = append(userConnInfoList, currentInfo)
	} else {
		userConnInfoList[connIndexOf] = currentInfo
	}

	connInfoListBytes, _ := json.Marshal(userConnInfoList)
	_, _ = rdsConn.Do("HSET", RedisUserConnInfoList+_ConnMaps.key, fmt.Sprintf("%d_%d", connType, userId), connInfoListBytes)
}

// RedisGetConnInfo 获取用户连接信息
func (_ConnMaps *ConnMaps) RedisGetConnInfo(rdsConn redis.Conn, connType int8, userId uint) []*UserConnInfo {
	userConnInfoList := make([]*UserConnInfo, 0)
	userConnInfoListBytes, err := redis.Bytes(rdsConn.Do("HGET", RedisUserConnInfoList+_ConnMaps.key, fmt.Sprintf("%d_%d", connType, userId)))
	if err == nil {
		_ = json.Unmarshal(userConnInfoListBytes, &userConnInfoList)
	}
	return userConnInfoList
}

// RedisDelUUIDConnInfo 删除用户uuid 连接数据
func (_ConnMaps *ConnMaps) RedisDelUUIDConnInfo(rdsConn redis.Conn, connType int8, userId uint, uuidStr string) {
	userConnInfoList := _ConnMaps.RedisGetConnInfo(rdsConn, connType, userId)

	// 如果只有一条数据, 那么直接删除
	if len(userConnInfoList) == 1 {
		_ConnMaps.RedisDelConnInfo(rdsConn, connType, userId)
		return
	}

	// 如果多个连接, 判断所有链接是否存在
	newConnInfoList := make([]*UserConnInfo, 0)
	for _, v := range userConnInfoList {
		// 判断 uuid 连接是否有效
		connInfo := _ConnMaps.GetConn(connType, v.UUID)
		if connInfo != nil && v.UUID != uuidStr {
			newConnInfoList = append(newConnInfoList, v)
		}
	}
	connInfoBytes, _ := json.Marshal(newConnInfoList)

	_, _ = rdsConn.Do("HSET", RedisUserConnInfoList+_ConnMaps.key, fmt.Sprintf("%d_%d", connType, userId), connInfoBytes)
}

// RedisDelConnInfo 删除用户连接信息
func (_ConnMaps *ConnMaps) RedisDelConnInfo(rdsConn redis.Conn, connType int8, userId uint) {
	_, _ = rdsConn.Do("HDEL", RedisUserConnInfoList+_ConnMaps.key, fmt.Sprintf("%d_%d", connType, userId))
}
