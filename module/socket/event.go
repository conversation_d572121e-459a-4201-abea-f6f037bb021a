package socket

import "fmt"

// OnWebSocketMessageFunc 消息事件
func OnWebSocketMessageFunc(socketInstance *Socket, connType int8, uuidStr string, msg []byte) error {
	fmt.Println("websocket 消息事件未绑定 ===> ", string(msg))
	return nil
}

// OnWebSocketOpenFunc 打开事件方法
func OnWebSocketOpenFunc(socketInstance *Socket, connType int8, uuidStr string) {
	fmt.Println("websocket 打开事件未绑定 ===> ", uuidStr)
}

// OnWebSocketCloseFunc 关闭事件方法
func OnWebSocketCloseFunc(socketInstance *Socket, connInfo *ConnInfo) {
	fmt.Println("websocket 关闭事件未绑定 ===> ", connInfo.UserID)
}
