package handler

import (
	"zfeng/module/socket"

	"github.com/goccy/go-json"
	"github.com/gofiber/contrib/websocket"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// NewSocketConn 创建socket 连接
func NewSocketConn(connType int8, socketInterface *socket.Socket) fiber.Handler {
	return websocket.New(func(conn *websocket.Conn) {
		// 设置当前连接对象
		uuidStr := uuid.New().String()

		// 设置连接
		socketInterface.ConnMaps.SetConn(connType, uuidStr, conn)

		// 返回UUID
		messageBytes, _ := json.Marshal(&socket.ClientMessage{
			Op:   socket.MessageOperateInit,
			Data: uuidStr,
		})
		_ = conn.WriteMessage(websocket.TextMessage, messageBytes)

		// 心跳包设置
		go sendHeartbeat(socketInterface, connType, uuidStr)
		defer func() {
			connInfo := socketInterface.ConnMaps.GetConn(connType, uuidStr)
			socketInterface.CloseConn(connType, uuidStr)
			socketInterface.EventClose(socketInterface, connInfo)
		}()

		var (
			msg []byte
			err error
		)

		// 处理业务
		for {
			// 读取消息
			if _, msg, err = conn.ReadMessage(); err != nil {
				break
			}

			// 消息事件
			err = socketInterface.EventMessage(socketInterface, connType, uuidStr, msg)
			if err != nil {
				_ = socketInterface.ConnWriteJson(connType, uuidStr, &socket.ClientMessage{
					Op:   socket.MessageOperateErr,
					Data: err.Error(),
				})
			}
		}
	})
}
