package handler

import (
	"github.com/gofiber/contrib/websocket"
	"time"
	"zfeng/module/socket"
)

const (
	// HeartbeatTime 心跳时间
	HeartbeatTime = 30 * time.Second
)

// sendHeartbeat 心跳包处理
func sendHeartbeat(socketInterface *socket.Socket, connType int8, uuidStr string) {
	ticker := time.NewTicker(HeartbeatTime)
	defer ticker.Stop()

	for range ticker.C {
		connInfo := socketInterface.ConnMaps.GetConn(connType, uuidStr)
		if connInfo == nil {
			return
		}
		if err := socketInterface.ConnWriteMessage(connType, uuidStr, websocket.PingMessage, []byte{}); err != nil {
			socketInterface.CloseConn(connType, uuidStr)
			return
		}
	}
}
