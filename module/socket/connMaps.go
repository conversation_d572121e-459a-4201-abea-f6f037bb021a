package socket

import (
	"sync"

	"github.com/gofiber/contrib/websocket"
	"github.com/gomodule/redigo/redis"
)

const (
	ConnTypeAdmin   int8 = 1 // 管理
	ConnTypeUser    int8 = 2 // 用户
	ConnTypeProduct int8 = 3 // 产品
)

// ConnMaps 客户端Maps
type ConnMaps struct {
	sync.Mutex
	key  string // SocketKey
	maps map[string]*ConnInfo
}

type ConnInfo struct {
	Conn   *websocket.Conn // 连接对象
	Type   int8            // 绑定类型 1:管理 2:用户 3:产品
	UserId uint            // 用户ID
	AdminID uint            // 管理员ID
	UserID  uint            // 用户ID
}

// SetConn 设置客户端
func (_ConnMaps *ConnMaps) SetConn(connType int8, uuidStr string, conn *websocket.Conn) *ConnMaps {
	_ConnMaps.Lock()
	defer _ConnMaps.Unlock()

	_ConnMaps.maps[uuidStr] = &ConnInfo{Conn: conn, Type: connType}
	return _ConnMaps
}

// SetConnUserId 设置连接用户ID
func (_ConnMaps *ConnMaps) SetConnUserId(connType int8, uuidStr string, adminId uint, userId uint) *ConnMaps {
	connInfo := _ConnMaps.GetConn(connType, uuidStr)

	_ConnMaps.Lock()
	defer _ConnMaps.Unlock()
	if connInfo != nil {
		connInfo.AdminID = adminId
		connInfo.UserID = userId
	}
	return _ConnMaps
}

// GetConn 获取客户端
func (_ConnMaps *ConnMaps) GetConn(connType int8, uuidStr string) *ConnInfo {
	_ConnMaps.Lock()
	defer _ConnMaps.Unlock()

	if _, ok := _ConnMaps.maps[uuidStr]; ok {
		if _ConnMaps.maps[uuidStr].Type == connType {
			return _ConnMaps.maps[uuidStr]
		}
	}
	return nil
}

// CloseConn 关闭客户端
func (_ConnMaps *ConnMaps) CloseConn(rdsConn redis.Conn, connType int8, uuidStr string) {
	connInfo := _ConnMaps.GetConn(connType, uuidStr)
	if connInfo != nil {
		_ = connInfo.Conn.Close()
	}

	// 删除用户的缓存信息
	_ConnMaps.RedisDelUUIDConnInfo(rdsConn, connType, connInfo.UserID, uuidStr)

	// 删除 用户连接列表中的当前连接
	_ConnMaps.Lock()
	delete(_ConnMaps.maps, uuidStr)
	_ConnMaps.Unlock()
}
