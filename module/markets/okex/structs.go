package okex

import "github.com/goccy/go-json"

const (
	Platform = "okex" //	Okex

	SubscribeChannelTickers = "tickers" // 行情
	SubscribeChannelBooks   = "books"   // 深度
	SubscribeChannelTrades  = "trades"  // 成交
)

// Subscribe 订阅
type Subscribe struct {
	Op   string          `json:"op"`
	Args []SubscribeArgs `json:"args"`
}

// SubscribeArgs 订阅参数
type SubscribeArgs struct {
	InstId  string `json:"instId"`
	Channel string `json:"channel"`
}

// SubscribeResponse 订阅响应
type SubscribeResponse struct {
	Arg  *SubscribeArgs  `json:"arg"`
	Data json.RawMessage `json:"data"`
}
