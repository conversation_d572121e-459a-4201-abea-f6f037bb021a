package okex

import (
	"strconv"
	"zfeng/module/markets/interfaces"
	"zfeng/module/socket/client"
	"zfeng/utils"

	"github.com/goccy/go-json"
	"github.com/gomodule/redigo/redis"
)

/*
	REST API 		https://www.okx.com
	公开频道 		 wss://ws.okx.com:8443/ws/v5/public
*/

const (
	// 公共频道
	BaseSocketUrl = "wss://ws.okx.com:8443/ws/v5/public"

	// REST API
	BaseRestUrl = "https://www.okx.com"
)

// Okex 实现Markets接口
type Okex struct {
	BaseSocketUrl string
	BaseRestUrl   string
}

// NewOkex 创建一个新的Okex实例
func NewOkex() interfaces.Markets {
	return &Okex{
		BaseSocketUrl: BaseSocketUrl,
		BaseRestUrl:   BaseRestUrl,
	}
}

// NewWebsocket 创建一个新的Websocket实例
func (o *Okex) NewWebsocket(subscribes []*client.Subscribe, fun func(platform string, channel string, data interface{})) client.SocketClientInterface {
	return client.NewSocketClient(BaseSocketUrl).InitSubscribes(subscribes).SetWebSocketMessageFunc(func(rdsConn redis.Conn, msg []byte) error {
		subscribeData := &SubscribeResponse{}
		err := json.Unmarshal(msg, subscribeData)

		if err == nil {
			switch subscribeData.Arg.Channel {
			case SubscribeChannelTickers:
				// 最新行情
				rawData := []map[string]string{}
				err := json.Unmarshal(subscribeData.Data, &rawData)
				if err == nil && len(rawData) > 0 {
					data := &interfaces.Ticker{}
					data.Last, _ = strconv.ParseFloat(rawData[0]["last"], 64)
					data.LastSz, _ = strconv.ParseFloat(rawData[0]["lastSz"], 64)
					data.Open24h, _ = strconv.ParseFloat(rawData[0]["open24h"], 64)
					data.High24h, _ = strconv.ParseFloat(rawData[0]["high24h"], 64)
					data.Low24h, _ = strconv.ParseFloat(rawData[0]["low24h"], 64)
					data.Vol24h, _ = strconv.ParseFloat(rawData[0]["vol24h"], 64)
					data.VolCcy24h, _ = strconv.ParseFloat(rawData[0]["volCcy24h"], 64)
					data.Ts, _ = strconv.ParseInt(rawData[0]["ts"], 10, 64)
					fun(Platform, subscribeData.Arg.Channel, data)
				}
			case SubscribeChannelBooks:
				// 行情深度
			case SubscribeChannelTrades:
				// 成交量
			}
		}
		return nil
	}).Connect()
}

// GetTickers 获取所有产品行情信息
func (o *Okex) GetTickers(instType string) (*interfaces.Tickers, error) {
	params := map[string]string{
		"instType": instType,
	}

	// 返回数据
	resps := struct {
		Code string              `json:"code"`
		Msg  string              `json:"msg"`
		Data []map[string]string `json:"data"`
	}{}
	err := utils.NewHttpClient(o.BaseRestUrl).SetGetParams(params).Get("/api/v5/market/tickers").ToStruct(&resps)
	if err != nil {
		return nil, err
	}

	data := &interfaces.Tickers{Data: make([]*interfaces.Ticker, 0)}
	for _, v := range resps.Data {
		last, _ := strconv.ParseFloat(v["last"], 64)
		lastSz, _ := strconv.ParseFloat(v["lastSz"], 64)
		openPrice, _ := strconv.ParseFloat(v["open24h"], 64)
		highPrice, _ := strconv.ParseFloat(v["high24h"], 64)
		lowsPrice, _ := strconv.ParseFloat(v["low24h"], 64)
		vol, _ := strconv.ParseFloat(v["vol24h"], 64)
		volCcy, _ := strconv.ParseFloat(v["volCcy24h"], 64)
		createdAt, _ := strconv.ParseInt(v["ts"], 10, 64)
		data.Data = append(data.Data, &interfaces.Ticker{
			Symbol:    v["instId"],
			Last:      last,
			LastSz:    lastSz,
			Open24h:   openPrice,
			High24h:   highPrice,
			Low24h:    lowsPrice,
			Vol24h:    vol,
			VolCcy24h: volCcy,
			Ts:        createdAt,
		})
	}

	return data, nil
}

// GetTrades 获取产品成交数据
func (o *Okex) GetTrades(symbol string, limit int) ([]*interfaces.Trades, error) {
	params := map[string]string{
		"instId": symbol,
		"limit":  strconv.Itoa(limit),
	}

	// 返回数据
	resps := struct {
		Code string               `json:"code"`
		Msg  string               `json:"msg"`
		Data []*interfaces.Trades `json:"data"`
	}{}
	err := utils.NewHttpClient(o.BaseRestUrl).SetGetParams(params).Get("/api/v5/market/trades").ToStruct(&resps)
	if err != nil {
		return nil, err
	}

	return resps.Data, nil
}

// GetBooks 获取产品深度数据
func (o *Okex) GetBooks(symbol string, depth int) (*interfaces.Books, error) {
	params := map[string]string{
		"instId": symbol,
		"sz":     strconv.Itoa(depth),
	}

	// 返回数据
	resps := struct {
		Code string              `json:"code"`
		Msg  string              `json:"msg"`
		Data []*interfaces.Books `json:"data"`
	}{}
	err := utils.NewHttpClient(o.BaseRestUrl).SetGetParams(params).Get("/api/v5/market/books").ToStruct(&resps)
	if err != nil {
		return nil, err
	}

	return resps.Data[0], nil
}

// GetCandles 获取蜡烛图数据 limit 最大为300
func (o *Okex) GetCandles(symbol string, bar string, limit int) ([]*interfaces.Candles, error) {
	params := map[string]string{
		"instId": symbol,
		"bar":    bar,
		"limit":  strconv.Itoa(limit),
	}

	// 返回数据
	resps := struct {
		Code string     `json:"code"`
		Msg  string     `json:"msg"`
		Data [][]string `json:"data"`
	}{}
	err := utils.NewHttpClient(o.BaseRestUrl).SetGetParams(params).Get("/api/v5/market/candles").ToStruct(&resps)
	if err != nil {
		return nil, err
	}

	// 处理数据
	data := make([]*interfaces.Candles, 0)
	for _, rows := range resps.Data {
		createdAt, _ := strconv.ParseInt(rows[0], 10, 64)
		openPrice, _ := strconv.ParseFloat(rows[1], 64)
		highPrice, _ := strconv.ParseFloat(rows[2], 64)
		lowsPrice, _ := strconv.ParseFloat(rows[3], 64)
		closePrice, _ := strconv.ParseFloat(rows[4], 64)
		volume, _ := strconv.ParseFloat(rows[6], 64)
		amount, _ := strconv.ParseFloat(rows[7], 64)
		data = append(data, &interfaces.Candles{
			OpenPrice:  openPrice,
			HighPrice:  highPrice,
			LowsPrice:  lowsPrice,
			ClosePrice: closePrice,
			Volume:     volume,
			Amount:     amount,
			CreatedAt:  createdAt,
		})
	}

	return data, nil
}
