package interfaces

// Candles 蜡烛图数据
type Candles struct {
	OpenPrice  float64 `json:"openPrice"`  //开盘价格
	HighPrice  float64 `json:"highPrice"`  //最高价格
	LowsPrice  float64 `json:"lowsPrice"`  //最低价格
	ClosePrice float64 `json:"closePrice"` //收盘价格
	Volume     float64 `json:"volume"`     //交易量
	Amount     float64 `json:"amount"`     //成交额
	CreatedAt  int64   `json:"createdAt"`  //开盘时间
}

// Tickers 所有产品行情信息
type Tickers struct {
	Data []*Ticker `json:"data"`
}

// Ticker 产品行情信息
type Ticker struct {
	Symbol    string  `json:"symbol"`    // 产品标识
	Last      float64 `json:"last"`      // 最新成交价
	LastSz    float64 `json:"lastSz"`    // 最新成交的数量
	Open24h   float64 `json:"open24h"`   // 24小时开盘价
	High24h   float64 `json:"high24h"`   // 24小时最高价
	Low24h    float64 `json:"low24h"`    // 24小时最低价
	VolCcy24h float64 `json:"volCcy24h"` // 24小时成交额
	Vol24h    float64 `json:"vol24h"`    // 24小时成交量
	Ts        int64   `json:"ts"`        // 数据产生时间
}

// GetLastPrice 获取最新成交价
func (t *Tickers) GetLastPrice(symbol string) float64 {
	for _, v := range t.Data {
		if v.Symbol == symbol {
			return v.Last
		}
	}
	return 0
}

// Books 深度数据
type Books struct {
	Asks [][]string `json:"asks"` //	买深度
	Bids [][]string `json:"bids"` //	卖深度
	Ts   string     `json:"ts"`   //	时间
}

// Trades 交易深度
type Trades struct {
	InstID  string `json:"instId"`
	TradeID string `json:"tradeId"`
	Px      string `json:"px"`
	Sz      string `json:"sz"`
	Side    string `json:"side"`
	Ts      string `json:"ts"`
	Count   string `json:"count"`
}

// SubscribeData 订阅返回数据
type SubscribeData struct {
	Channel string      `json:"channel"`
	Symbol  string      `json:"symbol"`
	Data    interface{} `json:"data"`
}
