package interfaces

import "zfeng/module/socket/client"

/*
	官网 https://tradingeconomics.com/

	获取市场数据
	1. 外汇数据
	2. 期货数据
*/

// Markets 市场接口
type Markets interface {
	// NewWebsocket 创建一个新的Websocket
	NewWebsocket(subscribes []*client.Subscribe, fun func(platform string, channel string, data interface{})) client.SocketClientInterface

	// 获取蜡烛图数据
	GetCandles(symbol string, interval string, limit int) ([]*Candles, error)
	// 获取产品深度
	GetBooks(symbol string, depth int) (*Books, error)
	// 获取产品成交数据
	GetTrades(symbol string, limit int) ([]*Trades, error)
	// GetTickers 获取所有产品行情信息
	GetTickers(instType string) (*Tickers, error)
}
