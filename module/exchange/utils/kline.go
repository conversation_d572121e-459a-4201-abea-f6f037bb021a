package utils

import (
	"math"
	"time"
	"zfeng/module/exchange/interfaces"
	"zfeng/utils"

	"golang.org/x/exp/rand"
)

// GenerateKline 生成K线图
func GenerateKline(startPrice, endPrice float64, startTime, endTime time.Time) []*interfaces.KlineAttrs {
	var interval int64 = 60
	rows := int((endTime.Unix() - startTime.Unix()) / interval)
	volatility := float64(getIntegerDigits(int64((startPrice + endPrice) / 2)))
	bridgePrice := generateBrownianBridge(startPrice, endPrice, rows, volatility)
	return generateKLineData(startTime, interval, bridgePrice, rows, volatility)
}

// GetIntegerDigits 获取整数数字位
func getIntegerDigits(v int64) int {
	count := 1
	for i := 0; i < 20; i++ {
		if v/10 <= 1 {
			break
		}
		v = v / 10
		count *= 10
	}
	return count
}

// generateBrownianBridge 生成布朗桥价格路径，确保价格在 [min(start, end), max(start, end)] 范围内
func generateBrownianBridge(start, end float64, steps int, volatility float64) []float64 {
	// 确定价格范围
	minPrice := math.Min(start, end)
	maxPrice := math.Max(start, end)

	prices := make([]float64, steps+1)
	prices[0] = math.Max(math.Min(start, maxPrice), minPrice)   // 确保起始价格在范围内
	prices[steps] = math.Max(math.Min(end, maxPrice), minPrice) // 确保结束价格在范围内

	for i := 1; i < steps; i++ {
		// 时间点比例
		t := float64(i) / float64(steps)

		// 期望价格线性插值
		expected := start + t*(end-start)

		// 生成随机偏差，符合波动率
		// 这里使用标准正态分布乘以波动率和预定义的步长
		deviation := rand.NormFloat64() * utils.GenerateRandomFloat64(volatility/10, volatility, 2) * (end - start) / float64(steps)

		// 当前价格 = 期望价格 + 偏差
		prices[i] = expected + deviation

		// 确保价格在 [minPrice, maxPrice] 范围内
		prices[i] = math.Max(math.Min(prices[i], maxPrice), minPrice)
	}

	// 为确保价格连续性，可以对价格进行平滑处理
	for i := 1; i < len(prices); i++ {
		// 简单的线性插值以确保价格不会出现剧烈跳变
		prices[i] = (prices[i-1] + prices[i]) / 2

		// 再次确保价格在 [minPrice, maxPrice] 范围内
		prices[i] = math.Max(math.Min(prices[i], maxPrice), minPrice)
	}

	// 确保最后价格严格等于 end
	if end > 0 {
		prices[steps] = end
	}

	return prices
}

// generateKLineData 根据价格路径生成 K 线数据
func generateKLineData(startTime time.Time, interval int64, prices []float64, rows int, volatility float64) []*interfaces.KlineAttrs {
	var kLines []*interfaces.KlineAttrs

	for i := 0; i < rows; i++ {
		open := prices[i]
		close := prices[i+1]

		// 计算最高价和最低价
		high := math.Max(open, close) + utils.GenerateRandomFloat64(volatility/100, volatility/10, 2)
		low := math.Min(open, close) - utils.GenerateRandomFloat64(volatility/100, volatility/10, 2)
		// 确保最低价不低于0
		if low < 0 {
			low = 0
		}

		// 模拟成交量（可选）
		volume := rand.Float64()*1000 + 100 // 随机 100 到 1100

		// 添加到 K 线数据
		kLines = append(kLines, &interfaces.KlineAttrs{
			CreatedAt:  startTime.Unix(),
			OpenPrice:  round(open, 2),
			ClosePrice: round(close, 2),
			LowsPrice:  round(low, 2),
			HighPrice:  round(high, 2),
			Vol:        round(volume, 2),
		})
		startTime = startTime.Add(time.Duration(interval) * time.Second)
	}

	return kLines
}

// round 辅助函数：四舍五入
func round(val float64, precision uint) float64 {
	ratio := math.Pow(10, float64(precision))
	return math.Round(val*ratio) / ratio
}
