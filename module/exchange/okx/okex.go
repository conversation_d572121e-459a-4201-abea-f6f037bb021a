package okx

import (
	"errors"
	"github.com/goccy/go-json"
	"github.com/gomodule/redigo/redis"
	"go.uber.org/zap"
	"strconv"
	"strings"
	"time"
	"zfeng/module/exchange/interfaces"
	"zfeng/module/socket/client"
	"zfeng/utils"
)

const (
	WebSocketURL = "wss://ws.okx.com:8443/ws/v5/public"
	HttpBaseURL  = "https://www.okx.com"

	HttpCandlesURL = "/api/v5/market/candles"
	HttpBooksURL   = "/api/v5/market/books"
	HttpTradesURL  = "/api/v5/market/trades"

	RedisTickersName = "redisTickersNameOkx"
	RedisBooksName   = "redisBooksName"
	RedisTradesName  = "redisTradesName"
	RedisKlineName   = "redisKlineName"
	logMsgOkx        = "okx"
)

type Okx struct {
	messageFunc func(channel, symbol string, rdsConn redis.Conn, data interface{})
}

var Exchange = &Okx{}

// InitWebSocket 初始化websocket
func (e *Okx) InitWebSocket(symbolList []*client.Subscribe) interfaces.Exchange {
	client.NewSocketClient(WebSocketURL).InitSubscribes(symbolList).SetWebSocketMessageFunc(func(rdsConn redis.Conn, msg []byte) error {
		//	解析数据
		respJson := &SubscribeRespJson{}
		_ = json.Unmarshal(msg, &respJson)

		if respJson.Arg != nil {
			switch respJson.Arg.Channel {
			case interfaces.SubscribeChannelTickers:
				okExchangeTickers := make([]*OkxTickers, 0)
				_ = json.Unmarshal(respJson.Data, &okExchangeTickers)
				if len(okExchangeTickers) > 0 {
					last, _ := strconv.ParseFloat(okExchangeTickers[0].Last, 64)
					lastSz, _ := strconv.ParseFloat(okExchangeTickers[0].LastSz, 64)
					open24h, _ := strconv.ParseFloat(okExchangeTickers[0].Open24h, 64)
					high24h, _ := strconv.ParseFloat(okExchangeTickers[0].High24h, 64)
					low24h, _ := strconv.ParseFloat(okExchangeTickers[0].Low24h, 64)
					volCcy24h, _ := strconv.ParseFloat(okExchangeTickers[0].VolCcy24h, 64)
					vol24h, _ := strconv.ParseFloat(okExchangeTickers[0].Vol24h, 64)
					ts, _ := strconv.Atoi(okExchangeTickers[0].Ts)
					newTime := time.UnixMilli(int64(ts))
					tickers := &interfaces.Tickers{
						Symbol:    okExchangeTickers[0].InstId,
						Last:      last,
						LastSz:    lastSz,
						Open24h:   open24h,
						High24h:   high24h,
						Low24h:    low24h,
						VolCcy24h: volCcy24h,
						Vol24h:    vol24h,
						Ts:        int(newTime.Unix()),
					}
					e.setTickersData(rdsConn, tickers)
					e.messageFunc(respJson.Arg.Channel, respJson.Arg.InstID, rdsConn, tickers)
				}
			case interfaces.SubscribeChannelBooks:
				books := make([]*interfaces.Books, 0)
				_ = json.Unmarshal(respJson.Data, &books)
				if len(books) > 0 {
					e.messageFunc(respJson.Arg.Channel, respJson.Arg.InstID, rdsConn, books[0])
				}

			case interfaces.SubscribeChannelTrades:
				trades := make([]*interfaces.Trades, 0)
				_ = json.Unmarshal(respJson.Data, &trades)
				if len(trades) > 0 {
					e.messageFunc(respJson.Arg.Channel, respJson.Arg.InstID, rdsConn, trades[0])
				}
			}
		}
		return nil
	}).Connect()

	return e
}

// SetWebSocketMessageFunc 设置消息处理
func (e *Okx) SetWebSocketMessageFunc(fun func(channel, symbol string, rdsConn redis.Conn, data interface{})) interfaces.Exchange {
	e.messageFunc = fun
	return e
}

// GetTickersData 获取产品行情信息
func (e *Okx) GetTickersData(rds redis.Conn, symbol string) (interfaces.Tickers, error) {
	tmpKey := strings.Join([]string{RedisTickersName, symbol}, ":")
	data := interfaces.Tickers{}
	tickersBytes, err := redis.Bytes(rds.Do("GET", tmpKey))
	if err != nil {
		return data, err
	}
	err = json.Unmarshal(tickersBytes, &data)
	if err != nil {
		return data, err
	}
	return data, err
}

// setTickersData 设置产品行情信息
func (e *Okx) setTickersData(rds redis.Conn, tickers *interfaces.Tickers) {
	tmpKey := strings.Join([]string{RedisTickersName, tickers.Symbol}, ":")
	_, _ = rds.Do("SET", tmpKey, utils.JsonToBytes(tickers))
}

// GetBooksDta 获取深度数据
func (e *Okx) GetBooksDta(rds redis.Conn, symbol, sz string) (*interfaces.Books, error) {
	data := make([]*interfaces.Books, 0)
	key := strings.Join([]string{RedisBooksName, symbol, sz}, ":")
	tickersBytes, err := redis.Bytes(rds.Do("GET", key))
	if err == nil {
		_ = json.Unmarshal(tickersBytes, &data)
		return data[0], nil
	}
	httpClient := utils.NewClient()
	httpClient.AddParam("instId", symbol)
	httpClient.AddParam("sz", sz)
	respBytes, err := e.HttpRespJson(httpClient.Get(HttpBaseURL + HttpBooksURL))
	if err != nil {
		return nil, err
	}
	_ = json.Unmarshal(respBytes, &data)
	_, _ = rds.Do("SETEX", key, 5, respBytes)
	return data[0], nil
}

// GetTradesDta 获取交易数据
func (e *Okx) GetTradesDta(rds redis.Conn, symbol string, limit string) ([]*interfaces.Trades, error) {
	data := make([]*interfaces.Trades, 0)
	key := strings.Join([]string{RedisTradesName, symbol, limit}, ":")
	tickersBytes, err := redis.Bytes(rds.Do("GET", key))
	if err == nil {
		_ = json.Unmarshal(tickersBytes, &data)
		return data, nil
	}
	httpClient := utils.NewClient()
	httpClient.AddParam("instId", symbol)
	httpClient.AddParam("limit", limit)
	respBytes, err := e.HttpRespJson(httpClient.Get(HttpBaseURL + HttpTradesURL))
	if err != nil {
		return nil, err
	}
	_, _ = rds.Do("SETEX", key, respBytes)
	_ = json.Unmarshal(respBytes, &data)
	return data, nil
}

// GetKlineData 获取K线图数据
func (e *Okx) GetKlineData(rds redis.Conn, symbol, bar string, afterTime, before int64, nums int64, cacheTime int) ([]*interfaces.KlineAttrs, error) {
	key := strings.Join([]string{RedisKlineName, symbol, bar, strconv.Itoa(int(afterTime)), strconv.Itoa(int(before)), strconv.Itoa(int(nums)), strconv.Itoa(cacheTime)}, ":")
	klineAttrs := e.getKline(rds, key)
	if len(klineAttrs) == 0 {
		httpClient := utils.NewClient()
		httpClient.AddParam("instId", symbol)
		httpClient.AddParam("bar", bar)
		httpClient.AddParam("limit", strconv.FormatInt(nums, 10))
		httpClient.AddParam("after", strconv.FormatInt(afterTime, 10))
		httpClient.AddParam("before", strconv.FormatInt(before, 10))
		klineBytes, err := e.HttpRespJson(httpClient.Get(HttpBaseURL + HttpCandlesURL))
		if err != nil {
			zap.L().Error(logMsgOkx, zap.Error(err), zap.String("instId", symbol), zap.String("bar", bar), zap.String("data", string(klineBytes)))
			return klineAttrs, err
		}
		klineAttrs = e.klineConvert(klineBytes)
		e.setKline(rds, key, cacheTime, klineAttrs)
	}

	return klineAttrs, nil
}

// setKline 设置kline 数据
func (e *Okx) setKline(rds redis.Conn, key string, cacheTime int, klineAttrs []*interfaces.KlineAttrs) {
	if len(klineAttrs) == 0 {
		return
	}
	if cacheTime <= 0 {
		cacheTime = 10
	}
	_, _ = rds.Do("SETEX", key, cacheTime, utils.JsonToBytes(klineAttrs))
}

// getKline 获取kline 数据
func (e *Okx) getKline(rds redis.Conn, key string) []*interfaces.KlineAttrs {
	klineAttrs := make([]*interfaces.KlineAttrs, 0)
	klineBytes, _ := redis.Bytes(rds.Do("GET", key))
	_ = json.Unmarshal(klineBytes, &klineAttrs)
	return klineAttrs
}

// klineConvert kline 数据转化
func (e *Okx) klineConvert(bytes []byte) []*interfaces.KlineAttrs {
	dataMaps := make([][]string, 0)
	_ = json.Unmarshal(bytes, &dataMaps)
	klineAttrs := make([]*interfaces.KlineAttrs, 0)
	for _, datum := range dataMaps {
		openPrice, _ := strconv.ParseFloat(datum[1], 64)
		highPrice, _ := strconv.ParseFloat(datum[2], 64)
		lowsPrice, _ := strconv.ParseFloat(datum[3], 64)
		closePrice, _ := strconv.ParseFloat(datum[4], 64)
		vol, _ := strconv.ParseFloat(datum[5], 64)
		amount, _ := strconv.ParseFloat(datum[6], 64)
		ts, _ := strconv.ParseInt(datum[0], 10, 64)

		klineAttrs = append(klineAttrs, &interfaces.KlineAttrs{
			OpenPrice:  openPrice,
			HighPrice:  highPrice,
			LowsPrice:  lowsPrice,
			ClosePrice: closePrice,
			Vol:        vol,
			Amount:     amount,
			CreatedAt:  ts / 1000,
		})
	}
	return klineAttrs
}

// HttpRespJson 错误解析
func (e *Okx) HttpRespJson(respBytes []byte, err error) ([]byte, error) {
	if err != nil {
		return nil, err
	}

	// 返回错误消息
	respJson := &RespJson{}
	_ = json.Unmarshal(respBytes, &respJson)
	if respJson.Code != "0" {
		return nil, errors.New(respJson.Msg)
	}

	respDataBytes, _ := json.Marshal(respJson.Data)
	return respDataBytes, nil
}
