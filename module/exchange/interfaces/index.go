package interfaces

import (
	"github.com/gomodule/redigo/redis"
	"zfeng/module/socket/client"
)

type Exchange interface {
	// InitWebSocket 初始化Websocket
	InitWebSocket([]*client.Subscribe) Exchange

	// SetWebSocketMessageFunc 设置websocket 消息处理
	SetWebSocketMessageFunc(func(channel, symbol string, rdsConn redis.Conn, data interface{})) Exchange

	// GetTickersData 获取当前产品行情信息
	GetTickersData(rds redis.Conn, symbol string) (Tickers, error)

	// GetKlineData 获取K线图数据
	GetKlineData(rds redis.Conn, symbol, bar string, afterTime, before int64, nums int64, cacheTime int) ([]*KlineAttrs, error)

	// GetBooksDta 获取深度数据
	GetBooksDta(rds redis.Conn, symbol, sz string) (*Books, error)
}

const (
	SubscribeChannelTickers = "tickers" // 行情数据
	SubscribeChannelBooks   = "books"   // 深度数据
	SubscribeChannelTrades  = "trades"  // 交易数据
	SubscribeChannelMining  = "mining"
)

type Books struct {
	Asks [][]string `json:"asks"` //	买深度
	Bids [][]string `json:"bids"` //	卖深度
	Ts   string     `json:"ts"`   //	时间
}

// KlineAttrs K线图数据
type KlineAttrs struct {
	OpenPrice  float64 `json:"openPrice"`  //开盘价格
	HighPrice  float64 `json:"highPrice"`  //最高价格
	LowsPrice  float64 `json:"lowsPrice"`  //最低价格
	ClosePrice float64 `json:"closePrice"` //收盘价格
	Vol        float64 `json:"vol"`        //交易量
	Amount     float64 `json:"amount"`     //成交额
	CreatedAt  int64   `json:"createdAt"`  //开盘时间
}

// Tickers 产品行情信息
type Tickers struct {
	Symbol    string  `json:"symbol"`    // 产品标识
	Last      float64 `json:"last"`      // 最新成交价
	LastSz    float64 `json:"lastSz"`    // 最新成交的数量
	Open24h   float64 `json:"open24h"`   // 24小时开盘价
	High24h   float64 `json:"high24h"`   // 24小时最高价
	Low24h    float64 `json:"low24h"`    // 24小时最低价
	VolCcy24h float64 `json:"volCcy24h"` // 24小时成交量，以币为单位。 如果是衍生品合约，数值为交易货币的数量。 如果是币币/币币杠杆，数值为计价货币的数量。
	Vol24h    float64 `json:"vol24h"`    // 24小时成交量，以张为单位,如果是衍生品合约，数值为合约的张数。如果是币币/币币杠杆，数值为交易货币的数量。
	Ts        int     `json:"ts"`        // 数据产生时间
}

// Trades 交易深度
type Trades struct {
	InstID  string `json:"instId"`
	TradeID string `json:"tradeId"`
	Px      string `json:"px"`
	Sz      string `json:"sz"`
	Side    string `json:"side"`
	Ts      string `json:"ts"`
	Count   string `json:"count"`
}
