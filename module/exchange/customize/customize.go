package customize

import (
	"errors"
	"math"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"time"
	"zfeng/core/cache"
	"zfeng/core/databases"
	"zfeng/models"
	"zfeng/module/exchange/interfaces"
	utils2 "zfeng/module/exchange/utils"
	"zfeng/module/socket/client"
	"zfeng/utils"

	"github.com/goccy/go-json"
	"github.com/gomodule/redigo/redis"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	RedisTickersName = "redisTickersNameCustomize"
	RedisBooksName   = "redisBooksName"
	RedisTradesName  = "redisTradesName"
	RedisKlineName   = "redisKlineName"
	logMsgCustomize  = "okx"
)

type Customize struct {
	messageFunc func(channel, symbol string, rdsConn redis.Conn, data interface{})
}

var Exchange = &Customize{}

// InitWebSocket 初始化Websocket
func (c *Customize) InitWebSocket([]*client.Subscribe) interfaces.Exchange {
	go func() {
		ch := time.NewTicker(time.Minute)
		conn := cache.Rds.Get()
		defer conn.Close()
		for {
			productDecimal := make(map[string][2]int)
			symbols := make([]string, 0)
			productList := make([]*models.Product, 0)
			databases.Db.Model(&models.Product{}).
				Distinct("symbol").
				Where("type = ?", models.ProductTypeCustomize).
				Find(&productList)
			for _, v := range productList {
				symbols = append(symbols, v.Symbol)
				productDecimal[v.Symbol] = [2]int{v.Data.AssetsDecimal, v.Data.SymbolAssetsDecimal}
			}

			nowTime := time.Now()
			// 到一分钟查询这一分钟的开盘价，获取下一分钟的最高价格，最低价格，最新成交量，最新交易量
			openDay := nowTime.Day()
			if nowTime.Hour() < 8 {
				openDay = nowTime.Day() - 1
			}
			earlyMorning := time.Date(nowTime.Year(), nowTime.Month(), openDay, 8, 0, 0, 0, time.Local)
			tickersCurrent := make([]*interfaces.Tickers, 0)
			result := databases.Db.Model(&models.ProductKline{}).
				Select("product_symbol as symbol", "MAX(high_price) as High24h", "Min(lows_price) as Low24h", "SUM(amount) as LastSz", "SUM(vol) as volCcy24h").
				Group("product_symbol").
				Where("created_at BETWEEN ? and ?", earlyMorning.Format(time.DateTime), nowTime.Format(time.DateTime)).
				Where("product_symbol IN ?", symbols).
				Find(&tickersCurrent)
			if result.Error != nil {
				zap.L().Error(logMsgCustomize, zap.Error(result.Error))
			}

			tickersCurrentMap := make(map[string]*interfaces.Tickers)
			for _, tickers := range tickersCurrent {
				tickersCurrentMap[tickers.Symbol] = tickers
			}

			productKlineList := make([]*models.ProductKline, 0)
			databases.Db.Model(&models.ProductKline{}).
				Where("product_symbol in ?", symbols).
				Where("created_at = ?", nowTime.Truncate(time.Minute).Format(time.DateTime)).
				Find(&productKlineList)
			lastMap := make(map[string][60]float64)
			for _, v := range productKlineList {
				val, ok := tickersCurrentMap[v.ProductSymbol]
				if ok {
					lastMap[v.ProductSymbol] = c.sinusoidalChange(v.LowsPrice, v.HighPrice)
					val.Open24h = v.OpenPrice
					c.setTickersData(conn, val)
				}
			}

			productKlineList = make([]*models.ProductKline, 0)
			databases.Db.Model(&models.ProductKline{}).
				Where("product_symbol in ?", symbols).
				Where("created_at = ?", nowTime.Add(time.Minute).Truncate(time.Minute).Format(time.DateTime)).
				Find(&productKlineList)

			if len(productKlineList) > 0 {
				for i := 0; i < 60; i++ {
					for _, v := range productKlineList {
						ticker, err := c.GetTickersData(conn, v.ProductSymbol)
						if err == nil {
							ticker.Last = lastMap[v.ProductSymbol][i]
							ticker.LastSz = c.GetRandom(ticker.LastSz, ticker.LastSz, ticker.LastSz+v.Amount)
							ticker.VolCcy24h = c.GetRandom(ticker.VolCcy24h, ticker.VolCcy24h, ticker.VolCcy24h+v.Vol)
							ticker.Ts = int(time.Now().Unix())
							c.setTickersData(conn, &ticker)
							c.messageFunc(interfaces.SubscribeChannelTickers, ticker.Symbol, conn, ticker)

							// 生成深度数据
							books := &interfaces.Books{Ts: strconv.FormatInt(nowTime.Unix(), 10)}
							generateRandom := utils.GenerateRandomInt(1, 30)
							for j := 0; j < generateRandom; j++ {
								assetsDecimal := productDecimal[v.ProductSymbol][0]
								symbolAssetsDecimal := productDecimal[v.ProductSymbol][1]
								numbMin := 1 / math.Pow(10, float64(symbolAssetsDecimal))
								numbMax := 1 / math.Pow(10, float64(symbolAssetsDecimal)) * math.Pow(10, 6)

								money := strconv.FormatFloat(utils.GenerateRandomFloat64(ticker.Last, v.HighPrice, assetsDecimal), 'f', assetsDecimal, 64)
								numb := strconv.FormatFloat(utils.GenerateRandomFloat64(numbMin, numbMax, symbolAssetsDecimal), 'f', symbolAssetsDecimal, 64)
								vol := strconv.FormatInt(int64(utils.GenerateRandomInt(0, int(v.Amount))), 10)
								val := []string{money, numb, "0", vol}
								books.Asks = append(books.Asks, val)

								money1 := strconv.FormatFloat(utils.GenerateRandomFloat64(ticker.Last, v.HighPrice, assetsDecimal), 'f', assetsDecimal, 64)
								numb1 := strconv.FormatFloat(utils.GenerateRandomFloat64(numbMin, numbMax, symbolAssetsDecimal), 'f', symbolAssetsDecimal, 64)
								vol1 := strconv.FormatInt(int64(utils.GenerateRandomInt(0, int(v.Amount))), 10)
								val = []string{money1, numb1, "0", vol1}
								books.Bids = append(books.Bids, val)
							}
							c.messageFunc(interfaces.SubscribeChannelBooks, ticker.Symbol, conn, books)

							// 生成交易数据
							generateRandom = utils.GenerateRandomInt(1, 3)
							for j := 0; j < generateRandom; j++ {
								assetsDecimal := productDecimal[v.ProductSymbol][0]
								symbolAssetsDecimal := productDecimal[v.ProductSymbol][1]
								numbMin := 1 / math.Pow(10, float64(symbolAssetsDecimal))
								numbMax := 1 / math.Pow(10, float64(symbolAssetsDecimal)) * math.Pow(10, 6)
								money := strconv.FormatFloat(utils.GenerateRandomFloat64(ticker.Last, v.HighPrice, assetsDecimal), 'f', assetsDecimal, 64)
								sz := strconv.FormatFloat(utils.GenerateRandomFloat64(numbMin, numbMax, symbolAssetsDecimal), 'f', symbolAssetsDecimal, 64)
								side := "sell"
								buyOrSell := utils.GenerateRandomInt(0, 2)
								if buyOrSell == 0 {
									side = "buy"
								}
								trades := &interfaces.Trades{
									InstID:  ticker.Symbol,
									TradeID: utils.GenerateNumericString(12),
									Px:      money,
									Sz:      sz,
									Side:    side,
									Ts:      strconv.FormatInt(time.Now().Unix(), 10),
									Count:   strconv.FormatInt(int64(generateRandom), 10),
								}
								c.messageFunc(interfaces.SubscribeChannelTrades, ticker.Symbol, conn, trades)
							}
						}
					}
					time.Sleep(time.Second)
				}
			}

			<-ch.C
		}
	}()

	go c.timingUpdateKline()

	return nil
}

// sinusoidalChange 价格浮动
func (c *Customize) sinusoidalChange(min, max float64) [60]float64 {
	amplitude := (max - min) / 2 // 振幅
	offset := min + amplitude    // 基线

	var vals = [60]float64{}
	for i := 0; i < 60; i++ {
		// 生成正弦值，周期为1分钟
		progress := float64(i) / float64(60) * 2 * math.Pi
		value := offset + amplitude*math.Sin(progress)
		vals[i] = value
	}
	return vals
}

// timingUpdateKline 定时更新 Kline
func (c *Customize) timingUpdateKline() {
	ch := time.NewTicker(30 * time.Second)
	for {
		productList := make([]*models.Product, 0)
		databases.Db.Model(&models.Product{}).
			Where("type = ?", models.ProductTypeCustomize).
			Where("status = ?", models.ProductStatusEnabled).
			Find(&productList)
		for _, v := range productList {
			err := databases.Db.Transaction(func(tx *gorm.DB) error {
				productKline := &models.ProductKline{}
				rowsAffected := tx.Model(&models.ProductKline{}).
					Where("product_symbol = ?", v.Symbol).
					Order("created_at DESC").
					Limit(1).
					Find(&productKline).RowsAffected
				nowTime := time.Now()
				//  如果最新的时间大于一天后的时间则跳过
				if rowsAffected > 0 && productKline.CreatedAt.Unix() > nowTime.AddDate(0, 0, 1).Unix() {
					return nil
				}
				var afterTime time.Time
				klineList := make([]*interfaces.KlineAttrs, 0)
				startTime := nowTime.Truncate(time.Minute)
				startPrice := 100.0
				if rowsAffected > 0 {
					startTime = productKline.CreatedAt.Add(time.Minute)
					startPrice = productKline.ClosePrice
				} else {
					startTime = startTime.AddDate(0, 0, -1)
				}
				afterTime = startTime.AddDate(0, 0, 2)
				index := int(afterTime.Sub(startTime).Minutes())
				interval := 10
				for i := 0; i < index; i += interval {
					endPrice := c.floatWithAmplitude(startPrice, 50)
					if interval+i > index {
						interval = index - i
					}
					kLines := utils2.GenerateKline(startPrice, endPrice, startTime.Truncate(time.Minute), startTime.Add(time.Duration(interval)*time.Minute).Truncate(time.Minute))
					if len(kLines) > 0 {
						klineList = append(klineList, kLines...)
						startPrice = kLines[len(kLines)-1].ClosePrice
						startTime = startTime.Add(time.Duration(interval) * time.Minute)
						continue
					}
					break
				}

				sort.Slice(klineList, func(i, j int) bool {
					return klineList[i].CreatedAt < klineList[j].CreatedAt
				})
				productKlines := make([]*models.ProductKline, 0)
				for _, kline := range klineList {
					createdAt := time.Unix(kline.CreatedAt, 0)
					productKlines = append(productKlines, &models.ProductKline{
						ProductSymbol: v.Symbol,
						OpenPrice:     utils.FloatAccuracy(kline.OpenPrice, v.Data.SymbolAssetsDecimal),
						HighPrice:     utils.FloatAccuracy(kline.HighPrice, v.Data.SymbolAssetsDecimal),
						LowsPrice:     utils.FloatAccuracy(kline.LowsPrice, v.Data.SymbolAssetsDecimal),
						ClosePrice:    utils.FloatAccuracy(kline.ClosePrice, v.Data.SymbolAssetsDecimal),
						Vol:           kline.Vol,
						Amount:        kline.Amount,
						CreatedAt:     createdAt,
					})
				}

				if len(productKlines) > 0 {
					err := tx.Create(&productKlines).Error
					if err != nil {
						return err
					}

					// 删除7天前的数据
					minCreatedAtTime := productKlines[len(productKlines)-1].CreatedAt.AddDate(0, 0, -6)
					err = tx.Unscoped().Where("product_symbol = ?", productKline.ProductSymbol).Where("created_at < ?", minCreatedAtTime).Delete(&models.ProductKline{}).Error
					if err != nil {
						return err
					}
				}
				return nil
			})
			if err != nil {
				zap.L().Error(logMsgCustomize, zap.Error(err))
			}
		}
		<-ch.C
	}
}

// 获取Kline数据
func (c *Customize) getBasicKline(afterTime, before time.Time, limit string) ([]*interfaces.KlineAttrs, error) {
	httpClient := utils.NewClient()
	httpClient.AddParam("instId", "BTC-USDT")
	httpClient.AddParam("bar", "1m")
	httpClient.AddParam("limit", limit)
	if !afterTime.IsZero() {
		httpClient.AddParam("after", strconv.FormatInt(afterTime.UnixMilli(), 10))
	}
	if !before.IsZero() {
		httpClient.AddParam("before", strconv.FormatInt(before.UnixMilli(), 10))
	}
	respBytes, err := httpClient.Get("https://www.okx.com/api/v5/market/history-candles")
	if err != nil {
		return nil, err
	}
	respJson := struct {
		Code string     `json:"code"`
		Msg  string     `json:"msg"`
		Data [][]string `json:"data"`
	}{}
	_ = json.Unmarshal(respBytes, &respJson)
	if respJson.Code != "0" {
		return nil, errors.New(respJson.Msg)
	}
	klineAttrs := make([]*interfaces.KlineAttrs, 0)
	for _, datum := range respJson.Data {
		openPrice, _ := strconv.ParseFloat(datum[1], 64)
		highPrice, _ := strconv.ParseFloat(datum[2], 64)
		lowsPrice, _ := strconv.ParseFloat(datum[3], 64)
		closePrice, _ := strconv.ParseFloat(datum[4], 64)
		vol, _ := strconv.ParseFloat(datum[5], 64)
		amount, _ := strconv.ParseFloat(datum[6], 64)
		ts, _ := strconv.ParseInt(datum[0], 10, 64)
		klineAttrs = append(klineAttrs, &interfaces.KlineAttrs{
			OpenPrice:  openPrice,
			HighPrice:  highPrice,
			LowsPrice:  lowsPrice,
			ClosePrice: closePrice,
			Vol:        vol,
			Amount:     amount,
			CreatedAt:  ts / 1000,
		})
	}
	return klineAttrs, nil
}

// SetWebSocketMessageFunc 设置websocket 消息处理
func (c *Customize) SetWebSocketMessageFunc(fun func(channel, symbol string, rdsConn redis.Conn, data interface{})) interfaces.Exchange {
	c.messageFunc = fun
	return c
}

// GetTickersData 获取当前产品行情信息
func (c *Customize) GetTickersData(rds redis.Conn, symbol string) (interfaces.Tickers, error) {
	tmpKey := strings.Join([]string{RedisTickersName, symbol}, ":")
	data := interfaces.Tickers{}
	tickersBytes, err := redis.Bytes(rds.Do("GET", tmpKey))
	if err != nil {
		return data, err
	}
	err = json.Unmarshal(tickersBytes, &data)
	if err != nil {
		return data, err
	}
	return data, err
}

// setTickersData 设置产品行情信息
func (c *Customize) setTickersData(rds redis.Conn, tickers *interfaces.Tickers) {
	tmpKey := strings.Join([]string{RedisTickersName, tickers.Symbol}, ":")
	_, _ = rds.Do("SET", tmpKey, utils.JsonToBytes(tickers))
}

// setTickersData 设置产品深度信息
func (c *Customize) setBooksData(rds redis.Conn, symbol string, books *interfaces.Books) {
	tmpKey := strings.Join([]string{RedisBooksName, symbol}, ":")
	_, _ = rds.Do("SET", tmpKey, utils.JsonToBytes(books))
}

// setTickersData 设置产品交易信息
func (c *Customize) setTradesData(rds redis.Conn, symbol string, trades []*interfaces.Trades) {
	tmpKey := strings.Join([]string{RedisTradesName, symbol}, ":")
	_, _ = rds.Do("SET", tmpKey, utils.JsonToBytes(trades))
}

// GetKlineData 获取K线图数据
func (c *Customize) GetKlineData(rds redis.Conn, symbol, bar string, afterTime, before int64, nums int64, cacheTime int) ([]*interfaces.KlineAttrs, error) {
	productKlines := make([]*models.ProductKline, 0)
	barNumb, _ := strconv.Atoi(bar)
	if barNumb == 1 {
		databases.Db.Model(&models.ProductKline{}).
			Where("product_symbol = ?", symbol).
			Where("created_at between ? AND ?", time.Unix(before, 0).Format(time.DateTime), time.Unix(afterTime, 0).Format(time.DateTime)).
			Order("created_at desc").
			Limit(int(nums)).
			Find(&productKlines)
	} else {
		err := databases.Db.Raw(`
				WITH time_grouped AS (
					SELECT
						pk.product_symbol,
						pk.high_price,
						pk.lows_price,
						pk.vol,
						pk.amount,
						-- 按5分钟分组
						STR_TO_DATE(DATE_FORMAT(FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(pk.created_at) / ?) * ?), '%Y-%m-%d %H:%i:%s'), '%Y-%m-%d %H:%i:%s') AS created_at,
						-- 获取每5分钟组内的第一条记录作为整点开盘价
						FIRST_VALUE(pk.open_price) OVER (PARTITION BY pk.product_symbol, DATE_FORMAT(FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(pk.created_at) / ?) * ?), '%Y-%m-%d %H:%i') ORDER BY pk.created_at) AS open_price,
						-- 获取每5分钟组内的最后一条记录作为收盘价
						LAST_VALUE(pk.close_price) OVER (PARTITION BY pk.product_symbol, DATE_FORMAT(FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(pk.created_at) / ?) * ?), '%Y-%m-%d %H:%i')
							ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS close_price
					FROM
						product_kline AS pk
					WHERE
						pk.product_symbol = ?
					  AND pk.created_at BETWEEN ? AND ?
						ORDER BY created_at
				)
				SELECT
						tg.product_symbol,
						sum(tg.vol) as vol,
						sum(tg.amount) as amount,
						tg.open_price,  -- 整点开盘价
						tg.close_price,  -- 5分钟组的最后一分钟收盘价
						MAX(tg.high_price) AS high_price,  -- 最高价
						MIN(tg.lows_price) AS lows_price,  -- 最低价
						tg.created_at
					FROM
						time_grouped AS tg
					GROUP BY
						tg.created_at,tg.product_symbol, tg.open_price, tg.close_price
					ORDER BY
						tg.created_at desc;
`, barNumb*60, barNumb*60, barNumb*60, barNumb*60, barNumb*60, barNumb*60,
			symbol, time.Unix(before, 0).Format(time.DateTime), time.Unix(afterTime, 0).Format(time.DateTime)).Find(&productKlines).Error
		if err != nil {
			return nil, err
		}
	}

	klineAttrs := make([]*interfaces.KlineAttrs, 0)
	for i := len(productKlines) - 1; 0 <= i; i-- {
		klineAttrs = append(klineAttrs, &interfaces.KlineAttrs{
			OpenPrice:  productKlines[i].OpenPrice,
			HighPrice:  productKlines[i].HighPrice,
			LowsPrice:  productKlines[i].LowsPrice,
			ClosePrice: productKlines[i].ClosePrice,
			Vol:        productKlines[i].Vol,
			Amount:     productKlines[i].Amount,
			CreatedAt:  productKlines[i].CreatedAt.Unix(),
		})
	}
	return klineAttrs, nil
}

// GetBooksDta 获取深度数据
func (c *Customize) GetBooksDta(rds redis.Conn, symbol, limit string) (*interfaces.Books, error) {
	sz, err := strconv.Atoi(limit)
	nowTime := time.Now()
	// 生成深度数据
	books := &interfaces.Books{Ts: strconv.FormatInt(nowTime.Unix(), 10)}

	ticker, err := c.GetTickersData(rds, symbol)
	if err == nil {
		v := models.ProductKline{}
		databases.Db.Model(&models.ProductKline{}).
			Where("product_symbol = ?", symbol).
			Where("created_at = ?", nowTime.Add(time.Minute).Truncate(time.Minute).Format(time.DateTime)).
			Find(&v)
		for j := 0; j < sz; j++ {
			money := strconv.FormatFloat(utils.GenerateRandomFloat64(ticker.Last, v.HighPrice, 2), 'f', 2, 64)
			numb := strconv.FormatFloat(utils.GenerateRandomFloat64(0.0001, v.Vol, 6), 'f', 2, 64)
			vol := strconv.FormatInt(int64(utils.GenerateRandomInt(0, int(v.Amount))), 10)
			books.Asks = append(books.Asks, []string{money, numb, "0", vol})

			money1 := strconv.FormatFloat(ticker.Last, 'f', 2, 64)
			numb1 := strconv.FormatFloat(utils.GenerateRandomFloat64(0.0001, v.Vol, 6), 'f', 2, 64)
			vol1 := strconv.FormatInt(int64(utils.GenerateRandomInt(0, int(v.Amount))), 10)
			books.Bids = append(books.Bids, []string{money1, numb1, "0", vol1})
		}
	}

	return books, nil
}

// GetTradesDta 获取交易数据
func (c *Customize) GetTradesDta(rds redis.Conn, symbol string, limit string) ([]*interfaces.Trades, error) {
	size, _ := strconv.Atoi(limit)
	trades := make([]*interfaces.Trades, 0)
	ticker, err := c.GetTickersData(rds, symbol)
	nowTime := time.Now()
	if err == nil {
		v := models.ProductKline{}
		databases.Db.Model(&models.ProductKline{}).
			Where("product_symbol = ?", symbol).
			Where("created_at = ?", nowTime.Add(time.Minute).Truncate(time.Minute).Format(time.DateTime)).
			Find(&v)
		for j := 0; j < size; j++ {
			money := strconv.FormatFloat(utils.GenerateRandomFloat64(ticker.Last, v.HighPrice, 2), 'f', 2, 64)
			sz := strconv.FormatFloat(utils.GenerateRandomFloat64(0, v.Vol, 2), 'f', 2, 64)
			side := "sell"
			buyOrSell := utils.GenerateRandomInt(0, 2)
			if buyOrSell == 0 {
				side = "buy"
			}
			trades = append(trades, &interfaces.Trades{
				InstID:  ticker.Symbol,
				TradeID: utils.GenerateNumericString(12),
				Px:      money,
				Sz:      sz,
				Side:    side,
				Ts:      strconv.FormatInt(nowTime.Unix(), 10),
				Count:   strconv.FormatInt(int64(utils.GenerateRandomInt(0, 2)), 10),
			})
		}
	}

	return trades, nil
}

// GetRandom 设置24小时成交量
func (c *Customize) GetRandom(value float64, min, max float64) float64 {
	nowTime := time.Now()
	hour := nowTime.Hour()
	zeroTime := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, nowTime.Location())
	switch {
	case hour == 0 && nowTime.Unix()-zeroTime.Unix() < 5:
		// 当时间在00:00:00-00:00:05 之间返回0
		return 0
	case value == 0:
		// 当值为零时返回随机数
		return utils.GenerateRandomFloat64(min, max, 0)
	default:
		// 当值不为零的时候在原来的基础上随24小时随机加量
		value += utils.GenerateRandomFloat64(float64(1), float64(hour+2), 0)
		return value
	}
}

// FloatWithAmplitude 根据基本值和振幅范围返回一个浮动后的值
func (c *Customize) floatWithAmplitude(base float64, amplitude float64) float64 {
	rand.NewSource(time.Now().UnixNano()) // 初始化随机种子
	for {
		// 生成范围内的随机浮动值：[-amplitude, +amplitude]
		offset := (rand.Float64()*2 - 1) * amplitude
		result := base + offset
		if result > 0 {
			return result
		}
	}
}
