package ieforex

import "github.com/goccy/go-json"

// RspEhHttpPostData 易汇Post请求返回数据
type RspEhHttpPostData struct {
	Code int             `json:"code"` //	返回代码
	Data json.RawMessage `json:"data"` //	内容
	Id   string          `json:"id"`
	Type string          `json:"type"`
}

// RspEhHttpGetData 易汇Post请求返回数据
type RspEhHttpGetData struct {
	Code  int             `json:"code"`  //	返回代码
	Datas json.RawMessage `json:"datas"` //	内容
	Msg   string          `json:"msg"`
}

// Datas Eh产品详情数据
type Datas struct {
	List []List  `json:"list"` // 产品列表
	Rate float64 `json:"rate"` // 汇率
}

// List 产品列表
type List struct {
	Accuracy     int64       `json:"accuracy"`
	AskClose     string      `json:"askClose"`
	AskOpen      string      `json:"askOpen"`
	BidClose     string      `json:"bidClose"`
	Change       string      `json:"change"`
	Chg          float64     `json:"chg"`
	ColumnType   interface{} `json:"columnType"`
	Country      string      `json:"country"`
	Date         string      `json:"date"`
	Group        string      `json:"group"`
	IsHasRev     string      `json:"isHasRev"`
	IsMt4        string      `json:"isMt4"`
	LastDate     string      `json:"lastDate"`
	MidHigh      string      `json:"midHigh"`
	MidLow       string      `json:"midLow"`
	Open         float64     `json:"open"`
	SockContext  string      `json:"sockContext"`
	SockIP       string      `json:"sockIp"`
	SockPort     int64       `json:"sockPort"`
	Symbol       string      `json:"symbol"`
	SymbolCh     string      `json:"symbolCh"`
	WebsocketURL string      `json:"websocketUrl"`
}

// KlineEHData EH k线图数据
type KlineEHData struct {
	Open  string `json:"open"`
	Close string `json:"close"`
	High  string `json:"high"`
	Low   string `json:"low"`
	Time  string `json:"time"`
}

// EhParams 易汇获取产品K线图参数
type EhParams struct {
	Id   string        `json:"id"`
	Cmd  string        `json:"cmd"`
	Args []interface{} `json:"args"`
}

// ProductInfo 产品信息
type ProductInfo struct {
	Symbol    string
	Decimal   int
	LastPrice float64
}
