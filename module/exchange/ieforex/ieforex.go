package ieforex

import (
	"errors"
	"github.com/goccy/go-json"
	"github.com/gomodule/redigo/redis"
	"go.uber.org/zap"
	"net/url"
	"strconv"
	"sync"
	"zfeng/module/exchange/interfaces"
	"zfeng/module/socket/client"
	"zfeng/utils"
)

const (
	IeforexUrl = "wss://stream.talkfx.co/dconsumer/arrge"

	IeforexBaseURL = "https://ieforex.com"

	IeforexCandlesURL = "/dmarket/dsymbol/getHistoryData"

	HttpSymbolDetailsURL = "/dmarket/dsymbol/list/"

	RedisTickersNameIeforex = "redisTickersForexName"

	RedisKlineValue = "redisKlineValue"

	RedisKlineOldValue = RedisKlineValue + "Old"

	logMsgIeforex = "Ieforex"
)

type Ieforex struct {
	messageFunc    func(channel, symbol string, rdsConn redis.Conn, data interface{})
	rdsConn        redis.Conn
	ProductInfoMap sync.Map
}

var Exchange = &Ieforex{
	ProductInfoMap: sync.Map{},
}

// InitWebSocket 初始化websocket
func (i *Ieforex) InitWebSocket(symbolList []*client.Subscribe) interfaces.Exchange {
	channel := symbolList[0].Name
	_ = client.NewSocketClient(IeforexUrl).InitSubscribes(symbolList).SetWebSocketMessageFunc(func(rdsConn redis.Conn, msg []byte) error {
		msgData := make(map[string]interface{})
		err := json.Unmarshal(msg, &msgData)
		if err == nil && msgData != nil {
			if _, ok := msgData["source"]; ok && msgData["source"] == "1" {
				symbol := msgData["i"].(string)
				if p, isSymbolExist := i.ProductInfoMap.Load(symbol); isSymbolExist {
					tickers := &interfaces.Tickers{}
					tickers.Symbol = symbol
					productInfo, productInfoOk := p.(*ProductInfo)
					if !productInfoOk {
						return errors.New("assertion failed")
					}

					tickers.Symbol = productInfo.Symbol
					price, priceOk := msgData["price"].(float64)
					if priceOk {
						tickers.Last = utils.FloatAccuracy(price, productInfo.Decimal)
					}

					if bid, bidOk := msgData["bid"].(float64); bidOk {
						tickers.LastSz = bid
					}

					if dhigh, dhighOk := msgData["dhigh"].(float64); dhighOk {
						tickers.High24h = dhigh
					}

					if dlow, dlowOk := msgData["dlow"].(float64); dlowOk {
						tickers.Low24h = dlow
					}

					if dt, dtOk := msgData["dt"].(float64); dtOk {
						tickers.Ts = int(dt) / 1000
					}
					baseTickers, err := GetTickers(rdsConn, tickers.Symbol)
					if err != nil {
						return err
					}
					tickers.Open24h = baseTickers.Open24h
					tickers.VolCcy24h = GetRandom(baseTickers.VolCcy24h, 1, 5)
					tickers.Vol24h = GetRandom(baseTickers.Vol24h, 1, 5)

					setTickers(rdsConn, tickers)
					i.messageFunc(channel, tickers.Symbol, rdsConn, tickers)
				}
			}
		}
		return nil
	}).Connect()

	return i
}

// SetWebSocketMessageFunc 设置消息处理
func (i *Ieforex) SetWebSocketMessageFunc(fun func(channel, symbol string, rdsConn redis.Conn, data interface{})) interfaces.Exchange {
	i.messageFunc = fun
	return i
}

// GetTickersData 获取产品行情信息
func (i *Ieforex) GetTickersData(rds redis.Conn, symbol string) (interfaces.Tickers, error) {
	return GetTickers(rds, symbol)
}

// GetBooksDta 获取深度数据
func (i *Ieforex) GetBooksDta(rds redis.Conn, symbol, sz string) (*interfaces.Books, error) {
	return nil, nil
}

func (i *Ieforex) getYHDate(symbol, bar string, afterTime, before int64, nums int64) ([]byte, error) {
	// 拼接参数
	params := url.Values{}
	params.Set("message", utils.StructToString(EhParams{
		Id:   "trade." + bar + "." + symbol,
		Cmd:  "req",
		Args: []interface{}{"candle." + bar + "." + symbol, nums, before, afterTime},
	}))
	return utils.NewClient().Post(IeforexBaseURL+IeforexCandlesURL, "application/x-www-form-urlencoded", params.Encode())
}

// GetKlineData 获取K线图数据
func (i *Ieforex) GetKlineData(rds redis.Conn, symbol, bar string, afterTime, before int64, nums int64, cacheTime int) ([]*interfaces.KlineAttrs, error) {
	symbol = SymbolConvert(symbol)
	key := RedisKlineValue + ":" + bar + ":" + symbol + ":" + strconv.Itoa(cacheTime)
	data, err := GetKlineAttrs(rds, key)
	if err == nil {
		return data, nil

	}

	respBytes, err := i.getYHDate(symbol, bar, afterTime, before, nums)
	oldKey := RedisKlineOldValue + bar + symbol + strconv.Itoa(cacheTime)
	tempKLineData := make([]*KlineEHData, 0)
	if err == nil {
		respData := new(RspEhHttpPostData)
		err = json.Unmarshal(respBytes, &respData)
		if err != nil {
			zap.L().Error(logMsgIeforex, zap.Error(err))
			return nil, err
		}

		err = json.Unmarshal(respData.Data, &tempKLineData)
		if err != nil {
			zap.L().Error(logMsgIeforex, zap.Error(err))
			return nil, err
		}

		data = i.convertKline(tempKLineData)
		if len(data) > 0 {
			if err = SetKlineAttrs(rds, key, cacheTime, data); err != nil {
				zap.L().Error(logMsgIeforex, zap.Error(err))
				return nil, err
			}

			if err = SetKlineAttrs(rds, oldKey, cacheTime, data); err != nil {
				zap.L().Error(logMsgIeforex, zap.Error(err))
				return nil, err
			}
		}
		return data, nil
	}
	zap.L().Error(logMsgIeforex, zap.Error(err))

	// 拿旧的数据
	if respBytes, err = redis.Bytes(rds.Do("GET", oldKey)); err == nil {
		err = json.Unmarshal(respBytes, &data)
		if err != nil {
			zap.L().Error(logMsgIeforex, zap.Error(err))
			return nil, err
		}
		_, err = rds.Do("SETEX", key, 30, respBytes)
		if err != nil {
			zap.L().Error(logMsgIeforex, zap.Error(err))
			return nil, err
		}
		return data, nil
	}

	return nil, nil
}

// convertKline  Kline数据转换
func (i *Ieforex) convertKline(kLineData []*KlineEHData) []*interfaces.KlineAttrs {
	data := make([]*interfaces.KlineAttrs, 0)
	for i := len(kLineData) - 1; 0 < i; i-- {
		temp := &interfaces.KlineAttrs{}
		temp.OpenPrice, _ = strconv.ParseFloat(kLineData[i].Open, 10)
		temp.LowsPrice, _ = strconv.ParseFloat(kLineData[i].Low, 10)
		temp.HighPrice, _ = strconv.ParseFloat(kLineData[i].High, 10)
		temp.ClosePrice, _ = strconv.ParseFloat(kLineData[i].Close, 10)
		createTime, _ := strconv.ParseInt(kLineData[i].Time, 10, 64)
		temp.CreatedAt = createTime
		data = append(data, temp)
	}
	return data
}

// SetSymbolDecimalMap 标识精度
func (i *Ieforex) SetSymbolDecimalMap(symbols map[string]int) *Ieforex {
	for k, v := range symbols {
		baseSymbol := SymbolConvert(k)
		i.ProductInfoMap.Store(baseSymbol, &ProductInfo{
			Symbol:  k,
			Decimal: v,
		})
	}
	return i
}
