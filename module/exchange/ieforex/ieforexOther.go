package ieforex

import (
	"github.com/goccy/go-json"
	"github.com/gomodule/redigo/redis"
	"go.uber.org/zap"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"
	"zfeng/core/cache"
	"zfeng/module/exchange/interfaces"
	"zfeng/module/exchange/okx"
	"zfeng/utils"
)

// UpdateBaseTickers 更新基础数据
func UpdateBaseTickers(symbols []string, productInfoMap *sync.Map) {
	nowTime := time.Now()
	params := url.Values{
		"t":       {strconv.FormatInt(nowTime.Unix(), 10)},
		"symbols": {strings.Join(symbols, ",")},
	}

	respBytes, err := utils.NewClient().Request("GET", IeforexBaseURL+HttpSymbolDetailsURL+"?"+params.Encode(), "")

	//接口是否正常
	respData := new(RspEhHttpGetData)
	err = json.Unmarshal(respBytes, &respData)
	if err != nil {
		zap.L().Error(logMsgIeforex, zap.Error(err))
		return
	}
	var datas Datas
	err = json.Unmarshal(respData.Datas, &datas)
	if err != nil {
		zap.L().Error(logMsgIeforex, zap.Error(err), zap.Reflect("body", datas))
		return
	}

	rdsConn := cache.Rds.Get()
	defer rdsConn.Close()
	for _, v := range datas.List {
		p, ok := productInfoMap.Load(v.Symbol)
		if ok {
			productInfo := p.(*ProductInfo)
			tickers := &interfaces.Tickers{Symbol: productInfo.Symbol, Open24h: v.Open, Last: utils.FloatAccuracy(v.Open+v.Chg, productInfo.Decimal)}
			tickers.High24h, _ = strconv.ParseFloat(v.MidHigh, 64)
			tickers.Low24h, _ = strconv.ParseFloat(v.MidLow, 64)
			tickers.VolCcy24h = GetRandom(tickers.VolCcy24h, 10, 100)
			tickers.Vol24h = GetRandom(tickers.Vol24h, 10, 100)
			tickers.Ts = int(nowTime.Unix()) / 1000
			setTickers(rdsConn, tickers)
		}
	}
	return
}

// setData 设置产品行情信息
func setTickers(rds redis.Conn, tickers *interfaces.Tickers) {
	key := okx.RedisTickersName + tickers.Symbol
	_, _ = rds.Do("SET", key, utils.StructToString(tickers))
	return
}

// GetTickers 获取产品行情信息
func GetTickers(rds redis.Conn, symbol string) (interfaces.Tickers, error) {
	data := interfaces.Tickers{}
	tickersBytes, err := redis.Bytes(rds.Do("GET", okx.RedisTickersName+symbol))
	if err != nil {
		return data, err
	}
	_ = json.Unmarshal(tickersBytes, &data)
	return data, nil
}

// GetRandom 设置24小时成交量
func GetRandom(value float64, min, max int) float64 {
	nowTime := time.Now()
	hour := nowTime.Hour()
	zeroTime := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, nowTime.Location())
	switch {
	case hour == 0 && nowTime.Unix()-zeroTime.Unix() < 5:
		// 当时间在00:00:00-00:00:05 之间返回0
		return 0
	case value == 0:
		// 当值为零时返回随机数
		return utils.GenerateRandomFloat64(float64(min), float64(max), 0)
	default:
		// 当值不为零的时候在原来的基础上随24小时随机加量
		value += utils.GenerateRandomFloat64(float64(1), float64(hour+2), 0)
		return value
	}
}

// SymbolConvert 标识转换
func SymbolConvert(symbol string) string {
	if strings.Contains(symbol, "-") {
		symbol = strings.ReplaceAll(symbol, "-", "")
	}
	return symbol
}

// GetKlineAttrs 获取k线图数据
func GetKlineAttrs(rds redis.Conn, key string) ([]*interfaces.KlineAttrs, error) {
	data := make([]*interfaces.KlineAttrs, 0)
	respBytes, err := redis.Bytes(rds.Do("GET", key))
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(respBytes, &data)
	if err != nil {
		return nil, err
	}

	return data, nil
}

// SetKlineAttrs 设置k线图数据
func SetKlineAttrs(rds redis.Conn, key string, time int, klineAttrs []*interfaces.KlineAttrs) error {
	_, err := rds.Do("SETEX", key, time, utils.JsonToBytes(klineAttrs))
	return err
}

// GetProductInfo 查询产品相关数据
func GetProductInfo(symbolDecimal map[string]int) ([]string, *sync.Map) {
	productInfoMap := sync.Map{}
	symbols := make([]string, 0)
	for symbol, decimal := range symbolDecimal {
		tmpSymbol := SymbolConvert(symbol)
		productInfoMap.Store(tmpSymbol, &ProductInfo{
			Symbol:  symbol,
			Decimal: decimal,
		})
		symbols = append(symbols, tmpSymbol)
	}
	return symbols, &productInfoMap
}
