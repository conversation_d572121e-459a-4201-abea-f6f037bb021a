package trading

import (
	"bytes"
	"compress/gzip"
	"compress/zlib"
	"encoding/base64"
	"fmt"
	"github.com/goccy/go-json"
	"github.com/gofiber/contrib/websocket"
	"github.com/gomodule/redigo/redis"
	"github.com/pkg/errors"
	"golang.org/x/crypto/nacl/secretbox"
	"io"
	"net/url"
	"strconv"
	"strings"
	"time"
	"zfeng/core/cache"
	"zfeng/module/exchange/interfaces"
	"zfeng/module/socket/client"
	"zfeng/utils"
)

const (
	klinePath              = "https://d3ii0wo49og5mi.cloudfront.net/markets"
	sipPath                = "https://live.tradingeconomics.com/socket.io"
	wsPath                 = "wss://live.tradingeconomics.com/socket.io"
	teDecryptK             = "j9ONifjoKzxt7kmfYTdKK/5vve0b9Y1UCj/n50jr8d8="
	teDecryptN             = "Ipp9HNSfVBUntqFK7PrtofYaOPV312xy"
	key                    = "tradingeconomics-charts-core-api-key"
	RedisTickersName       = "redisTickersNameTrading"
	RedisTickersSourceName = "redisTickersSourceName"
	RedisKlineName         = "redisKlineNameTrading"
	logMsgTrading          = "trading"
)

type Trading struct {
	messageFunc func(channel, symbol string, rdsConn redis.Conn, data interface{})
	symbolList  []string
}

var Exchange = &Trading{}

// InitWebSocket 初始化websocket
func (t *Trading) InitWebSocket(symbolList []*client.Subscribe) interfaces.Exchange {
	_ = json.Unmarshal(symbolList[0].Data, &t.symbolList)
	channel := symbolList[0].Name
	go t.UpdateTicker(t.symbolList)
	tmp := []any{"subscribe", map[string][]string{"s": t.symbolList}}
	instance := client.NewSocketClient(t.getWsPath()).SetHeartbeatTime(-1).SetReconnectTime(1).SetBeforeConnectFunc(func(c client.SocketClientInterface) error {
		// 是否停止交易
		weekday := time.Now().Weekday().String()
		switch weekday {
		case "Saturday":
			return errors.New("Saturday")
		case "Sunday":
			return errors.New("Sunday")
		}

		c.SetAddr(t.getWsPath())
		return nil
	})

	instance.InitSubscribes([]*client.Subscribe{{Data: []byte("2probe")}})
	instance.SetWebSocketMessageFunc(func(rdsConn redis.Conn, bytes []byte) error {
		isData := len(bytes) < 46
		numb := t.extractNumbers(bytes)
		switch {
		case numb == 3 && isData:
			_ = instance.ConnWriteMessage(websocket.TextMessage, []byte("5"))
			_ = instance.ConnWriteMessage(websocket.TextMessage, []byte("40"))
		case numb == 2 && isData:
			_ = instance.ConnWriteMessage(websocket.TextMessage, []byte("3"))
		case numb == 40 && isData:
			msg := append([]byte("42"), utils.JsonToBytes(tmp)...)
			_ = instance.ConnWriteMessage(websocket.TextMessage, msg)
		default:
			v, err := t.decryptMessage(bytes, teDecryptK, teDecryptN)
			if err != nil {
				return err
			}
			commodityData := CommodityData{}
			err = json.Unmarshal(v, &commodityData)
			if err != nil {
				return err
			}
			symbol := strings.ToLower(commodityData.S)
			ticker, err := t.GetTickersData(rdsConn, symbol)
			newTime := time.UnixMilli(commodityData.Dt)
			if err != nil {
				ticker = interfaces.Tickers{
					Symbol:  symbol,
					Last:    commodityData.P,
					Open24h: commodityData.P - commodityData.Nch,
					Ts:      int(newTime.Unix()),
				}
			} else {
				ticker.Last = commodityData.P
				ticker.Open24h = commodityData.P - commodityData.Nch
				ticker.Ts = int(newTime.Unix())
			}
			t.setTickersData(rdsConn, ticker)
			//zap.L().Info(logMsgTrading, zap.Error(err), zap.String("instId", symbol), zap.Reflect("data", ticker))
			t.messageFunc(channel, symbol, rdsConn, ticker)
		}
		return nil
	})
	_ = instance.Connect()

	return t
}

// SetWebSocketMessageFunc 设置消息处理
func (t *Trading) SetWebSocketMessageFunc(fun func(channel, symbol string, rdsConn redis.Conn, data interface{})) interfaces.Exchange {
	t.messageFunc = fun
	return t
}

// GetTickersData 获取产品行情信息
func (t *Trading) GetTickersData(rds redis.Conn, symbol string) (interfaces.Tickers, error) {
	data := interfaces.Tickers{}
	tmpKey := strings.Join([]string{RedisTickersName, symbol}, ":")
	tickersBytes, err := redis.Bytes(rds.Do("GET", tmpKey))
	if err != nil {
		return data, err
	}
	err = json.Unmarshal(tickersBytes, &data)
	if err != nil {
		return data, err
	}
	return data, err
}

// setTickersData 设置产品行情信息
func (t *Trading) setTickersData(rds redis.Conn, tickers interfaces.Tickers) {
	tmpKey := strings.Join([]string{RedisTickersName, tickers.Symbol}, ":")
	_, _ = rds.Do("SET", tmpKey, utils.JsonToBytes(tickers))
}

// setTickersData 设置产品行情信息
func (t *Trading) setTickersSourceData(rds redis.Conn, tickers CommodityData) {
	_, _ = rds.Do("SET", RedisTickersSourceName+strings.ToLower(tickers.S), utils.JsonToBytes(tickers))
}

// getTickersSourceData 设置产品源行情信息
func (t *Trading) getTickersSourceData(rds redis.Conn, s string) *CommodityData {
	tmpKey := strings.Join([]string{RedisTickersSourceName, s}, ":")
	byteData, e := redis.Bytes(rds.Do("GET", tmpKey))
	if e != nil {
		return nil
	}
	tickerSource := CommodityData{}
	e = json.Unmarshal(byteData, &tickerSource)
	if e != nil {
		return nil
	}
	return &tickerSource
}

// GetBooksDta 获取深度数据
func (t *Trading) GetBooksDta(rds redis.Conn, symbol, sz string) (*interfaces.Books, error) {
	return nil, nil
}

// GetTradesDta 获取交易数据
func (t *Trading) GetTradesDta(rds redis.Conn, symbol string, limit string) ([]*interfaces.Trades, error) {
	return nil, nil
}

// GetKlineData 获取K线图数据
func (t *Trading) GetKlineData(rds redis.Conn, symbol, bar string, afterTime, before int64, nums int64, cacheTime int) ([]*interfaces.KlineAttrs, error) {
	cachKey := strings.Join([]string{RedisKlineName, symbol, bar, strconv.Itoa(int(nums)), strconv.Itoa(cacheTime)}, ":")
	kline := t.getKline(rds, cachKey)
	if len(kline) == 0 {
		httpClient := utils.NewClient()
		httpClient.AddParam("interval", bar) // 1分:1m 5分:5m 15分:15m 1小时:1h 1天:1d 1周:1w 1月:1month
		//httpClient.AddParam("span", "1d")     // 1天:1d 1周:1w 1月:1m 6月:6m 1年:1y 5年:5y 10年:10y 25年:25y 50年:50y
		httpClient.AddParam("n", strconv.Itoa(int(nums))) // 数量
		httpClient.AddParam("ohlc", "1")                  // k线图类型 0 1
		httpClient.AddParam("key", "20240229:nazare")
		respData, err := httpClient.Get(klinePath + "/" + symbol)
		if err != nil {
			return nil, err
		}
		kline, err = t.klineConversion(respData)
		if err != nil {
			return nil, err
		}
		tickerSource := t.getTickersSourceData(rds, symbol)
		if tickerSource != nil {
			kline = append(kline, &interfaces.KlineAttrs{
				OpenPrice:  kline[len(kline)-1].ClosePrice,
				HighPrice:  tickerSource.P,
				LowsPrice:  tickerSource.P,
				ClosePrice: tickerSource.P,
				CreatedAt:  time.Now().Unix() / 100 * 100,
			})
		}
		kline = kline[1:]
		t.setKline(rds, cachKey, cacheTime, kline)
		return kline, nil
	}

	return kline, nil
}

// getKline 防止Kline缓存
func (t *Trading) getKline(rds redis.Conn, key string) []*interfaces.KlineAttrs {
	kline := make([]*interfaces.KlineAttrs, 0)
	klineBytes, _ := redis.Bytes(rds.Do("GET", key))
	_ = json.Unmarshal(klineBytes, &kline)
	return kline
}

// setKline 设置kline缓存
func (t *Trading) setKline(rds redis.Conn, key string, cacheTime int, klineAttrs []*interfaces.KlineAttrs) {
	if len(klineAttrs) == 0 {
		return
	}
	if cacheTime <= 0 {
		cacheTime = 10
	}
	_, _ = rds.Do("SETEX", key, cacheTime, utils.JsonToBytes(klineAttrs))
}

// extractNumbers 提取数字
func (t *Trading) extractNumbers(b []byte) int {
	var val []byte
	for _, v := range b {
		if 48 <= v && v <= 57 {
			val = append(val, v)
			continue
		}
		break
	}
	parseInt, err := strconv.ParseInt(string(val), 10, 64)
	if err != nil {
		return -1
	}
	return int(parseInt)
}

// klineConversion 转换数据
func (t *Trading) klineConversion(tmpVal []byte) ([]*interfaces.KlineAttrs, error) {
	vStr := strings.Trim(string(tmpVal), "\"")
	decryptData, err := t.dataMagic(vStr, key)
	if err != nil {
		return nil, err
	}

	tmp := Response{}
	err = json.Unmarshal(decryptData, &tmp)
	if err != nil {
		return nil, err
	}

	kline := make([]*interfaces.KlineAttrs, 0)
	tmpData := make([][]any, 0)
	// 过滤第一个存在nil的数据
	if len(tmp.Series) != 0 && len(tmp.Series[0].Data) != 0 {
		tmpData = tmp.Series[0].Data
		for _, a := range tmpData[0] {
			if a == nil {
				tmpData = tmpData[1:]
			}
		}
	}
	for _, datum := range tmpData {
		kline = append(kline, &interfaces.KlineAttrs{
			OpenPrice:  datum[4].(float64),
			HighPrice:  datum[5].(float64),
			LowsPrice:  datum[6].(float64),
			ClosePrice: datum[7].(float64),
			CreatedAt:  int64(datum[0].(float64)),
		})
	}

	return kline, err
}

// dataMagic 数据魔法
func (t *Trading) dataMagic(b64Data, dk string) ([]byte, error) {
	// 1. Base64 解码
	decodedData, err := base64.StdEncoding.DecodeString(b64Data)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64: %v", err)
	}

	// 2. 按字节进行异或操作
	keyBytes := []byte(dk)
	for i := 0; i < len(decodedData); i++ {
		decodedData[i] ^= keyBytes[i%len(keyBytes)]
	}

	// 3. 解压数据
	decompressedData, err := t.decompressGzip(decodedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress encryptionData: %v", err)
	}

	return decompressedData, nil
}

// 解压
func (t *Trading) decompressGzip(compressedData []byte) ([]byte, error) {
	// 使用 gzip.NewReader 创建解压读取器
	reader, err := gzip.NewReader(bytes.NewReader(compressedData))
	if err != nil {
		return nil, fmt.Errorf("failed to create gzip reader: %v", err)
	}
	defer reader.Close()

	// 使用 bytes.Buffer 来存储解压后的数据
	var decompressedData bytes.Buffer
	_, err = io.Copy(&decompressedData, reader)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress encryptionData: %v", err)
	}

	// 返回解压后的字节数据
	return decompressedData.Bytes(), nil
}

// base64ToBytes base64 转换字节
func (t *Trading) base64ToBytes(encoded string) ([]byte, error) {
	data, err := base64.StdEncoding.DecodeString(encoded)
	if err != nil {
		return nil, err
	}
	return data, nil
}

// decryptMessage 解密解压
func (t *Trading) decryptMessage(ciphertext []byte, keyBase64 string, nonceBase64 string) ([]byte, error) {
	// 转换字节码
	k, err := t.base64ToBytes(keyBase64)
	if err != nil {
		return nil, errors.Wrap(err, "failed to decode key")
	}
	nonce, err := t.base64ToBytes(nonceBase64)
	if err != nil {
		return nil, errors.Wrap(err, "failed to decode nonce")
	}

	var nonceArray [24]byte
	copy(nonceArray[:], nonce)

	// 解密
	decrypted, ok := secretbox.Open(nil, ciphertext, &nonceArray, (*[32]byte)(k))
	if !ok {
		return nil, fmt.Errorf("decryption failed")
	}

	decompressed, err := t.decompressZlib(decrypted)
	if err != nil {
		return nil, errors.Wrap(err, "failed to decompress plaintext")
	}

	return decompressed, nil
}

// decompressZlib 解压
func (t *Trading) decompressZlib(data []byte) ([]byte, error) {
	reader, err := zlib.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, err
	}
	defer reader.Close()

	var decompressed bytes.Buffer
	_, err = decompressed.ReadFrom(reader)
	if err != nil {
		return nil, err
	}
	return decompressed.Bytes(), nil
}

// getSid 获取sid
func (t *Trading) getSid(params Params) string {
	instance := utils.NewClient()
	instance.AddParam("key", params.Key)
	instance.AddParam("url", params.Url)
	instance.AddParam("EIO", params.EIO)
	instance.AddParam("transport", "polling")
	instance.AddParam("t", params.T)
	val, err := instance.Get(sipPath + "/")
	if err != nil {
		return ""
	}
	response := WsResponse{}
	if len(val) > 0 {
		err = json.Unmarshal(val[1:], &response)
		if err != nil {
			return ""
		}
		return response.SID
	}

	return ""
}

// UpdateTicker 更新行情数据
func (t *Trading) UpdateTicker(symbolList []string) {
	c := time.NewTicker(10 * time.Hour)
	for {
		conn := cache.Rds.Get()
		for _, s := range symbolList {
			klineData, err := t.GetKlineData(conn, s, "1d", 0, 0, 2, 60*60)
			if err != nil || len(klineData) == 0 || klineData[0] == nil {
				continue
			}
			ticker, err := t.GetTickersData(conn, s)
			ticker.Symbol = s
			ticker.High24h = klineData[0].HighPrice
			ticker.Low24h = klineData[0].LowsPrice
			ticker.VolCcy24h = GetRandom(ticker.VolCcy24h, 10, 100)
			ticker.Vol24h = GetRandom(ticker.Vol24h, 10, 100)
			t.setTickersData(conn, ticker)
			time.Sleep(time.Second)
		}
		_ = conn.Close()
		<-c.C
	}
}

func (t *Trading) getWsPath() string {
	params := Params{
		Key:       "rain",
		Url:       "/",
		EIO:       "4",
		Transport: "websocket",
		T:         fmt.Sprintf("%d", time.Now().UnixNano()/1e6),
	}
	param := url.Values{}
	param.Set("key", params.Key)
	param.Set("url", params.Url)
	param.Set("EIO", params.EIO)
	param.Set("transport", "websocket")
	for {
		sid := t.getSid(params)
		if sid != "" {
			param.Set("sid", sid)
			break
		}
	}
	return wsPath + "/?" + param.Encode()
}

// GetRandom 设置24小时成交量
func GetRandom(value float64, min, max int) float64 {
	nowTime := time.Now()
	hour := nowTime.Hour()
	zeroTime := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, nowTime.Location())
	switch {
	case hour == 0 && nowTime.Unix()-zeroTime.Unix() < 5:
		// 当时间在00:00:00-00:00:05 之间返回0
		return 0
	case value == 0:
		// 当值为零时返回随机数
		return utils.GenerateRandomFloat64(float64(min), float64(max), 0)
	default:
		// 当值不为零的时候在原来的基础上随24小时随机加量
		value += utils.GenerateRandomFloat64(float64(1), float64(hour+2), 0)
		return value
	}
}
