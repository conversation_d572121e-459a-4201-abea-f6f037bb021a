package telegram

import (
	"context"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"time"
	"zfeng/module/robots/interfaces"

	"github.com/go-telegram/bot"
	"github.com/go-telegram/bot/models"
)

// Telegram 机器人
type Telegram struct {
	bot                *bot.Bot
	ctx                context.Context
	cancelFunc         context.CancelFunc
	conf               *interfaces.RobotsConf
	defaultHandlerFunc func(ctx *context.Context, message *interfaces.RobotsMessage)
}

// NewTelegram 创建一个新的 Telegram 机器人
func NewTelegram(conf *interfaces.RobotsConf) *Telegram {
	ctx, cancel := signal.NotifyContext(context.Background(), os.Interrupt)
	return &Telegram{
		ctx:        ctx,
		cancelFunc: cancel,
		conf:       conf,
	}
}

// SetDefaultHandler 设置默认的处理器
func (t *Telegram) SetDefaultHandler(fun func(ctx *context.Context, message *interfaces.RobotsMessage)) *Telegram {
	t.defaultHandlerFunc = fun
	return t
}

func (t *Telegram) Start() error {
	var err error

	opts := []bot.Option{
		bot.WithDefaultHandler(func(ctx context.Context, bot *bot.Bot, update *models.Update) {
			if t.defaultHandlerFunc == nil {
				return
			}

			// 处理机器人消息
			t.defaultHandlerFunc(&ctx, &interfaces.RobotsMessage{
				ChatId: update.Message.Chat.ID,
				Text:   update.Message.Text,
			})
		}),
	}

	// 是否配置代理
	if t.conf.ProxyUrl != "" {
		proxyUrl, err := url.Parse(t.conf.ProxyUrl)
		if err != nil {
			return err
		}

		// 配置代理
		opts = append(opts, bot.WithHTTPClient(30*time.Second, &http.Client{
			Transport: &http.Transport{
				Proxy: http.ProxyURL(proxyUrl),
			},
		}))
	}

	// 创建机器人
	t.bot, err = bot.New(t.conf.Token, opts...)
	if err != nil {
		return err
	}

	t.bot.Start(t.ctx)
	return nil
}

// Close 关闭机器人
func (t *Telegram) Close() error {
	t.cancelFunc()
	return nil
}
