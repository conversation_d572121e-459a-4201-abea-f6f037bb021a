package service

import (
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"

	"github.com/gomodule/redigo/redis"
)

type SystemLevelService struct {
	db *model.Model
}

func NewSystemLevelService() *SystemLevelService {
	return &SystemLevelService{
		db: model.NewModel(),
	}
}

// GetLevelOptions 获取等级选项
func (s *SystemLevelService) GetLevelOptions(rdsConn redis.Conn, subAdminIDs []uint) ([]*views.SelectOption, error) {
	var levels []struct {
		ID        uint
		Name      string
		AdminID   uint
		AdminName string
	}
	s.db.Table("level l").
		Select("l.id", "l.name", "l.admin_id", "adm.username as admin_name").
		Joins("left join admin_user adm on l.admin_id = adm.id").
		Where("l.admin_id IN ?", subAdminIDs).
		Where("l.deleted_at IS NULL").
		Where("l.status = ?", models.LevelStatusEnabled).
		Find(&levels)

	translateService := NewTranslateService()
	options := make([]*views.SelectOption, 0)
	for _, level := range levels {
		levelName, _ := translateService.GetTranslateByFieldWithCache(rdsConn, level.AdminID, "zh-CN", level.Name)
		options = append(options, &views.SelectOption{
			Label: levelName + " (" + level.AdminName + ")",
			Value: level.ID,
		})
	}

	return options, nil
}
