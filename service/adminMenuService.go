package service

import (
	"fmt"
	"zfeng/core/model"
	"zfeng/models"

	"github.com/goccy/go-json"
	"github.com/gomodule/redigo/redis"
)

const (
	// AdminMenuTreeCacheKey 管理菜单树缓存Key
	AdminMenuTreeCacheKey = "AdminMenuTreeCacheKey"
	// AdminMenuTreeCacheExpiration 管理菜单树缓存过期时间（秒）
	AdminMenuTreeCacheExpiration = 3600
)

// AdminMenuService 管理菜单服务结构体
type AdminMenuService struct {
	db *model.Model
}

// NewAdminMenuService 创建新的管理菜单服务实例
func NewAdminMenuService() *AdminMenuService {
	return &AdminMenuService{
		db: model.NewModel(),
	}
}

// GetAdminMenuTreeWithCache 获取管理菜单树（带缓存）
func (s *AdminMenuService) GetAdminMenuTreeWithCache(rdsConn redis.Conn, userID uint) ([]string, []*models.AdminMenuDisplayData, error) {
	// 获取路由列表
	rolesRouters, _ := NewAdminRbacService().GetUserRolesRouters(rdsConn, userID)

	cacheKey := fmt.Sprintf("%s:%v", AdminMenuTreeCacheKey, userID)
	// 尝试从缓存中获取
	cachedData, err := redis.Bytes(rdsConn.Do("GET", cacheKey))
	if err == nil {
		var menuTree []*models.AdminMenuDisplayData
		if err = json.Unmarshal(cachedData, &menuTree); err == nil {
			return rolesRouters, menuTree, nil
		}
	}

	// 如果缓存中没有，从数据库获取
	menuTree, err := s.GetAdminMenuTree(0, rolesRouters)
	if err != nil {
		return rolesRouters, nil, fmt.Errorf("获取管理菜单树失败: %w", err)
	}

	// 将数据存入缓存
	if cachedData, err := json.Marshal(menuTree); err == nil {
		_, _ = rdsConn.Do("SETEX", cacheKey, AdminMenuTreeCacheExpiration, cachedData)
	}

	return rolesRouters, menuTree, nil
}

// GetAdminMenuTree 递归查询管理菜单树
func (s *AdminMenuService) GetAdminMenuTree(parentID uint, routes []string) ([]*models.AdminMenuDisplayData, error) {
	var menus []*models.AdminMenu
	err := s.db.Where("parent_id = ? AND (route IN ? OR route IS NULL) AND status = ?", parentID, routes, models.AdminMenuStatusEnabled).
		Order("sort ASC").
		Find(&menus).Error
	if err != nil {
		return nil, err
	}

	displayMenus := make([]*models.AdminMenuDisplayData, 0)
	for _, menu := range menus {
		displayMenu := menu.ToDisplayData()
		children, err := s.GetAdminMenuTree(menu.ID, routes)
		if err != nil {
			return nil, err
		}
		displayMenu.Children = children

		// 判断如果 route 为空且 children 为空，则不添加本条数据
		if menu.Route == "" && len(children) == 0 {
			continue
		}

		displayMenus = append(displayMenus, displayMenu)
	}

	return displayMenus, nil
}

// DeleteAdminMenuTreeCache 删除管理菜单树缓存
func (s *AdminMenuService) DeleteAdminMenuTreeCache(rdsConn redis.Conn, userID uint) error {
	cacheKey := fmt.Sprintf("%s:%v", AdminMenuTreeCacheKey, userID)
	_, err := rdsConn.Do("DEL", cacheKey)
	if err != nil {
		return fmt.Errorf("删除管理菜单树缓存失败: %w", err)
	}
	return nil
}

// DeleteAdminMenuTreeAllCache 删除所有管理菜单缓存
func (s *AdminMenuService) DeleteAdminMenuTreeAllCache(rdsConn redis.Conn) error {
	adminIds, _ := NewAdminUserService().GetSubAdminIDsWithCache(rdsConn, models.SuperAdminID)

	for _, v := range adminIds {
		s.DeleteAdminMenuTreeCache(rdsConn, v)
	}
	return nil
}
