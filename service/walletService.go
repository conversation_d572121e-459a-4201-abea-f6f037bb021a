package service

import (
	"errors"
	"fmt"
	"zfeng/core/cache"
	"zfeng/core/model"
	"zfeng/models"

	"github.com/gomodule/redigo/redis"
	"gorm.io/gorm"
)

// WalletService 钱包服务
type WalletService struct {
	db *model.Model
}

// NewWalletService 创建钱包服务
func NewWalletService() *WalletService {
	return &WalletService{
		db: model.NewModel(),
	}
}

// SpendBorrowBalance 花费余额【可负数余额】
func (s *WalletService) SpendBorrowBalance(tx *gorm.DB, rdsConn redis.Conn, acceptLang string, billType int8, sourceID uint, userInfo *models.User, amount float64) error {
	return s.SpendBalanceFunc(tx, rdsConn, acceptLang, billType, sourceID, userInfo, amount, true)
}

// SpendBalance 花费余额方法【正数】
func (s *WalletService) SpendBalance(tx *gorm.DB, rdsConn redis.Conn, acceptLang string, billType int8, sourceID uint, userInfo *models.User, amount float64) error {
	return s.SpendBalanceFunc(tx, rdsConn, acceptLang, billType, sourceID, userInfo, amount, false)
}

// SpendBalanceFunc 花费余额方法
func (s *WalletService) SpendBalanceFunc(tx *gorm.DB, rdsConn redis.Conn, acceptLang string, billType int8, sourceID uint, userInfo *models.User, amount float64, isBorrow bool) error {
	translateService := NewTranslateService()
	adminService := NewAdminUserService()
	settingService := NewAdminSettingService()

	// 添加 redis 锁
	lock := cache.NewRedisLock(rdsConn, fmt.Sprintf("walletBalanceLock_%d", userInfo.ID), 30)
	ok, err := lock.Lock()
	if err != nil {
		return err
	}
	if !ok {
		return errors.New("redis lock failed")
	}
	defer lock.Unlock()

	// 检查可用余额是否足够
	if !isBorrow {
		translate, _ := translateService.GetTranslateByFieldWithCache(rdsConn, 0, acceptLang, "insufficientBalance")
		switch billType {
		case models.BillTypeWithdrawal, models.BillTypeSwapsSend, models.BillTypeSystemDeduction, models.BillTypeTransferSend:
			if (userInfo.AvailableAmount - userInfo.FrozenAmount) < amount {
				return errors.New(translate)
			}
		default:
			if userInfo.AvailableAmount < amount {
				return errors.New(translate)
			}
		}
	}

	// 更新用户余额
	if err := tx.Model(&userInfo).Where("id = ?", userInfo.ID).Update("available_amount", gorm.Expr("available_amount - ?", amount)).Error; err != nil {
		translate := translateService.GetTranslateByFieldWithCacheToArgs(rdsConn, 0, acceptLang, "dataError", err.Error())
		return errors.New(translate)
	}

	// 创建账单记录
	billService := NewWalletBillService()
	if err := billService.CreateBill(tx, userInfo, nil, billType, sourceID, -amount); err != nil {
		translate := translateService.GetTranslateByFieldWithCacheToArgs(rdsConn, 0, acceptLang, "dataError", err.Error())
		return errors.New(translate)
	}

	// 资产分销奖励
	if userInfo.ParentID > 0 {
		distributionReward := make([]*models.AdminSettingDistributionReward, 0)
		merchantID, _ := adminService.GetMerchantIDWithCache(rdsConn, userInfo.AdminID)
		settingService.GetAdminSettingByFieldWithCache(rdsConn, merchantID, "walletDistributionReward").ToInterface(&distributionReward)
		s.DistributionBalanceReward(tx, rdsConn, acceptLang, sourceID, billType, userInfo.ParentID, 1, amount, distributionReward)
	}
	return nil
}

// IncreaseBalance 增加用户余额方法
func (s *WalletService) IncreaseBalance(tx *gorm.DB, rdsConn redis.Conn, acceptLang string, billType int8, sourceID uint, userInfo *models.User, amount float64) error {
	translateService := NewTranslateService()
	adminService := NewAdminUserService()
	settingService := NewAdminSettingService()

	// 添加 redis 锁
	lock := cache.NewRedisLock(rdsConn, fmt.Sprintf("walletBalanceLock_%d", userInfo.ID), 30)
	ok, err := lock.Lock()
	if err != nil {
		return err
	}
	if !ok {
		return errors.New("redis lock failed")
	}
	defer lock.Unlock()

	// 更新用户余额
	if err := tx.Model(&models.User{}).Where("id = ?", userInfo.ID).Update("available_amount", gorm.Expr("available_amount + ?", amount)).Error; err != nil {
		translate := translateService.GetTranslateByFieldWithCacheToArgs(rdsConn, 0, acceptLang, "dataError", err.Error())
		return errors.New(translate)
	}

	// 创建账单记录
	billService := NewWalletBillService()
	if err := billService.CreateBill(tx, userInfo, nil, billType, sourceID, amount); err != nil {
		translate := translateService.GetTranslateByFieldWithCacheToArgs(rdsConn, 0, acceptLang, "dataError", err.Error())
		return errors.New(translate)
	}

	// 资产分销奖励
	if userInfo.ParentID > 0 {
		distributionReward := make([]*models.AdminSettingDistributionReward, 0)
		merchantID, _ := adminService.GetMerchantIDWithCache(rdsConn, userInfo.AdminID)
		settingService.GetAdminSettingByFieldWithCache(rdsConn, merchantID, "walletDistributionReward").ToInterface(&distributionReward)
		s.DistributionBalanceReward(tx, rdsConn, acceptLang, sourceID, billType, userInfo.ParentID, 1, amount, distributionReward)
	}
	return nil
}

// SpendBorrowAssets 花费资产方法【可负数】
func (s *WalletService) SpendBorrowAssets(tx *gorm.DB, rdsConn redis.Conn, acceptLang string, billType int8, sourceID uint, userInfo *models.User, assetsInfo *models.WalletAssets, amount float64) error {
	return s.SpendAssetsFunc(tx, rdsConn, acceptLang, billType, sourceID, userInfo, assetsInfo, amount, true)
}

// SpendAssets 花费资产方法【正数】
func (s *WalletService) SpendAssets(tx *gorm.DB, rdsConn redis.Conn, acceptLang string, billType int8, sourceID uint, userInfo *models.User, assetsInfo *models.WalletAssets, amount float64) error {
	return s.SpendAssetsFunc(tx, rdsConn, acceptLang, billType, sourceID, userInfo, assetsInfo, amount, false)
}

// SpendAssetsFunc 花费资产方法
func (s *WalletService) SpendAssetsFunc(tx *gorm.DB, rdsConn redis.Conn, acceptLang string, billType int8, sourceID uint, userInfo *models.User, assetsInfo *models.WalletAssets, amount float64, isBorrow bool) error {
	translateService := NewTranslateService()
	adminService := NewAdminUserService()
	settingService := NewAdminSettingService()

	// 添加 redis 锁
	lock := cache.NewRedisLock(rdsConn, fmt.Sprintf("walletAssetsLock_%d", assetsInfo.ID), 30)
	ok, err := lock.Lock()
	if err != nil {
		return err
	}
	if !ok {
		return errors.New("redis lock failed")
	}
	defer lock.Unlock()

	// 检查用户资产是否存在
	userAssetsInfo := &models.UserAssets{}
	if err := tx.Model(&userAssetsInfo).Where("user_id = ?", userInfo.ID).Where("assets_id = ?", assetsInfo.ID).Find(&userAssetsInfo).Error; err != nil || userAssetsInfo.ID == 0 {
		translate, _ := translateService.GetTranslateByFieldWithCache(rdsConn, 0, acceptLang, "insufficientBalance")
		return errors.New(translate)
	}

	// 检查用户资产余额是否足够
	if !isBorrow {
		translate, _ := translateService.GetTranslateByFieldWithCache(rdsConn, 0, acceptLang, "insufficientBalance")
		switch billType {
		case models.BillTypeWithdrawal, models.BillTypeSwapsSend, models.BillTypeSystemDeduction, models.BillTypeTransferSend:
			if (userAssetsInfo.AvailableAmount - userAssetsInfo.FrozenAmount) < amount {
				return fmt.Errorf("%s[%s]", translate, assetsInfo.Name)
			}
		default:
			if userAssetsInfo.AvailableAmount < amount {
				return fmt.Errorf("%s[%s]", translate, assetsInfo.Name)
			}
		}
	}

	// 更新用户资产余额
	if err := tx.Model(&models.UserAssets{}).Where("id = ?", userAssetsInfo.ID).Update("available_amount", gorm.Expr("available_amount - ?", amount)).Error; err != nil {
		translate := translateService.GetTranslateByFieldWithCacheToArgs(rdsConn, 0, acceptLang, "dataError", err.Error())
		return errors.New(translate)
	}

	// 创建账单记录
	billService := NewWalletBillService()
	if err := billService.CreateBill(tx, userInfo, assetsInfo, billType, sourceID, -amount); err != nil {
		translate := translateService.GetTranslateByFieldWithCacheToArgs(rdsConn, 0, acceptLang, "dataError", err.Error())
		return errors.New(translate)
	}

	// 资产分销奖励
	if userInfo.ParentID > 0 {
		distributionReward := make([]*models.AdminSettingDistributionReward, 0)
		merchantID, _ := adminService.GetMerchantIDWithCache(rdsConn, userInfo.AdminID)
		settingService.GetAdminSettingByFieldWithCache(rdsConn, merchantID, "walletDistributionReward").ToInterface(&distributionReward)
		s.DistributionAssetsReward(tx, rdsConn, acceptLang, sourceID, billType, userInfo.ParentID, 1, assetsInfo, amount, distributionReward)
	}
	return nil
}

// IncreaseAssets 增加资产方法
func (s *WalletService) IncreaseAssets(tx *gorm.DB, rdsConn redis.Conn, acceptLang string, billType int8, sourceID uint, userInfo *models.User, assetsInfo *models.WalletAssets, amount float64) error {
	translateService := NewTranslateService()
	adminService := NewAdminUserService()
	settingService := NewAdminSettingService()

	// 添加 redis 锁
	lock := cache.NewRedisLock(rdsConn, fmt.Sprintf("walletAssetsLock_%d", assetsInfo.ID), 30)
	ok, err := lock.Lock()
	if err != nil {
		return err
	}
	if !ok {
		return errors.New("redis lock failed")
	}
	defer lock.Unlock()

	// 检查用户资产是否存在
	userAssetsInfo := &models.UserAssets{}
	if err := tx.Model(&userAssetsInfo).Where("user_id = ?", userInfo.ID).Where("assets_id = ?", assetsInfo.ID).Find(&userAssetsInfo).Error; err != nil {
		translate := translateService.GetTranslateByFieldWithCacheToArgs(rdsConn, 0, acceptLang, "dataError", "userAssetsInfo: "+err.Error())
		return errors.New(translate)
	}
	if userAssetsInfo.ID == 0 {
		// 如果用户资产不存在，则创建用户资产
		userAssetsInfo.AdminID = userInfo.AdminID
		userAssetsInfo.UserID = userInfo.ID
		userAssetsInfo.AssetsID = assetsInfo.ID
		userAssetsInfo.AvailableAmount = 0
		userAssetsInfo.FrozenAmount = 0
		userAssetsInfo.Data = models.UserAssetsData{}
		if err := tx.Create(&userAssetsInfo).Error; err != nil {
			translate := translateService.GetTranslateByFieldWithCacheToArgs(rdsConn, 0, acceptLang, "dataError", err.Error())
			return errors.New(translate)
		}
	}

	// 创建账单记录
	billService := NewWalletBillService()
	if err := billService.CreateBill(tx, userInfo, assetsInfo, billType, sourceID, amount); err != nil {
		translate := translateService.GetTranslateByFieldWithCacheToArgs(rdsConn, 0, acceptLang, "dataError", err.Error())
		return errors.New(translate)
	}

	// 更新用户资产余额
	if err := tx.Model(&userAssetsInfo).Where("id = ?", userAssetsInfo.ID).Update("available_amount", gorm.Expr("available_amount + ?", amount)).Error; err != nil {
		translate := translateService.GetTranslateByFieldWithCacheToArgs(rdsConn, 0, acceptLang, "dataError", err.Error())
		return errors.New(translate)
	}

	// 资产分销奖励
	if userInfo.ParentID > 0 {
		distributionReward := make([]*models.AdminSettingDistributionReward, 0)
		merchantID, _ := adminService.GetMerchantIDWithCache(rdsConn, userInfo.AdminID)
		settingService.GetAdminSettingByFieldWithCache(rdsConn, merchantID, "walletDistributionReward").ToInterface(&distributionReward)
		s.DistributionAssetsReward(tx, rdsConn, acceptLang, sourceID, billType, userInfo.ParentID, 1, assetsInfo, amount, distributionReward)
	}

	return nil
}

// DistributionBalanceReward 分销余额奖励
func (s *WalletService) DistributionBalanceReward(tx *gorm.DB, rdsConn redis.Conn, acceptLang string, sourceID uint, currentBillType int8, userParentID uint, deep int8, amount float64, distributionReward []*models.AdminSettingDistributionReward) error {
	if userParentID <= 0 {
		return nil
	}

	// 过滤找不到的用户
	userInfo := models.User{}
	tx.Model(&models.User{}).Where("id = ?", userParentID).Find(&userInfo)
	if userInfo.ID == 0 {
		return nil
	}

	for _, reward := range distributionReward {
		if reward.Level == deep && reward.Type == currentBillType {
			rewardAmount := amount * reward.Rate / 100

			// 增加用户余额
			err := s.IncreaseBalance(tx, rdsConn, acceptLang, models.BillTypeDistributionReward, sourceID, &userInfo, rewardAmount)
			if err != nil {
				return err
			}
		}
	}

	// 递归处理下级用户
	if userInfo.ParentID > 0 {
		return s.DistributionBalanceReward(tx, rdsConn, acceptLang, sourceID, currentBillType, userInfo.ParentID, deep+1, amount, distributionReward)
	}
	return nil
}

// DistributionAssetsReward 分销资产奖励
func (s *WalletService) DistributionAssetsReward(tx *gorm.DB, rdsConn redis.Conn, acceptLang string, sourceID uint, currentBillType int8, userParentID uint, deep int8, assetsInfo *models.WalletAssets, amount float64, distributionReward []*models.AdminSettingDistributionReward) error {
	if userParentID <= 0 {
		return nil
	}

	// 过滤找不到的用户
	userInfo := &models.User{}
	tx.Model(&userInfo).Where("id = ?", userParentID).Find(userInfo)
	if userInfo.ID == 0 {
		return nil
	}

	for _, reward := range distributionReward {
		if reward.Level == deep && reward.Type == currentBillType {
			rewardAmount := amount * reward.Rate / 100

			// 增加用户资产余额
			err := s.IncreaseAssets(tx, rdsConn, acceptLang, models.BillTypeDistributionReward, sourceID, userInfo, assetsInfo, rewardAmount)
			if err != nil {
				return err
			}
		}
	}

	// 递归处理下级用户
	if userInfo.ParentID > 0 {
		return s.DistributionAssetsReward(tx, rdsConn, acceptLang, sourceID, currentBillType, userInfo.ParentID, deep+1, assetsInfo, amount, distributionReward)
	}
	return nil
}
