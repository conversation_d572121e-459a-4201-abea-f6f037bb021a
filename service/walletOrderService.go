package service

import (
	"fmt"
	"zfeng/core/model"
	"zfeng/models"

	"github.com/gomodule/redigo/redis"
	"gorm.io/gorm"
)

// WalletOrderService 钱包订单服务
type WalletOrderService struct {
	db *model.Model
}

// NewWalletOrderService 创建钱包订单服务
func NewWalletOrderService() *WalletOrderService {
	return &WalletOrderService{
		db: model.NewModel(),
	}
}

// UpdateOrderStaus 更新订单状态
func (s *WalletOrderService) UpdateOrderStaus(tx *gorm.DB, userInfo *models.User, rdsConn redis.Conn, acceptLang string, orderInfo *models.WalletOrder) error {
	result := tx.Save(&orderInfo)
	if result.Error != nil {
		return fmt.Errorf("订单信息更新失败: %v", result.Error)
	}

	adminSettingService := NewAdminSettingService()
	adminService := NewAdminUserService()

	// 如果状态是成功|拒绝, 那么需要进行资产和余额的操作
	if orderInfo.Status == models.WalletOrderStatusCompleted || orderInfo.Status == models.WalletOrderStatusRejected {

		walletService := NewWalletService()

		if orderInfo.AssetsID > 0 {
			// 资产的操作
			assetsInfo := &models.WalletAssets{}
			result := tx.Model(&models.WalletAssets{}).Where("id = ?", orderInfo.AssetsID).First(assetsInfo)
			if result.Error != nil {
				return fmt.Errorf("资产信息获取失败: %v", result.Error)
			}

			// 如果订单状态是成功 并且 是资产充值订单
			if orderInfo.Status == models.WalletOrderStatusCompleted && orderInfo.Type == models.WalletOrderTypeAssetDeposit {
				actualAmount := orderInfo.Money - orderInfo.Fee
				err := walletService.IncreaseAssets(tx, rdsConn, acceptLang, models.BillTypeDeposit, orderInfo.ID, userInfo, assetsInfo, actualAmount)
				if err != nil {
					return fmt.Errorf("资产充值失败: %v", err)
				}
			}

			// 如果订单状态是拒绝 并且 是资产提现订单
			if orderInfo.Status == models.WalletOrderStatusRejected && orderInfo.Type == models.WalletOrderTypeAssetWithdrawal {
				err := walletService.IncreaseAssets(tx, rdsConn, acceptLang, models.BillTypeWithdrawalReject, orderInfo.ID, userInfo, assetsInfo, orderInfo.Money)
				if err != nil {
					return fmt.Errorf("资产提现失败: %v", err)
				}
			}
		} else {
			// 如果订单状态是成功 并且 是充值订单
			if orderInfo.Status == models.WalletOrderStatusCompleted && orderInfo.Type == models.WalletOrderTypeDeposit {
				actualAmount := orderInfo.Money - orderInfo.Fee
				if orderInfo.Data.Currency != "" {
					assetsInfo := models.WalletAssets{}
					tx.Model(&models.WalletAssets{}).Where("currency = ?", orderInfo.Data.Currency).Where("admin_id = ?", orderInfo.AdminID).Find(&assetsInfo)
					if assetsInfo.ID != 0 {
						actualAmount = orderInfo.Money*assetsInfo.Rate - orderInfo.Fee
					}
				}
				err := walletService.IncreaseBalance(tx, rdsConn, acceptLang, models.BillTypeDeposit, orderInfo.ID, userInfo, actualAmount)
				if err != nil {
					return fmt.Errorf("余额充值失败: %v", err)
				}

				// 判断是否充值金额升级等级
				merchantAdminId, _ := adminService.GetMerchantIDWithCache(rdsConn, userInfo.AdminID)
				buyLevelStr, _ := adminSettingService.GetAdminSettingByFieldWithCache(rdsConn, merchantAdminId, models.AdminSettingBuyLevelOptions).ToString()
				if buyLevelStr == models.UserLevelBuyDeposit {
					var sumDeposit float64
					tx.Model(&models.WalletOrder{}).Where("user_id = ? AND assets_id = 0 AND type = ? AND status = ?", userInfo.ID, models.WalletOrderTypeDeposit, models.WalletOrderStatusCompleted).Select("sum(money)").Scan(&sumDeposit)

					// 获取用户等级
					userLevel := models.UserLevel{}
					tx.Model(&models.UserLevel{}).Where("user_id = ? AND status = ?", userInfo.ID, models.UserLevelStatusEnabled).Find(&userLevel)

					var userLevelSymbol int8 = 0
					if userLevel.ID > 0 {
						latestLevel := userLevel.GetLatestLevel()
						if latestLevel != nil {
							userLevelSymbol = latestLevel.Symbol
						}
					}
					buyLevelMannerStr, _ := adminSettingService.GetAdminSettingByFieldWithCache(rdsConn, merchantAdminId, models.AdminSettingBuyLevelOptions2).ToString()
					// 获取大于当前用户等级, 并且大于当前总金额的等级
					systemLevel := models.Level{}
					tx.Model(&models.Level{}).Where("admin_id = ?", merchantAdminId).Where("symbol > ?", userLevelSymbol).Where("money <= ?", sumDeposit).Order("money DESC").Find(&systemLevel)
					if systemLevel.ID > 0 {
						err := models.UpdateUserLevel(tx, userInfo, &userLevel, systemLevel, buyLevelMannerStr)
						if err != nil {
							return err
						}
					}
				}
			}
			// 如果订单状态是拒绝 并且 是提现订单
			if orderInfo.Status == models.WalletOrderStatusRejected && orderInfo.Type == models.WalletOrderTypeWithdrawal {
				err := walletService.IncreaseBalance(tx, rdsConn, acceptLang, models.BillTypeWithdrawalReject, orderInfo.ID, userInfo, orderInfo.Money)
				if err != nil {
					return fmt.Errorf("余额提现失败: %v", err)
				}
			}
		}
	}
	return nil
}
