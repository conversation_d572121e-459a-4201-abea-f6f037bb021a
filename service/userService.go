package service

import (
	"zfeng/core/context"
	"zfeng/core/model"
	"zfeng/middleware"
	"zfeng/models"
	"zfeng/utils"

	"github.com/gomodule/redigo/redis"
)

// UserService 用户服务结构体
type UserService struct {
	db *model.Model
}

// NewUserService 创建新的用户服务实例
func NewUserService() *UserService {
	return &UserService{
		db: model.NewModel(),
	}
}

// GetUserLevelInfo 获取用户等级信息
func (s *UserService) GetUserLevelInfo(rdsConn redis.Conn, merchantID uint, userID uint, lang string) models.UserLevelInfo {
	userLevel := &models.UserLevel{}
	s.db.Where("user_id = ?", userID).Where("status = ?", models.UserLevelStatusEnabled).Find(userLevel)

	userLevelInfo := models.UserLevelInfo{}
	// 初始化空切片
	userLevelInfo.Data.Levels = []models.Level{}

	if userLevel.ID > 0 && len(userLevel.Data.Levels) > 0 {
		translateService := NewTranslateService()

		// 根据购买类型获取等级信息
		var level models.Level
		if userLevel.Data.BuyType == models.UserLevelBuyTypeClassification {
			// 按等级分类购买：获取最高等级
			highestSymbol := int8(-1)
			for _, l := range userLevel.Data.Levels {
				if l.Symbol > highestSymbol {
					highestSymbol = l.Symbol
					level = l
				}
			}
		} else {
			// 全额购买和补差价购买：只有一个等级
			level = userLevel.Data.Levels[0]
		}

		userLevelInfo.Name = translateService.GetTranslatesByFieldsWithCache(rdsConn, merchantID, lang, level.Name)
		userLevelInfo.Icon = level.Icon
		userLevelInfo.Symbol = level.Symbol
		userLevelInfo.ExpiredAt = userLevel.ExpiredAt
		userLevelInfo.Data = userLevel.Data
	}

	return userLevelInfo
}

// GetUserAuthStatus 获取用户实名状态
func (s *UserService) GetUserAuthStatus(rdsConn redis.Conn, merchantID uint, userID uint, mode int8) int8 {
	authInfo := &models.UserAuth{}
	s.db.Where("user_id = ?", userID).Where("mode = ?", mode).Find(authInfo)

	authStatus := models.UserAuthStatusPending
	if authInfo.ID > 0 {
		authStatus = authInfo.Status
	}
	return authStatus
}

// GetTokenClaims 获取用户TokenClaims
func (s *UserService) GetTokenClaims(c *context.CustomCtx) *context.TokenClaims {
	tokenStr := utils.GetToken(c.Ctx)
	var claims *context.TokenClaims
	var err error
	if tokenStr != "" {
		claims, err = middleware.ValidateToken(tokenStr, middleware.FrontendPublicKey)
		if err == nil {
			return claims
		}
	}

	// 如果token不存在或无效，根据域名获取管理员和商户信息
	adminService := NewAdminUserService()
	claims = &context.TokenClaims{}
	if adminID, err := adminService.GetAdminUserByDomainWithCache(c.Rds, c.OriginHost); err == nil {
		claims.AdminID = adminID
		claims.MerchantID, _ = adminService.GetMerchantIDWithCache(c.Rds, adminID)
	}

	return claims
}
