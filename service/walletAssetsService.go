package service

import (
	"github.com/gomodule/redigo/redis"
	"zfeng/core/databases"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
)

// WalletAssetsService 钱包资产服务
type WalletAssetsService struct {
	db *model.Model
}

// NewWalletAssetsService 创建钱包资产服务
func NewWalletAssetsService() *WalletAssetsService {
	return &WalletAssetsService{
		db: model.NewModel(),
	}
}

// GetAssetsOptions 获取资产选项
func (s *WalletAssetsService) GetAssetsOptions(subAdminIDs []uint) []*views.SelectOption {
	var assets []struct {
		ID        uint
		Name      string
		AdminName string
	}
	s.db.Table("wallet_assets wa").
		Select("wa.id", "wa.name", "adm.username as admin_name").
		Joins("left join admin_user adm on wa.admin_id = adm.id").
		Where("wa.admin_id IN ?", subAdminIDs).
		Where("wa.deleted_at IS NULL").
		Where("wa.status = ?", models.WalletAssetsStatusEnabled).
		Find(&assets)

	options := make([]*views.SelectOption, len(assets))
	for i, asset := range assets {
		options[i] = &views.SelectOption{
			Label: asset.Name + " (" + asset.AdminName + ")",
			Value: asset.ID,
		}
	}
	return options
}

// GetMerchantWalletAssets 获取商户钱包资产
func (s *WalletAssetsService) GetMerchantWalletAssets(merchantID uint) []*models.WalletAssets {
	var walletAssetsList []*models.WalletAssets
	s.db.Model(&models.WalletAssets{}).Where("admin_id = ?", merchantID).Find(&walletAssetsList)
	return walletAssetsList
}

// GetAssetsInfo 获取资产信息
func (s *WalletAssetsService) GetAssetsInfo(rds redis.Conn, t *TranslateService, lang string, assetsId, merchantID, userId uint) models.AssetsInfo {
	assetsInfo := models.AssetsInfo{}
	if assetsInfo.ID == 0 && userId > 0 {
		assetsInfo.Name = t.GetTranslatesByFieldsWithCache(rds, merchantID, lang, "balance")
		assetsInfo.Currency = "USD"
		assetsInfo.Icon = "/icons/balance.png"
		userInfo := models.User{}
		if result := databases.Db.Where("id = ?", userId).
			Where("status = ?", models.UserStatusActive).
			Find(&userInfo); result.Error == nil {
			assetsInfo.UserAssetsInfo = models.UserAssetsInfo{
				ID:              userInfo.ID,
				Icon:            "/icons/balance.png",
				AssetsID:        int(userInfo.ID),
				AvailableAmount: userInfo.AvailableAmount,
			}
		}
	}

	// 获取用户资产, 如果为0那么使用余额
	databases.Db.Model(&assetsInfo).
		Preload("UserAssetsInfo", databases.Db.Where("user_id = ?", userId)).
		Where("id = ?", assetsId).
		Where("admin_id = ?", merchantID).
		Find(&assetsInfo)
	return assetsInfo
}
