package service

import (
	"fmt"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"

	"github.com/goccy/go-json"
	"github.com/gomodule/redigo/redis"
)

const (
	frontendMenusCacheKey = "frontendMenusCacheKey"
)

// SystemMenuService 前台菜单服务结构体
type SystemMenuService struct {
	db *model.Model
}

// NewSystemMenuService 创建新的前台菜单服务实例
func NewSystemMenuService() *SystemMenuService {
	return &SystemMenuService{
		db: model.NewModel(),
	}
}

// GetParentMenuOptions 获取父级菜单选项
func (s *SystemMenuService) GetParentMenuOptions(merchantID uint) ([]*views.SelectOption, error) {
	var menus []*models.Menu
	var parentIDs []uint

	// 找到所有 parentID 不等于 0 的记录
	if err := s.db.Model(&models.Menu{}).Where("admin_id = ? AND parent_id != 0", merchantID).
		Distinct("parent_id").
		Pluck("parent_id", &parentIDs).Error; err != nil {
		return nil, err
	}

	// 查找所有可能作为父级的菜单
	if err := s.db.Where("admin_id = ? AND id IN ?", merchantID, parentIDs).
		Order("sort ASC").
		Find(&menus).Error; err != nil {
		return nil, err
	}

	// 构建选项
	options := make([]*views.SelectOption, 0, len(menus))
	options = append(options, &views.SelectOption{
		Label: "顶级菜单",
		Value: 0,
	})
	for _, menu := range menus {
		options = append(options, &views.SelectOption{
			Label: menu.Name,
			Value: menu.ID,
		})
	}

	return options, nil
}

// GetFrontendMenusByTypeWithCache 获取商户缓存前台类型菜单列表
func (s *SystemMenuService) GetFrontendMenusByTypeWithCache(rdsConn redis.Conn, lang string, merchantID uint, menuType int8) ([]*models.MenuDisplayData, error) {
	cacheKey := fmt.Sprintf("%s:%v:%s", frontendMenusCacheKey, menuType, lang)
	// 尝试从缓存中获取数据
	cachedData, err := redis.Bytes(rdsConn.Do("HGET", cacheKey, merchantID))
	if err == nil {
		var menus []*models.MenuDisplayData
		if err = json.Unmarshal(cachedData, &menus); err == nil {
			return menus, nil
		}
	}

	// 如果缓存中没有数据，从数据库中获取
	menus, err := s.getFrontendMenusByTypeRecursively(rdsConn, lang, merchantID, 0, menuType)
	if err != nil {
		return nil, err
	}

	// 将数据存入缓存
	if cachedData, err := json.Marshal(menus); err == nil {
		_, _ = rdsConn.Do("HSET", cacheKey, merchantID, cachedData)
	}

	return menus, nil
}

// getFrontendMenusByTypeRecursively 递归获取前台菜单
func (s *SystemMenuService) getFrontendMenusByTypeRecursively(rdsConn redis.Conn, lang string, merchantID, parentID uint, menuType int8) ([]*models.MenuDisplayData, error) {
	var menus []*models.MenuDisplayData
	if err := s.db.Model(&models.Menu{}).Where("admin_id = ? AND type = ? AND parent_id = ? AND status = ?", merchantID, menuType, parentID, models.MenuStatusEnabled).
		Order("sort ASC").
		Find(&menus).Error; err != nil {
		return nil, err
	}

	translateService := NewTranslateService()
	for _, menu := range menus {
		children, err := s.getFrontendMenusByTypeRecursively(rdsConn, lang, merchantID, menu.ID, menuType)
		if err != nil {
			return nil, err
		}
		menu.Data.Label = translateService.GetTranslatesByFieldsWithCache(rdsConn, merchantID, lang, menu.Data.Label)
		if menu.Data.Small != "" {
			menu.Data.Small = translateService.GetTranslatesByFieldsWithCache(rdsConn, merchantID, lang, menu.Data.Small)
		}
		menu.Name = menu.Data.Label
		menu.Children = children
	}

	return menus, nil
}

// DeleteFrontendMenusCache 删除商户前台菜单缓存
func (s *SystemMenuService) DeleteFrontendMenusCache(rdsConn redis.Conn, merchantID uint, menuType int8) error {
	langs, _ := NewSystemLangService().GetAdminLanguagesWithCache(rdsConn, merchantID)
	for _, lang := range langs {
		s.DeleteFrontendMenusByTypeCache(rdsConn, merchantID, menuType, lang.Symbol)
	}
	return nil
}

// DeleteFrontendMenusByTypeCache 删除商户前台菜单缓存
func (s *SystemMenuService) DeleteFrontendMenusByTypeCache(rdsConn redis.Conn, merchantID uint, menuType int8, lang string) error {
	cacheKey := fmt.Sprintf("%s:%v:%s", frontendMenusCacheKey, menuType, lang)
	// 删除指定商户的前台菜单缓存
	_, err := rdsConn.Do("HDEL", cacheKey, merchantID)
	return err
}

// DeleteFrontendMenusAllCacheByLang 删除商户前台菜单缓存
func (s *SystemMenuService) DeleteFrontendMenusAllCacheByLang(rdsConn redis.Conn, merchantID uint, lang string) error {
	s.DeleteFrontendMenusByTypeCache(rdsConn, merchantID, models.MenuTypeMobileNavigationBar, lang)
	s.DeleteFrontendMenusByTypeCache(rdsConn, merchantID, models.MenuTypeDesktopNavigationBar, lang)
	s.DeleteFrontendMenusByTypeCache(rdsConn, merchantID, models.MenuTypeCommonUserMenu, lang)
	s.DeleteFrontendMenusByTypeCache(rdsConn, merchantID, models.MenuTypeCommonUserMoreMenu, lang)
	s.DeleteFrontendMenusByTypeCache(rdsConn, merchantID, models.MenuTypeCommonWalletMenu, lang)
	return nil
}
