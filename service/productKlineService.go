package service

import (
	"time"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/module/exchange/interfaces"
	"zfeng/module/exchange/utils"
)

// ProductKlineService k线图服务
type ProductKlineService struct {
	db *model.Model
}

// NewProductKlineService 创建k线图服务
func NewProductKlineService() *ProductKlineService {
	return &ProductKlineService{
		db: model.NewModel(),
	}
}

// GenerateKline 生成K线图
func (p *ProductKlineService) GenerateKline(targetPrice float64, startTime, endTime time.Time, startProductKline *models.ProductKline) []*models.ProductKline {
	startPrice := startProductKline.ClosePrice
	endPrice := startPrice
	difference := targetPrice - startPrice
	interval := 10
	index := int(endTime.Sub(startTime).Minutes())
	klineList := make([]*interfaces.KlineAttrs, 0)
	for i := 0; i < index; i += interval {
		if difference > 100 { // 如果价格差距大于100则根据生成幅度调整
			tmpPrice := difference / float64(index/interval)
			endPrice += tmpPrice
		} else if difference < -100 { // 如果价格差距小于-100则根据生成幅度调整
			tmpPrice := difference / float64(index/interval)
			startPrice -= tmpPrice
		} else {
			interval = index
		}
		if interval+i >= index {
			interval = index - i
		}
		if endPrice > targetPrice || interval+i >= index {
			endPrice = targetPrice
		}

		kLines := utils.GenerateKline(startPrice, endPrice, startTime.Truncate(time.Minute), startTime.Add(time.Duration(interval)*time.Minute).Truncate(time.Minute))
		if len(kLines) > 0 {
			klineList = append(klineList, kLines...)
			startPrice = kLines[len(kLines)-1].ClosePrice
			startTime = startTime.Add(time.Duration(interval) * time.Minute)
			continue
		}
		break
	}

	productKlines := make([]*models.ProductKline, 0)
	for _, kline := range klineList {
		newProductKline := models.ProductKline{}
		newProductKline.ProductSymbol = startProductKline.ProductSymbol
		newProductKline.OpenPrice = kline.OpenPrice
		newProductKline.ClosePrice = kline.ClosePrice
		newProductKline.HighPrice = kline.HighPrice
		newProductKline.LowsPrice = kline.LowsPrice
		newProductKline.Vol = kline.Vol
		newProductKline.CreatedAt = time.Unix(kline.CreatedAt, 0)
		productKlines = append(productKlines, &newProductKline)
	}

	return productKlines
}
