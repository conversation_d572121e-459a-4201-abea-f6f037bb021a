package service

import (
	"errors"
	"fmt"
	"strings"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"

	"github.com/goccy/go-json"
	"github.com/gomodule/redigo/redis"
)

const (
	// rootAdminCacheKey 是用于缓存根管理员ID的Redis键前缀
	rootAdminCacheKey = "rootAdminCacheKey"
	// rootAdminCacheExpiration  是根管理员ID缓存的过期时间（秒）
	rootAdminCacheExpiration = 86400

	// adminUserByDomain 域名查询管理ID
	adminUserByDomainCacheKey = "adminUserByDomainCacheKey"
	// adminUserByDomainExpiration 域名查询管理过期时间
	adminUserByDomainExpiration = 86400

	// adminChatURLCacheKey 客服链接缓存
	adminChatURLCacheKey = "adminChatURLCacheKey"
	// adminChatURLExpiration 客服链接过期时间
	adminChatURLExpiration = 86400

	// adminSubadminIDsCacheKey 获取当前管理ID及其子集的ID数组并使用缓存保存
	adminSubadminIDsCacheKey = "adminSubadminIDsCacheKey"
	// adminSubadminIDsExpiration 获取当前管理ID及其子集的ID数组过期时间
	adminSubadminIDsExpiration = 86400
)

// AdminUserService 管理用户服务结构体
type AdminUserService struct {
	db *model.Model
}

// NewAdminUserService 创建新的管理用户服务实例
func NewAdminUserService() *AdminUserService {
	return &AdminUserService{
		db: model.NewModel(),
	}
}

// GetSubAdminIDsWithCache 获取当前管理ID及其子集的ID数组并使用缓存保存
func (s *AdminUserService) GetSubAdminIDsWithCache(rdsConn redis.Conn, currentID uint) ([]uint, error) {
	// 尝试从缓存中获取数据
	cachedData, err := redis.Bytes(rdsConn.Do("GET", fmt.Sprintf("%s:%d", adminSubadminIDsCacheKey, currentID)))
	if err == nil {
		var subAdminIDs []uint
		if err = json.Unmarshal(cachedData, &subAdminIDs); err == nil {
			return subAdminIDs, nil
		}
	}

	// 如果缓存中没有数据，从数据库中递归获取
	subAdminIDs, err := s.getSubAdminIDsRecursively(currentID)
	if err != nil {
		return nil, err
	}

	// 将数据存入缓存
	if len(subAdminIDs) > 0 {
		cachedData, _ := json.Marshal(subAdminIDs)
		_, _ = rdsConn.Do("SETEX", fmt.Sprintf("%s:%d", adminSubadminIDsCacheKey, currentID), adminSubadminIDsExpiration, cachedData)
	}

	return subAdminIDs, nil
}

// getSubAdminIDsRecursively 递归获取所有子管理员ID
func (s *AdminUserService) getSubAdminIDsRecursively(adminID uint) ([]uint, error) {
	var subAdmins []models.AdminUser
	err := s.db.Where("parent_id = ?", adminID).Select("id").Find(&subAdmins).Error
	if err != nil {
		return nil, err
	}

	subAdminIDs := []uint{adminID}
	for _, admin := range subAdmins {
		childIDs, err := s.getSubAdminIDsRecursively(admin.ID)
		if err != nil {
			return nil, err
		}
		subAdminIDs = append(subAdminIDs, childIDs...)
	}

	return subAdminIDs, nil
}

// GetAdminUserByDomainWithCache 根据域名查询缓存管理用户
func (s *AdminUserService) GetAdminUserByDomainWithCache(rdsConn redis.Conn, domain string) (uint, error) {
	cacheKey := fmt.Sprintf("%s:%s", adminUserByDomainCacheKey, domain)

	// 尝试从缓存中获取数据
	cachedData, err := redis.Uint64(rdsConn.Do("GET", cacheKey))
	if err == nil {
		return uint(cachedData), nil
	}

	// 如果缓存中没有数据，从数据库中获取
	var adminUser models.AdminUser
	err = s.db.Where("FIND_IN_SET(?, domains) > 0 AND status = ?", domain, models.AdminUserStatusEnabled).First(&adminUser).Error
	if err != nil {
		return 0, errors.New("the current domain name is not bound to any administrator")
	}

	// 将数据存入缓存
	_, _ = rdsConn.Do("SETEX", cacheKey, adminUserByDomainExpiration, adminUser.ID) // 缓存1小时

	return adminUser.ID, nil
}

// CheckDomainValidity 检查域名是否合法
func (s *AdminUserService) CheckDomainValidity(rdsConn redis.Conn, adminInfo models.AdminUser, newDomains string) error {
	if newDomains == "" {
		return nil
	}

	// 检查新域名是否已被其他管理员使用
	var existingAdmin models.AdminUser
	newDomainsList := strings.Split(newDomains, ",")
	for _, domain := range newDomainsList {
		err := s.db.Where("FIND_IN_SET(?, domains) > 0 AND id != ?", domain, adminInfo.ID).Find(&existingAdmin).Error
		if err != nil {
			return err
		}
		if existingAdmin.ID > 0 {
			return fmt.Errorf("域名 %s 已被其他管理员使用", domain)
		}
		s.DeleteAdminUserByDomainCache(rdsConn, domain)
	}

	// 删除当前管理员域名对应的缓存
	oldDomainsList := strings.Split(adminInfo.Domains, ",")
	for _, domain := range oldDomainsList {
		s.DeleteAdminUserByDomainCache(rdsConn, domain)
	}
	return nil
}

// GetChatURLWithCache 获取客服链接使用缓存
func (s *AdminUserService) GetChatURLWithCache(rdsConn redis.Conn, adminID uint) (string, error) {
	cacheKey := fmt.Sprintf("%s:%d", adminChatURLCacheKey, adminID)
	cachedData, err := redis.String(rdsConn.Do("GET", cacheKey))
	if err == nil {
		return cachedData, nil
	}

	var adminUser models.AdminUser
	err = s.db.First(&adminUser, adminID).Error
	if err != nil {
		return "", err
	}

	_, _ = rdsConn.Do("SETEX", cacheKey, adminChatURLExpiration, adminUser.ChatURL)
	return adminUser.ChatURL, nil
}

// GetMerchantIDWithCache 获取商户ID使用缓存
func (s *AdminUserService) GetMerchantIDWithCache(rdsConn redis.Conn, adminID uint) (uint, error) {
	// 尝试从Redis缓存中获取
	cacheKey := fmt.Sprintf("%s:%d", rootAdminCacheKey, adminID)
	cachedID, err := redis.Uint64(rdsConn.Do("GET", cacheKey))
	if err == nil {
		return uint(cachedID), nil
	}

	var adminUser models.AdminUser
	err = s.db.Select("id", "parent_id").First(&adminUser, adminID).Error
	if err != nil {
		return 0, err
	}

	var rootID uint
	if adminUser.ParentID == 0 || adminUser.ParentID == models.SuperAdminID {
		rootID = adminUser.ID
	} else {
		rootID, err = s.GetMerchantIDWithCache(rdsConn, adminUser.ParentID)
		if err != nil {
			return 0, err
		}
	}

	// 将结果存入Redis缓存
	_, _ = rdsConn.Do("SETEX", cacheKey, rootAdminCacheExpiration, rootID) // 缓存24小时
	return rootID, nil
}

// GetSubAdminOptions 获取下级管理 options
func (s *AdminUserService) GetSubAdminOptions(adminID uint) []*views.SelectOption {
	var allAdmins []models.AdminUser
	err := s.db.Select("id", "parent_id", "username").Find(&allAdmins).Error
	if err != nil {
		return nil
	}

	currentAdmin := models.AdminUser{}
	for _, admin := range allAdmins {
		if admin.ID == adminID {
			currentAdmin = admin
			break
		}
	}

	// 递归函数来获取所有子管理员
	var getSubAdmins func(uint) []models.AdminUser
	getSubAdmins = func(id uint) []models.AdminUser {
		result := []models.AdminUser{}
		for _, admin := range allAdmins {
			if admin.ParentID == id {
				result = append(result, admin)
				result = append(result, getSubAdmins(admin.ID)...)
			}
		}
		return result
	}

	// 获取当前管理员及其所有子管理员
	subAdmins := append([]models.AdminUser{{BaseModel: model.BaseModel{ID: currentAdmin.ID}, Username: currentAdmin.Username}}, getSubAdmins(adminID)...)

	// 创建选项
	options := make([]*views.SelectOption, len(subAdmins))
	for i, admin := range subAdmins {
		options[i] = &views.SelectOption{
			Label: admin.Username,
			Value: admin.ID,
		}
	}

	return options
}

// DeleteAdminUserByDomainCache 删除指定域名的管理员用户缓存
func (s *AdminUserService) DeleteAdminUserByDomainCache(rdsConn redis.Conn, domain string) error {
	cacheKey := fmt.Sprintf("%s:%s", adminUserByDomainCacheKey, domain)
	_, err := rdsConn.Do("DEL", cacheKey)
	return err
}

// DeleteAdminUserByChatURL 删除制定用户的客服链接缓存
func (s *AdminUserService) DeleteAdminUserByChatURL(rdsConn redis.Conn, adminID uint) error {
	cacheKey := fmt.Sprintf("%s:%d", adminChatURLCacheKey, adminID)
	_, err := rdsConn.Do("DEL", cacheKey)
	return err
}

// DeleteAdminSubadminIDsCache 删除指定管理员的子管理员ID缓存
func (s *AdminUserService) DeleteAdminSubadminIDsCache(rdsConn redis.Conn, adminID uint) error {
	_, err := rdsConn.Do("DEL", fmt.Sprintf("%s:%d", adminSubadminIDsCacheKey, adminID))
	return err
}
