package service

import (
	"zfeng/core/model"
	"zfeng/models"
)

// SystemChannelService 渠道服务
type SystemChannelService struct {
	db *model.Model
}

// NewSystemChannelService 创建渠道服务
func NewSystemChannelService() *SystemChannelService {
	return &SystemChannelService{
		db: model.NewModel(),
	}
}

// GetChannelOptions 获取渠道列表
func (s *SystemChannelService) GetChannelOptions(merchantID uint) ([]*models.ChannelDisplayData, error) {
	channels := []*models.ChannelDisplayData{}
	result := s.db.Model(&models.Channel{}).Where("type = ?", models.ChannelTypeDefault).Where("admin_id = ?", merchantID).Where("status = ?", models.ChannelStatusEnabled).Find(&channels)
	if result.Error != nil {
		return nil, result.Error
	}
	return channels, nil
}
