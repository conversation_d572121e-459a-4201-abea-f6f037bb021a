package service

import (
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"

	"github.com/goccy/go-json"
	"github.com/gomodule/redigo/redis"
)

const (
	adminLanguagesCacheKey = "adminLanguagesCacheKey"
)

// SystemLangService 系统语言服务结构体
type SystemLangService struct {
	db *model.Model
}

// NewSystemLangService 创建新的系统语言服务实例
func NewSystemLangService() *SystemLangService {
	return &SystemLangService{
		db: model.NewModel(),
	}
}

// GetAdminLanguagesWithCache 获取商户所有缓存语言列表
func (s *SystemLangService) GetAdminLanguagesWithCache(rdsConn redis.Conn, merchantID uint) ([]*models.LangDisplayData, error) {
	// 尝试从缓存中获取数据
	cachedData, err := redis.Bytes(rdsConn.Do("HGET", adminLanguagesCacheKey, merchantID))
	if err == nil {
		var languages []*models.LangDisplayData
		if err := json.Unmarshal(cachedData, &languages); err == nil {
			return languages, nil
		}
	}

	// 如果缓存中没有数据，从数据库中获取
	languages := make([]*models.Lang, 0)
	if err := s.db.Where("admin_id = ? AND status = ?", merchantID, models.LangStatusEnabled).
		Order("sort ASC").
		Find(&languages).Error; err != nil {
		return nil, err
	}

	langDisplayData := make([]*models.LangDisplayData, len(languages))
	for i, lang := range languages {
		langDisplayData[i] = &models.LangDisplayData{
			ID:     lang.ID,
			Name:   lang.Name,
			Icon:   lang.Icon,
			Symbol: lang.Symbol,
			Alias:  lang.Alias,
		}
	}

	// 将数据存入缓存
	if cachedData, err := json.Marshal(langDisplayData); err == nil {
		_, _ = rdsConn.Do("HSET", adminLanguagesCacheKey, merchantID, cachedData)
	}

	return langDisplayData, nil
}

// GetAdminLangOptions 获取商户语言选项
func (s *SystemLangService) GetAdminLangOptions(rdsConn redis.Conn, merchantID uint) ([]*views.SelectOption, error) {
	languages, err := s.GetAdminLanguagesWithCache(rdsConn, merchantID)
	if err != nil {
		return nil, err
	}

	options := make([]*views.SelectOption, len(languages))
	for i, lang := range languages {
		options[i] = &views.SelectOption{
			Label: lang.Name,
			Value: lang.Symbol,
		}
	}

	return options, nil
}

// DeleteAdminLanguagesCache 删除商户语言缓存
func (s *SystemLangService) DeleteAdminLanguagesCache(rdsConn redis.Conn, merchantID uint) error {
	// 删除指定商户的语言缓存
	_, err := rdsConn.Do("HDEL", adminLanguagesCacheKey, merchantID)
	return err
}
