package service

import (
	"errors"
	"fmt"
	"zfeng/core/model"
	"zfeng/middleware"
	"zfeng/models"

	"github.com/gomodule/redigo/redis"

	"gorm.io/gorm"
)

var walletAssetsMaps = make(map[uint]uint)

// MerchantService 商户服务
type MerchantService struct {
	db *model.Model
}

// NewMerchantService 创建新的商户服务实例
func NewMerchantService() *MerchantService {
	return &MerchantService{
		db: model.NewModel(),
	}
}

// ResetMerchantData 商户数据重置
func (s *MerchantService) ResetMerchantData(rdsConn redis.Conn, merchantID uint) error {
	adminInfo := models.AdminUser{}
	s.db.Where("id = ?", merchantID).Where("parent_id = ?", models.SuperAdminID).Find(&adminInfo)
	if adminInfo.ID == 0 {
		return errors.New("商户管理不存在~")
	}

	adminService := NewAdminUserService()
	subAdminIDs, err := adminService.getSubAdminIDsRecursively(merchantID)
	if err != nil {
		return err
	}

	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 删除商户数据
		err = s.DeleteMerchantData(tx, merchantID, subAdminIDs)
		if err != nil {
			return err
		}

		// 初始化商户数据
		err = s.InitMerchantData(tx, merchantID)
		if err != nil {
			return err
		}

		// 删除商户缓存
		return s.DeleteAllMerchantCacheData(rdsConn, merchantID)
	})

	return err
}

func (s *MerchantService) InitMerchantData(tx *gorm.DB, merchantID uint) error {
	// 分类, 产品, 前台菜单, 钱包资产, 钱包支付
	initModels := []initModel{
		{model: &models.AdminSetting{}, selectQuery: fmt.Sprintf("*,%v as admin_id,0 as id", merchantID), whereQuery: "admin_id = ?", whereArgs: []interface{}{models.SuperAdminID}},
		{model: &models.Article{}, selectQuery: fmt.Sprintf("*,%v as admin_id,0 as id", merchantID), whereQuery: "admin_id = ?", whereArgs: []interface{}{models.SuperAdminID}},
		{model: &models.Channel{}, selectQuery: fmt.Sprintf("*,%v as admin_id,0 as id, CONCAT('', FLOOR(RAND() * 100000000000)) as app_id", merchantID), whereQuery: "admin_id = ?", whereArgs: []interface{}{models.SuperAdminID}},
		{model: &models.Country{}, selectQuery: fmt.Sprintf("*,%v as admin_id,0 as id", merchantID), whereQuery: "admin_id = ?", whereArgs: []interface{}{models.SuperAdminID}},
		{model: &models.Lang{}, selectQuery: fmt.Sprintf("*,%v as admin_id,0 as id", merchantID), whereQuery: "admin_id = ?", whereArgs: []interface{}{models.SuperAdminID}},
		{model: &models.Level{}, selectQuery: fmt.Sprintf("*,%v as admin_id,0 as id", merchantID), whereQuery: "admin_id = ?", whereArgs: []interface{}{models.SuperAdminID}},
		{model: &models.Translate{}, selectQuery: fmt.Sprintf("*,%v as admin_id,0 as id", merchantID), whereQuery: "admin_id = ?", whereArgs: []interface{}{models.SuperAdminID}},
		{model: &models.WalletPayment{}, selectQuery: fmt.Sprintf("*,%v as admin_id,0 as id", merchantID), whereQuery: "admin_id = ? AND assets_id = ?", whereArgs: []interface{}{models.SuperAdminID, 0}},
	}

	// 初始化钱包资产对应的 钱包支付 - 必须在其他使用钱包资产之前执行
	assetsList := []*walletAssetsPayment{}
	s.db.Model(&models.WalletAssets{}).Preload("WalletPayment").Where("admin_id = ?", models.SuperAdminID).Find(&assetsList)
	for _, v := range assetsList {
		oldID := v.ID
		v.WalletAssets.AdminID = merchantID
		v.WalletAssets.ID = 0
		result := tx.Create(&v.WalletAssets)
		if result.Error != nil {
			return result.Error
		}
		walletAssetsMaps[oldID] = v.WalletAssets.ID

		for _, payment := range v.WalletPayment {
			payment.AdminID = merchantID
			payment.ID = 0
			payment.AssetsID = v.WalletAssets.ID
			result := tx.Create(&payment)
			if result.Error != nil {
				return result.Error
			}
		}
	}

	// 初始化商户菜单
	err := s.RecursivelyMenu(tx, 0, merchantID, 0)
	if err != nil {
		return err
	}

	// 初始化商户分类 - 产品
	err = s.RecursivelyCategoryProduct(tx, 0, merchantID, 0)
	if err != nil {
		return err
	}

	return s.initMerchantData(tx, initModels)
}

// DeleteMerchantData 删除商户数据
func (s *MerchantService) DeleteMerchantData(tx *gorm.DB, merchantID uint, subAdminIDs []uint) error {
	deleteModels := []deleteModel{
		{model: &models.AuthItem{}, whereQuery: "type = ? AND name LIKE ?", whereArgs: []interface{}{models.AuthItemTypeRole, "%" + fmt.Sprintf("(%v)", merchantID)}},
		{model: &models.AuthChild{}, whereQuery: "type = ? AND parent = ?", whereArgs: []interface{}{models.AuthChildTypeRoleRole, fmt.Sprintf(models.AdminRolesPrefix, merchantID)}},
		{model: &models.AuthChild{}, whereQuery: "type = ? AND parent LIKE ?", whereArgs: []interface{}{models.AuthChildTypeRolePermission, "%" + fmt.Sprintf("(%v)", merchantID)}},
		{model: &models.AdminUser{}, whereQuery: "parent_id = ?", whereArgs: []interface{}{merchantID}},
		{model: &models.AdminLogs{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.AdminSetting{}, whereQuery: "admin_id = ?", whereArgs: []interface{}{merchantID}},
		{model: &models.ExtendSetting{}, whereQuery: "admin_id = ?", whereArgs: []interface{}{merchantID}},
		{model: &models.Category{}, whereQuery: "admin_id = ?", whereArgs: []interface{}{merchantID}},
		{model: &models.Product{}, whereQuery: "admin_id = ?", whereArgs: []interface{}{merchantID}},
		{model: &models.Order{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.Article{}, whereQuery: "admin_id = ?", whereArgs: []interface{}{merchantID}},
		{model: &models.Channel{}, whereQuery: "admin_id = ?", whereArgs: []interface{}{merchantID}},
		{model: &models.Country{}, whereQuery: "admin_id = ?", whereArgs: []interface{}{merchantID}},
		{model: &models.Lang{}, whereQuery: "admin_id = ?", whereArgs: []interface{}{merchantID}},
		{model: &models.Level{}, whereQuery: "admin_id = ?", whereArgs: []interface{}{merchantID}},
		{model: &models.Menu{}, whereQuery: "admin_id = ?", whereArgs: []interface{}{merchantID}},
		{model: &models.Notify{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.Translate{}, whereQuery: "admin_id = ?", whereArgs: []interface{}{merchantID}},
		{model: &models.Access{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.User{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.UserAssets{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.UserAuth{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.UserLevel{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.Setting{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.Swaps{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.Transfer{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.WalletAccount{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.WalletAssets{}, whereQuery: "admin_id = ?", whereArgs: []interface{}{merchantID}},
		{model: &models.WalletPayment{}, whereQuery: "admin_id = ?", whereArgs: []interface{}{merchantID}},
		{model: &models.WalletBill{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.WalletOrder{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.ChatsMessages{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.ChatsSessions{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
		{model: &models.Collect{}, whereQuery: "admin_id IN ?", whereArgs: []interface{}{subAdminIDs}},
	}
	return s.deleteMerchantModels(tx, deleteModels)
}

// deleteMerchantModels 删除商户模型
func (s *MerchantService) deleteMerchantModels(tx *gorm.DB, models []deleteModel) error {
	for _, model := range models {
		result := tx.Unscoped().Where(model.whereQuery, model.whereArgs...).Delete(model.model)
		if result.Error != nil {
			return result.Error
		}
	}
	return nil
}

// syncMerchantData 同步商户数据
func (s *MerchantService) initMerchantData(tx *gorm.DB, models []initModel) error {
	for _, model := range models {
		// 找到需要同步的数据
		rows := []map[string]interface{}{}
		tx.Model(model.model).Select(model.selectQuery, model.selectArgs...).Where(model.whereQuery, model.whereArgs...).Find(&rows)

		// 批量插入数据
		result := tx.Model(model.model).CreateInBatches(rows, 500)
		if result.Error != nil {
			return result.Error
		}
	}
	return nil
}

// RecursivelyMenu 递归查询菜单
func (s *MerchantService) RecursivelyMenu(tx *gorm.DB, parentID uint, merchantID uint, merchantMenuParentID uint) error {
	var menus []*models.Menu
	err := tx.Model(&models.Menu{}).Where("parent_id = ?", parentID).Where("admin_id = ?", models.SuperAdminID).Find(&menus).Error
	if err != nil {
		return err
	}

	for _, menu := range menus {
		superParentID := menu.ID
		menu.AdminID = merchantID
		menu.ParentID = merchantMenuParentID
		menu.ID = 0

		// 插入前台菜单
		result := tx.Create(&menu)
		if result.Error != nil {
			return result.Error
		}

		err := s.RecursivelyMenu(tx, superParentID, merchantID, menu.ID)
		if err != nil {
			return err
		}
	}

	return nil
}

// RecursivelyCategoryProduct 递归查询分类 - 产品
func (s *MerchantService) RecursivelyCategoryProduct(tx *gorm.DB, parentID uint, merchantID uint, merchantCategoryParentID uint) error {
	categories := make([]*categoryProduct, 0)
	err := tx.Preload("Product").Where("parent_id = ?", parentID).Where("admin_id = ?", models.SuperAdminID).Find(&categories).Error
	if err != nil {
		return err
	}

	for _, category := range categories {
		superParentID := category.ID
		category.AdminID = merchantID
		category.ParentID = merchantCategoryParentID
		category.ID = 0

		for _, product := range category.Product {
			product.AdminID = merchantID
			product.CategoryID = 0
			product.ID = 0
			product.AssetsID = walletAssetsMaps[product.AssetsID]
			product.Data.SymbolAssetsID = walletAssetsMaps[product.Data.SymbolAssetsID]
		}

		result := tx.Create(&category)
		if result.Error != nil {
			return result.Error
		}

		err := s.RecursivelyCategoryProduct(tx, superParentID, merchantID, category.ID)
		if err != nil {
			return err
		}
	}

	return nil
}

type categoryProduct struct {
	models.Category
	Product []*models.Product `gorm:"foreignKey:CategoryID"`
}

func (cp *categoryProduct) TableName() string {
	return "category"
}

// walletAssetsPayment 钱包资产对应的 钱包支付
type walletAssetsPayment struct {
	models.WalletAssets
	WalletPayment []*models.WalletPayment `gorm:"foreignKey:AssetsID"`
}

type deleteModel struct {
	model      interface{}
	whereQuery interface{}
	whereArgs  []interface{}
}

type initModel struct {
	model       interface{}
	selectQuery interface{}
	selectArgs  []interface{}
	whereQuery  interface{}
	whereArgs   []interface{}
}

// DeleteAllMerchantCacheData 删除所有商户缓存数据
func (s *MerchantService) DeleteAllMerchantCacheData(rdsConn redis.Conn, merchantID uint) error {
	// 清除商户Token缓存
	cacheKey := fmt.Sprintf("%s:%d:*", middleware.UserTokensPrefix, merchantID)
	// 获取所有匹配的键
	keys, _ := redis.Strings(rdsConn.Do("KEYS", cacheKey))
	// 如果有匹配的键，则删除它们
	if len(keys) > 0 {
		_, _ = rdsConn.Do("DEL", redis.Args{}.AddFlat(keys)...)
	}

	// 删除商户所有翻译缓存
	_ = NewTranslateService().DeleteAllTranslateCache(rdsConn, merchantID)

	//删除商户所有设置缓存
	_ = NewAdminSettingService().DeleteAllAdminSettingCache(rdsConn, merchantID)

	//删除商户语言缓存
	_ = NewSystemLangService().DeleteAdminLanguagesCache(rdsConn, merchantID)

	//删除商户国家缓存
	_ = NewSystemCountryService().DeleteAdminCountriesCache(rdsConn, merchantID)

	userService := NewAdminUserService()
	//删除商户的子管理员ID缓存
	_ = userService.DeleteAdminSubadminIDsCache(rdsConn, merchantID)

	//删除商户前台菜单缓存
	systemMenuService := NewSystemMenuService()
	langList, _ := NewSystemLangService().GetAdminLanguagesWithCache(rdsConn, merchantID)
	for _, lang := range langList {
		_ = systemMenuService.DeleteFrontendMenusAllCacheByLang(rdsConn, merchantID, lang.Symbol)
	}
	return nil
}
