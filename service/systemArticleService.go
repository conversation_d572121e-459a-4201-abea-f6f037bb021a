package service

import (
	"fmt"
	"zfeng/core/model"
	"zfeng/models"

	"github.com/goccy/go-json"
	"github.com/gomodule/redigo/redis"
)

// SystemArticleService 文章服务层
type SystemArticleService struct {
	db *model.Model
}

// NewSystemArticleService 创建文章 服务层
func NewSystemArticleService() *SystemArticleService {
	return &SystemArticleService{
		db: model.NewModel(),
	}
}

// UpdateHelpers 更新帮助文章
func (s *SystemArticleService) UpdateHelpers(rdsConn redis.Conn, merchantId uint) error {
	// 查询所有的帮助文章
	helpersList := make([]*models.Article, 0)
	result := s.db.Where("type = ?", models.ArticleTypeHelper).Where("admin_id = ?", merchantId).Where("status = ?", models.ArticleStatusEnabled).Order("sort ASC").Find(&helpersList)
	if result.Error != nil {
		return fmt.Errorf("查询文章失败: %s", result.Error.Error())
	}

	// 新的帮助中心文章内容
	newHelpers := make([]map[string]interface{}, 0)
	for _, helper := range helpersList {
		newHelpers = append(newHelpers, map[string]interface{}{
			"name":    helper.Title,
			"content": helper.Content,
			"link":    "/article/" + helper.Symbol,
			"target":  "_self",
		})
	}

	// 更新管理设置缓存
	helpersArticleStr, _ := json.Marshal(newHelpers)
	result = model.NewModel().Model(&models.AdminSetting{}).Where("admin_id = ?", merchantId).Where("field = ?", "siteHelpCenter").Update("value", string(helpersArticleStr))
	if result.Error != nil {
		return result.Error
	}

	// 清楚缓存
	adminSettingService := NewAdminSettingService()
	adminSettingService.DeleteAdminSettingByFieldCache(rdsConn, merchantId, "siteHelpCenter")
	return nil
}
