package service

import (
	"fmt"
	"strconv"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"

	"github.com/gomodule/redigo/redis"
)

const (
	// RedisAdminSettingStopStrategy 设置管理员停盘策略库名
	RedisAdminSettingStopStrategy = "RedisAdminSettingStopStrategy"
)

type ProductCategoryService struct {
	db *model.Model
}

func NewProductCategoryService() *ProductCategoryService {
	return &ProductCategoryService{
		db: model.NewModel(),
	}
}

// GetCategoryOptions 获取分类选项
func (s *ProductCategoryService) GetCategoryOptions(rdsConn redis.Conn, subAdminIDs []uint) []*views.SelectOption {
	var categorys []struct {
		ID        uint
		Name      string
		AdminID   uint
		AdminName string
	}
	s.db.Table("category c").
		Select("c.id", "c.name", "c.admin_id", "adm.username as admin_name").
		Joins("left join admin_user adm on c.admin_id = adm.id").
		Where("c.admin_id IN ?", subAdminIDs).
		Where("c.deleted_at IS NULL").
		Find(&categorys)

	translateService := NewTranslateService()
	options := make([]*views.SelectOption, 0)
	options = append([]*views.SelectOption{{Label: "顶级分类", Value: 0}}, options...)
	for _, category := range categorys {
		categoryName, _ := translateService.GetTranslateByFieldWithCache(rdsConn, category.AdminID, "zh-CN", category.Name)
		options = append(options, &views.SelectOption{
			Label: categoryName + " (" + category.AdminName + ")",
			Value: category.ID,
		})
	}
	return options
}

func (s *ProductCategoryService) ProductCategoryChildren(rdsConn redis.Conn, acceptLanguage string, parentId uint, settingAdminId uint) []*models.CategoryDisplayData {
	productList := make([]*models.CategoryDisplayData, 0)
	s.db.Model(&models.Category{}).
		Where("admin_id = ?", settingAdminId).
		Where("parent_id = ?", parentId).
		Where("status = ?", models.CategoryStatusEnabled).
		Where("type != ?", models.CategoryTypeStaking).
		Order("sort ASC").
		Find(&productList)
	if len(productList) == 0 {
		return productList
	}

	translateService := NewTranslateService()
	for _, product := range productList {
		product.Name = translateService.GetTranslatesByFieldsWithCache(rdsConn, settingAdminId, acceptLanguage, product.Name)
		product.Children = s.ProductCategoryChildren(rdsConn, acceptLanguage, product.ID, settingAdminId)
	}
	return productList
}

// ProductCategoryFuturesRateOptions 获取期货选项
func (s *ProductCategoryService) ProductCategoryFuturesRateOptions(rdsConn redis.Conn, merchantID uint) []*views.SelectOption {
	futuresRateOptions := make([]*views.SelectOption, 0)
	// 获取期货收益率
	futuresRate := make([]*models.FuturesRate, 0)
	settingService := NewAdminSettingService()
	err := settingService.GetAdminSettingByFieldWithCache(rdsConn, merchantID, "futuresRate").ToInterface(&futuresRate)
	if err != nil {
		return futuresRateOptions
	}

	for i, option := range futuresRate {
		futuresRateOptions = append(futuresRateOptions, &views.SelectOption{
			Label: fmt.Sprintf("%s%s%.2f%s", option.Label, "/", option.Value, "%"),
			Value: i,
		})
	}
	return futuresRateOptions
}

// GetProductCategoryIDs 获取分类下级IDs
func (s *ProductCategoryService) GetProductCategoryIDs(merchantID uint, categoryID uint) []uint {
	categoryIDs := make([]uint, 0)
	db := model.NewModel()
	db.Raw(`
	WITH RECURSIVE category_hierarchy AS (
	   SELECT c1.id, c1.name, c1.parent_id, c1.status
	   FROM category as c1
	   WHERE c1.parent_id = ? AND c1.status = ? AND c1.type != ? and c1.admin_id = ? and c1.deleted_at is null -- 选择激活的父级分类
	   UNION ALL
	   SELECT c.id, c.name, c.parent_id, c.status
	   FROM category c
	            INNER JOIN category_hierarchy ch ON c.parent_id = ch.id
	   WHERE c.status = ?  and c.admin_id = ?
	)
	SELECT id FROM category_hierarchy`,
		categoryID, models.CategoryStatusEnabled, models.CategoryTypeStaking, merchantID, models.CategoryStatusEnabled, merchantID).Scan(&categoryIDs)

	return append(categoryIDs, categoryID)
}

// RedisDelStopStrategy 删除缓存管理配置信息
func (s *ProductCategoryService) RedisDelStopStrategy(conn redis.Conn, merchantID uint) {
	_, err := conn.Do("DEL", RedisAdminSettingStopStrategy+strconv.Itoa(int(merchantID)))
	if err != nil {
		panic(err)
	}
}
