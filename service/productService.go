package service

import (
	"errors"
	"math/big"
	"strings"
	"time"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/module/exchange/customize"
	"zfeng/module/exchange/ieforex"
	"zfeng/module/exchange/interfaces"
	"zfeng/module/exchange/okx"
	"zfeng/module/exchange/trading"
	"zfeng/utils"

	"github.com/gomodule/redigo/redis"
	"gorm.io/gorm"
)

type Product struct {
	db *model.Model
}

func NewProductService() *Product {
	return &Product{
		db: model.NewModel(),
	}
}

// GetProductSymbolList 获取全部产品标识
func (p *Product) GetProductSymbolList() []string {
	symbolList := make([]string, 0)
	p.db.Model(&models.Product{}).
		Distinct("symbol").
		Where("status = ?", models.ProductStatusEnabled).
		Pluck("symbol", &symbolList)
	return symbolList
}

// GetProductOKEXSymbolList 获取OKX产品标识
func (p *Product) GetProductOKEXSymbolList() []string {
	symbolList := make([]string, 0)
	p.db.Model(&models.Product{}).
		Distinct("symbol").
		Where("admin_id = ?", models.SuperAdminID).
		Where("status = ?", models.ProductStatusEnabled).
		Where("type = ?", models.ProductTypeOKEX).
		Pluck("symbol", &symbolList)

	return symbolList
}

// GetProductTradingSymbolList 获取Trading产品标识
func (p *Product) GetProductTradingSymbolList() []string {
	symbolList := make([]string, 0)
	p.db.Model(&models.Product{}).
		Distinct("symbol").
		Where("admin_id = ?", models.SuperAdminID).
		Where("status = ?", models.ProductStatusEnabled).
		Where("type = ?", models.ProductTypeTrading).
		Pluck("symbol", &symbolList)
	return symbolList
}

// GetProductInvestingSymbolDecimalList 获取英为产品表示和精度
func (p *Product) GetProductInvestingSymbolDecimalList() map[string]int {
	symbolDecimalList := make(map[string]int)
	result, err := p.db.Model(&models.Product{}).
		Select("symbol", "decimal").
		Where("admin_id = ?", models.SuperAdminID).
		Where("status = ?", models.ProductStatusEnabled).
		Where("type = ?", models.ProductTypeIeForex).
		Rows()
	if err != nil {
		return nil
	}
	defer result.Close()
	for result.Next() {
		symbol := ""
		var decimal float64
		err = result.Scan(&symbol, &decimal)
		if err != nil {
			return nil
		}
		symbolDecimalList[symbol] = int(decimal)
	}

	return symbolDecimalList
}

// GetProductIeForexSymbolDecimalList 获取易汇产品精度
func (p *Product) GetProductIeForexSymbolDecimalList() map[string]int {
	symbolDecimalList := make(map[string]int)
	productList := make([]*models.Product, 0)
	p.db.Model(&models.Product{}).
		Select("symbol", "data").
		Where("admin_id = ?", models.SuperAdminID).
		Where("status = ?", models.ProductStatusEnabled).
		Where("type = ?", models.ProductTypeIeForex).
		Find(&productList)

	for _, v := range productList {
		symbolDecimalList[v.Symbol] = v.Data.AssetsDecimal
	}

	return symbolDecimalList
}

// OrderRandomPrice 随机订单价格 1涨 2跌
// currentPrice 原始价格
// mode 涨跌
// accuracy 在小数点后第几位涨跌
func (p *Product) OrderRandomPrice(currentPrice float64, mode, accuracy int) float64 {
	denominator := 1.0
	for i := 0; i < accuracy; i++ {
		denominator = denominator * 10
	}
	randomNumber := utils.GenerateRandomFloat64(1, 9, 0) / denominator
	SellPrice := utils.GenerateRandomFloat64(0, 1, accuracy)
	switch mode {
	case 1:
		currentPrice += randomNumber
		currentPrice += SellPrice
	case 2:
		currentPrice -= randomNumber
		currentPrice -= SellPrice
	}
	return utils.FloatAccuracy(currentPrice, accuracy)
}

// IsWinOrLose 输赢判断
func (p *Product) IsWinOrLose(price, sellPrice float64, side int8) int {
	isWinOrLose := models.ProductOrderStatusLose
	priceTmp := big.NewFloat(price)
	sellPriceTmp := big.NewFloat(sellPrice)
	switch side {
	case models.ProductOrderSideBuy: // 买涨
		if sellPriceTmp.Cmp(priceTmp) == 1 {
			isWinOrLose = models.ProductOrderStatusWin
		}
	case models.ProductOrderSideSell: // 买跌
		v := sellPriceTmp.Cmp(priceTmp)
		if v == -1 || v == 0 {
			isWinOrLose = models.ProductOrderStatusWin
		}
	}
	return isWinOrLose
}

// LoseOrWin 输赢设置
func (p *Product) LoseOrWin(price float64, accuracy, loseOrWinMode int, side int8) float64 {
	sellPrice := price
	switch loseOrWinMode {
	case models.ProductOrderStatusLose: // 买输
		switch side {
		case models.ProductOrderSideBuy: // 买涨
			sellPrice = p.OrderRandomPrice(price, 2, accuracy)

		case models.ProductOrderSideSell: // 买跌
			sellPrice = p.OrderRandomPrice(price, 1, accuracy)
		}
	case models.ProductOrderStatusWin: // 买赢

		switch side {
		case models.ProductOrderSideBuy: // 买涨
			sellPrice = p.OrderRandomPrice(price, 1, accuracy)

		case models.ProductOrderSideSell: // 买跌
			sellPrice = p.OrderRandomPrice(price, 2, accuracy)
		}
	}
	return sellPrice
}

// GetTickers 获取产品行情信息
func (p *Product) GetTickers(rdsConn redis.Conn, productType int8, symbol string) (interfaces.Tickers, error) {
	switch productType {
	case models.ProductTypeOKEX:
		return okx.Exchange.GetTickersData(rdsConn, symbol)
	case models.ProductTypeIeForex:
		return ieforex.Exchange.GetTickersData(rdsConn, symbol)
	case models.ProductTypeTrading:
		return trading.Exchange.GetTickersData(rdsConn, symbol)
	case models.ProductTypeCustomize:
		return customize.Exchange.GetTickersData(rdsConn, symbol)
	}
	return interfaces.Tickers{}, errors.New("notFoundProduct")
}

// GetTrades 获取产品交易信息
func (p *Product) GetTrades(rdsConn redis.Conn, productType int8, symbol, limit string) ([]*interfaces.Trades, error) {
	switch productType {
	case models.ProductTypeOKEX:
		return okx.Exchange.GetTradesDta(rdsConn, symbol, limit)
	case models.ProductTypeCustomize:
		return customize.Exchange.GetTradesDta(rdsConn, symbol, limit)
	}
	return nil, errors.New("notFoundProduct")
}

// GetBooks 获取产品深度信息
func (p *Product) GetBooks(rdsConn redis.Conn, productType int8, symbol, limit string) (*interfaces.Books, error) {
	switch productType {
	case models.ProductTypeOKEX:
		return okx.Exchange.GetBooksDta(rdsConn, symbol, limit)
	case models.ProductTypeCustomize:
		return customize.Exchange.GetBooksDta(rdsConn, symbol, limit)
	}
	return nil, errors.New("notFoundProduct")
}

// GetKline 获取产品k线图数据
func (p *Product) GetKline(rdsConn redis.Conn, productType int8, symbol, bar string, afterTime, before time.Time, nums int64, cacheTimeTem time.Duration) ([]*interfaces.KlineAttrs, error) {
	cacheTime := int(cacheTimeTem.Seconds())
	switch productType {
	case models.ProductTypeOKEX:
		return okx.Exchange.GetKlineData(rdsConn, symbol, bar, afterTime.UnixMilli(), before.UnixMilli(), nums, cacheTime)
	case models.ProductTypeIeForex:
		return ieforex.Exchange.GetKlineData(rdsConn, symbol, bar, afterTime.Unix(), before.Unix(), nums, cacheTime)
	case models.ProductTypeTrading:
		return trading.Exchange.GetKlineData(rdsConn, symbol, bar, afterTime.Unix(), before.Unix(), nums, cacheTime)
	case models.ProductTypeCustomize:
		return customize.Exchange.GetKlineData(rdsConn, symbol, bar, afterTime.Unix(), before.Unix(), nums, cacheTime)
	}
	return nil, errors.New("notFoundProduct")
}

func (p *Product) GetProductInfoDB(merchantID, categoryID, userID uint, fields ...string) *gorm.DB {
	db := model.NewModel()
	categoryIds := NewProductCategoryService().GetProductCategoryIDs(merchantID, categoryID)
	fields = append(fields, []string{"p.id", "p.category_id", "p.images", "p.symbol", "p.name", "p.is_translate", "p.assets_id", "p.type", "p.fee", "p.data", "p.sort", "c.type as CategoryType", "IF(co.id IS NULL, FALSE, TRUE) AS IsCollect"}...)
	return db.Table("product as p").
		Select(strings.Join(fields, ",")).
		Joins("left join category as c on c.id = p.category_id").
		Joins("left join collect as co on co.product_id = p.id and co.user_id = ?", userID).
		Where("p.admin_id = ?", merchantID).
		Where("p.status = ?", models.ProductStatusEnabled).
		Where("c.id IN ?", categoryIds).
		Where("p.deleted_at is null")
}
