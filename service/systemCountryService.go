package service

import (
	"zfeng/core/model"
	"zfeng/models"

	"github.com/goccy/go-json"
	"github.com/gomodule/redigo/redis"
)

const (
	adminCountriesCacheKey = "adminCountriesCacheKey"
)

// SystemCountryService 系统国家服务结构体
type SystemCountryService struct {
	db *model.Model
}

// NewSystemCountryService 创建新的系统国家服务实例
func NewSystemCountryService() *SystemCountryService {
	return &SystemCountryService{
		db: model.NewModel(),
	}
}

// GetAdminCountriesWithCache 获取商户所有缓存国家列表
func (s *SystemCountryService) GetAdminCountriesWithCache(rdsConn redis.Conn, merchantID uint) ([]*models.CountryDisplayData, error) {
	// 尝试从缓存中获取数据
	cachedData, err := redis.Bytes(rdsConn.Do("HGET", adminCountriesCacheKey, merchantID))
	if err == nil {
		var countries []*models.CountryDisplayData
		if err := json.Unmarshal(cachedData, &countries); err == nil {
			return countries, nil
		}
	}

	// 如果缓存中没有数据，从数据库中获取
	var countries []*models.CountryDisplayData
	if err := s.db.Model(&models.Country{}).Where("admin_id = ? AND status = ?", merchantID, models.CountryStatusEnabled).
		Order("sort ASC").
		Find(&countries).Error; err != nil {
		return nil, err
	}

	// 将数据存入缓存
	if cachedData, err := json.Marshal(countries); err == nil {
		_, _ = rdsConn.Do("HSET", adminCountriesCacheKey, merchantID, cachedData)
	}

	return countries, nil
}

// DeleteAdminCountriesCache 删除商户国家缓存
func (s *SystemCountryService) DeleteAdminCountriesCache(rdsConn redis.Conn, merchantID uint) error {
	// 删除指定商户的国家缓存
	_, err := rdsConn.Do("HDEL", adminCountriesCacheKey, merchantID)
	return err
}
