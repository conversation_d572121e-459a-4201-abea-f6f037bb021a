package service

import (
	"fmt"
	"zfeng/core/model"
	"zfeng/models"

	"github.com/gomodule/redigo/redis"
)

const (
	translateCacheKeyPrefix    = "translateCacheKeyPrefix"
	frontendTranslatesCacheKey = "frontendTranslatesCacheKey"
)

// TranslateService 翻译服务结构体
type TranslateService struct {
	db *model.Model
}

// NewTranslateService 创建新的翻译服务实例
func NewTranslateService() *TranslateService {
	return &TranslateService{
		db: model.NewModel(),
	}
}

// GetTranslateByFieldWithCacheToArgs 根据字段获取缓存翻译并格式化
func (s *TranslateService) GetTranslateByFieldWithCacheToArgs(rdsConn redis.Conn, merchantID uint, lang, field string, args ...interface{}) string {
	str, _ := s.GetTranslateByFieldWithCache(rdsConn, merchantID, lang, field)
	str = fmt.Sprintf(str, args...)
	return str
}

// GetTranslatesByFieldsWithCache 根据多建铭获取联合缓存翻译
func (s *TranslateService) GetTranslatesByFieldsWithCache(rdsConn redis.Conn, merchantID uint, lang string, fields ...string) string {
	strs := ""
	for _, v := range fields {
		str, _ := s.GetTranslateByFieldWithCache(rdsConn, merchantID, lang, v)
		strs += str
	}

	// 如果strs为空, 那么返回NULL
	if strs == "" {
		return models.TranslateEmptyValue
	}
	return strs
}

// GetTranslatesEmptyByFieldsWithCache 根据多建铭获取联合缓存翻译 包含空值
func (s *TranslateService) GetTranslatesEmptyByFieldsWithCache(rdsConn redis.Conn, merchantID uint, lang string, fields ...string) string {
	strs := ""
	for _, v := range fields {
		str, _ := s.GetTranslateByFieldWithCache(rdsConn, merchantID, lang, v)
		strs += str
	}
	return strs
}

// GetTranslateByFieldWithCache 根据建铭获取缓存翻译
func (s *TranslateService) GetTranslateByFieldWithCache(rdsConn redis.Conn, merchantID uint, lang, field string) (string, error) {
	cacheKey := fmt.Sprintf("%s:%d:%s", translateCacheKeyPrefix, merchantID, lang)
	// 尝试从缓存中获取数据
	cachedValue, err := redis.String(rdsConn.Do("HGET", cacheKey, field))
	if err == nil {
		return cachedValue, nil
	}

	// 如果缓存中没有数据，从数据库中获取
	var translate models.Translate
	s.db.Where("admin_id = ? AND lang = ? AND field = ? AND status = ?", merchantID, lang, field, models.TranslateStatusEnabled).Find(&translate)
	if translate.ID == 0 {
		return "", fmt.Errorf("translate not found")
	}

	// 将数据存入缓存
	_, _ = rdsConn.Do("HSET", cacheKey, field, translate.Value)
	return translate.Value, nil
}

// GetFrontendTranslatesWithCache 获取前台翻译缓存
func (s *TranslateService) GetFrontendTranslatesWithCache(rdsConn redis.Conn, merchantID uint, lang string) (map[string]string, error) {
	cacheKey := fmt.Sprintf("%s:%d:%s", frontendTranslatesCacheKey, merchantID, lang)

	// 尝试从缓存中获取数据
	cachedLanguages, err := redis.StringMap(rdsConn.Do("HGETALL", cacheKey))
	if err == nil && len(cachedLanguages) > 0 {
		return cachedLanguages, nil
	}

	// 如果缓存中没有数据，从数据库中获取
	var languages []struct {
		Field string
		Value string
	}
	err = s.db.Model(&models.Translate{}).
		Where("admin_id = ? AND type = ? AND lang = ? AND status = ?", merchantID, models.TranslateTypeFrontend, lang, models.TranslateStatusEnabled).
		Select("field, value").
		Find(&languages).Error
	if err != nil {
		return nil, err
	}

	// 将数据存入缓存
	result := make(map[string]string)
	if len(languages) > 0 {
		for _, item := range languages {
			result[item.Field] = item.Value
			_, _ = rdsConn.Do("HSET", cacheKey, item.Field, item.Value)
		}
	}

	return result, nil
}

// DeleteFrontendTranslatesCache 删除前台翻译缓存
func (s *TranslateService) DeleteFrontendTranslatesCache(rdsConn redis.Conn, merchantID uint, lang string) error {
	// 删除整个语言的缓存
	cacheKey := fmt.Sprintf("%s:%d:%s", frontendTranslatesCacheKey, merchantID, lang)
	_, err := rdsConn.Do("DEL", cacheKey)
	if err != nil {
		return err
	}

	cacheFieldKey := fmt.Sprintf("%s:%d:%s", translateCacheKeyPrefix, merchantID, lang)
	_, err = rdsConn.Do("DEL", cacheFieldKey)
	return err
}

// DeleteTranslateCache 删除翻译缓存
func (s *TranslateService) DeleteTranslateCache(rdsConn redis.Conn, merchantID uint, lang string) error {
	cacheKey := fmt.Sprintf("%s:%d:%s", translateCacheKeyPrefix, merchantID, lang)
	_, err := rdsConn.Do("DEL", cacheKey)
	if err != nil {
		return err
	}

	return s.DeleteFrontendTranslatesCache(rdsConn, merchantID, lang)
}

// DeleteAllTranslateCache 删除商户所有翻译缓存
func (s *TranslateService) DeleteAllTranslateCache(rdsConn redis.Conn, merchantID uint) error {
	// 获取所有语言
	var langs []string
	err := s.db.Model(&models.Lang{}).
		Where("admin_id = ? AND status = ?", merchantID, models.LangStatusEnabled).
		Pluck("symbol", &langs).Error
	if err != nil {
		return fmt.Errorf("获取语言列表失败: %w", err)
	}

	// 遍历所有语言，删除对应的缓存
	for _, lang := range langs {
		err = s.DeleteFrontendTranslatesCache(rdsConn, merchantID, lang)
		if err != nil {
			return fmt.Errorf("删除前台翻译缓存失败 (lang: %s): %w", lang, err)
		}

		err = s.DeleteTranslateCache(rdsConn, merchantID, lang)
		if err != nil {
			return fmt.Errorf("删除翻译缓存失败 (lang: %s): %w", lang, err)
		}
	}

	return nil
}

// DeleteProductCache 删除指定产品缓存
func (s *TranslateService) DeleteProductCache(rdsConn redis.Conn, merchantID uint, lang, field string) error {
	cacheFieldKey := fmt.Sprintf("%s:%d:%s", translateCacheKeyPrefix, merchantID, lang)
	_, err := rdsConn.Do("HDEL", cacheFieldKey, field)
	return err
}
