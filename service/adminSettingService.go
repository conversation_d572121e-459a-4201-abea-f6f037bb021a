package service

import (
	"fmt"
	"strconv"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/core/views/vues"
	"zfeng/models"

	"github.com/goccy/go-json"
	"github.com/gomodule/redigo/redis"
)

const (
	// adminSettingCacheKeyPrefix 管理设置缓存Key
	adminSettingCacheKeyPrefix = "adminSettingCacheKeyPrefix"
)

// AdminSettingService 管理设置服务层结构体
type AdminSettingService struct {
	DB    *model.Model
	value string // 内容值
	err   error  //	错误信息
}

// NewAdminSettingService 创建新的管理设置服务实例
func NewAdminSettingService() *AdminSettingService {
	return &AdminSettingService{
		DB: model.NewModel(),
	}
}

// ToInterface 将字符串转换为指定的结构体
func (s *AdminSettingService) ToInterface(v interface{}) error {
	if s.err != nil {
		return s.err
	}
	return json.Unmarshal([]byte(s.value), v)
}

// ToInt 将字符串转换为整数
func (s *AdminSettingService) ToInt() (int, error) {
	if s.err != nil {
		return 0, s.err
	}
	return strconv.Atoi(s.value)
}

// ToFloat64 将字符串转换为float64
func (s *AdminSettingService) ToFloat64() (float64, error) {
	if s.err != nil {
		return 0, s.err
	}
	return strconv.ParseFloat(s.value, 64)
}

// ToString 返回字符串值
func (s *AdminSettingService) ToString() (string, error) {
	if s.err != nil {
		return "", nil
	}
	return s.value, s.err
}

// ToCheckbox 将字符串转换为复选框
func (s *AdminSettingService) ToCheckbox() (map[string]bool, error) {
	data := make(map[string]bool)
	if s.err != nil {
		return data, s.err
	}

	checkboxes := make([]*views.CheckboxOption, 0)
	err := s.ToInterface(&checkboxes)
	if err != nil {
		return data, err
	}

	for _, checkbox := range checkboxes {
		data[checkbox.Value.(string)] = checkbox.Checked
	}
	return data, nil
}

// GetAdminSettingByField 获取指定字段管理设置数据
func (s *AdminSettingService) ToOptions() ([]*views.SelectOption, error) {
	var checkboxes []*views.CheckboxOption
	err := s.ToInterface(&checkboxes)
	if err != nil {
		return nil, err
	}

	options := make([]*views.SelectOption, 0)
	for _, checkbox := range checkboxes {
		if checkbox.Checked {
			options = append(options, &views.SelectOption{
				Label: checkbox.Label,
				Value: checkbox.Value,
			})
		}
	}
	return options, nil
}

// GetAdminSettingByFieldWithCache 获取指定字段管理设置缓存数据
func (s *AdminSettingService) GetAdminSettingByFieldWithCache(rdsConn redis.Conn, merchantID uint, field string) *AdminSettingService {
	cacheKey := fmt.Sprintf("%s:%d", adminSettingCacheKeyPrefix, merchantID)

	// 尝试从缓存中获取数据
	cachedValue, err := redis.String(rdsConn.Do("HGET", cacheKey, field))
	if err == nil {
		s.value = cachedValue
	}

	// 如果缓存中没有数据，从数据库中获取
	var adminSetting models.AdminSetting
	err = s.DB.Where("admin_id = ? AND field = ?", merchantID, field).Find(&adminSetting).Error
	if err != nil {
		s.err = err
	}

	// 将数据存入哈希缓存
	_, _ = rdsConn.Do("HSET", cacheKey, field, adminSetting.Value)
	s.value = adminSetting.Value
	return s
}

// DeleteAdminSettingByFieldCache 删除指定字段的管理设置缓存
func (s *AdminSettingService) DeleteAdminSettingByFieldCache(rdsConn redis.Conn, merchantID uint, field string) error {
	cacheKey := fmt.Sprintf("%s:%d", adminSettingCacheKeyPrefix, merchantID)

	// 删除指定字段的缓存
	_, err := rdsConn.Do("HDEL", cacheKey, field)
	return err
}

// DeleteAllAdminSettingCache 删除所有管理设置的缓存
func (s *AdminSettingService) DeleteAllAdminSettingCache(rdsConn redis.Conn, merchantID uint) error {
	cacheKey := fmt.Sprintf("%s:%d", adminSettingCacheKeyPrefix, merchantID)

	// 删除整个商户的管理设置缓存
	_, err := rdsConn.Do("DEL", cacheKey)
	return err
}

// GetMerchantGroupSettingForm 获取商户配置Form
func (s *AdminSettingService) GetMerchantGroupSettingForm(rdsConn redis.Conn, merchantID uint, groupID uint) []*views.Input {
	inputs := make([]*views.Input, 0)

	// 获取超级管理员 基础配置
	var adminSettings []*models.AdminSetting
	err := s.DB.Where("admin_id = ? AND group_id = ?", models.SuperAdminID, models.AdminSettingGroupDefault).Find(&adminSettings).Error
	if err != nil {
		return inputs
	}

	for _, setting := range adminSettings {
		input := &views.Input{
			Label:    setting.Name,
			Field:    setting.Field,
			Default:  setting.Value,
			Type:     setting.Data.Input.Type,
			Readonly: setting.Data.Input.Readonly,
			Rules:    setting.Data.Input.Rules,
			Options:  setting.Data.Input.Options,
		}

		// 获取当前商户缓存内容, 并且设置 default
		cacheValue, _ := s.GetAdminSettingByFieldWithCache(rdsConn, merchantID, setting.Field).ToString()
		if cacheValue != "" {
			input.SetDefault(cacheValue)
		}
		inputs = append(inputs, input)
	}

	return inputs
}

// GetMerchantGroupSettingData 获取商户组的设置数据
func (s *AdminSettingService) GetMerchantGroupSettingData(rdsConn redis.Conn, merchantID uint, groupID uint) map[string]interface{} {
	settingData := make(map[string]interface{})

	// 获取超级管理员的基础配置
	var adminSettings []*models.AdminSetting
	err := s.DB.Where("admin_id = ? AND group_id = ?", models.SuperAdminID, models.AdminSettingGroupDefault).Find(&adminSettings).Error
	if err != nil {
		return settingData
	}

	for _, setting := range adminSettings {
		// 获取当前商户缓存内容
		cacheValue, err := s.GetAdminSettingByFieldWithCache(rdsConn, merchantID, setting.Field).ToString()
		if err != nil {
			// 如果获取缓存失败，使用默认值
			cacheValue = setting.Value
		}
		if cacheValue == "" {
			cacheValue = setting.Value
		}

		// 根据设置类型转换值
		settingData[setting.Field] = views.InputValueToInterface(setting.Data.Input.Type, cacheValue)
	}

	return settingData
}

// 在vueInputs 中获取 display 值
func (s *AdminSettingService) GetVueInputsDisplayValue(vueInputs [][]*vues.Input, field string) bool {
	for _, inputs := range vueInputs {
		for _, input := range inputs {
			if input.Field == field {
				return input.Display
			}
		}
	}
	return true
}
