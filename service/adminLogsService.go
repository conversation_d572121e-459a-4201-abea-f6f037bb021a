package service

import (
	"strings"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/utils"

	"github.com/gofiber/fiber/v2"
)

// AdminLogsService 操作日志服务
type AdminLogsService struct {
	db *model.Model
}

// NewAdminLogsService 创建操作日志服务
func NewAdminLogsService() *AdminLogsService {
	return &AdminLogsService{
		db: model.NewModel(),
	}
}

// Create 创建操作日志
func (s *AdminLogsService) Create(c *fiber.Ctx, adminID uint, routeName string) error {
	allowedRouters := []string{"create", "update", "delete"}
	// 检查路由是否包含需要记录日志的操作
	path := c.Path()
	isLogs := false
	for _, route := range allowedRouters {
		if strings.Contains(path, route) {
			isLogs = true
			break
		}
	}

	// 如果没有匹配到需要记录日志的操作，则直接返回
	if !isLogs {
		return nil
	}

	return s.db.Create(&models.AdminLogs{
		AdminID: adminID,
		Action:  routeName,
		IP:      utils.GetClientIP(c),
		Route:   c.Path(),
		Method:  c.Method(),
		Params:  string(c.Body()),
		Headers: models.HeaderInfo{
			UserAgent:      c.Get("User-Agent"),
			Referer:        c.Get("Referer"),
			AcceptLanguage: c.Get("Accept-Language"),
		},
	}).Error
}
