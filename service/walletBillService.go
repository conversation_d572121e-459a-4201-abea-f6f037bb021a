package service

import (
	"time"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"

	"gorm.io/gorm"
)

// WalletBillService 钱包账单服务
type WalletBillService struct {
	db *model.Model
}

// NewWalletBillService 创建钱包账单服务
func NewWalletBillService() *WalletBillService {

	return &WalletBillService{
		db: model.NewModel(),
	}
}

// GetCheckboxOptions 获取账单类型选项
func (s *WalletBillService) GetCheckboxOptions() []*views.CheckboxOption {
	checkboxes := make([]*views.CheckboxOption, 0)
	for _, v := range models.BillTypeNames {
		checkboxes = append(checkboxes, &views.CheckboxOption{
			Label:   v.Label,
			Value:   v.Value,
			Checked: true,
		})
	}
	return checkboxes
}

// GetBillOptions 获取账单类型选项
func (s *WalletBillService) GetBillOptions() []*views.SelectOption {
	options := make([]*views.SelectOption, 0)
	for _, v := range models.BillTypeNames {
		options = append(options, v)
	}
	return options
}

// GetAmountTypeOptions 获取金额类型选项
func (s *WalletBillService) GetAmountTypeOptions() []*views.SelectOption {
	return []*views.SelectOption{
		{Label: "系统加款", Value: models.BillTypeSystemAddition},
		{Label: "系统扣款", Value: models.BillTypeSystemDeduction},
		{Label: "系统奖励", Value: models.BillTypeSystemReward},
	}
}

// CreateBill 创建账单
func (s *WalletBillService) CreateBill(tx *gorm.DB, userInfo *models.User, assetsInfo *models.WalletAssets, billType int8, sourceID uint, money float64) error {
	balance := userInfo.AvailableAmount
	billName := models.BillTypeNames[billType].Label
	var AssetsID uint
	if assetsInfo != nil && assetsInfo.ID > 0 {
		AssetsID = assetsInfo.ID
		billName = billName + "[" + assetsInfo.Name + "]"
		userAssets := &models.UserAssets{}
		if err := tx.Model(&models.UserAssets{}).Where("assets_id = ?", assetsInfo.ID).Where("user_id = ?", userInfo.ID).First(userAssets).Error; err != nil {
			return err
		}
		balance = userAssets.AvailableAmount
		// 修改可用资产
		if billType > 0 {
			userAssets.AvailableAmount += money
		} else {
			userAssets.AvailableAmount -= money
		}
	} else {
		// 修改可用余额
		if billType > 0 {
			userInfo.AvailableAmount += money
		} else {
			userInfo.AvailableAmount -= money
		}
	}
	return tx.Create(&models.WalletBill{
		Type:     billType,
		Name:     billName,
		AdminID:  userInfo.AdminID,
		UserID:   userInfo.ID,
		AssetsID: AssetsID,
		SourceID: sourceID,
		Money:    money,
		Balance:  balance,
	}).Error
}

// GetDailyStats 获取每日统计
func (s *WalletBillService) GetDailyStats(userID uint, totalAssets float64) []*models.WalletBillDailyStats {
	var results []*models.WalletBillDailyStats
	s.db.Model(&models.WalletBill{}).
		Select(`
            DATE(wallet_bill.created_at) as date,
            SUM(CASE 
                WHEN wallet_bill.assets_id > 0 THEN wallet_bill.money * wallet_assets.rate 
                ELSE wallet_bill.money 
            END) as money,
            MAX(CASE 
                WHEN wallet_bill.assets_id > 0 THEN wallet_bill.balance * wallet_assets.rate 
                ELSE wallet_bill.balance 
            END) as balance
        `).
		Joins("LEFT JOIN wallet_assets ON wallet_bill.assets_id = wallet_assets.id").
		Where("wallet_bill.user_id = ?", userID).
		Where("wallet_bill.created_at >= DATE_SUB(CURDATE(), INTERVAL 14 DAY)").
		Where("wallet_bill.deleted_at IS NULL").
		Group("DATE(wallet_bill.created_at)").
		Order("date").
		Scan(&results)

	// 获取14天之前的一条数据
	if totalAssets > 0 && len(results) == 0 {
		results = []*models.WalletBillDailyStats{
			{Date: time.Now().AddDate(0, 0, -14), Money: totalAssets, Balance: totalAssets},
		}
	}

	// 获取当前时间
	now := time.Now()
	// 创建一个map用于快速查找已有数据
	statsMap := make(map[string]*models.WalletBillDailyStats)
	for _, stat := range results {
		key := stat.Date.Format("2006-01-02")
		statsMap[key] = stat
	}

	// 重置results准备重新填充
	newResults := make([]*models.WalletBillDailyStats, 0)

	// 填充最近15天的数据
	for i := -14; i <= 0; i++ {
		date := now.AddDate(0, 0, i)
		dateStr := date.Format("2006-01-02")

		// 创建当天的统计数据
		dailyStat := &models.WalletBillDailyStats{
			Date:    date,
			Money:   0,
			Balance: 0,
		}

		// 如果当天有数据,直接使用
		if stat, exists := statsMap[dateStr]; exists {
			if len(newResults) > 0 {
				dailyStat.Money = stat.Money + newResults[len(newResults)-1].Money
				dailyStat.Balance = stat.Money + newResults[len(newResults)-1].Balance
			} else {
				dailyStat.Money = stat.Money
				dailyStat.Balance = stat.Balance
			}
		} else {
			if len(newResults) > 0 && newResults[len(newResults)-1].Date.Format("2006-01-02") <= dateStr {
				dailyStat.Money = newResults[len(newResults)-1].Money
				dailyStat.Balance = newResults[len(newResults)-1].Balance
			}
		}

		newResults = append(newResults, dailyStat)
	}

	results = newResults

	return results
}
