package service

import (
	"errors"
	"fmt"
	"time"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/module/exchange/interfaces"
	"zfeng/utils"

	"github.com/gomodule/redigo/redis"
	"gorm.io/gorm"
)

type ProductOrder struct {
	db *model.Model
}

func NewProductOrderService() *ProductOrder {
	return &ProductOrder{
		db: model.NewModel(),
	}
}

// ProductOrderClose 产品订单手动平仓·
func (p *ProductOrder) ProductOrderClose(redConn redis.Conn, merchantID uint, acceptLang string, orderInfo *models.OrderInfo) error {
	productInfo := orderInfo.ProductInfo
	orderData := orderInfo.Data
	tickers, err := NewProductService().GetTickers(redConn, productInfo.Type, productInfo.Symbol)
	if err == nil && tickers.Last > 0 {
		// 记录卖出价格
		orderData.SellPrice = tickers.Last
		orderData.Amount = orderInfo.Amount(tickers.Last)

		userInfo := &models.User{}
		if result := p.db.Where("status = ?", models.UserStatusActive).
			Where("id = ?", orderInfo.UserID).
			Find(userInfo); result.Error != nil {
			return errors.New("abnormalOperation")
		}

		// 写入资产
		userWalletService := NewWalletService()
		err = p.db.Transaction(func(tx *gorm.DB) error {
			if orderData.Amount > 0 && productInfo.AssetsID > 0 {
				assetsInfo := &models.WalletAssets{}
				if result := p.db.Model(assetsInfo).
					Where("id = ?", productInfo.AssetsID).
					Where("admin_id = ?", merchantID).
					Find(assetsInfo); result.Error != nil {
					return errors.New("abnormalOperation")
				}
				// 平仓收益
				if err = userWalletService.IncreaseAssets(tx, redConn, acceptLang, models.BillTypeProductEarnings, orderInfo.ID, userInfo, assetsInfo, orderData.Amount); err != nil {
					tx.Rollback()
					return err
				}
			} else {
				// 余额支付
				err = userWalletService.IncreaseBalance(tx, redConn, acceptLang, models.BillTypeProductPurchase, orderInfo.ID, userInfo, orderData.Amount)
				if err != nil {
					tx.Rollback()
					return err
				}
			}

			result := tx.Where(orderInfo.ID).Updates(&models.Order{
				Status: models.ProductOrderStatusCompleted,
				Data:   orderData,
				Fee:    orderInfo.AmountFee(tickers.Last),
			})
			return result.Error
		})
		if err != nil {
			return err
		}
	} else {
		return errors.New("abnormalOperation")
	}
	return nil
}

// ProductOrderRevoke 撤销订单
func (p *ProductOrder) ProductOrderRevoke(redConn redis.Conn, acceptLang string, orderInfo *models.Order) error {
	userInfo := &models.User{}
	if result := p.db.Where("status = ?", models.UserStatusActive).
		Where("id = ?", orderInfo.UserID).
		Find(userInfo); result.RowsAffected == 0 {
		return errors.New("abnormalOperation")
	}

	productInfo := &models.Product{}
	p.db.Model(productInfo).
		Where("id = ?", orderInfo.ProductID).
		Where("status = ?", models.ProductStatusEnabled).
		Find(productInfo)

	if orderInfo.ID > 0 && productInfo.ID > 0 {
		var depositAssetsID uint
		switch {
		case orderInfo.Type == models.ProductOrderTypeSpot && orderInfo.Status == models.ProductOrderStatusWaiting:
			depositAssetsID = productInfo.AssetsID
			if orderInfo.Side == models.ProductOrderSideSell {
				depositAssetsID = productInfo.Data.SymbolAssetsID
			}
		case orderInfo.Type == models.ProductOrderTypeContract && orderInfo.Status == models.ProductOrderStatusWaiting:
			depositAssetsID = productInfo.AssetsID
		case orderInfo.Type == models.ProductOrderTypeStaking && (orderInfo.Status == models.ProductOrderStatusRunning && orderInfo.Data.Index == 0):
			depositAssetsID = productInfo.Data.SymbolAssetsID
			if orderInfo.Data.Amount != 0 {
				orderInfo.Money = orderInfo.Data.Amount
			}
		default:
			return errors.New("abnormalOperation")
		}

		userWalletService := NewWalletService()
		adminService := NewAdminUserService()
		merchantID, _ := adminService.GetMerchantIDWithCache(redConn, orderInfo.AdminID)

		tx := p.db.Begin()
		if depositAssetsID > 0 {
			assetsInfo := &models.WalletAssets{}
			result := p.db.Model(assetsInfo).
				Where("id = ?", depositAssetsID).
				Where("admin_id = ?", merchantID).
				Find(assetsInfo)
			if result.RowsAffected == 0 {
				return errors.New("abnormalOperation")
			}
			if err := userWalletService.IncreaseAssets(tx, redConn, acceptLang, models.BillTypeProductRefund, orderInfo.ID, userInfo, assetsInfo, orderInfo.Money); err != nil {
				tx.Rollback()
				return err
			}
		} else {
			// 余额支付
			err := userWalletService.IncreaseBalance(tx, redConn, acceptLang, models.BillTypeProductPurchase, orderInfo.ID, userInfo, orderInfo.Money)
			if err != nil {
				tx.Rollback()
				return err
			}
		}

		//	更新当前订单状态
		result := tx.Model(&models.Order{}).
			Where(orderInfo.ID).
			Update("status", models.ProductOrderStatusCancelled)
		if result.Error != nil {
			tx.Rollback()
			return result.Error
		}
		tx.Commit()
	}
	return nil
}

type OrderCreateParams struct {
	ID        uint    `json:"id"`
	UserID    uint    `json:"userID"`
	Side      int8    `json:"side"`
	Type      int8    `json:"type"`
	Mode      int8    `json:"mode"`
	Index     int     `json:"index"`
	Price     float64 `json:"price"`
	TakePrice float64 `json:"takePrice"`
	StopPrice float64 `json:"stopPrice"`
	Money     float64 `json:"money"`
}

// ProductOrderCreate 创建产品订单
func (p *ProductOrder) ProductOrderCreate(redConn redis.Conn, merchantID uint, acceptLang string, params *OrderCreateParams) error {
	translateService := NewTranslateService()
	//	获取当前产品信息
	productInfo := &models.Product{}
	result := p.db.Where("id = ?", params.ID).
		Where("admin_id = ?", merchantID).
		Find(productInfo)
	if result.RowsAffected == 0 {
		return errors.New(translateService.GetTranslatesByFieldsWithCache(redConn, 0, acceptLang, "abnormalOperation"))
	}
	adminService := NewAdminUserService()
	subAdminIDs, _ := adminService.GetSubAdminIDsWithCache(redConn, merchantID)
	//	当前用户信息
	userInfo := &models.User{}
	result = p.db.Model(userInfo).
		Where("id = ?", params.UserID).
		Where("admin_id IN ?", subAdminIDs).
		Find(userInfo)
	if result.RowsAffected == 0 {
		return errors.New(translateService.GetTranslatesByFieldsWithCache(redConn, merchantID, acceptLang, "userNotExist"))
	}

	//	获取配置文件
	adminSettingService := NewAdminSettingService()
	tempLateOrderMap, err := adminSettingService.GetAdminSettingByFieldWithCache(redConn, merchantID, "siteUserStatus").ToCheckbox()
	if err != nil {
		return errors.New(translateService.GetTranslatesByFieldsWithCache(redConn, 0, acceptLang, "abnormalOperation"))
	}

	// 用户是否被冻结
	if tempLateOrderMap["freezeOrder"] && userInfo.Status == models.UserStatusFrozen {
		return errors.New(translateService.GetTranslatesByFieldsWithCache(redConn, merchantID, acceptLang, "accountIsFrozen"))
	}

	//	信用分低于x禁止下单
	siteMinCreditScore, err := adminSettingService.GetAdminSettingByFieldWithCache(redConn, merchantID, "siteMinCreditScore").ToInt()
	if err != nil {
		return errors.New(translateService.GetTranslatesByFieldsWithCache(redConn, 0, acceptLang, "abnormalOperation"))
	}

	if tempLateOrderMap["creditOrder"] && userInfo.Score < siteMinCreditScore {
		return errors.New(translateService.GetTranslateByFieldWithCacheToArgs(redConn, merchantID, acceptLang, "lowCreditScore", siteMinCreditScore))
	}

	// 产品行情信息
	tickers, err := NewProductService().GetTickers(redConn, productInfo.Type, productInfo.Symbol)
	if (err != nil || tickers.Last <= 0) && params.Type != models.ProductOrderTypeStaking {
		return errors.New(translateService.GetTranslatesByFieldsWithCache(redConn, 0, acceptLang, "abnormalOperation"))
	}

	// 产品限制价格
	productData := productInfo.Data
	if (params.Type == models.ProductOrderTypeSpot && params.Side == models.ProductOrderSideBuy && params.Money <= productData.MinMoney) ||
		(params.Type == models.ProductOrderTypeSpot && params.Side == models.ProductOrderSideSell && (params.Money*tickers.Last) <= productData.MinMoney) ||
		(params.Type == models.ProductOrderTypeContract && params.Money < productData.MinMoney) ||
		(params.Type == models.ProductOrderTypeFutures && params.Money < productData.MinMoney) ||
		(params.Type == models.ProductOrderTypeStaking && params.Money < productData.MinMoney) {
		minimumAmount := translateService.GetTranslatesByFieldsWithCache(redConn, merchantID, acceptLang, "minimumAmount")
		return errors.New(fmt.Sprintf(minimumAmount, productData.MinMoney))
	}

	tx := p.db.Begin()
	switch params.Type {
	// 币币交易流程
	case models.ProductOrderTypeSpot:
		err = p.CreateSpot(redConn, tx, merchantID, acceptLang, tickers, productInfo, userInfo, params) // 币币交易流程
	// 期货交易
	case models.ProductOrderTypeFutures:
		if utils.IsMarketClosedForTrading(time.Now()) && productInfo.Type != models.ProductTypeOKEX {
			return errors.New(translateService.GetTranslatesByFieldsWithCache(redConn, merchantID, acceptLang, "closed"))
		}

		settingService := NewAdminSettingService()
		futuresRateList := make([]*models.FuturesRate, 0)
		err = settingService.GetAdminSettingByFieldWithCache(redConn, merchantID, "futuresRate").ToInterface(&futuresRateList)
		if err != nil {
			tx.Rollback()
			return err
		}
		err = p.CreateFutures(redConn, tx, merchantID, acceptLang, tickers, productInfo, userInfo, params, futuresRateList)
	// 合约交易流程
	case models.ProductOrderTypeContract:
		err = p.CreateContact(redConn, tx, merchantID, acceptLang, tickers, productInfo, userInfo, params)
		// 质押订单
	case models.ProductOrderTypeStaking:
		stakingStrategy := &models.StakingStrategy{}
		if productInfo.Data.StakingStrategy == nil || len(productInfo.Data.StakingStrategy) <= params.Index {
			return errors.New(translateService.GetTranslatesByFieldsWithCache(redConn, 0, acceptLang, "abnormalOperation"))
		}
		stakingStrategy = productInfo.Data.StakingStrategy[params.Index]

		err = p.CreateStaking(redConn, tx, merchantID, acceptLang, productInfo, userInfo, params, stakingStrategy)
	}
	if err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()
	return nil
}

// CreateFutures 创建期货订单
func (p *ProductOrder) CreateFutures(rdsConn redis.Conn, tx *gorm.DB, merchantID uint, acceptLang string, tickers interfaces.Tickers, productInfo *models.Product, userInfo *models.User, params *OrderCreateParams, futuresRateList []*models.FuturesRate) error {
	translateService := NewTranslateService()
	if params.Index >= len(futuresRateList) {
		return errors.New(translateService.GetTranslateByFieldWithCacheToArgs(rdsConn, merchantID, acceptLang, "formatError"))
	}

	// 用户余额要大于最小金额
	if userInfo.AvailableAmount < futuresRateList[params.Index].MinAmount {
		translate, _ := translateService.GetTranslateByFieldWithCache(rdsConn, 0, acceptLang, "insufficientBalance")
		return errors.New(translate)
	}

	var buyNums = params.Money
	if productInfo.AssetsID == 0 || productInfo.Data.SymbolAssetsID == 0 {
		buyNums = params.Money / tickers.Last
	}

	nowTime := time.Now()
	orderData := models.OrderData{
		Second: futuresRateList[params.Index].Second,
		Rate:   futuresRateList[params.Index].Value / 100,
		Price:  tickers.Last,
	}

	orderInfo := &models.Order{
		AdminID:   userInfo.AdminID,
		UserID:    userInfo.ID,
		ProductID: productInfo.ID,
		OrderSN:   utils.GenerateOrderSN(),
		Money:     params.Money,
		Status:    models.ProductOrderStatusRunning,
		Mode:      models.ProductOrderModeMarket,
		Side:      params.Side,
		Fee:       productInfo.Fee,
		Type:      params.Type,
		Nums:      buyNums,
		Data:      orderData,
		ExpiredAt: time.Unix(nowTime.Unix()+int64(futuresRateList[params.Index].Second), 0),
	}
	result := tx.Create(orderInfo)
	if result.Error != nil {
		return result.Error
	}

	userWalletService := NewWalletService()

	if err := userWalletService.SpendBalance(tx, rdsConn, "", models.BillTypeProductPurchase, orderInfo.ID, userInfo, orderInfo.Fee); err != nil {
		return err
	}

	if productInfo.AssetsID != 0 {
		assetsInfo := &models.WalletAssets{}
		result = tx.Model(assetsInfo).
			Where("id = ?", productInfo.AssetsID).
			Where("admin_id = ?", merchantID).
			Find(assetsInfo)
		// 资金支付
		return userWalletService.SpendAssets(tx, rdsConn, acceptLang, models.BillTypeProductPurchase, orderInfo.ID, userInfo, assetsInfo, params.Money)
	}
	//	余额支出
	return userWalletService.SpendBalance(tx, rdsConn, acceptLang, models.BillTypeProductPurchase, orderInfo.ID, userInfo, params.Money)
}

// CreateContact 创建合约订单
func (p *ProductOrder) CreateContact(rdsConn redis.Conn, tx *gorm.DB, merchantID uint, acceptLang string, tickers interfaces.Tickers, productInfo *models.Product, userInfo *models.User, params *OrderCreateParams) error {
	if params.Index == 0 {
		translateService := NewTranslateService()
		return errors.New(translateService.GetTranslateByFieldWithCacheToArgs(rdsConn, merchantID, acceptLang, "abnormalOperation"))
	}
	var buyPrice = tickers.Last
	var buyNums = params.Money / tickers.Last
	orderStatus := models.ProductOrderStatusRunning
	if params.Mode == models.ProductOrderModeLimit {
		orderStatus = models.ProductOrderStatusWaiting
		buyPrice = params.Price
		buyNums = 0
	}

	spendAssetsId := productInfo.AssetsID

	orderInfo := &models.Order{
		AdminID:   userInfo.AdminID,
		UserID:    userInfo.ID,
		ProductID: productInfo.ID,
		OrderSN:   utils.GenerateOrderSN(),
		Money:     params.Money,
		Status:    orderStatus,
		Side:      params.Side,
		Mode:      params.Mode,
		Type:      params.Type,
		Nums:      buyNums,
		Data: models.OrderData{
			Index:     params.Index,
			TakePrice: params.TakePrice,
			StopPrice: params.StopPrice,
			Price:     buyPrice,
		},
	}

	result := tx.Create(orderInfo)
	if result.Error != nil {
		return result.Error
	}

	userWalletService := NewWalletService()
	if spendAssetsId != 0 {
		assetsInfo := &models.WalletAssets{}
		result = tx.Model(assetsInfo).
			Where("id = ?", spendAssetsId).
			Where("admin_id = ?", merchantID).
			Find(assetsInfo)
		if result.RowsAffected == 0 {
			return errors.New("abnormalOperation")
		}
		// 资金支付
		return userWalletService.SpendAssets(tx, rdsConn, acceptLang, models.BillTypeProductPurchase, orderInfo.ID, userInfo, assetsInfo, params.Money)
	} else {
		//	余额支出
		return userWalletService.SpendBalance(tx, rdsConn, acceptLang, models.BillTypeProductPurchase, orderInfo.ID, userInfo, params.Money)
	}
}

// CreateSpot 创建币币交易订单
func (p *ProductOrder) CreateSpot(rdsConn redis.Conn, tx *gorm.DB, merchantID uint, acceptLang string, tickers interfaces.Tickers, productInfo *models.Product, userInfo *models.User, params *OrderCreateParams) error {
	spendAssetsId := productInfo.AssetsID
	depositAssetsId := productInfo.Data.SymbolAssetsID

	var buyPrice = tickers.Last
	var buyNums = params.Money / tickers.Last
	if params.Side == models.ProductOrderSideSell {
		buyNums = params.Money * tickers.Last
		spendAssetsId = productInfo.Data.SymbolAssetsID
		depositAssetsId = productInfo.AssetsID
	}

	var buyFee = buyNums * productInfo.Fee
	orderStatus := models.ProductOrderStatusCompleted
	if params.Mode == models.ProductOrderModeLimit {
		orderStatus = models.ProductOrderStatusWaiting
		buyPrice = params.Price
		buyNums = 0
		buyFee = 0
	}

	// 创建订单
	orderInfo := &models.Order{
		AdminID:   userInfo.AdminID,
		UserID:    userInfo.ID,
		ProductID: productInfo.ID,
		OrderSN:   utils.GenerateOrderSN(),
		Money:     params.Money,
		Status:    orderStatus,
		Side:      params.Side,
		Mode:      params.Mode,
		Type:      params.Type,
		Data: models.OrderData{
			Price: buyPrice,
		},
		Fee:  buyFee,
		Nums: buyNums,
	}
	result := tx.Create(orderInfo)
	if result.Error != nil {
		return result.Error
	}

	userWalletService := NewWalletService()
	if spendAssetsId != 0 {
		assetsInfo := &models.WalletAssets{}
		result = tx.Model(assetsInfo).
			Where("id = ?", spendAssetsId).
			Where("admin_id = ?", merchantID).
			Find(assetsInfo)
		if result.Error != nil {
			translateService := NewTranslateService()
			return errors.New(translateService.GetTranslateByFieldWithCacheToArgs(rdsConn, merchantID, acceptLang, "abnormalOperation"))
		}

		// 资金支付
		err := userWalletService.SpendAssets(tx, rdsConn, acceptLang, models.BillTypeProductPurchase, orderInfo.ID, userInfo, assetsInfo, params.Money)
		if err != nil {
			return err
		}
	} else {
		// 余额支付
		err := userWalletService.SpendBalance(tx, rdsConn, acceptLang, models.BillTypeProductPurchase, orderInfo.ID, userInfo, params.Money)
		if err != nil {
			return err
		}
	}

	// 如果市价购买 获取资产数量
	if params.Mode == models.ProductOrderModeMarket {
		if depositAssetsId != 0 {
			depositAssetsInfo := &models.WalletAssets{}
			result = tx.Model(depositAssetsInfo).
				Where("id = ?", depositAssetsId).
				Where("admin_id = ?", merchantID).
				Find(depositAssetsInfo)
			if result.Error != nil {
				return errors.New("abnormalOperation")
			}

			// 资金消费
			err := userWalletService.IncreaseAssets(tx, rdsConn, acceptLang, models.BillTypeProductEarnings, orderInfo.ID, userInfo, depositAssetsInfo, buyNums)
			if err != nil {
				return err
			}
		} else {
			// 资金消费
			err := userWalletService.IncreaseBalance(tx, rdsConn, acceptLang, models.BillTypeProductEarnings, orderInfo.ID, userInfo, buyNums)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// CreateStaking 创建质押订单
func (p *ProductOrder) CreateStaking(rdsConn redis.Conn, tx *gorm.DB, merchantID uint, acceptLang string, productInfo *models.Product, userInfo *models.User, params *OrderCreateParams, strategy *models.StakingStrategy) error {
	if params.Side == models.ProductOrderSideSell || params.Mode == models.ProductOrderModeLimit {
		return errors.New("abnormalOperation")
	}

	nowTime := time.Now()
	var buyNums = params.Money
	spendAssetsId := productInfo.Data.SymbolAssetsID

	orderData := models.OrderData{}
	orderData.Index = params.Index
	orderData.StakingStrategy = *strategy
	second := orderData.StakingStrategy.GetSecond()
	targetTime := nowTime.Add(time.Second * time.Duration(float64(second)/orderData.StakingStrategy.RateCycle))
	if second == 0 {
		targetTime = nowTime.Add(time.Hour * 24)
	}
	orderInfo := &models.Order{
		AdminID:   userInfo.AdminID,
		UserID:    userInfo.ID,
		ProductID: productInfo.ID,
		OrderSN:   utils.GenerateOrderSN(),
		Money:     params.Money,
		Status:    models.ProductOrderStatusRunning,
		Side:      params.Side,
		Mode:      params.Mode,
		Type:      params.Type,
		Nums:      buyNums,
		Data:      orderData,
		ExpiredAt: targetTime,
	}

	result := tx.Create(orderInfo)
	if result.Error != nil {
		return result.Error
	}

	userWalletService := NewWalletService()

	if err := userWalletService.SpendBalance(tx, rdsConn, "", models.BillTypeProductPurchase, orderInfo.ID, userInfo, orderInfo.Fee); err != nil {
		return err
	}

	if spendAssetsId != 0 {
		assetsInfo := &models.WalletAssets{}
		result = tx.Model(assetsInfo).
			Where("id = ?", spendAssetsId).
			Where("admin_id = ?", merchantID).
			Find(assetsInfo)
		if result.RowsAffected == 0 {
			return errors.New("abnormalOperation")
		}

		// 资金支付
		err := userWalletService.SpendAssets(tx, rdsConn, acceptLang, models.BillTypeProductPurchase, orderInfo.ID, userInfo, assetsInfo, params.Money)
		if err != nil {
			return err
		}
	} else {
		//	余额支出
		return userWalletService.SpendBalance(tx, rdsConn, acceptLang, models.BillTypeProductPurchase, orderInfo.ID, userInfo, params.Money)
	}

	return nil
}
