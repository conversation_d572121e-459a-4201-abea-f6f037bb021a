package service

import (
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"
)

// WalletPaymentService 钱包支付服务
type WalletPaymentService struct {
	db *model.Model
}

// NewWalletPaymentService 创建钱包支付服务
func NewWalletPaymentService() *WalletPaymentService {
	return &WalletPaymentService{
		db: model.NewModel(),
	}
}

// GetPaymentOptions 获取支付方式选项
func (s *WalletPaymentService) GetPaymentOptions(subAdminIDs []uint, types []int8, modes []int8) []*views.SelectOption {
	var payments []struct {
		ID         uint
		Name       string
		AssetsID   uint
		AssetsName string
		AdminName  string
	}
	s.db.Table("wallet_payment wp").
		Select("wp.id", "wp.name", "wp.assets_id", "wa.name as assets_name", "adm.username as admin_name").
		Joins("left join admin_user adm on wp.admin_id = adm.id").
		Joins("left join wallet_assets wa on wp.assets_id = wa.id").
		Where("wp.admin_id IN ?", subAdminIDs).
		Where("wp.status = ?", models.PaymentStatusEnabled).
		Where("wp.deleted_at IS NULL").
		Where("wp.mode IN ?", modes).Where("wp.type IN ?", types).
		Find(&payments)

	options := make([]*views.SelectOption, 0)
	for _, payment := range payments {
		optionLabel := payment.Name
		if payment.AssetsID > 0 {
			optionLabel = optionLabel + "[资产]"
		}
		optionLabel = optionLabel + " (" + payment.AdminName + ")"

		options = append(options, &views.SelectOption{Label: optionLabel, Value: payment.ID})
	}
	return options
}
