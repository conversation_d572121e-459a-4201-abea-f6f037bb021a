package service

import (
	"errors"
	"fmt"
	"slices"
	"strings"
	"zfeng/core/cache"
	"zfeng/core/model"
	"zfeng/core/views"
	"zfeng/models"

	"github.com/gofiber/fiber/v2"
	"github.com/gomodule/redigo/redis"
)

const (
	// AdminPermissionsCacheKey 管理权限缓存Key
	AdminPermissionsCacheKey = "AdminPermissionsCacheKey"
)

// AdminRbacService 管理RBAC服务结构体
type AdminRbacService struct {
	db *model.Model
}

// NewAdminRbacService 创建新的管理RBAC服务实例
func NewAdminRbacService() *AdminRbacService {
	return &AdminRbacService{
		db: model.NewModel(),
	}
}

// GetUserPermissionsWithCache 获取用户权限和权限路由
func (s *AdminRbacService) GetUserPermissionsWithCache(rdsConn redis.Conn, adminID uint) ([]*models.AuthChild, error) {
	cacheKey := fmt.Sprintf("%s:%d", AdminPermissionsCacheKey, adminID)

	// 尝试从缓存中获取用户权限
	cachedData, err := redis.StringMap(rdsConn.Do("HGETALL", cacheKey))
	if err == nil && len(cachedData) > 0 {
		rolesPermissions := make([]*models.AuthChild, 0, len(cachedData))
		for k, v := range cachedData {
			rolesPermissions = append(rolesPermissions, &models.AuthChild{
				Parent: k,
				Child:  v,
			})
		}
		return rolesPermissions, nil
	}

	// 如果缓存中没有，则从数据库获取
	roles, err := s.GetUserRoles(adminID)
	if err != nil {
		return nil, fmt.Errorf("获取用户角色失败: %w", err)
	}

	rolesPermissions, err := s.GetRolePermissions(roles)
	if err != nil {
		return nil, fmt.Errorf("获取角色权限失败: %w", err)
	}

	for _, v := range rolesPermissions {
		if _, err := rdsConn.Do("HSET", cacheKey, v.Parent, v.Child); err != nil {
			return nil, fmt.Errorf("缓存权限失败: %w", err)
		}
	}

	// 设置过期时间
	if _, err := rdsConn.Do("EXPIRE", cacheKey, 3600); err != nil {
		return nil, fmt.Errorf("设置缓存过期时间失败: %w", err)
	}

	return rolesPermissions, nil
}

// GetUserRolesRouters 获取用户角色路由
func (s *AdminRbacService) GetUserRolesRouters(rdsConn redis.Conn, adminID uint) ([]string, error) {
	rolesPermissions, err := s.GetUserPermissionsWithCache(rdsConn, adminID)
	if err != nil {
		return nil, fmt.Errorf("获取用户权限失败: %w", err)
	}

	routers := make([]string, 0)
	for _, v := range rolesPermissions {
		if v.Parent != "" {
			routers = append(routers, v.Parent)
		}
	}

	return routers, nil
}

// GetUserRoles 获取用户角色
func (s *AdminRbacService) GetUserRoles(adminID uint) ([]string, error) {
	var roles []string
	err := s.db.Model(&models.AuthAssignment{}).
		Select("name").
		Where("admin_id = ?", adminID).
		Pluck("name", &roles).Error
	return roles, err
}

// GetSubRoleOptions 获取子级角色 options
func (s *AdminRbacService) GetSubRoleOptions(adminID uint) []*views.SelectOption {
	roles, _ := s.GetUserRoles(adminID)
	var subRoles []models.AuthChild
	err := s.db.Select("child").
		Where("type = ? AND (parent IN ? OR parent = ?)",
			models.AuthChildTypeRoleRole,
			roles,
			fmt.Sprintf(models.AdminRolesPrefix, adminID)).
		Where("deleted_at IS NULL").
		Find(&subRoles).Error
	if err != nil {
		return nil
	}

	options := make([]*views.SelectOption, len(subRoles))
	for i, role := range subRoles {
		options[i] = &views.SelectOption{
			Label: role.Child,
			Value: role.Child,
		}
	}

	return options
}

// GetRolePermissionOptions 获取角色权限options
func (s *AdminRbacService) GetRolePermissionOptions(roles []string, currentRoles []string) ([]*views.CheckboxOption, error) {
	rolesPermissions, err := s.GetRolePermissions(roles)
	if err != nil {
		return nil, fmt.Errorf("获取角色权限失败: %w", err)
	}

	currentRolesPermissions, err := s.GetRolePermissions(currentRoles)
	if err != nil {
		return nil, fmt.Errorf("获取当前角色权限失败: %w", err)
	}

	options := make([]*views.CheckboxOption, 0)

	for _, permission := range rolesPermissions {
		if permission.Child != "" {
			value := false
			for _, currentPermission := range currentRolesPermissions {
				if currentPermission.Child == permission.Child {
					value = true
					break
				}
			}
			options = append(options, &views.CheckboxOption{
				Label:   permission.Child,
				Value:   permission.Child,
				Checked: value,
			})
		}
	}

	return options, nil
}

// CheckRoleMatchesSubRoles 检查给定的角色是否匹配子角色选项
func (s *AdminRbacService) CheckRoleMatchesSubRoles(adminID uint, roleToCheck string) bool {
	subRoleOptions := s.GetSubRoleOptions(adminID)

	for _, option := range subRoleOptions {
		if option.Value.(string) == roleToCheck {
			return true
		}
	}

	return false
}

// GetRolePermissions 获取角色权限
func (s *AdminRbacService) GetRolePermissions(roles []string) ([]*models.AuthChild, error) {
	permissions := make([]*models.AuthChild, 0)
	if len(roles) == 0 {
		return permissions, nil
	}

	err := s.db.Model(&models.AuthChild{}).
		Select("DISTINCT auth_child.child as child, ac2.child as parent").
		Joins("LEFT JOIN auth_child ac2 ON auth_child.child = ac2.parent").
		Where("auth_child.parent IN ? AND auth_child.type = ?", roles, models.AuthChildTypeRolePermission).
		Find(&permissions).Error
	return permissions, err
}

// GetPermissionByRoute 获取路由对应的权限
func (s *AdminRbacService) GetPermissionByRoute(rdsConn redis.Conn, adminID uint, permissionRoute string) *models.AuthChild {
	rolesPermissions, err := s.GetUserPermissionsWithCache(rdsConn, adminID)
	if err != nil {
		return nil
	}

	for _, v := range rolesPermissions {
		if v.Parent == permissionRoute {
			return v
		}
	}

	return nil
}

// CheckUserPermissions 检查用户是否拥有指定的权限列表
func (s *AdminRbacService) CheckUserPermissions(adminID uint, requiredPermissions []*views.CheckboxOption) error {
	roles, err := s.GetUserRoles(adminID)
	if err != nil {
		return err
	}
	permissions, err := s.GetRolePermissions(roles)
	if err != nil {
		return err
	}

	// 辅助函数：检查切片中是否包含某个元素
	contains := func(slice []*models.AuthChild, item string) bool {
		for _, s := range slice {
			if s.Child == item {
				return true
			}
		}
		return false
	}

	// 检查所有请求的权限是否在当前管理员的权限列表中
	for _, permission := range requiredPermissions {
		if !contains(permissions, permission.Value.(string)) {
			return errors.New("请求的权限不在您的权限范围内【" + permission.Label + "】")
		}
	}
	return nil
}

// DeleteAdminPermissionsCache 删除指定用户的权限缓存
func (s *AdminRbacService) DeleteAdminPermissionsCache(rdsConn redis.Conn, adminID uint) error {
	cacheKey := fmt.Sprintf("%s:%d", AdminPermissionsCacheKey, adminID)
	_, err := rdsConn.Do("DEL", cacheKey)
	if err != nil {
		return fmt.Errorf("删除用户权限缓存失败: %w", err)
	}

	// 并且删除当前用户的缓存菜单
	return NewAdminMenuService().DeleteAdminMenuTreeCache(rdsConn, adminID)
}

// DeleteRolePermissionsCache 删除指定角色的权限缓存
func (s *AdminRbacService) DeleteRolePermissionsCache(rdsConn redis.Conn, roleName string) error {
	// 获取所有与该角色相关的管理员
	var assignments []models.AuthAssignment
	if err := model.NewModel().Where("name = ?", roleName).Find(&assignments).Error; err != nil {
		return fmt.Errorf("获取角色分配失败: %w", err)
	}

	// 删除每个相关管理员的权限缓存
	for _, assignment := range assignments {
		if err := s.DeleteAdminPermissionsCache(rdsConn, assignment.AdminId); err != nil {
			return fmt.Errorf("删除管理员 %d 的权限缓存失败: %w", assignment.AdminId, err)
		}
	}

	return nil
}

// InitRouterPermissions 初始化路由权限
func InitRouterPermissions(prefix string, app *fiber.App) {
	routers := app.GetRoutes()

	// 获取所有角色和现有路由
	var roles []*models.AuthItem
	var existingRoutes []string
	var err error

	if err = model.NewModel().Where("type = ?", models.AuthItemTypeRole).Find(&roles).Error; err != nil {
		panic(fmt.Errorf("获取角色失败: %w", err))
	}

	if err = model.NewModel().Model(&models.AuthItem{}).Where("type = ?", models.AuthItemTypeRoute).Pluck("name", &existingRoutes).Error; err != nil {
		panic(fmt.Errorf("获取现有路由失败: %w", err))
	}

	// 使用 map 来优化查找效率
	existingRoutesMap := make(map[string]bool)
	for _, route := range existingRoutes {
		existingRoutesMap[route] = true
	}

	var newAuthItems []*models.AuthItem
	var newAuthChilds []*models.AuthChild

	// 处理新路由
	for _, route := range routers {
		if route.Path != prefix && strings.HasPrefix(route.Path, prefix) {
			routePath := strings.TrimPrefix(route.Path, prefix)
			if !existingRoutesMap[routePath] {
				newAuthItems = append(newAuthItems,
					&models.AuthItem{Name: route.Name, Type: models.AuthItemTypePermission},
					&models.AuthItem{Name: routePath, Type: models.AuthItemTypeRoute},
				)
				newAuthChilds = append(newAuthChilds,
					&models.AuthChild{Parent: route.Name, Child: routePath, Type: models.AuthChildTypePermissionRoute},
				)

				processRolePermissions(roles, route.Name, &newAuthChilds)
			}
		}
	}

	// 批量创建新的权限项和关系
	if len(newAuthItems) > 0 {
		if err := createNewAuthItemsAndRelations(newAuthItems, newAuthChilds); err != nil {
			panic(err)
		}

		if err := clearAdminPermissionsCache(); err != nil {
			panic(err)
		}
	}
}

// processRolePermissions 处理角色权限
func processRolePermissions(roles []*models.AuthItem, routeName string, newAuthChilds *[]*models.AuthChild) {
	for _, role := range roles {
		if len(role.Data.FilterPermissions) > 0 {
			if !slices.Contains(role.Data.FilterPermissions, routeName) {
				*newAuthChilds = append(*newAuthChilds,
					&models.AuthChild{Parent: role.Name, Child: routeName, Type: models.AuthChildTypeRolePermission},
				)
			}
		} else if len(role.Data.Permissions) > 0 {
			if slices.Contains(role.Data.Permissions, routeName) || slices.Contains(role.Data.Permissions, "*") {
				*newAuthChilds = append(*newAuthChilds,
					&models.AuthChild{Parent: role.Name, Child: routeName, Type: models.AuthChildTypeRolePermission},
				)
			}
		}
	}
}

// createNewAuthItemsAndRelations 创建新的权限项和关系
func createNewAuthItemsAndRelations(newAuthItems []*models.AuthItem, newAuthChilds []*models.AuthChild) error {
	if err := model.NewModel().CreateInBatches(newAuthItems, 100).Error; err != nil {
		return fmt.Errorf("创建新权限项失败: %w", err)
	}
	if err := model.NewModel().CreateInBatches(newAuthChilds, 100).Error; err != nil {
		return fmt.Errorf("创建新权限关系失败: %w", err)
	}
	return nil
}

// clearAdminPermissionsCache 清除管理员权限缓存
func clearAdminPermissionsCache() error {
	rdsConn := cache.Rds.Get()
	defer rdsConn.Close()

	var adminList []*models.AdminUser
	if err := model.NewModel().Find(&adminList).Error; err != nil {
		return fmt.Errorf("获取管理员列表失败: %w", err)
	}

	rbacService := NewAdminRbacService()
	for _, admin := range adminList {
		if err := rbacService.DeleteAdminPermissionsCache(rdsConn, admin.ID); err != nil {
			return fmt.Errorf("删除管理员 %d 的权限缓存失败: %w", admin.ID, err)
		}
	}

	return nil
}
