package crontab

import (
	"zfeng/crontab/systems"
	"zfeng/service"

	"github.com/robfig/cron/v3"
)

const (
	logMsg = "crontab"
)

// Inits 定时任务初始化数据
func Inits() {
	c := cron.New()
	defer c.Start()

	// 初始化系统定时任务
	systems.SyncWalletAssetsRate(c)

	// 产品订单
	productService := service.NewProductService()
	symbolsDecimal := productService.GetProductIeForexSymbolDecimalList()
	ProductOrderCrontab(c, symbolsDecimal)
}
