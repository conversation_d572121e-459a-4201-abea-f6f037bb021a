package systems

import (
	"strconv"
	"zfeng/core/model"
	"zfeng/models"
	"zfeng/utils"

	"github.com/robfig/cron/v3"
)

type AssetsParams struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
	Data []struct {
		InstId    string `json:"instId"`
		InstType  string `json:"instType"`
		Last      string `json:"last"`
		LastSz    string `json:"lastSz"`
		AskPx     string `json:"askPx"`
		AskSz     string `json:"askSz"`
		BidPx     string `json:"bidPx"`
		BidSz     string `json:"bidSz"`
		Open24h   string `json:"open24h"`
		High24h   string `json:"high24h"`
		Low24h    string `json:"low24h"`
		Vol24h    string `json:"vol24h"`
		VolCcy24h string `json:"volCcy24h"`
		Ts        string `json:"ts"`
		SodUtc0   string `json:"sodUtc0"`
		SodUtc8   string `json:"sodUtc8"`
	} `json:"data"`
}

func (r *AssetsParams) getRate(currency string) float64 {
	for _, v := range r.Data {
		if v.InstId == currency+"-USDT" {
			rate, _ := strconv.ParseFloat(v.Last, 64)
			return rate
		}
	}
	return 0
}

// SyncWalletAssetsRate 同步钱包资产汇率 10分钟一次
func SyncWalletAssetsRate(c *cron.Cron) {
	c.AddFunc("*/10 * * * *", func() {
		okxGetTickers()
	})
}

func okxGetTickers() *AssetsParams {
	data := &AssetsParams{}
	utils.NewHttpClient("https://www.okx.com/api/v5").
		Get("/market/tickers?instType=SPOT").ToStruct(data)

	walletAssets := make([]*models.WalletAssets, 0)
	db := model.NewModel()
	db.Where("status = ?", models.WalletAssetsStatusEnabled).Find(&walletAssets)

	// 更新汇率
	for _, v := range walletAssets {
		rate := data.getRate(v.Currency)
		if rate > 0 {
			db.Model(&models.WalletAssets{}).Where("id = ?", v.ID).Update("rate", rate)
		}
	}
	return data
}
