package crontab

import (
	"github.com/gomodule/redigo/redis"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"time"
	"zfeng/core/model"
	"zfeng/inits/instance/markets"
	"zfeng/models"
	"zfeng/module/socket"
	"zfeng/service"
)

// InitializeProductStakingOrder 质押订单待办
func InitializeProductStakingOrder(rdsConn redis.Conn, orderList []*models.OrderInfo) {
	db := model.NewModel()
	nowTime := time.Now()
	for _, info := range orderList {
		orderData := info.Data
		starking := info.Data.StakingStrategy
		second := starking.GetSecond()
		targetTime := nowTime.Add(time.Second * time.Duration(float64(second)/starking.RateCycle))
		// 活期收益按每天计算
		if second == 0 {
			targetTime = nowTime.Add(time.Hour * 24)
		}
		orderData.Price = starking.Rate / 100 * info.Money
		orderData.Amount += orderData.Price
		orderData.WhichDay++
		userInfo := info.UserInfo
		err := db.Transaction(func(tx *gorm.DB) error {
			merchantID, _ := service.NewAdminUserService().GetMerchantIDWithCache(rdsConn, userInfo.AdminID)
			depositAssetsId := info.ProductInfo.Data.SymbolAssetsID
			// 到期结算
			if starking.RateCycle <= orderData.WhichDay {
				info.Status = models.ProductOrderStatusCompleted
				orderData.Price = info.Money
			}
			if depositAssetsId != 0 {
				assetsInfo := &models.WalletAssets{}
				result := db.Model(assetsInfo).Where("id = ?", depositAssetsId).Where("admin_id = ?", merchantID).Find(assetsInfo)
				if result.RowsAffected == 0 {
					return gorm.ErrRecordNotFound
				}

				// 资产结算
				if err := service.NewWalletService().IncreaseAssets(tx, rdsConn, "", models.BillTypeProductEarnings, info.ID, userInfo, assetsInfo, orderData.Price); err != nil {
					tx.Rollback()
					return err
				}
			} else {
				// 余额结算
				if err := service.NewWalletService().IncreaseBalance(tx, rdsConn, "", models.BillTypeProductEarnings, info.ID, userInfo, orderData.Price); err != nil {
					tx.Rollback()
					return err
				}
			}

			info.ExpiredAt = targetTime
			if result := tx.Model(&models.Order{}).
				Where("id = ?", info.ID).
				Updates(&models.Order{
					Status:    info.Status,
					Data:      orderData,
					ExpiredAt: info.ExpiredAt,
				}); result.Error != nil {
				return result.Error
			}

			return nil
		})
		if err != nil {
			zap.L().Error(logMsg, zap.Error(err))
		}
		markets.ProductSocket.RedisUserPublish(rdsConn, socket.ConnTypeProduct, socket.MessageOperateOrder, info.UserID, "Finish")
	}

}
