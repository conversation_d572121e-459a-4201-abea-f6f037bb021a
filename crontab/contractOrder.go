package crontab

import (
	"zfeng/core/model"
	"zfeng/inits/instance/markets"
	"zfeng/models"
	"zfeng/module/exchange/interfaces"
	"zfeng/module/socket"
	"zfeng/service"

	"github.com/gomodule/redigo/redis"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// InitializeProductContractOrder 合约进行的订单
func InitializeProductContractOrder(rdsConn redis.Conn, orderList []*models.OrderInfo) {
	productService := service.NewProductService()
	for _, orderInfo := range orderList {
		productInfo := orderInfo.ProductInfo
		tickers, err := productService.GetTickers(rdsConn, productInfo.Type, productInfo.Symbol)
		if err != nil || tickers.Last <= 0 {
			continue
		}

		var isTrue bool
		orderData := orderInfo.Data
		if orderData.SellPrice > 0 {
			tickers.Last = orderData.SellPrice
		}

		userInfo := orderInfo.UserInfo

		// 	爆仓操作
		isTrue = contractLiquidate(rdsConn, userInfo, orderInfo, orderData, tickers)
		if isTrue {
			continue
		}

		// 	止赢操作
		isTrue = contractTakePrice(rdsConn, userInfo, orderInfo, &orderData, tickers)
		if isTrue {
			continue
		}

		// 	止损操作
		isTrue = contractStopPrice(rdsConn, userInfo, orderInfo, &orderData, tickers)
		if isTrue {
			continue
		}
	}
}

// contractLiquidate 合约爆仓操作
func contractLiquidate(rdsConn redis.Conn, userInfo *models.User, orderInfo *models.OrderInfo, orderData models.OrderData, tickers interfaces.Tickers) bool {
	if orderInfo.IsLiquidate(tickers.Last) {
		db := model.NewModel()
		orderData.SellPrice = tickers.Last
		orderData.Amount = -orderInfo.Money
		result := db.Updates(&models.Order{
			BaseModel: model.BaseModel{ID: orderInfo.ID},
			Status:    models.ProductOrderStatusLiquidate,
			Data:      orderData,
		})
		if result.Error != nil {
			zap.L().Error(logMsg, zap.Error(result.Error))
		}

		markets.ProductSocket.RedisUserPublish(rdsConn, socket.ConnTypeProduct, socket.MessageOperateOrder, userInfo.ID, "Finish")

		return true
	}

	return false
}

// contractStopPrice 合约止损订单
func contractStopPrice(rdsConn redis.Conn, userInfo *models.User, orderInfo *models.OrderInfo, orderData *models.OrderData, tickers interfaces.Tickers) bool {
	productInfo := orderInfo.ProductInfo
	merchantID, _ := service.NewAdminUserService().GetMerchantIDWithCache(rdsConn, userInfo.AdminID)

	if orderInfo.IsStopLoss(tickers.Last) {
		db := model.NewModel()
		// 记录卖出价格
		orderData.SellPrice = tickers.Last
		orderData.Amount = orderInfo.Amount(tickers.Last)

		// 写入资产
		err := db.Transaction(func(tx *gorm.DB) error {
			if productInfo.AssetsID != 0 {
				// 资产转移
				assetsInfo := &models.WalletAssets{}
				if result := db.Model(assetsInfo).Where("id = ?", productInfo.AssetsID).Where("admin_id = ?", merchantID).Find(assetsInfo); result.RowsAffected == 0 {
					return gorm.ErrRecordNotFound
				}

				// 资产花费
				if err := service.NewWalletService().IncreaseAssets(tx, rdsConn, "", models.BillTypeProductEarnings, orderInfo.ID, userInfo, assetsInfo, orderData.Amount); err != nil {
					tx.Rollback()
					return err
				}
			} else {
				// 余额花费
				if err := service.NewWalletService().IncreaseBalance(tx, rdsConn, "", models.BillTypeProductEarnings, orderInfo.ID, userInfo, orderData.Amount); err != nil {
					tx.Rollback()
					return err
				}
			}

			if result := tx.Model(&models.Order{}).Where("id = ?", orderInfo.ID).Updates(&models.Order{
				Status: models.ProductOrderStatusCompleted,
				Data:   *orderData,
				Fee:    orderInfo.AmountFee(tickers.Last),
			}); result.Error != nil {
				return result.Error
			}

			return nil
		})
		if err != nil {
			zap.L().Error(logMsg, zap.Error(err))
			return false
		}

		markets.ProductSocket.RedisUserPublish(rdsConn, socket.ConnTypeProduct, socket.MessageOperateOrder, userInfo.ID, "Finish")
		return true
	}

	return false
}

// contractTakePrice 合约止赢操作
func contractTakePrice(rdsConn redis.Conn, userInfo *models.User, orderInfo *models.OrderInfo, orderData *models.OrderData, tickers interfaces.Tickers) bool {
	productInfo := orderInfo.ProductInfo
	merchantID, _ := service.NewAdminUserService().GetMerchantIDWithCache(rdsConn, userInfo.AdminID)

	if orderInfo.IsTakeProfit(tickers.Last) {
		// 记录卖出价格
		orderData.SellPrice = tickers.Last
		orderData.Amount = orderInfo.Amount(tickers.Last)

		// 写入资产
		db := model.NewModel()
		err := db.Transaction(func(tx *gorm.DB) error {
			if productInfo.AssetsID != 0 {
				assetsInfo := &models.WalletAssets{}
				result := db.Model(assetsInfo).Where("id = ?", productInfo.AssetsID).Where("admin_id = ?", merchantID).Find(assetsInfo)
				if result.RowsAffected == 0 {
					return gorm.ErrRecordNotFound
				}

				// 资产花费
				if err := service.NewWalletService().IncreaseAssets(tx, rdsConn, "", models.BillTypeProductEarnings, orderInfo.ID, userInfo, assetsInfo, orderData.Amount); err != nil {
					tx.Rollback()
					return err
				}
			} else {
				// 资产花费
				if err := service.NewWalletService().IncreaseBalance(tx, rdsConn, "", models.BillTypeProductEarnings, orderInfo.ID, userInfo, orderData.Amount); err != nil {
					tx.Rollback()
					return err
				}
			}

			result := tx.Model(&models.Order{}).Where("id = ?", orderInfo.ID).Updates(&models.Order{
				Status: models.ProductOrderStatusCompleted,
				Data:   *orderData,
				Fee:    orderInfo.AmountFee(tickers.Last),
			})
			if result.Error != nil {
				return result.Error
			}

			return nil
		})

		if err != nil {
			zap.L().Error(logMsg, zap.Error(err))
			return false
		}
		markets.ProductSocket.RedisUserPublish(rdsConn, socket.ConnTypeProduct, socket.MessageOperateOrder, userInfo.ID, "Finish")
		return true
	}

	return false
}
