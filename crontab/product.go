package crontab

import (
	"github.com/robfig/cron/v3"
	"time"
	"zfeng/core/cache"
	"zfeng/core/model"
	"zfeng/models"
)

// ProductOrderCrontab 产品定时任务
func ProductOrderCrontab(c *cron.Cron, symbolsDecimal map[string]int) {
	c.AddFunc("@every 1s", func() {
		rdsConn := cache.Rds.Get()
		orderList := make([]*models.OrderInfo, 0)
		model.NewModel().
			Preload("ProductInfo").
			Preload("UserInfo").
			Find(&orderList)
		productPendingOrder := make([]*models.OrderInfo, 0)
		productContractOrder := make([]*models.OrderInfo, 0)
		productFuturesOrder := make([]*models.OrderInfo, 0)
		productStakingOrder := make([]*models.OrderInfo, 0)

		nowTime := time.Now()
		for _, orderInfo := range orderList {
			switch {
			case (orderInfo.Type == models.ProductOrderTypeSpot || orderInfo.Type == models.ProductOrderTypeContract) && orderInfo.Status == models.ProductOrderStatusWaiting:
				productPendingOrder = append(productPendingOrder, orderInfo)
			case orderInfo.Type == models.ProductOrderTypeContract && orderInfo.Status == models.ProductOrderStatusRunning:
				productContractOrder = append(productContractOrder, orderInfo)
			case orderInfo.Type == models.ProductOrderTypeFutures && orderInfo.Status == models.ProductOrderStatusRunning && orderInfo.ExpiredAt.Unix() <= nowTime.Unix():
				productFuturesOrder = append(productFuturesOrder, orderInfo)
			case orderInfo.Type == models.ProductOrderTypeStaking && orderInfo.Status == models.ProductOrderStatusRunning && orderInfo.ExpiredAt.Unix() <= nowTime.Unix():
				productStakingOrder = append(productStakingOrder, orderInfo)
			}
		}

		// 产品限价订单 初始化
		InitializeProductPendingOrder(rdsConn, productPendingOrder)

		// 产品合约订单 初始化
		InitializeProductContractOrder(rdsConn, productContractOrder)

		// 产品期货订单 初始化
		InitializeProductFuturesOrder(rdsConn, productFuturesOrder)

		// 产品质押订单
		InitializeProductStakingOrder(rdsConn, productStakingOrder)

		// 初始化易汇产品数据
		//GenerateEhTicker(rdsConn, symbolsDecimal)

		rdsConn.Close()
	})
}
