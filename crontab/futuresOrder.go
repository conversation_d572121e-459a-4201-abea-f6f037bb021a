package crontab

import (
	"math/rand"
	"strconv"
	"time"
	"zfeng/core/model"
	"zfeng/inits/instance/markets"
	"zfeng/models"
	"zfeng/module/socket"
	"zfeng/service"

	"github.com/gomodule/redigo/redis"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// InitializeProductFuturesOrder 期货订单初始化
func InitializeProductFuturesOrder(rdsConn redis.Conn, orderList []*models.OrderInfo) {
	db := model.NewModel()
	productService := service.NewProductService()
	for _, orderInfo := range orderList {
		productInfo := orderInfo.ProductInfo
		tickers, err := productService.GetTickers(rdsConn, productInfo.Type, productInfo.Symbol)
		if err == nil && tickers.Last > 0 {
			orderData := orderInfo.Data
			userSettingInfo := models.Setting{}
			rowsAffected := db.Model(&userSettingInfo).
				Where("field = ?", models.UserSettingsFuturesWinningRate).
				Where("user_id = ?", orderInfo.UserID).
				Find(&userSettingInfo).RowsAffected
			futuresWinningRate, _ := strconv.Atoi(userSettingInfo.Value)
			if rowsAffected > 0 && futuresWinningRate >= 0 {
				if Probability(futuresWinningRate) {
					tickers.Last = productService.LoseOrWin(orderData.Price, productInfo.Data.SymbolAssetsDecimal, models.ProductOrderStatusWin, orderInfo.Side)
				} else {
					tickers.Last = productService.LoseOrWin(orderData.Price, productInfo.Data.SymbolAssetsDecimal, models.ProductOrderStatusLose, orderInfo.Side)
				}
			}
			if orderData.SellPrice > 0 {
				tickers.Last = orderData.SellPrice
			}

			// 最终价格
			orderNums := 0 - orderInfo.Money
			if (orderInfo.Side == models.ProductOrderSideBuy && orderData.Price < tickers.Last) ||
				(orderInfo.Side == models.ProductOrderSideSell && orderData.Price >= tickers.Last) {
				orderNums = orderInfo.Money + orderInfo.Money*orderData.Rate
			}
			err = db.Transaction(func(tx *gorm.DB) error {
				userInfo := orderInfo.UserInfo
				if orderNums > 0 {
					assetsInfo := &models.WalletAssets{}
					if productInfo.AssetsID > 0 {
						if result := db.Where("id = ?", productInfo.AssetsID).Find(assetsInfo); result.Error != nil || result.RowsAffected == 0 {
							return result.Error
						}
						// 资产消费
						if err := service.NewWalletService().IncreaseAssets(tx, rdsConn, "", models.BillTypeProductEarnings, orderInfo.ID, userInfo, assetsInfo, orderNums); err != nil {
							return err
						}
					} else {
						// 余额消费
						if err := service.NewWalletService().IncreaseBalance(tx, rdsConn, "", models.BillTypeProductEarnings, orderInfo.ID, userInfo, orderNums); err != nil {
							return err
						}
					}
				}
				orderData.SellPrice = tickers.Last
				orderData.Amount = orderNums
				if result := tx.Model(&models.Order{}).Where("id = ?", orderInfo.ID).Updates(&models.Order{
					Status: models.ProductOrderStatusCompleted,
					Data:   orderData,
				}); result.Error != nil {
					return result.Error
				}
				// 发送消息
				return nil
			})
			if err != nil {
				zap.L().Error(logMsg, zap.Error(err))
				continue
			}
			markets.ProductSocket.RedisUserPublish(rdsConn, socket.ConnTypeProduct, socket.MessageOperateOrder, orderInfo.UserID, "Finish")
		}
	}
}

// Probability 赢取几率
func Probability(val int) bool {
	rand.NewSource(time.Now().UnixNano())
	randInt := rand.Intn(100)
	return randInt < val
}
