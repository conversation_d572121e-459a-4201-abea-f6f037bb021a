package crontab

import (
	"sync"
	"time"
	"zfeng/inits/instance/markets"
	"zfeng/module/exchange/ieforex"
	"zfeng/module/exchange/interfaces"
	"zfeng/service"
	"zfeng/utils"

	"github.com/gomodule/redigo/redis"
	"github.com/robfig/cron/v3"
)

// UpdateEhTicker 更新易汇最新数据
func UpdateEhTicker(c *cron.Cron, symbols []string, productInfoMap *sync.Map) {
	ieforex.UpdateBaseTickers(symbols, productInfoMap)
	// 每十秒进行一次更新
	c.AddFunc("@every 1s", func() {
		for _, symbol := range symbols {
			time.Sleep(2 * time.Second)
			ieforex.UpdateBaseTickers([]string{symbol}, productInfoMap)
		}
	})
}

// GenerateEhTicker 生成易汇数据
func GenerateEhTicker(rdsConn redis.Conn, symbolsDecimal map[string]int) {
	productService := service.NewProductService()
	for k, v := range symbolsDecimal {
		tickers, err := ieforex.GetTickers(rdsConn, k)
		if err != nil {
			continue
		}
		random := utils.GenerateRandomInt(1, 3)
		tickers.Last = utils.FloatAccuracy(productService.OrderRandomPrice(tickers.Last, random, v), v)
		tickers.VolCcy24h = ieforex.GetRandom(tickers.VolCcy24h, 1, 5)
		tickers.Vol24h = ieforex.GetRandom(tickers.Vol24h, 1, 5)
		tickers.Ts = int(time.Now().Unix())
		markets.MessageFunc(interfaces.SubscribeChannelTickers, tickers.Symbol, rdsConn, tickers)
	}
}
