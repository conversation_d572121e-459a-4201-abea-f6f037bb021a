package crontab

import (
	"zfeng/core/model"
	"zfeng/inits/instance/markets"
	"zfeng/models"
	"zfeng/module/exchange/interfaces"
	"zfeng/module/socket"
	"zfeng/service"

	"github.com/gomodule/redigo/redis"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// InitializeProductPendingOrder 产品待办的订单
func InitializeProductPendingOrder(rdsConn redis.Conn, orderList []*models.OrderInfo) {
	productService := service.NewProductService()
	for _, orderInfo := range orderList {
		productInfo := orderInfo.ProductInfo
		tickers, err := productService.GetTickers(rdsConn, productInfo.Type, productInfo.Symbol)
		if err == nil && tickers.Last > 0 && orderInfo.Mode == models.ProductOrderModeLimit {
			// 如果 买入, 并且大于限价价格, 如果卖出, 并且小于限价价格  ==== 过滤
			if (orderInfo.Side == models.ProductOrderSideBuy && tickers.Last > orderInfo.Data.Price) ||
				(orderInfo.Side == models.ProductOrderSideSell && tickers.Last < orderInfo.Data.Price) {
				continue
			}

			var err error
			switch orderInfo.Type {
			// 币币交易 限价订单处理
			case models.ProductOrderTypeSpot:
				// 更新订单状态, 并且用户进行收益
				err = productPendingSpotOrder(rdsConn, orderInfo, tickers)

			// 合约交易 限价订单处理
			case models.ProductOrderTypeContract:
				err = productPendingContractOrder(orderInfo, tickers)
			}

			if err != nil {
				zap.L().Error(logMsg, zap.Error(err))
				continue
			}
			markets.ProductSocket.RedisUserPublish(rdsConn, socket.ConnTypeProduct, socket.MessageOperateOrder, orderInfo.UserID, "Finish")
		}
	}
}

// productPendingContractOrder 限价合约订单处理
func productPendingContractOrder(orderInfo *models.OrderInfo, tickers interfaces.Tickers) error {
	buyPrice := orderInfo.Data.Price
	// 如果买跌,并且买入价格比当前价格更低， 那么使用最新价格, 如果买涨，并且买入价格比当前价更高，那么使用最新价格
	if (orderInfo.Side == models.ProductOrderSideSell && buyPrice < tickers.Last) || (orderInfo.Side == models.ProductOrderSideBuy && buyPrice > tickers.Last) {
		buyPrice = tickers.Last
	}

	orderInfo.Data.Price = buyPrice
	db := model.NewModel()
	result := db.Model(&models.Order{}).Where("id = ?", orderInfo.ID).Updates(&models.Order{
		Status: models.ProductOrderStatusRunning,
		Nums:   orderInfo.Money / tickers.Last,
		Data:   orderInfo.Data,
	})
	if result.Error != nil {
		zap.L().Error(logMsg, zap.Error(result.Error))
		return result.Error
	}
	return nil
}

// productPendingSpotOrder 待办的限价币币交易
func productPendingSpotOrder(rdsConn redis.Conn, orderInfo *models.OrderInfo, tickers interfaces.Tickers) error {
	productInfo := orderInfo.ProductInfo
	db := model.NewModel()
	return db.Transaction(func(tx *gorm.DB) error {
		userInfo := orderInfo.UserInfo
		// 如果买跌,并且买入价格比当前价格更低， 那么使用最新价格, 如果买涨，并且买入价格比当前价更高，那么使用最新价格
		buyPrice := orderInfo.Data.Price
		if (orderInfo.Side == models.ProductOrderSideSell && buyPrice < tickers.Last) || (orderInfo.Side == models.ProductOrderSideBuy && buyPrice > tickers.Last) {
			buyPrice = tickers.Last
		}
		orderInfo.Data.Price = buyPrice

		depositAssetsId := productInfo.Data.SymbolAssetsID
		sumNums := orderInfo.Money / tickers.Last
		if orderInfo.Side == models.ProductOrderSideSell {
			depositAssetsId = productInfo.AssetsID
			sumNums = orderInfo.Money * tickers.Last
		}

		buyNums := sumNums - sumNums*productInfo.Fee
		merchantID, _ := service.NewAdminUserService().GetMerchantIDWithCache(rdsConn, userInfo.AdminID)
		if depositAssetsId != 0 {
			assetsInfo := &models.WalletAssets{}
			result := db.Model(assetsInfo).Where("id = ?", depositAssetsId).Where("admin_id = ?", merchantID).Find(assetsInfo)
			if result.RowsAffected == 0 {
				return gorm.ErrRecordNotFound
			}

			// 资产结算
			if err := service.NewWalletService().IncreaseAssets(tx, rdsConn, "", models.BillTypeProductEarnings, orderInfo.ID, userInfo, assetsInfo, buyNums); err != nil {
				tx.Rollback()
				return err
			}
		} else {
			// 资产结算
			if err := service.NewWalletService().IncreaseBalance(tx, rdsConn, "", models.BillTypeProductEarnings, orderInfo.ID, userInfo, buyNums); err != nil {
				tx.Rollback()
				return err
			}
		}

		// 更新状态
		result := tx.Model(&models.Order{}).Where("id = ?", orderInfo.ID).Updates(&models.Order{
			Status: models.ProductOrderStatusCompleted,
			Fee:    sumNums * productInfo.Fee,
			Nums:   sumNums,
			Data:   orderInfo.Data,
		})
		if result.Error != nil {
			zap.L().Error(logMsg, zap.Error(result.Error))
			return result.Error
		}

		return nil
	})
}
